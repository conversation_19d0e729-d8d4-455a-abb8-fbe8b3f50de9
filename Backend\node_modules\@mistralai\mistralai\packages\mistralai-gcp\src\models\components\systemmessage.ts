/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  TextChunk,
  TextChunk$inboundSchema,
  TextChunk$Outbound,
  TextChunk$outboundSchema,
} from "./textchunk.js";

export type SystemMessageContent = string | Array<TextChunk>;

export const Role = {
  System: "system",
} as const;
export type Role = ClosedEnum<typeof Role>;

export type SystemMessage = {
  content: string | Array<TextChunk>;
  role?: Role | undefined;
};

/** @internal */
export const SystemMessageContent$inboundSchema: z.ZodType<
  SystemMessageContent,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(TextChunk$inboundSchema)]);

/** @internal */
export type SystemMessageContent$Outbound = string | Array<TextChunk$Outbound>;

/** @internal */
export const SystemMessageContent$outboundSchema: z.ZodType<
  SystemMessageContent$Outbound,
  z.ZodTypeDef,
  SystemMessageContent
> = z.union([z.string(), z.array(TextChunk$outboundSchema)]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace SystemMessageContent$ {
  /** @deprecated use `SystemMessageContent$inboundSchema` instead. */
  export const inboundSchema = SystemMessageContent$inboundSchema;
  /** @deprecated use `SystemMessageContent$outboundSchema` instead. */
  export const outboundSchema = SystemMessageContent$outboundSchema;
  /** @deprecated use `SystemMessageContent$Outbound` instead. */
  export type Outbound = SystemMessageContent$Outbound;
}

export function systemMessageContentToJSON(
  systemMessageContent: SystemMessageContent,
): string {
  return JSON.stringify(
    SystemMessageContent$outboundSchema.parse(systemMessageContent),
  );
}

export function systemMessageContentFromJSON(
  jsonString: string,
): SafeParseResult<SystemMessageContent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => SystemMessageContent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'SystemMessageContent' from JSON`,
  );
}

/** @internal */
export const Role$inboundSchema: z.ZodNativeEnum<typeof Role> = z.nativeEnum(
  Role,
);

/** @internal */
export const Role$outboundSchema: z.ZodNativeEnum<typeof Role> =
  Role$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Role$ {
  /** @deprecated use `Role$inboundSchema` instead. */
  export const inboundSchema = Role$inboundSchema;
  /** @deprecated use `Role$outboundSchema` instead. */
  export const outboundSchema = Role$outboundSchema;
}

/** @internal */
export const SystemMessage$inboundSchema: z.ZodType<
  SystemMessage,
  z.ZodTypeDef,
  unknown
> = z.object({
  content: z.union([z.string(), z.array(TextChunk$inboundSchema)]),
  role: Role$inboundSchema.default("system"),
});

/** @internal */
export type SystemMessage$Outbound = {
  content: string | Array<TextChunk$Outbound>;
  role: string;
};

/** @internal */
export const SystemMessage$outboundSchema: z.ZodType<
  SystemMessage$Outbound,
  z.ZodTypeDef,
  SystemMessage
> = z.object({
  content: z.union([z.string(), z.array(TextChunk$outboundSchema)]),
  role: Role$outboundSchema.default("system"),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace SystemMessage$ {
  /** @deprecated use `SystemMessage$inboundSchema` instead. */
  export const inboundSchema = SystemMessage$inboundSchema;
  /** @deprecated use `SystemMessage$outboundSchema` instead. */
  export const outboundSchema = SystemMessage$outboundSchema;
  /** @deprecated use `SystemMessage$Outbound` instead. */
  export type Outbound = SystemMessage$Outbound;
}

export function systemMessageToJSON(systemMessage: SystemMessage): string {
  return JSON.stringify(SystemMessage$outboundSchema.parse(systemMessage));
}

export function systemMessageFromJSON(
  jsonString: string,
): SafeParseResult<SystemMessage, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => SystemMessage$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'SystemMessage' from JSON`,
  );
}
