/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FilePurpose,
  FilePurpose$inboundSchema,
  FilePurpose$outboundSchema,
} from "./filepurpose.js";
import {
  SampleType,
  SampleType$inboundSchema,
  SampleType$outboundSchema,
} from "./sampletype.js";
import {
  Source,
  Source$inboundSchema,
  Source$outboundSchema,
} from "./source.js";

export type UploadFileOut = {
  /**
   * The unique identifier of the file.
   */
  id: string;
  /**
   * The object type, which is always "file".
   */
  object: string;
  /**
   * The size of the file, in bytes.
   */
  sizeBytes: number;
  /**
   * The UNIX timestamp (in seconds) of the event.
   */
  createdAt: number;
  /**
   * The name of the uploaded file.
   */
  filename: string;
  purpose: FilePurpose;
  sampleType: SampleType;
  numLines?: number | null | undefined;
  mimetype?: string | null | undefined;
  source: Source;
  signature?: string | null | undefined;
};

/** @internal */
export const UploadFileOut$inboundSchema: z.ZodType<
  UploadFileOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  object: z.string(),
  bytes: z.number().int(),
  created_at: z.number().int(),
  filename: z.string(),
  purpose: FilePurpose$inboundSchema,
  sample_type: SampleType$inboundSchema,
  num_lines: z.nullable(z.number().int()).optional(),
  mimetype: z.nullable(z.string()).optional(),
  source: Source$inboundSchema,
  signature: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "bytes": "sizeBytes",
    "created_at": "createdAt",
    "sample_type": "sampleType",
    "num_lines": "numLines",
  });
});

/** @internal */
export type UploadFileOut$Outbound = {
  id: string;
  object: string;
  bytes: number;
  created_at: number;
  filename: string;
  purpose: string;
  sample_type: string;
  num_lines?: number | null | undefined;
  mimetype?: string | null | undefined;
  source: string;
  signature?: string | null | undefined;
};

/** @internal */
export const UploadFileOut$outboundSchema: z.ZodType<
  UploadFileOut$Outbound,
  z.ZodTypeDef,
  UploadFileOut
> = z.object({
  id: z.string(),
  object: z.string(),
  sizeBytes: z.number().int(),
  createdAt: z.number().int(),
  filename: z.string(),
  purpose: FilePurpose$outboundSchema,
  sampleType: SampleType$outboundSchema,
  numLines: z.nullable(z.number().int()).optional(),
  mimetype: z.nullable(z.string()).optional(),
  source: Source$outboundSchema,
  signature: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    sizeBytes: "bytes",
    createdAt: "created_at",
    sampleType: "sample_type",
    numLines: "num_lines",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UploadFileOut$ {
  /** @deprecated use `UploadFileOut$inboundSchema` instead. */
  export const inboundSchema = UploadFileOut$inboundSchema;
  /** @deprecated use `UploadFileOut$outboundSchema` instead. */
  export const outboundSchema = UploadFileOut$outboundSchema;
  /** @deprecated use `UploadFileOut$Outbound` instead. */
  export type Outbound = UploadFileOut$Outbound;
}

export function uploadFileOutToJSON(uploadFileOut: UploadFileOut): string {
  return JSON.stringify(UploadFileOut$outboundSchema.parse(uploadFileOut));
}

export function uploadFileOutFromJSON(
  jsonString: string,
): SafeParseResult<UploadFileOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UploadFileOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UploadFileOut' from JSON`,
  );
}
