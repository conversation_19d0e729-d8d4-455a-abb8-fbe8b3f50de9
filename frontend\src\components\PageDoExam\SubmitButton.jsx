import React from 'react';
import LoadingSpinner from '../loading/LoadingSpinner';
import { useDispatch } from 'react-redux';
import { setShowModalSubmit } from 'src/features/doExam/doExamSlice';

/**
 * Button component for submitting the exam
 *
 * @param {Object} props - Component props
 * @param {Function} props.handleSubmit - Function to handle submission
 * @param {boolean} props.isLoading - Whether submission is in progress
 */
const SubmitButton = ({ isLoading }) => {
  const dispatch = useDispatch();
  const handleSubmit = () => {
    dispatch(setShowModalSubmit(true));
  };

  return (
    <button
      onClick={handleSubmit}
      className="bg-sky-600 hover:bg-sky-700 text-white font-bold py-1 px-4 rounded w-full transition"
      disabled={isLoading}
      aria-label="Submit exam"
    >
      Nộp bài
    </button>
  );
};

export default SubmitButton;
