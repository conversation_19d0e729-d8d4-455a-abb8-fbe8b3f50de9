import React, { useState, useEffect, useRef } from "react";
import { Smile } from "lucide-react";
import EmojiPickerLib from "emoji-picker-react";

const EmojiPicker = ({ onSelect }) => {
    const [open, setOpen] = useState(false);
    const pickerRef = useRef();

    // Đóng khi click ra ngoài
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (pickerRef.current && !pickerRef.current.contains(event.target)) {
                setOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
        <div className="relative flex justify-center items-center" ref={pickerRef}>
            <button
                onClick={() => setOpen(!open)}
                className="text-gray-500 hover:text-sky-600 transition"
                title="Chèn biểu tượng cảm xúc"
                type="button"
            >
                <Smile size={18} />
            </button>

            {open && (
                <div className="absolute bottom-12 -right-8 z-50 text-[12px]">
                    <EmojiPickerLib
                        onEmojiClick={(emojiData) => {
                            onSelect(emojiData.emoji);
                            setOpen(false);
                        }}
                        autoFocusSearch={false}
                        height={400}
                        width={250}
                        searchDisabled={true}

                    />
                </div>
            )}
        </div>
    );
};

export default EmojiPicker;
