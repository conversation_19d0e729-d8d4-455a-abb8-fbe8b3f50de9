import { useDispatch, useSelector } from "react-redux";
import {
    readSheetAndUpdateTuitionSheet,
    setMonth,
    setSheetUrl,
    setColNames,
    setSheetIndex
} from "src/features/sheet/sheetSlice";
import { X } from "lucide-react";

const UpdateTuitionSheetModal = ({ onClose, isOpen }) => {
    const dispatch = useDispatch();
    const { loadingUpdate, success, data, colNames, sheetUrl, month, sheetIndex } = useSelector((state) => state.sheet);

    const handleUpdateSheet = () => {
        dispatch(readSheetAndUpdateTuitionSheet({ sheetUrl, month, colNames, sheetIndex }));
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
            <div className="bg-white w-full max-w-md rounded-xl shadow-lg p-6 relative">
                {/* Close Icon */}
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-500 hover:text-black"
                >
                    <X size={20} />
                </button>

                {/* Title */}
                <h2 className="text-xl font-semibold mb-4 text-center">Cập nhật học phí từ Google Sheet</h2>


                <p className="text-sm text-gray-500 mb-4">
                    Chức năng sẽ tự động tìm kiếm dữ liệu từ các cột rồi tự động update nên vui lòng nhập đúng tên cột
                </p>

                {/* Sheet URL Input */}
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Sheet URL</label>
                    <input
                        type="text"
                        value={sheetUrl || ""}
                        onChange={(e) => dispatch(setSheetUrl(e.target.value))}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-300"
                        placeholder="Nhập đường dẫn Google Sheet"
                    />
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Vị trí Sheet</label>
                    <select
                        value={sheetIndex}
                        onChange={(e) => dispatch(setSheetIndex(Number(e.target.value)))}
                        className="border border-gray-300 rounded px-3 py-2 w-full"
                    >
                        <option value={0}>Sheet 1</option>
                        <option value={1}>Sheet 2</option>
                        <option value={2}>Sheet 3</option>
                        <option value={3}>Sheet 4</option>
                        <option value={4}>Sheet 5</option>
                    </select>
                </div>


                {/* Month Input */}
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Tháng</label>
                    <input
                        type="month"
                        value={month || ""}
                        onChange={(e) => dispatch(setMonth(e.target.value))}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-300"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Cột SĐT HS</label>
                    <input
                        type="text"
                        value={colNames.studentPhone || ""}
                        onChange={(e) => dispatch(setColNames({ ...colNames, studentPhone: e.target.value }))}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-300"
                        placeholder="VD: SĐT HS"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Cột SĐT PH</label>
                    <input
                        type="text"
                        value={colNames.parentPhone || ""}
                        onChange={(e) => dispatch(setColNames({ ...colNames, parentPhone: e.target.value }))}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-300"
                        placeholder="VD: SĐT PH"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Cột HỌ VÀ TÊN</label>
                    <input
                        type="text"
                        value={colNames.fullName || ""}
                        onChange={(e) => dispatch(setColNames({ ...colNames, fullName: e.target.value }))}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-300"
                        placeholder="VD: HỌ VÀ TÊN"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Cột tích học phí</label>
                    <input
                        type="text"
                        value={colNames.targetMonthCol || ""}
                        onChange={(e) => dispatch(setColNames({ ...colNames, targetMonthCol: e.target.value }))}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-300"
                        placeholder="VD: T"
                    />
                </div>


                {/* Update Button */}
                <button
                    onClick={handleUpdateSheet}
                    disabled={loadingUpdate}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded transition"
                >
                    {loadingUpdate ? "Đang cập nhật..." : "Cập nhật"}
                </button>

                {/* Success Message */}
                {success && (
                    <p className="mt-3 text-green-600 text-sm text-center">
                        Sheet cập nhật thành công!
                    </p>
                )}

                {/* JSON Preview */}
                {data && (
                    <pre className="mt-4 max-h-40 overflow-y-auto text-xs bg-gray-100 p-3 rounded">
                        {JSON.stringify(data, null, 2)}
                    </pre>
                )}
            </div>
        </div>
    );
};

export default UpdateTuitionSheetModal;
