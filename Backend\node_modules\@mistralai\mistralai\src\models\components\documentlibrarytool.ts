/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const DocumentLibraryToolType = {
  DocumentLibrary: "document_library",
} as const;
export type DocumentLibraryToolType = ClosedEnum<
  typeof DocumentLibraryToolType
>;

export type DocumentLibraryTool = {
  type?: DocumentLibraryToolType | undefined;
  /**
   * Ids of the library in which to search.
   */
  libraryIds: Array<string>;
};

/** @internal */
export const DocumentLibraryToolType$inboundSchema: z.ZodNativeEnum<
  typeof DocumentLibraryToolType
> = z.nativeEnum(DocumentLibraryToolType);

/** @internal */
export const DocumentLibraryToolType$outboundSchema: z.ZodNativeEnum<
  typeof DocumentLibraryToolType
> = DocumentLibraryToolType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DocumentLibraryToolType$ {
  /** @deprecated use `DocumentLibraryToolType$inboundSchema` instead. */
  export const inboundSchema = DocumentLibraryToolType$inboundSchema;
  /** @deprecated use `DocumentLibraryToolType$outboundSchema` instead. */
  export const outboundSchema = DocumentLibraryToolType$outboundSchema;
}

/** @internal */
export const DocumentLibraryTool$inboundSchema: z.ZodType<
  DocumentLibraryTool,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: DocumentLibraryToolType$inboundSchema.default("document_library"),
  library_ids: z.array(z.string()),
}).transform((v) => {
  return remap$(v, {
    "library_ids": "libraryIds",
  });
});

/** @internal */
export type DocumentLibraryTool$Outbound = {
  type: string;
  library_ids: Array<string>;
};

/** @internal */
export const DocumentLibraryTool$outboundSchema: z.ZodType<
  DocumentLibraryTool$Outbound,
  z.ZodTypeDef,
  DocumentLibraryTool
> = z.object({
  type: DocumentLibraryToolType$outboundSchema.default("document_library"),
  libraryIds: z.array(z.string()),
}).transform((v) => {
  return remap$(v, {
    libraryIds: "library_ids",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DocumentLibraryTool$ {
  /** @deprecated use `DocumentLibraryTool$inboundSchema` instead. */
  export const inboundSchema = DocumentLibraryTool$inboundSchema;
  /** @deprecated use `DocumentLibraryTool$outboundSchema` instead. */
  export const outboundSchema = DocumentLibraryTool$outboundSchema;
  /** @deprecated use `DocumentLibraryTool$Outbound` instead. */
  export type Outbound = DocumentLibraryTool$Outbound;
}

export function documentLibraryToolToJSON(
  documentLibraryTool: DocumentLibraryTool,
): string {
  return JSON.stringify(
    DocumentLibraryTool$outboundSchema.parse(documentLibraryTool),
  );
}

export function documentLibraryToolFromJSON(
  jsonString: string,
): SafeParseResult<DocumentLibraryTool, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DocumentLibraryTool$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DocumentLibraryTool' from JSON`,
  );
}
