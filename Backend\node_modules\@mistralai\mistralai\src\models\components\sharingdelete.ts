/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  EntityType,
  EntityType$inboundSchema,
  EntityType$outboundSchema,
} from "./entitytype.js";

export type SharingDelete = {
  orgId: string;
  /**
   * The id of the entity (user, workspace or organization) to share with
   */
  shareWithUuid: string;
  /**
   * The type of entity, used to share a library.
   */
  shareWithType: EntityType;
};

/** @internal */
export const SharingDelete$inboundSchema: z.ZodType<
  SharingDelete,
  z.ZodTypeDef,
  unknown
> = z.object({
  org_id: z.string(),
  share_with_uuid: z.string(),
  share_with_type: EntityType$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "org_id": "orgId",
    "share_with_uuid": "shareWithUuid",
    "share_with_type": "shareWithType",
  });
});

/** @internal */
export type SharingDelete$Outbound = {
  org_id: string;
  share_with_uuid: string;
  share_with_type: string;
};

/** @internal */
export const SharingDelete$outboundSchema: z.ZodType<
  SharingDelete$Outbound,
  z.ZodTypeDef,
  SharingDelete
> = z.object({
  orgId: z.string(),
  shareWithUuid: z.string(),
  shareWithType: EntityType$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    orgId: "org_id",
    shareWithUuid: "share_with_uuid",
    shareWithType: "share_with_type",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace SharingDelete$ {
  /** @deprecated use `SharingDelete$inboundSchema` instead. */
  export const inboundSchema = SharingDelete$inboundSchema;
  /** @deprecated use `SharingDelete$outboundSchema` instead. */
  export const outboundSchema = SharingDelete$outboundSchema;
  /** @deprecated use `SharingDelete$Outbound` instead. */
  export type Outbound = SharingDelete$Outbound;
}

export function sharingDeleteToJSON(sharingDelete: SharingDelete): string {
  return JSON.stringify(SharingDelete$outboundSchema.parse(sharingDelete));
}

export function sharingDeleteFromJSON(
  jsonString: string,
): SafeParseResult<SharingDelete, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => SharingDelete$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'SharingDelete' from JSON`,
  );
}
