/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BuiltInConnectors,
  BuiltInConnectors$inboundSchema,
  BuiltInConnectors$outboundSchema,
} from "./builtinconnectors.js";

export const ToolExecutionStartedEventType = {
  ToolExecutionStarted: "tool.execution.started",
} as const;
export type ToolExecutionStartedEventType = ClosedEnum<
  typeof ToolExecutionStartedEventType
>;

export type ToolExecutionStartedEvent = {
  type?: ToolExecutionStartedEventType | undefined;
  createdAt?: Date | undefined;
  outputIndex?: number | undefined;
  id: string;
  name: BuiltInConnectors;
  arguments: string;
};

/** @internal */
export const ToolExecutionStartedEventType$inboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionStartedEventType
> = z.nativeEnum(ToolExecutionStartedEventType);

/** @internal */
export const ToolExecutionStartedEventType$outboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionStartedEventType
> = ToolExecutionStartedEventType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionStartedEventType$ {
  /** @deprecated use `ToolExecutionStartedEventType$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionStartedEventType$inboundSchema;
  /** @deprecated use `ToolExecutionStartedEventType$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionStartedEventType$outboundSchema;
}

/** @internal */
export const ToolExecutionStartedEvent$inboundSchema: z.ZodType<
  ToolExecutionStartedEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ToolExecutionStartedEventType$inboundSchema.default(
    "tool.execution.started",
  ),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  output_index: z.number().int().default(0),
  id: z.string(),
  name: BuiltInConnectors$inboundSchema,
  arguments: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "output_index": "outputIndex",
  });
});

/** @internal */
export type ToolExecutionStartedEvent$Outbound = {
  type: string;
  created_at?: string | undefined;
  output_index: number;
  id: string;
  name: string;
  arguments: string;
};

/** @internal */
export const ToolExecutionStartedEvent$outboundSchema: z.ZodType<
  ToolExecutionStartedEvent$Outbound,
  z.ZodTypeDef,
  ToolExecutionStartedEvent
> = z.object({
  type: ToolExecutionStartedEventType$outboundSchema.default(
    "tool.execution.started",
  ),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  outputIndex: z.number().int().default(0),
  id: z.string(),
  name: BuiltInConnectors$outboundSchema,
  arguments: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    outputIndex: "output_index",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionStartedEvent$ {
  /** @deprecated use `ToolExecutionStartedEvent$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionStartedEvent$inboundSchema;
  /** @deprecated use `ToolExecutionStartedEvent$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionStartedEvent$outboundSchema;
  /** @deprecated use `ToolExecutionStartedEvent$Outbound` instead. */
  export type Outbound = ToolExecutionStartedEvent$Outbound;
}

export function toolExecutionStartedEventToJSON(
  toolExecutionStartedEvent: ToolExecutionStartedEvent,
): string {
  return JSON.stringify(
    ToolExecutionStartedEvent$outboundSchema.parse(toolExecutionStartedEvent),
  );
}

export function toolExecutionStartedEventFromJSON(
  jsonString: string,
): SafeParseResult<ToolExecutionStartedEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolExecutionStartedEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolExecutionStartedEvent' from JSON`,
  );
}
