[{"D:\\ToanThayBee\\frontend\\src\\index.js": "1", "D:\\ToanThayBee\\frontend\\src\\App.js": "2", "D:\\ToanThayBee\\frontend\\src\\reportWebVitals.js": "3", "D:\\ToanThayBee\\frontend\\src\\redux\\store.js": "4", "D:\\ToanThayBee\\frontend\\src\\pages\\LoginPage.jsx": "5", "D:\\ToanThayBee\\frontend\\src\\components\\ProtectedRoute.jsx": "6", "D:\\ToanThayBee\\frontend\\src\\components\\error\\NotificationDisplay.jsx": "7", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\CodeManagement.jsx": "8", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\HomePageManagement.jsx": "9", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticleManagement.jsx": "10", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticlePostPage.jsx": "11", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "12", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\questionManagement.jsx": "13", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "14", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamManagement.jsx": "15", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "16", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "17", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "18", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "19", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "20", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassManagement.jsx": "21", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\LessonManagement.jsx": "22", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "23", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "25", "D:\\ToanThayBee\\frontend\\src\\features\\sidebar\\sidebarSlice.js": "26", "D:\\ToanThayBee\\frontend\\src\\features\\user\\userSlice.js": "27", "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterSlice.js": "28", "D:\\ToanThayBee\\frontend\\src\\features\\question\\questionSlice.js": "29", "D:\\ToanThayBee\\frontend\\src\\features\\auth\\authSlice.js": "30", "D:\\ToanThayBee\\frontend\\src\\features\\state\\stateApiSlice.js": "31", "D:\\ToanThayBee\\frontend\\src\\features\\code\\codeSlice.js": "32", "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examSlice.js": "33", "D:\\ToanThayBee\\frontend\\src\\features\\image\\imageSlice.js": "34", "D:\\ToanThayBee\\frontend\\src\\features\\answer\\answerSlice.js": "35", "D:\\ToanThayBee\\frontend\\src\\features\\attempt\\attemptSlice.js": "36", "D:\\ToanThayBee\\frontend\\src\\features\\class\\classSlice.js": "37", "D:\\ToanThayBee\\frontend\\src\\features\\article\\articleSlice.js": "38", "D:\\ToanThayBee\\frontend\\src\\utils\\sanitizeInput.js": "39", "D:\\ToanThayBee\\frontend\\src\\utils\\validation.js": "40", "D:\\ToanThayBee\\frontend\\src\\features\\achievement\\achievementSlice.js": "41", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForAuthPage.jsx": "42", "D:\\ToanThayBee\\frontend\\src\\components\\button\\GoogleLoginButton.jsx": "43", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingSpinner.jsx": "44", "D:\\ToanThayBee\\frontend\\src\\layouts\\AdminLayout.jsx": "45", "D:\\ToanThayBee\\frontend\\src\\layouts\\AuthLayout.jsx": "46", "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputForAuthPage.jsx": "47", "D:\\ToanThayBee\\frontend\\src\\components\\checkBox\\AuthCheckbox.jsx": "48", "D:\\ToanThayBee\\frontend\\src\\components\\logo\\BeeMathLogo.jsx": "49", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FunctionBarAdmin.jsx": "50", "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "51", "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\AuthDropMenu.jsx": "52", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBarAttemp.jsx": "53", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreDistributionChart.jsx": "54", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddQuestionModal.jsx": "55", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddCodeModal.jsx": "56", "D:\\ToanThayBee\\frontend\\src\\components\\table\\CodeTable.jsx": "57", "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTable.jsx": "58", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ArticleTable.jsx": "59", "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutMultipleImages.jsx": "60", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AdminModal.jsx": "61", "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownEditer.jsx": "62", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\QuestionDetail.jsx": "63", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ExamDetail.jsx": "64", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\PreviewExam.jsx": "65", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ExamTable.jsx": "66", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ClassDetail.jsx": "67", "D:\\ToanThayBee\\frontend\\src\\components\\table\\UserClassTable.jsx": "68", "D:\\ToanThayBee\\frontend\\src\\components\\Footer.jsx": "69", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassTable.jsx": "70", "D:\\ToanThayBee\\frontend\\src\\components\\input\\suggestInputBarAdmin.jsx": "71", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddClassModal.jsx": "72", "D:\\ToanThayBee\\frontend\\src\\components\\image\\LearningItemIcon.jsx": "73", "D:\\ToanThayBee\\frontend\\src\\components\\YouTubePlayer.jsx": "74", "D:\\ToanThayBee\\frontend\\src\\components\\ViewDetail.jsx": "75", "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayoutHome.jsx": "76", "D:\\ToanThayBee\\frontend\\src\\utils\\formatters.js": "77", "D:\\ToanThayBee\\frontend\\src\\components\\image\\SlideShow.jsx": "78", "D:\\ToanThayBee\\frontend\\src\\components\\latex\\RenderLatex.jsx": "79", "D:\\ToanThayBee\\frontend\\src\\components\\NetworkSpeedTest.jsx": "80", "D:\\ToanThayBee\\frontend\\src\\components\\CustomSchedule.jsx": "81", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ExamRegulationModal.jsx": "82", "D:\\ToanThayBee\\frontend\\src\\components\\StudentThoughts.jsx": "83", "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputSearch.jsx": "84", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\JoinClassModal.jsx": "85", "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayout.jsx": "86", "D:\\ToanThayBee\\frontend\\src\\components\\Pagination.jsx": "87", "D:\\ToanThayBee\\frontend\\src\\components\\card\\countDownCard.jsx": "88", "D:\\ToanThayBee\\frontend\\src\\components\\image\\ClassImage.jsx": "89", "D:\\ToanThayBee\\frontend\\src\\components\\ViewPdf.jsx": "90", "D:\\ToanThayBee\\frontend\\src\\utils\\apiHandler.js": "91", "D:\\ToanThayBee\\frontend\\src\\services\\authApi.js": "92", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreSummaryTable.jsx": "93", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "94", "D:\\ToanThayBee\\frontend\\src\\components\\card\\RelatedExamCard.jsx": "95", "D:\\ToanThayBee\\frontend\\src\\components\\card\\ExamCard.jsx": "96", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreBarChart.jsx": "97", "D:\\ToanThayBee\\frontend\\src\\components\\QrCode.jsx": "98", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ScreenButton.jsx": "99", "D:\\ToanThayBee\\frontend\\src\\services\\userApi.js": "100", "D:\\ToanThayBee\\frontend\\src\\services\\questionApi.js": "101", "D:\\ToanThayBee\\frontend\\src\\components\\ViewLearning.jsx": "102", "D:\\ToanThayBee\\frontend\\src\\components\\Schedule.jsx": "103", "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderDoExamPage.jsx": "104", "D:\\ToanThayBee\\frontend\\src\\components\\achievement\\AchievementSection.jsx": "105", "D:\\ToanThayBee\\frontend\\src\\components\\article\\Breadcrumb.jsx": "106", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\FilterExamSidebar.jsx": "107", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleList.jsx": "108", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleBreadcrumb.jsx": "109", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleSidebar.jsx": "110", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleRelatedSidebar.jsx": "111", "D:\\ToanThayBee\\frontend\\src\\components\\article\\SearchBar.jsx": "112", "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementStatTable.jsx": "113", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleHeader.jsx": "114", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleContent.jsx": "115", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "116", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementImageModal.jsx": "117", "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementCategoryTable.jsx": "118", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementStatModal.jsx": "119", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementImageModal.jsx": "120", "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementImageTable.jsx": "121", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "122", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddStudentModal.jsx": "123", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementStatModal.jsx": "124", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\UserDetail.jsx": "125", "D:\\ToanThayBee\\frontend\\src\\services\\codeApi.js": "126", "D:\\ToanThayBee\\frontend\\src\\services\\imageApi.js": "127", "D:\\ToanThayBee\\frontend\\src\\components\\table\\userTable.jsx": "128", "D:\\ToanThayBee\\frontend\\src\\services\\answerApi.js": "129", "D:\\ToanThayBee\\frontend\\src\\services\\articleApi.js": "130", "D:\\ToanThayBee\\frontend\\src\\services\\examApi.js": "131", "D:\\ToanThayBee\\frontend\\src\\services\\achievementApi.js": "132", "D:\\ToanThayBee\\frontend\\src\\components\\pagination\\Pagination.jsx": "133", "D:\\ToanThayBee\\frontend\\src\\services\\attemptApi.js": "134", "D:\\ToanThayBee\\frontend\\src\\services\\classApi.js": "135", "D:\\ToanThayBee\\frontend\\src\\utils\\excelExport.js": "136", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\AdminSidebar.jsx": "137", "D:\\ToanThayBee\\frontend\\src\\components\\ParticlesBackground.jsx": "138", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "139", "D:\\ToanThayBee\\frontend\\src\\components\\image\\UploadImage.jsx": "140", "D:\\ToanThayBee\\frontend\\src\\components\\table\\StatementTableRow.jsx": "141", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ChangeDescriptionCode.jsx": "142", "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTableRow.jsx": "143", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmDeleteModal.jsx": "144", "D:\\ToanThayBee\\frontend\\src\\components\\table\\TooltipTd.jsx": "145", "D:\\ToanThayBee\\frontend\\src\\components\\UploadPdf.jsx": "146", "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutImgae.jsx": "147", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\DetailTr.jsx": "148", "D:\\ToanThayBee\\frontend\\src\\utils\\question\\questionUtils.js": "149", "D:\\ToanThayBee\\frontend\\src\\components\\ScheduleModal.jsx": "150", "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderHome.jsx": "151", "D:\\ToanThayBee\\frontend\\src\\components\\header\\Header.jsx": "152", "D:\\ToanThayBee\\frontend\\src\\services\\api.js": "153", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleCard.jsx": "154", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ChapterFilters.jsx": "155", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TickSideBar.jsx": "156", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ClassFilters.jsx": "157", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmModal.jsx": "158", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ActiveFilters.jsx": "159", "D:\\ToanThayBee\\frontend\\src\\components\\article\\CategoryFilters.jsx": "160", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\UserSidebar.jsx": "161", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\HeaderSidebar.jsx": "162", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\StudentCardModal.jsx": "163", "D:\\ToanThayBee\\frontend\\src\\components\\header\\ChoiceHeader.jsx": "164", "D:\\ToanThayBee\\frontend\\src\\services\\responseInterceptor.js": "165", "D:\\ToanThayBee\\frontend\\src\\services\\requestInterceptor.js": "166", "D:\\ToanThayBee\\frontend\\src\\components\\image\\AvatarUploader.jsx": "167", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\OverViewPage.jsx": "168", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassDetailPage.jsx": "169", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\Home.jsx": "170", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassUserPage.jsx": "171", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\PracticePage.jsx": "172", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\LearningPage.jsx": "173", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\DoExamPage.jsx": "174", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ExamDetail.jsx": "175", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ScorePage.jsx": "176", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticleListPage.jsx": "177", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticlePage.jsx": "178", "D:\\ToanThayBee\\frontend\\src\\utils\\fullscreenUtils.js": "179", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\QuestionSection.jsx": "180", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ThemeToggleButton.jsx": "181", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TimeDisplay.jsx": "182", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SizeSlider.jsx": "183", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SettingsButton.jsx": "184", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ViewModeToggle.jsx": "185", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "186", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\SpinnerDemo.jsx": "187", "D:\\ToanThayBee\\frontend\\src\\features\\questionReport\\questionReportSlice.js": "188", "D:\\ToanThayBee\\frontend\\src\\services\\questionReportApi.js": "189", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ReportButton.jsx": "190", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ReportQuestionModal.jsx": "191", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\QuestionReportManagement.jsx": "192", "D:\\ToanThayBee\\frontend\\src\\components\\utils\\NoTranslate.jsx": "193", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "194", "D:\\ToanThayBee\\frontend\\src\\features\\notification\\notificationSlice.js": "195", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "196", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "197", "D:\\ToanThayBee\\frontend\\src\\components\\breadcrumb\\Breadcrumb.jsx": "198", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ModernArticleSidebar.jsx": "199", "D:\\ToanThayBee\\frontend\\src\\utils\\cacheManager.js": "200", "D:\\ToanThayBee\\frontend\\src\\services\\notificationApi.js": "201", "D:\\ToanThayBee\\frontend\\src\\components\\notification\\NotificationPanel.jsx": "202", "D:\\ToanThayBee\\frontend\\src\\features\\tuition\\tuitionSlice.js": "203", "D:\\ToanThayBee\\frontend\\src\\services\\tuitionApi.js": "204", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "205", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "206", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "207", "D:\\ToanThayBee\\frontend\\src\\components\\ClassSearchInput.jsx": "208", "D:\\ToanThayBee\\frontend\\src\\components\\UserSearchInput.jsx": "209", "D:\\ToanThayBee\\frontend\\src\\components\\PaymentModal.jsx": "210", "D:\\ToanThayBee\\frontend\\src\\components\\MultiClassSelector.jsx": "211", "D:\\ToanThayBee\\frontend\\src\\features\\attendance\\attendanceSlice.js": "212", "D:\\ToanThayBee\\frontend\\src\\services\\attendanceApi.js": "213", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\AttendancePage.jsx": "214", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "215", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "216", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "217", "D:\\ToanThayBee\\frontend\\src\\components\\attendance\\AttendanceCard.jsx": "218", "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\lessonSlice.js": "219", "D:\\ToanThayBee\\frontend\\src\\services\\lessonApi.js": "220", "D:\\ToanThayBee\\frontend\\src\\components\\team\\TeamSection.jsx": "221", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "222", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "223", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "224", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBar.jsx": "225", "D:\\ToanThayBee\\frontend\\src\\components\\banner\\ClassBanner.jsx": "226", "D:\\ToanThayBee\\frontend\\src\\layouts\\ClassAdminLayout.jsx": "227", "D:\\ToanThayBee\\frontend\\src\\utils\\maintenanceUtils.js": "228", "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceCleaner.jsx": "229", "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceWrapper.jsx": "230", "D:\\ToanThayBee\\frontend\\src\\config\\maintenance.js": "231", "D:\\ToanThayBee\\frontend\\src\\pages\\MaintenancePage.jsx": "232", "D:\\ToanThayBee\\frontend\\src\\components\\ScrollToTop.jsx": "233", "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownPreview.jsx": "234", "D:\\ToanThayBee\\frontend\\src\\layouts\\UserAdminLayout.jsx": "235", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\UserClassManagement.jsx": "236", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassOfUserTable.jsx": "237", "D:\\ToanThayBee\\frontend\\src\\features\\learningItem\\learningItemSlice.js": "238", "D:\\ToanThayBee\\frontend\\src\\services\\learningItemApi.js": "239", "D:\\ToanThayBee\\frontend\\src\\features\\doExam\\doExamSlice.js": "240", "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterReducer.js": "241", "D:\\ToanThayBee\\frontend\\src\\features\\pagination\\paginationReducer.js": "242", "D:\\ToanThayBee\\frontend\\src\\services\\doExamApi.js": "243", "D:\\ToanThayBee\\frontend\\src\\layouts\\404NotFound.jsx": "244", "D:\\ToanThayBee\\frontend\\src\\components\\input\\CustomSearchInput.jsx": "245", "D:\\ToanThayBee\\frontend\\src\\components\\UnpaidTuitionModal.jsx": "246", "D:\\ToanThayBee\\frontend\\src\\layouts\\ExamAdminLayout.jsx": "247", "D:\\ToanThayBee\\frontend\\src\\components\\table\\TotalComponent.jsx": "248", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForUserPage.jsx": "249", "D:\\ToanThayBee\\frontend\\src\\features\\sheet\\sheetSlice.js": "250", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "251", "D:\\ToanThayBee\\frontend\\src\\services\\sheetApi.js": "252", "D:\\ToanThayBee\\frontend\\src\\components\\ExamSearchInput.jsx": "253", "D:\\ToanThayBee\\frontend\\src\\components\\MultiLessonSelector.jsx": "254", "D:\\ToanThayBee\\frontend\\src\\components\\LessonSearchInput.jsx": "255", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "256", "D:\\ToanThayBee\\frontend\\src\\features\\calendar\\calendarSlice.js": "257", "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\index.js": "258", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\CalenderMonth.jsx": "259", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\DayView.jsx": "260", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\MonthView.jsx": "261", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\SidebarCalender.jsx": "262", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\WeekView.jsx": "263", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\TimeSlots.jsx": "264", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\NavigateTimeButton.jsx": "265", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "266", "D:\\ToanThayBee\\frontend\\src\\constants\\UserType.js": "267", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "268", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "269", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "270", "D:\\ToanThayBee\\frontend\\src\\services\\ocrExamApi.js": "271", "D:\\ToanThayBee\\frontend\\src\\components\\table\\TableAdmin.jsx": "272", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingData.jsx": "273", "D:\\ToanThayBee\\frontend\\src\\services\\apin8n.js": "274", "D:\\ToanThayBee\\frontend\\src\\utils\\setupKatexWarningFilter.js": "275", "D:\\ToanThayBee\\frontend\\src\\features\\dashboard\\dashboardSlice.js": "276", "D:\\ToanThayBee\\frontend\\src\\features\\questionsExam\\questionsExamSlice.js": "277", "D:\\ToanThayBee\\frontend\\src\\features\\addExam\\addExamSlice.js": "278", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\AddExamAdmin.jsx": "279", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StaffManagement.jsx": "280", "D:\\ToanThayBee\\frontend\\src\\services\\dashboardApi.js": "281", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddImagesModal.jsx": "282", "D:\\ToanThayBee\\frontend\\src\\hooks\\useDebouncedEffect.js": "283", "D:\\ToanThayBee\\frontend\\src\\components\\layout\\AdminLayout.jsx": "284", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\RightContent.jsx": "285", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\LeftContent.jsx": "286", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\LeftContent.jsx": "287", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\RightContent.jsx": "288", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionView.jsx": "289", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\SolutionEditor.jsx": "290", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\NavigateBar.jsx": "291", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\CompactStepHeader.jsx": "292", "D:\\ToanThayBee\\frontend\\src\\components\\input\\TextArea.jsx": "293", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\ImageView.jsx": "294", "D:\\ToanThayBee\\frontend\\src\\components\\image\\ImageDropZone.jsx": "295", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx": "296", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionContent.jsx": "297", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx": "298", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx": "299", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\question\\QuestionPage.jsx": "300", "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examDetailSlice.js": "301", "D:\\ToanThayBee\\frontend\\src\\features\\practice\\practiceSlice.js": "302", "D:\\ToanThayBee\\frontend\\src\\features\\scorePage\\scorePageSlice.js": "303", "D:\\ToanThayBee\\frontend\\src\\features\\ai\\aiSlice.js": "304", "D:\\ToanThayBee\\frontend\\src\\features\\comments\\ExamCommentsSlice.js": "305", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\BatteryLoading.jsx": "306", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ActionButton.jsx": "307", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingText.jsx": "308", "D:\\ToanThayBee\\frontend\\src\\utils\\codeUtils.js": "309", "D:\\ToanThayBee\\frontend\\src\\components\\filter\\SortBar.jsx": "310", "D:\\ToanThayBee\\frontend\\src\\components\\header\\ExamOverviewHeader.jsx": "311", "D:\\ToanThayBee\\frontend\\src\\components\\header\\LearningHeader.jsx": "312", "D:\\ToanThayBee\\frontend\\src\\components\\filter\\FilterBar.jsx": "313", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamContent.jsx": "314", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamSideBar.jsx": "315", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ModalSubmitExam.jsx": "316", "D:\\ToanThayBee\\frontend\\src\\components\\Schedule1.jsx": "317", "D:\\ToanThayBee\\frontend\\src\\utils\\shareUntil.js": "318", "D:\\ToanThayBee\\frontend\\src\\components\\common\\OutsideClickWrapper.jsx": "319", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentSection.jsx": "320", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\RankingView.jsx": "321", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\PreviewView.jsx": "322", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\HistoryView.jsx": "323", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AiChatWidget.jsx": "324", "D:\\ToanThayBee\\frontend\\src\\services\\examCommentsApi.js": "325", "D:\\ToanThayBee\\frontend\\src\\services\\studentExamApi.js": "326", "D:\\ToanThayBee\\frontend\\src\\services\\aiApi.js": "327", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\MultipleChoiceQuestion.jsx": "328", "D:\\ToanThayBee\\frontend\\src\\components\\header\\ButtonHeader.jsx": "329", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ShortAnswerQuestion.jsx": "330", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\LoadingQuestions.jsx": "331", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionSectionTitle.jsx": "332", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ProgressBar.jsx": "333", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionCounter.jsx": "334", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\SubmitButton.jsx": "335", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\TrueFalseQuestion.jsx": "336", "D:\\ToanThayBee\\frontend\\src\\components\\StarRating.jsx": "337", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\LoadingCommentItem.jsx": "338", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentInput.jsx": "339", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentItem.jsx": "340", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\QuestionDropdown.jsx": "341", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\OnlineLoading.jsx": "342", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\UserInfoPanel.jsx": "343", "D:\\ToanThayBee\\frontend\\src\\components\\common\\WrapperWithTooltip.jsx": "344", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionContent.jsx": "345", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\EmojiPicker.jsx": "346", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionImage.jsx": "347", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AIWidget.jsx": "348", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\FloatingAIWidget.jsx": "349", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\StudentManagementBar.jsx": "350"}, {"size": 837, "mtime": 1748831088160, "results": "351", "hashOfConfig": "352"}, {"size": 12232, "mtime": 1754037662792, "results": "353", "hashOfConfig": "352"}, {"size": 362, "mtime": 1740442931001, "results": "354", "hashOfConfig": "352"}, {"size": 3269, "mtime": 1754037662824, "results": "355", "hashOfConfig": "352"}, {"size": 7439, "mtime": 1752528418123, "results": "356", "hashOfConfig": "352"}, {"size": 1169, "mtime": 1753875510513, "results": "357", "hashOfConfig": "352"}, {"size": 7222, "mtime": 1753875510527, "results": "358", "hashOfConfig": "352"}, {"size": 2152, "mtime": 1751286250564, "results": "359", "hashOfConfig": "352"}, {"size": 11683, "mtime": 1748233656474, "results": "360", "hashOfConfig": "352"}, {"size": 1271, "mtime": 1744200312845, "results": "361", "hashOfConfig": "352"}, {"size": 21516, "mtime": 1751879443271, "results": "362", "hashOfConfig": "352"}, {"size": 350, "mtime": 1750332992366, "results": "363", "hashOfConfig": "352"}, {"size": 1979, "mtime": 1750250347447, "results": "364", "hashOfConfig": "352"}, {"size": 551, "mtime": 1742179063192, "results": "365", "hashOfConfig": "352"}, {"size": 2397, "mtime": 1752528418131, "results": "366", "hashOfConfig": "352"}, {"size": 19813, "mtime": 1750332992382, "results": "367", "hashOfConfig": "352"}, {"size": 2949, "mtime": 1754042630198, "results": "368", "hashOfConfig": "352"}, {"size": 1898, "mtime": 1750332992366, "results": "369", "hashOfConfig": "352"}, {"size": 9223, "mtime": 1751794342083, "results": "370", "hashOfConfig": "352"}, {"size": 275, "mtime": 1748225026676, "results": "371", "hashOfConfig": "352"}, {"size": 1739, "mtime": 1750250347447, "results": "372", "hashOfConfig": "352"}, {"size": 45070, "mtime": 1752565597538, "results": "373", "hashOfConfig": "352"}, {"size": 7884, "mtime": 1747360179971, "results": "374", "hashOfConfig": "352"}, {"size": 3498, "mtime": 1754307538699, "results": "375", "hashOfConfig": "352"}, {"size": 348, "mtime": 1749107127996, "results": "376", "hashOfConfig": "352"}, {"size": 733, "mtime": 1751794342077, "results": "377", "hashOfConfig": "352"}, {"size": 7714, "mtime": 1754307198240, "results": "378", "hashOfConfig": "352"}, {"size": 3345, "mtime": 1748243232604, "results": "379", "hashOfConfig": "352"}, {"size": 9913, "mtime": 1753875510541, "results": "380", "hashOfConfig": "352"}, {"size": 10253, "mtime": 1753875510536, "results": "381", "hashOfConfig": "352"}, {"size": 1380, "mtime": 1742271817436, "results": "382", "hashOfConfig": "352"}, {"size": 3523, "mtime": 1751794342076, "results": "383", "hashOfConfig": "352"}, {"size": 13809, "mtime": 1753875510541, "results": "384", "hashOfConfig": "352"}, {"size": 7895, "mtime": 1754037662810, "results": "385", "hashOfConfig": "352"}, {"size": 1717, "mtime": 1753875510536, "results": "386", "hashOfConfig": "352"}, {"size": 5924, "mtime": 1753875510536, "results": "387", "hashOfConfig": "352"}, {"size": 18524, "mtime": 1753875510541, "results": "388", "hashOfConfig": "352"}, {"size": 5150, "mtime": 1748424127072, "results": "389", "hashOfConfig": "352"}, {"size": 1337, "mtime": 1747742780655, "results": "390", "hashOfConfig": "352"}, {"size": 2480, "mtime": 1747742780655, "results": "391", "hashOfConfig": "352"}, {"size": 11766, "mtime": 1745568049769, "results": "392", "hashOfConfig": "352"}, {"size": 1339, "mtime": 1751437201798, "results": "393", "hashOfConfig": "352"}, {"size": 690, "mtime": 1740526054766, "results": "394", "hashOfConfig": "352"}, {"size": 1151, "mtime": 1750250347422, "results": "395", "hashOfConfig": "352"}, {"size": 673, "mtime": 1742650835143, "results": "396", "hashOfConfig": "352"}, {"size": 1495, "mtime": 1751434984402, "results": "397", "hashOfConfig": "352"}, {"size": 2276, "mtime": 1751437363838, "results": "398", "hashOfConfig": "352"}, {"size": 1228, "mtime": 1751434984402, "results": "399", "hashOfConfig": "352"}, {"size": 635, "mtime": 1744073048907, "results": "400", "hashOfConfig": "352"}, {"size": 9670, "mtime": 1752528418099, "results": "401", "hashOfConfig": "352"}, {"size": 3255, "mtime": 1752528418099, "results": "402", "hashOfConfig": "352"}, {"size": 3200, "mtime": 1743313304069, "results": "403", "hashOfConfig": "352"}, {"size": 5637, "mtime": 1747360179930, "results": "404", "hashOfConfig": "352"}, {"size": 1734, "mtime": 1743261282404, "results": "405", "hashOfConfig": "352"}, {"size": 28691, "mtime": 1744972102091, "results": "406", "hashOfConfig": "352"}, {"size": 5752, "mtime": 1751286250564, "results": "407", "hashOfConfig": "352"}, {"size": 4288, "mtime": 1752528418109, "results": "408", "hashOfConfig": "352"}, {"size": 10068, "mtime": 1752528418115, "results": "409", "hashOfConfig": "352"}, {"size": 7391, "mtime": 1752528418109, "results": "410", "hashOfConfig": "352"}, {"size": 7401, "mtime": 1747360179945, "results": "411", "hashOfConfig": "352"}, {"size": 1709, "mtime": 1752528418106, "results": "412", "hashOfConfig": "352"}, {"size": 1574, "mtime": 1744211538547, "results": "413", "hashOfConfig": "352"}, {"size": 28398, "mtime": 1749474339456, "results": "414", "hashOfConfig": "352"}, {"size": 17782, "mtime": 1753875510526, "results": "415", "hashOfConfig": "352"}, {"size": 23277, "mtime": 1754301028749, "results": "416", "hashOfConfig": "352"}, {"size": 6857, "mtime": 1752528418109, "results": "417", "hashOfConfig": "352"}, {"size": 17584, "mtime": 1750768842399, "results": "418", "hashOfConfig": "352"}, {"size": 8616, "mtime": 1752528418115, "results": "419", "hashOfConfig": "352"}, {"size": 10256, "mtime": 1744252686164, "results": "420", "hashOfConfig": "352"}, {"size": 5905, "mtime": 1752528418109, "results": "421", "hashOfConfig": "352"}, {"size": 3767, "mtime": 1752528418099, "results": "422", "hashOfConfig": "352"}, {"size": 19936, "mtime": 1748952128766, "results": "423", "hashOfConfig": "352"}, {"size": 645, "mtime": 1753875510532, "results": "424", "hashOfConfig": "352"}, {"size": 1815, "mtime": 1753875510517, "results": "425", "hashOfConfig": "352"}, {"size": 31136, "mtime": 1751794342051, "results": "426", "hashOfConfig": "352"}, {"size": 1205, "mtime": 1748942182512, "results": "427", "hashOfConfig": "352"}, {"size": 3670, "mtime": 1753875510551, "results": "428", "hashOfConfig": "352"}, {"size": 7937, "mtime": 1753875510533, "results": "429", "hashOfConfig": "352"}, {"size": 4276, "mtime": 1751879443263, "results": "430", "hashOfConfig": "352"}, {"size": 3146, "mtime": 1743125827996, "results": "431", "hashOfConfig": "352"}, {"size": 6050, "mtime": 1748239151420, "results": "432", "hashOfConfig": "352"}, {"size": 5818, "mtime": 1753875510535, "results": "433", "hashOfConfig": "352"}, {"size": 949, "mtime": 1747360179920, "results": "434", "hashOfConfig": "352"}, {"size": 2787, "mtime": 1750250347422, "results": "435", "hashOfConfig": "352"}, {"size": 4087, "mtime": 1753875510536, "results": "436", "hashOfConfig": "352"}, {"size": 1876, "mtime": 1754037662815, "results": "437", "hashOfConfig": "352"}, {"size": 3327, "mtime": 1753875510513, "results": "438", "hashOfConfig": "352"}, {"size": 2201, "mtime": 1744079807330, "results": "439", "hashOfConfig": "352"}, {"size": 6034, "mtime": 1748424127072, "results": "440", "hashOfConfig": "352"}, {"size": 841, "mtime": 1748949217083, "results": "441", "hashOfConfig": "352"}, {"size": 3005, "mtime": 1750869775159, "results": "442", "hashOfConfig": "352"}, {"size": 935, "mtime": 1745370017395, "results": "443", "hashOfConfig": "352"}, {"size": 1990, "mtime": 1743040478626, "results": "444", "hashOfConfig": "352"}, {"size": 2380, "mtime": 1743040371503, "results": "445", "hashOfConfig": "352"}, {"size": 5565, "mtime": 1745808607396, "results": "446", "hashOfConfig": "352"}, {"size": 5867, "mtime": 1753875510521, "results": "447", "hashOfConfig": "352"}, {"size": 2295, "mtime": 1747360179932, "results": "448", "hashOfConfig": "352"}, {"size": 313, "mtime": 1748229848432, "results": "449", "hashOfConfig": "352"}, {"size": 2985, "mtime": 1753875510521, "results": "450", "hashOfConfig": "352"}, {"size": 2098, "mtime": 1754307511316, "results": "451", "hashOfConfig": "352"}, {"size": 4165, "mtime": 1753875510551, "results": "452", "hashOfConfig": "352"}, {"size": 50713, "mtime": 1753875510516, "results": "453", "hashOfConfig": "352"}, {"size": 31011, "mtime": 1751004342884, "results": "454", "hashOfConfig": "352"}, {"size": 10898, "mtime": 1753875510532, "results": "455", "hashOfConfig": "352"}, {"size": 13822, "mtime": 1748237193361, "results": "456", "hashOfConfig": "352"}, {"size": 993, "mtime": 1747360179920, "results": "457", "hashOfConfig": "352"}, {"size": 10634, "mtime": 1745568049769, "results": "458", "hashOfConfig": "459"}, {"size": 2193, "mtime": 1747360179920, "results": "460", "hashOfConfig": "352"}, {"size": 1359, "mtime": 1747360179920, "results": "461", "hashOfConfig": "352"}, {"size": 2967, "mtime": 1744212069162, "results": "462", "hashOfConfig": "352"}, {"size": 4872, "mtime": 1747360179920, "results": "463", "hashOfConfig": "352"}, {"size": 902, "mtime": 1747360179930, "results": "464", "hashOfConfig": "352"}, {"size": 7151, "mtime": 1747360179961, "results": "465", "hashOfConfig": "352"}, {"size": 2245, "mtime": 1744211990050, "results": "466", "hashOfConfig": "352"}, {"size": 1641, "mtime": 1751879443263, "results": "467", "hashOfConfig": "352"}, {"size": 8511, "mtime": 1745582519057, "results": "468", "hashOfConfig": "352"}, {"size": 8211, "mtime": 1745568049750, "results": "469", "hashOfConfig": "352"}, {"size": 7421, "mtime": 1747360179955, "results": "470", "hashOfConfig": "352"}, {"size": 7894, "mtime": 1745568049750, "results": "471", "hashOfConfig": "352"}, {"size": 9990, "mtime": 1747360179947, "results": "472", "hashOfConfig": "352"}, {"size": 8563, "mtime": 1747360179961, "results": "473", "hashOfConfig": "352"}, {"size": 8784, "mtime": 1747360179947, "results": "474", "hashOfConfig": "352"}, {"size": 14565, "mtime": 1750841346217, "results": "475", "hashOfConfig": "352"}, {"size": 8094, "mtime": 1747360179947, "results": "476", "hashOfConfig": "352"}, {"size": 19265, "mtime": 1751794342061, "results": "477", "hashOfConfig": "352"}, {"size": 822, "mtime": 1751286250564, "results": "478", "hashOfConfig": "352"}, {"size": 2634, "mtime": 1754037662827, "results": "479", "hashOfConfig": "352"}, {"size": 6170, "mtime": 1754307494399, "results": "480", "hashOfConfig": "352"}, {"size": 297, "mtime": 1742720286594, "results": "481", "hashOfConfig": "352"}, {"size": 826, "mtime": 1748424127082, "results": "482", "hashOfConfig": "352"}, {"size": 3408, "mtime": 1754037662827, "results": "483", "hashOfConfig": "352"}, {"size": 4800, "mtime": 1745568049769, "results": "484", "hashOfConfig": "352"}, {"size": 4921, "mtime": 1747360179955, "results": "485", "hashOfConfig": "352"}, {"size": 1158, "mtime": 1753875510551, "results": "486", "hashOfConfig": "352"}, {"size": 4755, "mtime": 1751434984402, "results": "487", "hashOfConfig": "352"}, {"size": 10728, "mtime": 1750250347464, "results": "488", "hashOfConfig": "352"}, {"size": 9098, "mtime": 1753875510536, "results": "489", "hashOfConfig": "352"}, {"size": 411, "mtime": 1743206204710, "results": "490", "hashOfConfig": "352"}, {"size": 503, "mtime": 1744085691629, "results": "491", "hashOfConfig": "352"}, {"size": 8372, "mtime": 1752528418099, "results": "492", "hashOfConfig": "352"}, {"size": 3094, "mtime": 1742422820538, "results": "493", "hashOfConfig": "352"}, {"size": 2290, "mtime": 1744034601299, "results": "494", "hashOfConfig": "352"}, {"size": 2166, "mtime": 1742422800227, "results": "495", "hashOfConfig": "352"}, {"size": 1219, "mtime": 1747742780638, "results": "496", "hashOfConfig": "352"}, {"size": 2003, "mtime": 1742005297939, "results": "497", "hashOfConfig": "352"}, {"size": 10223, "mtime": 1754037662801, "results": "498", "hashOfConfig": "352"}, {"size": 7876, "mtime": 1747360179944, "results": "499", "hashOfConfig": "352"}, {"size": 4922, "mtime": 1748333524172, "results": "500", "hashOfConfig": "352"}, {"size": 24147, "mtime": 1754045901466, "results": "501", "hashOfConfig": "352"}, {"size": 20555, "mtime": 1748232252877, "results": "502", "hashOfConfig": "352"}, {"size": 12148, "mtime": 1748236140592, "results": "503", "hashOfConfig": "352"}, {"size": 28555, "mtime": 1753875510531, "results": "504", "hashOfConfig": "352"}, {"size": 394, "mtime": 1754219282862, "results": "505", "hashOfConfig": "352"}, {"size": 1885, "mtime": 1747360179920, "results": "506", "hashOfConfig": "352"}, {"size": 3182, "mtime": 1747360179920, "results": "507", "hashOfConfig": "352"}, {"size": 1337, "mtime": 1743870941098, "results": "508", "hashOfConfig": "459"}, {"size": 2938, "mtime": 1747360179930, "results": "509", "hashOfConfig": "352"}, {"size": 1812, "mtime": 1751879443263, "results": "510", "hashOfConfig": "352"}, {"size": 5412, "mtime": 1747360179920, "results": "511", "hashOfConfig": "352"}, {"size": 2928, "mtime": 1747360179920, "results": "512", "hashOfConfig": "352"}, {"size": 6380, "mtime": 1747381941763, "results": "513", "hashOfConfig": "352"}, {"size": 1576, "mtime": 1751799058328, "results": "514", "hashOfConfig": "352"}, {"size": 11569, "mtime": 1753875510536, "results": "515", "hashOfConfig": "352"}, {"size": 2600, "mtime": 1748846797546, "results": "516", "hashOfConfig": "352"}, {"size": 4099, "mtime": 1754218166559, "results": "517", "hashOfConfig": "352"}, {"size": 1345, "mtime": 1750250347462, "results": "518", "hashOfConfig": "352"}, {"size": 3297, "mtime": 1743823876255, "results": "519", "hashOfConfig": "352"}, {"size": 15741, "mtime": 1753875510548, "results": "520", "hashOfConfig": "352"}, {"size": 17237, "mtime": 1753875510541, "results": "521", "hashOfConfig": "352"}, {"size": 70430, "mtime": 1748942168397, "results": "522", "hashOfConfig": "352"}, {"size": 15829, "mtime": 1753875510546, "results": "523", "hashOfConfig": "352"}, {"size": 12968, "mtime": 1753875510548, "results": "524", "hashOfConfig": "352"}, {"size": 34299, "mtime": 1753875510546, "results": "525", "hashOfConfig": "352"}, {"size": 13626, "mtime": 1753875510548, "results": "526", "hashOfConfig": "352"}, {"size": 21291, "mtime": 1754037662819, "results": "527", "hashOfConfig": "352"}, {"size": 41124, "mtime": 1754219652509, "results": "528", "hashOfConfig": "352"}, {"size": 16767, "mtime": 1748846663294, "results": "529", "hashOfConfig": "352"}, {"size": 8060, "mtime": 1747360179971, "results": "530", "hashOfConfig": "352"}, {"size": 3253, "mtime": 1745808607428, "results": "531", "hashOfConfig": "352"}, {"size": 2298, "mtime": 1750250347428, "results": "532", "hashOfConfig": "352"}, {"size": 848, "mtime": 1745808607413, "results": "533", "hashOfConfig": "352"}, {"size": 1176, "mtime": 1753875510536, "results": "534", "hashOfConfig": "352"}, {"size": 1437, "mtime": 1753875510536, "results": "535", "hashOfConfig": "352"}, {"size": 3467, "mtime": 1745808607408, "results": "536", "hashOfConfig": "352"}, {"size": 1102, "mtime": 1753875510536, "results": "537", "hashOfConfig": "352"}, {"size": 2801, "mtime": 1750250347428, "results": "538", "hashOfConfig": "352"}, {"size": 7136, "mtime": 1747360179947, "results": "539", "hashOfConfig": "352"}, {"size": 2129, "mtime": 1745923751544, "results": "540", "hashOfConfig": "352"}, {"size": 1184, "mtime": 1745923490808, "results": "541", "hashOfConfig": "352"}, {"size": 924, "mtime": 1745921871581, "results": "542", "hashOfConfig": "352"}, {"size": 13380, "mtime": 1754037662804, "results": "543", "hashOfConfig": "352"}, {"size": 20572, "mtime": 1752528418131, "results": "544", "hashOfConfig": "352"}, {"size": 1135, "mtime": 1748333524174, "results": "545", "hashOfConfig": "352"}, {"size": 12544, "mtime": 1748225026681, "results": "546", "hashOfConfig": "352"}, {"size": 10859, "mtime": 1748225026673, "results": "547", "hashOfConfig": "352"}, {"size": 7652, "mtime": 1747360179932, "results": "548", "hashOfConfig": "352"}, {"size": 6276, "mtime": 1747360179932, "results": "549", "hashOfConfig": "352"}, {"size": 3589, "mtime": 1747360179932, "results": "550", "hashOfConfig": "352"}, {"size": 16791, "mtime": 1748425117389, "results": "551", "hashOfConfig": "352"}, {"size": 2123, "mtime": 1747360179986, "results": "552", "hashOfConfig": "352"}, {"size": 4129, "mtime": 1747360179986, "results": "553", "hashOfConfig": "352"}, {"size": 11963, "mtime": 1750332992366, "results": "554", "hashOfConfig": "352"}, {"size": 12245, "mtime": 1750339362394, "results": "555", "hashOfConfig": "352"}, {"size": 5589, "mtime": 1750250347464, "results": "556", "hashOfConfig": "352"}, {"size": 73138, "mtime": 1750420257824, "results": "557", "hashOfConfig": "352"}, {"size": 13612, "mtime": 1750250347459, "results": "558", "hashOfConfig": "352"}, {"size": 10116, "mtime": 1750250347459, "results": "559", "hashOfConfig": "352"}, {"size": 4152, "mtime": 1749107979109, "results": "560", "hashOfConfig": "352"}, {"size": 4762, "mtime": 1748512613872, "results": "561", "hashOfConfig": "352"}, {"size": 8134, "mtime": 1750418443077, "results": "562", "hashOfConfig": "352"}, {"size": 2524, "mtime": 1747742780631, "results": "563", "hashOfConfig": "352"}, {"size": 16049, "mtime": 1751794342065, "results": "564", "hashOfConfig": "352"}, {"size": 2002, "mtime": 1751794342102, "results": "565", "hashOfConfig": "352"}, {"size": 55806, "mtime": 1752528418131, "results": "566", "hashOfConfig": "352"}, {"size": 16429, "mtime": 1751794342096, "results": "567", "hashOfConfig": "352"}, {"size": 41991, "mtime": 1751794342083, "results": "568", "hashOfConfig": "352"}, {"size": 11446, "mtime": 1748225026675, "results": "569", "hashOfConfig": "352"}, {"size": 8222, "mtime": 1748247735813, "results": "570", "hashOfConfig": "352"}, {"size": 9160, "mtime": 1753875510541, "results": "571", "hashOfConfig": "352"}, {"size": 2115, "mtime": 1753875510551, "results": "572", "hashOfConfig": "352"}, {"size": 12880, "mtime": 1748234340710, "results": "573", "hashOfConfig": "352"}, {"size": 27641, "mtime": 1748237297926, "results": "574", "hashOfConfig": "352"}, {"size": 29009, "mtime": 1748942038625, "results": "575", "hashOfConfig": "352"}, {"size": 36682, "mtime": 1748261019419, "results": "576", "hashOfConfig": "352"}, {"size": 12053, "mtime": 1754307374063, "results": "577", "hashOfConfig": "352"}, {"size": 16393, "mtime": 1748424127070, "results": "578", "hashOfConfig": "352"}, {"size": 3992, "mtime": 1753875510541, "results": "579", "hashOfConfig": "352"}, {"size": 4028, "mtime": 1748831088180, "results": "580", "hashOfConfig": "352"}, {"size": 864, "mtime": 1748831088160, "results": "581", "hashOfConfig": "352"}, {"size": 1762, "mtime": 1748831088160, "results": "582", "hashOfConfig": "352"}, {"size": 1983, "mtime": 1751286250564, "results": "583", "hashOfConfig": "352"}, {"size": 3617, "mtime": 1748831088171, "results": "584", "hashOfConfig": "352"}, {"size": 365, "mtime": 1748942366216, "results": "585", "hashOfConfig": "352"}, {"size": 1850, "mtime": 1752528418106, "results": "586", "hashOfConfig": "352"}, {"size": 4156, "mtime": 1753875510541, "results": "587", "hashOfConfig": "352"}, {"size": 8669, "mtime": 1749108405009, "results": "588", "hashOfConfig": "352"}, {"size": 8088, "mtime": 1751794342065, "results": "589", "hashOfConfig": "352"}, {"size": 5114, "mtime": 1751004342904, "results": "590", "hashOfConfig": "352"}, {"size": 958, "mtime": 1751004342915, "results": "591", "hashOfConfig": "352"}, {"size": 19880, "mtime": 1753875510541, "results": "592", "hashOfConfig": "352"}, {"size": 388, "mtime": 1750250347440, "results": "593", "hashOfConfig": "352"}, {"size": 913, "mtime": 1751794342077, "results": "594", "hashOfConfig": "352"}, {"size": 1664, "mtime": 1750250347462, "results": "595", "hashOfConfig": "352"}, {"size": 1948, "mtime": 1751794342082, "results": "596", "hashOfConfig": "352"}, {"size": 2401, "mtime": 1750418443079, "results": "597", "hashOfConfig": "352"}, {"size": 7765, "mtime": 1750418443077, "results": "598", "hashOfConfig": "352"}, {"size": 3926, "mtime": 1752528418123, "results": "599", "hashOfConfig": "352"}, {"size": 2836, "mtime": 1750250347428, "results": "600", "hashOfConfig": "352"}, {"size": 579, "mtime": 1750250347412, "results": "601", "hashOfConfig": "352"}, {"size": 2502, "mtime": 1752528418119, "results": "602", "hashOfConfig": "352"}, {"size": 6803, "mtime": 1752528418109, "results": "603", "hashOfConfig": "352"}, {"size": 200, "mtime": 1750418807924, "results": "604", "hashOfConfig": "352"}, {"size": 4832, "mtime": 1750586864082, "results": "605", "hashOfConfig": "352"}, {"size": 3066, "mtime": 1750768842393, "results": "606", "hashOfConfig": "352"}, {"size": 4807, "mtime": 1750768842383, "results": "607", "hashOfConfig": "352"}, {"size": 4498, "mtime": 1751004342912, "results": "608", "hashOfConfig": "352"}, {"size": 4263, "mtime": 1751004342899, "results": "609", "hashOfConfig": "352"}, {"size": 72, "mtime": 1748227925877, "results": "610", "hashOfConfig": "352"}, {"size": 3878, "mtime": 1751004342884, "results": "611", "hashOfConfig": "352"}, {"size": 22243, "mtime": 1751004342890, "results": "612", "hashOfConfig": "352"}, {"size": 17942, "mtime": 1751004342890, "results": "613", "hashOfConfig": "352"}, {"size": 4331, "mtime": 1751004342891, "results": "614", "hashOfConfig": "352"}, {"size": 23770, "mtime": 1751004342891, "results": "615", "hashOfConfig": "352"}, {"size": 846, "mtime": 1751004342891, "results": "616", "hashOfConfig": "352"}, {"size": 1252, "mtime": 1751004342891, "results": "617", "hashOfConfig": "352"}, {"size": 17515, "mtime": 1752528418131, "results": "618", "hashOfConfig": "352"}, {"size": 218, "mtime": 1751434984402, "results": "619", "hashOfConfig": "352"}, {"size": 4350, "mtime": 1751794342096, "results": "620", "hashOfConfig": "352"}, {"size": 759, "mtime": 1751794342091, "results": "621", "hashOfConfig": "352"}, {"size": 4259, "mtime": 1751794342091, "results": "622", "hashOfConfig": "352"}, {"size": 680, "mtime": 1754041171135, "results": "623", "hashOfConfig": "352"}, {"size": 851, "mtime": 1752528418115, "results": "624", "hashOfConfig": "352"}, {"size": 1300, "mtime": 1753875510534, "results": "625", "hashOfConfig": "352"}, {"size": 449, "mtime": 1751794342102, "results": "626", "hashOfConfig": "352"}, {"size": 1750, "mtime": 1751879443273, "results": "627", "hashOfConfig": "352"}, {"size": 6342, "mtime": 1752528418115, "results": "628", "hashOfConfig": "352"}, {"size": 9107, "mtime": 1754037662810, "results": "629", "hashOfConfig": "352"}, {"size": 5530, "mtime": 1754037662810, "results": "630", "hashOfConfig": "352"}, {"size": 6505, "mtime": 1754041914606, "results": "631", "hashOfConfig": "352"}, {"size": 8061, "mtime": 1752528418138, "results": "632", "hashOfConfig": "352"}, {"size": 1346, "mtime": 1752528418138, "results": "633", "hashOfConfig": "352"}, {"size": 13175, "mtime": 1752528418106, "results": "634", "hashOfConfig": "352"}, {"size": 592, "mtime": 1752528418123, "results": "635", "hashOfConfig": "352"}, {"size": 1611, "mtime": 1752528418106, "results": "636", "hashOfConfig": "352"}, {"size": 7069, "mtime": 1754037662800, "results": "637", "hashOfConfig": "352"}, {"size": 28741, "mtime": 1754299894303, "results": "638", "hashOfConfig": "352"}, {"size": 43149, "mtime": 1754041861548, "results": "639", "hashOfConfig": "352"}, {"size": 20286, "mtime": 1754037662793, "results": "640", "hashOfConfig": "352"}, {"size": 9040, "mtime": 1752528418099, "results": "641", "hashOfConfig": "352"}, {"size": 6089, "mtime": 1754300386743, "results": "642", "hashOfConfig": "352"}, {"size": 593, "mtime": 1752528418090, "results": "643", "hashOfConfig": "352"}, {"size": 2173, "mtime": 1754037662793, "results": "644", "hashOfConfig": "352"}, {"size": 3352, "mtime": 1754299958143, "results": "645", "hashOfConfig": "352"}, {"size": 2297, "mtime": 1752528418090, "results": "646", "hashOfConfig": "352"}, {"size": 3061, "mtime": 1752528418099, "results": "647", "hashOfConfig": "352"}, {"size": 1511, "mtime": 1752528418099, "results": "648", "hashOfConfig": "352"}, {"size": 4044, "mtime": 1752528418099, "results": "649", "hashOfConfig": "352"}, {"size": 3600, "mtime": 1752528418099, "results": "650", "hashOfConfig": "352"}, {"size": 2305, "mtime": 1752528418099, "results": "651", "hashOfConfig": "352"}, {"size": 31528, "mtime": 1754037662821, "results": "652", "hashOfConfig": "352"}, {"size": 7652, "mtime": 1754037662810, "results": "653", "hashOfConfig": "352"}, {"size": 4259, "mtime": 1753875510541, "results": "654", "hashOfConfig": "352"}, {"size": 3528, "mtime": 1753875510541, "results": "655", "hashOfConfig": "352"}, {"size": 3017, "mtime": 1754037662810, "results": "656", "hashOfConfig": "352"}, {"size": 7599, "mtime": 1753875510541, "results": "657", "hashOfConfig": "352"}, {"size": 340, "mtime": 1753875510534, "results": "658", "hashOfConfig": "352"}, {"size": 709, "mtime": 1754037662804, "results": "659", "hashOfConfig": "352"}, {"size": 376, "mtime": 1753875510534, "results": "660", "hashOfConfig": "352"}, {"size": 139, "mtime": 1754037662827, "results": "661", "hashOfConfig": "352"}, {"size": 3054, "mtime": 1753875510529, "results": "662", "hashOfConfig": "352"}, {"size": 6757, "mtime": 1754037662804, "results": "663", "hashOfConfig": "352"}, {"size": 3645, "mtime": 1753875510532, "results": "664", "hashOfConfig": "352"}, {"size": 3362, "mtime": 1753875510529, "results": "665", "hashOfConfig": "352"}, {"size": 4615, "mtime": 1753875510509, "results": "666", "hashOfConfig": "352"}, {"size": 7086, "mtime": 1753875510509, "results": "667", "hashOfConfig": "352"}, {"size": 1904, "mtime": 1753875510509, "results": "668", "hashOfConfig": "352"}, {"size": 30960, "mtime": 1753875510513, "results": "669", "hashOfConfig": "352"}, {"size": 1206, "mtime": 1754037662832, "results": "670", "hashOfConfig": "352"}, {"size": 1178, "mtime": 1753875510525, "results": "671", "hashOfConfig": "352"}, {"size": 4133, "mtime": 1753875510525, "results": "672", "hashOfConfig": "352"}, {"size": 13910, "mtime": 1753875510528, "results": "673", "hashOfConfig": "352"}, {"size": 7570, "mtime": 1753875510528, "results": "674", "hashOfConfig": "352"}, {"size": 10269, "mtime": 1753875510527, "results": "675", "hashOfConfig": "352"}, {"size": 8712, "mtime": 1754289080905, "results": "676", "hashOfConfig": "352"}, {"size": 904, "mtime": 1753875510551, "results": "677", "hashOfConfig": "352"}, {"size": 411, "mtime": 1753875510551, "results": "678", "hashOfConfig": "352"}, {"size": 719, "mtime": 1754037662826, "results": "679", "hashOfConfig": "352"}, {"size": 3881, "mtime": 1753875510510, "results": "680", "hashOfConfig": "352"}, {"size": 740, "mtime": 1753875510530, "results": "681", "hashOfConfig": "352"}, {"size": 2747, "mtime": 1753875510511, "results": "682", "hashOfConfig": "352"}, {"size": 1760, "mtime": 1753875510509, "results": "683", "hashOfConfig": "352"}, {"size": 496, "mtime": 1753875510511, "results": "684", "hashOfConfig": "352"}, {"size": 1668, "mtime": 1753875510510, "results": "685", "hashOfConfig": "352"}, {"size": 1183, "mtime": 1753875510511, "results": "686", "hashOfConfig": "352"}, {"size": 911, "mtime": 1753875510511, "results": "687", "hashOfConfig": "352"}, {"size": 7071, "mtime": 1753875510511, "results": "688", "hashOfConfig": "352"}, {"size": 5299, "mtime": 1753875510513, "results": "689", "hashOfConfig": "352"}, {"size": 647, "mtime": 1753875510525, "results": "690", "hashOfConfig": "352"}, {"size": 1507, "mtime": 1753875510524, "results": "691", "hashOfConfig": "352"}, {"size": 8615, "mtime": 1753875510524, "results": "692", "hashOfConfig": "352"}, {"size": 4137, "mtime": 1753875510517, "results": "693", "hashOfConfig": "352"}, {"size": 576, "mtime": 1754037662804, "results": "694", "hashOfConfig": "352"}, {"size": 3550, "mtime": 1753875510529, "results": "695", "hashOfConfig": "352"}, {"size": 493, "mtime": 1753875510526, "results": "696", "hashOfConfig": "352"}, {"size": 1204, "mtime": 1753875510510, "results": "697", "hashOfConfig": "352"}, {"size": 1722, "mtime": 1753875510525, "results": "698", "hashOfConfig": "352"}, {"size": 1138, "mtime": 1753875510511, "results": "699", "hashOfConfig": "352"}, {"size": 15200, "mtime": 1754042470054, "results": "700", "hashOfConfig": "352"}, {"size": 2623, "mtime": 1754041838305, "results": "701", "hashOfConfig": "352"}, {"size": 7766, "mtime": 1754307525782, "results": "702", "hashOfConfig": "352"}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12khgps", {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1jjs0tz", {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1363", "messages": "1364", "suppressedMessages": "1365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1366", "messages": "1367", "suppressedMessages": "1368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1369", "messages": "1370", "suppressedMessages": "1371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1372", "messages": "1373", "suppressedMessages": "1374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1375", "messages": "1376", "suppressedMessages": "1377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1378", "messages": "1379", "suppressedMessages": "1380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1390", "messages": "1391", "suppressedMessages": "1392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1393", "messages": "1394", "suppressedMessages": "1395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1396", "messages": "1397", "suppressedMessages": "1398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1399", "messages": "1400", "suppressedMessages": "1401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1402", "messages": "1403", "suppressedMessages": "1404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1405", "messages": "1406", "suppressedMessages": "1407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1408", "messages": "1409", "suppressedMessages": "1410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1411", "messages": "1412", "suppressedMessages": "1413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1414", "messages": "1415", "suppressedMessages": "1416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1417", "messages": "1418", "suppressedMessages": "1419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1420", "messages": "1421", "suppressedMessages": "1422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1423", "messages": "1424", "suppressedMessages": "1425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1426", "messages": "1427", "suppressedMessages": "1428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1429", "messages": "1430", "suppressedMessages": "1431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1432", "messages": "1433", "suppressedMessages": "1434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1435", "messages": "1436", "suppressedMessages": "1437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1438", "messages": "1439", "suppressedMessages": "1440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1441", "messages": "1442", "suppressedMessages": "1443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1444", "messages": "1445", "suppressedMessages": "1446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1447", "messages": "1448", "suppressedMessages": "1449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1450", "messages": "1451", "suppressedMessages": "1452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1453", "messages": "1454", "suppressedMessages": "1455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1456", "messages": "1457", "suppressedMessages": "1458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1459", "messages": "1460", "suppressedMessages": "1461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1462", "messages": "1463", "suppressedMessages": "1464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1465", "messages": "1466", "suppressedMessages": "1467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1468", "messages": "1469", "suppressedMessages": "1470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1471", "messages": "1472", "suppressedMessages": "1473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1474", "messages": "1475", "suppressedMessages": "1476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1477", "messages": "1478", "suppressedMessages": "1479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1480", "messages": "1481", "suppressedMessages": "1482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1483", "messages": "1484", "suppressedMessages": "1485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1486", "messages": "1487", "suppressedMessages": "1488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1489", "messages": "1490", "suppressedMessages": "1491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1492", "messages": "1493", "suppressedMessages": "1494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1495", "messages": "1496", "suppressedMessages": "1497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1498", "messages": "1499", "suppressedMessages": "1500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1501", "messages": "1502", "suppressedMessages": "1503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1504", "messages": "1505", "suppressedMessages": "1506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1507", "messages": "1508", "suppressedMessages": "1509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1510", "messages": "1511", "suppressedMessages": "1512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1513", "messages": "1514", "suppressedMessages": "1515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1516", "messages": "1517", "suppressedMessages": "1518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1519", "messages": "1520", "suppressedMessages": "1521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1522", "messages": "1523", "suppressedMessages": "1524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1525", "messages": "1526", "suppressedMessages": "1527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1528", "messages": "1529", "suppressedMessages": "1530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1531", "messages": "1532", "suppressedMessages": "1533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1534", "messages": "1535", "suppressedMessages": "1536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1537", "messages": "1538", "suppressedMessages": "1539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1540", "messages": "1541", "suppressedMessages": "1542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1543", "messages": "1544", "suppressedMessages": "1545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1546", "messages": "1547", "suppressedMessages": "1548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1549", "messages": "1550", "suppressedMessages": "1551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1552", "messages": "1553", "suppressedMessages": "1554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1555", "messages": "1556", "suppressedMessages": "1557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1558", "messages": "1559", "suppressedMessages": "1560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1561", "messages": "1562", "suppressedMessages": "1563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1564", "messages": "1565", "suppressedMessages": "1566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1567", "messages": "1568", "suppressedMessages": "1569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1570", "messages": "1571", "suppressedMessages": "1572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1573", "messages": "1574", "suppressedMessages": "1575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1576", "messages": "1577", "suppressedMessages": "1578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1579", "messages": "1580", "suppressedMessages": "1581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1582", "messages": "1583", "suppressedMessages": "1584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1585", "messages": "1586", "suppressedMessages": "1587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1588", "messages": "1589", "suppressedMessages": "1590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1591", "messages": "1592", "suppressedMessages": "1593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1594", "messages": "1595", "suppressedMessages": "1596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1597", "messages": "1598", "suppressedMessages": "1599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1600", "messages": "1601", "suppressedMessages": "1602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1603", "messages": "1604", "suppressedMessages": "1605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1606", "messages": "1607", "suppressedMessages": "1608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1609", "messages": "1610", "suppressedMessages": "1611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1612", "messages": "1613", "suppressedMessages": "1614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1615", "messages": "1616", "suppressedMessages": "1617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1618", "messages": "1619", "suppressedMessages": "1620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1621", "messages": "1622", "suppressedMessages": "1623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1624", "messages": "1625", "suppressedMessages": "1626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1627", "messages": "1628", "suppressedMessages": "1629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1630", "messages": "1631", "suppressedMessages": "1632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1633", "messages": "1634", "suppressedMessages": "1635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1636", "messages": "1637", "suppressedMessages": "1638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1639", "messages": "1640", "suppressedMessages": "1641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1642", "messages": "1643", "suppressedMessages": "1644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1645", "messages": "1646", "suppressedMessages": "1647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1648", "messages": "1649", "suppressedMessages": "1650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1651", "messages": "1652", "suppressedMessages": "1653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1654", "messages": "1655", "suppressedMessages": "1656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1657", "messages": "1658", "suppressedMessages": "1659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1660", "messages": "1661", "suppressedMessages": "1662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1663", "messages": "1664", "suppressedMessages": "1665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1666", "messages": "1667", "suppressedMessages": "1668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1669", "messages": "1670", "suppressedMessages": "1671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1672", "messages": "1673", "suppressedMessages": "1674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1675", "messages": "1676", "suppressedMessages": "1677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1678", "messages": "1679", "suppressedMessages": "1680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1681", "messages": "1682", "suppressedMessages": "1683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1684", "messages": "1685", "suppressedMessages": "1686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1687", "messages": "1688", "suppressedMessages": "1689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1690", "messages": "1691", "suppressedMessages": "1692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1693", "messages": "1694", "suppressedMessages": "1695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1696", "messages": "1697", "suppressedMessages": "1698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1699", "messages": "1700", "suppressedMessages": "1701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1702", "messages": "1703", "suppressedMessages": "1704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1705", "messages": "1706", "suppressedMessages": "1707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1708", "messages": "1709", "suppressedMessages": "1710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1711", "messages": "1712", "suppressedMessages": "1713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1714", "messages": "1715", "suppressedMessages": "1716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1717", "messages": "1718", "suppressedMessages": "1719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1720", "messages": "1721", "suppressedMessages": "1722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1723", "messages": "1724", "suppressedMessages": "1725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1726", "messages": "1727", "suppressedMessages": "1728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1729", "messages": "1730", "suppressedMessages": "1731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1732", "messages": "1733", "suppressedMessages": "1734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1735", "messages": "1736", "suppressedMessages": "1737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1738", "messages": "1739", "suppressedMessages": "1740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1741", "messages": "1742", "suppressedMessages": "1743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1744", "messages": "1745", "suppressedMessages": "1746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1747", "messages": "1748", "suppressedMessages": "1749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1750", "messages": "1751", "suppressedMessages": "1752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\ToanThayBee\\frontend\\src\\index.js", [], [], "D:\\ToanThayBee\\frontend\\src\\App.js", ["1753"], [], "D:\\ToanThayBee\\frontend\\src\\reportWebVitals.js", [], [], "D:\\ToanThayBee\\frontend\\src\\redux\\store.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\LoginPage.jsx", ["1754", "1755", "1756", "1757", "1758"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ProtectedRoute.jsx", ["1759"], [], "D:\\ToanThayBee\\frontend\\src\\components\\error\\NotificationDisplay.jsx", ["1760", "1761"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\CodeManagement.jsx", ["1762", "1763"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\HomePageManagement.jsx", ["1764", "1765", "1766", "1767", "1768"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticleManagement.jsx", ["1769"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticlePostPage.jsx", ["1770"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1771"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\questionManagement.jsx", ["1772", "1773"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1774", "1775"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1776", "1777"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1791", "1792"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1801", "1802", "1803", "1804", "1805"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1806"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1807", "1808", "1809", "1810", "1811", "1812", "1813"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1814"], [], "D:\\ToanThayBee\\frontend\\src\\features\\sidebar\\sidebarSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\user\\userSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\question\\questionSlice.js", ["1815"], [], "D:\\ToanThayBee\\frontend\\src\\features\\auth\\authSlice.js", ["1816"], [], "D:\\ToanThayBee\\frontend\\src\\features\\state\\stateApiSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\code\\codeSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\image\\imageSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\answer\\answerSlice.js", ["1817", "1818", "1819"], [], "D:\\ToanThayBee\\frontend\\src\\features\\attempt\\attemptSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\class\\classSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\article\\articleSlice.js", ["1820"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\sanitizeInput.js", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\validation.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\achievement\\achievementSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\AdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\AuthLayout.jsx", ["1821"], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputForAuthPage.jsx", ["1822"], [], "D:\\ToanThayBee\\frontend\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830"], [], "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1831"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBarAttemp.jsx", ["1832"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddQuestionModal.jsx", ["1833"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddCodeModal.jsx", ["1834"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\CodeTable.jsx", ["1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTable.jsx", ["1843", "1844", "1845"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ArticleTable.jsx", ["1846", "1847"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutMultipleImages.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AdminModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\QuestionDetail.jsx", ["1848", "1849"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ExamDetail.jsx", ["1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\PreviewExam.jsx", ["1858", "1859", "1860", "1861", "1862", "1863"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ExamTable.jsx", ["1864"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ClassDetail.jsx", ["1865", "1866", "1867", "1868", "1869"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\UserClassTable.jsx", ["1870", "1871", "1872"], [], "D:\\ToanThayBee\\frontend\\src\\components\\Footer.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassTable.jsx", ["1873", "1874", "1875"], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddClassModal.jsx", ["1876", "1877", "1878"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\LearningItemIcon.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\YouTubePlayer.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ViewDetail.jsx", ["1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886"], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayoutHome.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\formatters.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\SlideShow.jsx", ["1887", "1888", "1889", "1890", "1891", "1892"], [], "D:\\ToanThayBee\\frontend\\src\\components\\latex\\RenderLatex.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\NetworkSpeedTest.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\CustomSchedule.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\StudentThoughts.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputSearch.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\JoinClassModal.jsx", ["1893", "1894", "1895"], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\Pagination.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\card\\countDownCard.jsx", ["1896"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\ClassImage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ViewPdf.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\apiHandler.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\authApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\card\\RelatedExamCard.jsx", ["1897", "1898", "1899", "1900", "1901"], [], "D:\\ToanThayBee\\frontend\\src\\components\\card\\ExamCard.jsx", ["1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917", "1918", "1919"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\QrCode.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ScreenButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\userApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\questionApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ViewLearning.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\Schedule.jsx", ["1920", "1921", "1922", "1923"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderDoExamPage.jsx", ["1924", "1925", "1926", "1927"], [], "D:\\ToanThayBee\\frontend\\src\\components\\achievement\\AchievementSection.jsx", ["1928", "1929", "1930", "1931", "1932"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\Breadcrumb.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1933", "1934", "1935"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleList.jsx", ["1936"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleSidebar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\SearchBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementStatTable.jsx", ["1937", "1938"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleContent.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1939"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementCategoryTable.jsx", ["1940"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1941"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementImageTable.jsx", ["1942", "1943"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddStudentModal.jsx", ["1944", "1945"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\UserDetail.jsx", ["1946"], [], "D:\\ToanThayBee\\frontend\\src\\services\\codeApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\imageApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\userTable.jsx", ["1947", "1948", "1949"], [], "D:\\ToanThayBee\\frontend\\src\\services\\answerApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\articleApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\examApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\achievementApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\pagination\\Pagination.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\attemptApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\classApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\excelExport.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\AdminSidebar.jsx", ["1950", "1951", "1952", "1953", "1954"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ParticlesBackground.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\UploadImage.jsx", ["1955"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\StatementTableRow.jsx", ["1956"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTableRow.jsx", ["1957"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\TooltipTd.jsx", ["1958"], [], "D:\\ToanThayBee\\frontend\\src\\components\\UploadPdf.jsx", ["1959"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutImgae.jsx", ["1960"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\DetailTr.jsx", ["1961"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\question\\questionUtils.js", ["1962", "1963", "1964", "1965", "1966", "1967", "1968", "1969", "1970", "1971", "1972", "1973", "1974", "1975", "1976", "1977", "1978", "1979"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ScheduleModal.jsx", ["1980"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderHome.jsx", ["1981", "1982", "1983", "1984"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\Header.jsx", ["1985", "1986", "1987", "1988", "1989", "1990"], [], "D:\\ToanThayBee\\frontend\\src\\services\\api.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleCard.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ChapterFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ClassFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmModal.jsx", ["1991"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ActiveFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\CategoryFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\UserSidebar.jsx", ["1992"], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1993", "1994", "1995", "1996", "1997", "1998"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\StudentCardModal.jsx", ["1999", "2000", "2001", "2002"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\ChoiceHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\responseInterceptor.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\requestInterceptor.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\AvatarUploader.jsx", ["2003", "2004"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\OverViewPage.jsx", ["2005"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["2006", "2007"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\Home.jsx", ["2008", "2009", "2010", "2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021", "2022"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassUserPage.jsx", ["2023", "2024", "2025", "2026", "2027", "2028", "2029", "2030", "2031", "2032"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\PracticePage.jsx", ["2033", "2034", "2035"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\LearningPage.jsx", ["2036", "2037", "2038", "2039", "2040", "2041", "2042", "2043"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\DoExamPage.jsx", ["2044", "2045", "2046", "2047", "2048"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ExamDetail.jsx", ["2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056", "2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066", "2067", "2068", "2069", "2070", "2071"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ScorePage.jsx", ["2072", "2073", "2074", "2075", "2076", "2077", "2078", "2079", "2080", "2081"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticleListPage.jsx", ["2082", "2083", "2084", "2085", "2086", "2087", "2088"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticlePage.jsx", ["2089", "2090", "2091", "2092", "2093", "2094", "2095"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\fullscreenUtils.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\questionReport\\questionReportSlice.js", ["2096"], [], "D:\\ToanThayBee\\frontend\\src\\services\\questionReportApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ReportButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\QuestionReportManagement.jsx", ["2097", "2098", "2099"], [], "D:\\ToanThayBee\\frontend\\src\\components\\utils\\NoTranslate.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\notification\\notificationSlice.js", ["2100"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ModernArticleSidebar.jsx", ["2101", "2102", "2103", "2104", "2105", "2106", "2107"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\cacheManager.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\notificationApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\notification\\NotificationPanel.jsx", ["2108", "2109", "2110", "2111"], [], "D:\\ToanThayBee\\frontend\\src\\features\\tuition\\tuitionSlice.js", ["2112"], [], "D:\\ToanThayBee\\frontend\\src\\services\\tuitionApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["2113", "2114", "2115", "2116", "2117", "2118", "2119", "2120", "2121", "2122", "2123", "2124", "2125", "2126", "2127", "2128", "2129", "2130", "2131", "2132", "2133", "2134", "2135", "2136", "2137", "2138", "2139", "2140", "2141", "2142", "2143", "2144"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["2145", "2146", "2147", "2148"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["2149", "2150", "2151", "2152", "2153", "2154"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ClassSearchInput.jsx", ["2155"], [], "D:\\ToanThayBee\\frontend\\src\\components\\UserSearchInput.jsx", ["2156"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PaymentModal.jsx", ["2157"], [], "D:\\ToanThayBee\\frontend\\src\\components\\MultiClassSelector.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\attendance\\attendanceSlice.js", ["2158", "2159"], [], "D:\\ToanThayBee\\frontend\\src\\services\\attendanceApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\AttendancePage.jsx", ["2160", "2161", "2162", "2163", "2164", "2165", "2166", "2167"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["2168"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["2169", "2170", "2171", "2172", "2173"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\lessonSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\lessonApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\team\\TeamSection.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["2174", "2175", "2176"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["2177", "2178"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\banner\\ClassBanner.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\ClassAdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\maintenanceUtils.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceCleaner.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceWrapper.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\config\\maintenance.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\MaintenancePage.jsx", ["2179", "2180"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ScrollToTop.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\UserAdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["2181", "2182", "2183"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassOfUserTable.jsx", ["2184"], [], "D:\\ToanThayBee\\frontend\\src\\features\\learningItem\\learningItemSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\learningItemApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\doExam\\doExamSlice.js", ["2185", "2186", "2187", "2188", "2189"], [], "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterReducer.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\pagination\\paginationReducer.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\doExamApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\404NotFound.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\CustomSearchInput.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\UnpaidTuitionModal.jsx", ["2190", "2191", "2192", "2193"], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\ExamAdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\TotalComponent.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\sheet\\sheetSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\sheetApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ExamSearchInput.jsx", ["2194"], [], "D:\\ToanThayBee\\frontend\\src\\components\\MultiLessonSelector.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\LessonSearchInput.jsx", ["2195"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["2196", "2197", "2198", "2199", "2200", "2201", "2202", "2203"], [], "D:\\ToanThayBee\\frontend\\src\\features\\calendar\\calendarSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\index.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\DayView.jsx", ["2204", "2205", "2206"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\MonthView.jsx", ["2207"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\SidebarCalender.jsx", ["2208"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\WeekView.jsx", ["2209", "2210", "2211", "2212", "2213", "2214", "2215", "2216", "2217", "2218", "2219", "2220"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\TimeSlots.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", ["2221", "2222"], [], "D:\\ToanThayBee\\frontend\\src\\constants\\UserType.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["2223", "2224", "2225", "2226", "2227"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["2228", "2229", "2230", "2231", "2232", "2233"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["2234", "2235", "2236"], [], "D:\\ToanThayBee\\frontend\\src\\services\\ocrExamApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\TableAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingData.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\apin8n.js", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\setupKatexWarningFilter.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\dashboard\\dashboardSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\questionsExam\\questionsExamSlice.js", ["2237"], [], "D:\\ToanThayBee\\frontend\\src\\features\\addExam\\addExamSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\AddExamAdmin.jsx", ["2238", "2239", "2240", "2241", "2242", "2243", "2244", "2245", "2246", "2247", "2248", "2249", "2250", "2251", "2252", "2253", "2254", "2255", "2256", "2257", "2258", "2259", "2260", "2261", "2262", "2263", "2264", "2265", "2266", "2267"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StaffManagement.jsx", ["2268", "2269", "2270"], [], "D:\\ToanThayBee\\frontend\\src\\services\\dashboardApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddImagesModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\hooks\\useDebouncedEffect.js", ["2271", "2272"], [], "D:\\ToanThayBee\\frontend\\src\\components\\layout\\AdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\RightContent.jsx", ["2273", "2274", "2275"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\LeftContent.jsx", ["2276"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\LeftContent.jsx", ["2277", "2278", "2279", "2280", "2281", "2282", "2283", "2284", "2285", "2286", "2287", "2288", "2289"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\RightContent.jsx", ["2290", "2291", "2292", "2293", "2294", "2295"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionView.jsx", ["2296", "2297", "2298", "2299", "2300", "2301", "2302", "2303"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\SolutionEditor.jsx", ["2304", "2305", "2306", "2307"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\NavigateBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\CompactStepHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\TextArea.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\ImageView.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\ImageDropZone.jsx", ["2308"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionContent.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx", ["2309", "2310"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\question\\QuestionPage.jsx", ["2311"], [], "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examDetailSlice.js", ["2312", "2313", "2314", "2315", "2316", "2317"], [], "D:\\ToanThayBee\\frontend\\src\\features\\practice\\practiceSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\scorePage\\scorePageSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\ai\\aiSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\comments\\ExamCommentsSlice.js", ["2318", "2319", "2320", "2321"], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\BatteryLoading.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ActionButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingText.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\codeUtils.js", ["2322"], [], "D:\\ToanThayBee\\frontend\\src\\components\\filter\\SortBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\ExamOverviewHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\LearningHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\filter\\FilterBar.jsx", ["2323"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamContent.jsx", ["2324", "2325", "2326"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamSideBar.jsx", ["2327"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ModalSubmitExam.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\Schedule1.jsx", ["2328", "2329"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\shareUntil.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\common\\OutsideClickWrapper.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentSection.jsx", ["2330", "2331", "2332", "2333", "2334", "2335"], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\RankingView.jsx", ["2336", "2337"], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\PreviewView.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\HistoryView.jsx", ["2338"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AiChatWidget.jsx", ["2339", "2340", "2341"], [], "D:\\ToanThayBee\\frontend\\src\\services\\examCommentsApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\studentExamApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\aiApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\MultipleChoiceQuestion.jsx", ["2342", "2343", "2344", "2345"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\ButtonHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ShortAnswerQuestion.jsx", ["2346", "2347", "2348", "2349", "2350", "2351"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\LoadingQuestions.jsx", ["2352", "2353"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionSectionTitle.jsx", ["2354"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ProgressBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionCounter.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\SubmitButton.jsx", ["2355"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\TrueFalseQuestion.jsx", ["2356", "2357", "2358"], [], "D:\\ToanThayBee\\frontend\\src\\components\\StarRating.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\LoadingCommentItem.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentInput.jsx", ["2359"], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentItem.jsx", ["2360", "2361", "2362", "2363", "2364"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\QuestionDropdown.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\OnlineLoading.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\UserInfoPanel.jsx", ["2365", "2366", "2367", "2368", "2369"], [], "D:\\ToanThayBee\\frontend\\src\\components\\common\\WrapperWithTooltip.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionContent.jsx", ["2370"], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\EmojiPicker.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionImage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AIWidget.jsx", ["2371", "2372", "2373"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\FloatingAIWidget.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\StudentManagementBar.jsx", ["2374", "2375", "2376", "2377"], [], {"ruleId": "2378", "severity": 1, "message": "2379", "line": 49, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 49, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2382", "line": 4, "column": 23, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2383", "line": 8, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2384", "line": 11, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 21}, {"ruleId": "2385", "severity": 1, "message": "2386", "line": 42, "column": 8, "nodeType": "2387", "endLine": 42, "endColumn": 24, "suggestions": "2388"}, {"ruleId": "2378", "severity": 1, "message": "2389", "line": 49, "column": 15, "nodeType": "2380", "messageId": "2381", "endLine": 49, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2391", "line": 1, "column": 38, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 44}, {"ruleId": "2378", "severity": 1, "message": "2392", "line": 11, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 10, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 10, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2394", "line": 18, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 36}, {"ruleId": "2378", "severity": 1, "message": "2395", "line": 6, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2396", "line": 6, "column": 27, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 31}, {"ruleId": "2378", "severity": 1, "message": "2397", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2398", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2399", "line": 88, "column": 23, "nodeType": "2380", "messageId": "2381", "endLine": 88, "endColumn": 31}, {"ruleId": "2385", "severity": 1, "message": "2400", "line": 26, "column": 8, "nodeType": "2387", "endLine": 26, "endColumn": 19, "suggestions": "2401"}, {"ruleId": "2402", "severity": 1, "message": "2403", "line": 393, "column": 45, "nodeType": "2404", "endLine": 398, "endColumn": 47}, {"ruleId": "2378", "severity": 1, "message": "2405", "line": 1, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2406", "line": 14, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2394", "line": 15, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 36}, {"ruleId": "2378", "severity": 1, "message": "2407", "line": 2, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2409", "line": 15, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2394", "line": 15, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 36}, {"ruleId": "2378", "severity": 1, "message": "2405", "line": 1, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2410", "line": 8, "column": 37, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 48}, {"ruleId": "2378", "severity": 1, "message": "2411", "line": 11, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2412", "line": 22, "column": 18, "nodeType": "2380", "messageId": "2381", "endLine": 22, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2413", "line": 23, "column": 21, "nodeType": "2380", "messageId": "2381", "endLine": 23, "endColumn": 31}, {"ruleId": "2378", "severity": 1, "message": "2414", "line": 24, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2415", "line": 25, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 25, "endColumn": 39}, {"ruleId": "2378", "severity": 1, "message": "2416", "line": 26, "column": 30, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 49}, {"ruleId": "2378", "severity": 1, "message": "2417", "line": 27, "column": 21, "nodeType": "2380", "messageId": "2381", "endLine": 27, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2418", "line": 27, "column": 41, "nodeType": "2380", "messageId": "2381", "endLine": 27, "endColumn": 51}, {"ruleId": "2378", "severity": 1, "message": "2419", "line": 31, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 31, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2420", "line": 34, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 34, "endColumn": 35}, {"ruleId": "2378", "severity": 1, "message": "2421", "line": 38, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 38, "endColumn": 33}, {"ruleId": "2378", "severity": 1, "message": "2422", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 21}, {"ruleId": "2385", "severity": 1, "message": "2423", "line": 27, "column": 8, "nodeType": "2387", "endLine": 27, "endColumn": 18, "suggestions": "2424"}, {"ruleId": "2378", "severity": 1, "message": "2405", "line": 1, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2407", "line": 2, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2425", "line": 6, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2426", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2427", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2419", "line": 25, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 25, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2421", "line": 28, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 28, "endColumn": 33}, {"ruleId": "2378", "severity": 1, "message": "2428", "line": 31, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 31, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2429", "line": 6, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2430", "line": 9, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 18, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2432", "line": 20, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 20, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2433", "line": 26, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2434", "line": 5, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 39}, {"ruleId": "2378", "severity": 1, "message": "2429", "line": 15, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2430", "line": 16, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 16, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 24, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2432", "line": 30, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 30, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2435", "line": 34, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 34, "endColumn": 23}, {"ruleId": "2385", "severity": 1, "message": "2436", "line": 73, "column": 31, "nodeType": "2380", "endLine": 73, "endColumn": 42}, {"ruleId": "2385", "severity": 1, "message": "2437", "line": 269, "column": 8, "nodeType": "2387", "endLine": 269, "endColumn": 18, "suggestions": "2438"}, {"ruleId": "2378", "severity": 1, "message": "2405", "line": 1, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2439", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2440", "line": 39, "column": 19, "nodeType": "2380", "messageId": "2381", "endLine": 39, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2441", "line": 3, "column": 34, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 67}, {"ruleId": "2378", "severity": 1, "message": "2442", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2439", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 17}, {"ruleId": "2443", "severity": 1, "message": "2444", "line": 140, "column": 80, "nodeType": "2445", "messageId": "2446", "endLine": 140, "endColumn": 82}, {"ruleId": "2378", "severity": 1, "message": "2447", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2448", "line": 19, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2434", "line": 7, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 39}, {"ruleId": "2378", "severity": 1, "message": "2449", "line": 25, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 25, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2450", "line": 26, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2451", "line": 48, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 48, "endColumn": 22}, {"ruleId": "2385", "severity": 1, "message": "2452", "line": 69, "column": 8, "nodeType": "2387", "endLine": 69, "endColumn": 30, "suggestions": "2453"}, {"ruleId": "2378", "severity": 1, "message": "2454", "line": 79, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 79, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2455", "line": 87, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 87, "endColumn": 21}, {"ruleId": "2385", "severity": 1, "message": "2456", "line": 18, "column": 8, "nodeType": "2387", "endLine": 18, "endColumn": 10, "suggestions": "2457"}, {"ruleId": "2378", "severity": 1, "message": "2458", "line": 11, "column": 41, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 51}, {"ruleId": "2378", "severity": 1, "message": "2459", "line": 7, "column": 47, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 68}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 2, "column": 20, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 1, "column": 20, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2461", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2462", "line": 12, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2463", "line": 13, "column": 36, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 45}, {"ruleId": "2378", "severity": 1, "message": "2464", "line": 14, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2465", "line": 14, "column": 16, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2466", "line": 29, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 29, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2467", "line": 10, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 10, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2458", "line": 18, "column": 36, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 46}, {"ruleId": "2385", "severity": 1, "message": "2468", "line": 43, "column": 8, "nodeType": "2387", "endLine": 43, "endColumn": 26, "suggestions": "2469"}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2470", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2471", "line": 8, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 28}, {"ruleId": "2385", "severity": 1, "message": "2472", "line": 45, "column": 8, "nodeType": "2387", "endLine": 45, "endColumn": 32, "suggestions": "2473"}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2474", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2475", "line": 6, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2471", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 28}, {"ruleId": "2385", "severity": 1, "message": "2476", "line": 57, "column": 8, "nodeType": "2387", "endLine": 57, "endColumn": 28, "suggestions": "2477"}, {"ruleId": "2378", "severity": 1, "message": "2421", "line": 71, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 71, "endColumn": 33}, {"ruleId": "2378", "severity": 1, "message": "2420", "line": 75, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 75, "endColumn": 35}, {"ruleId": "2378", "severity": 1, "message": "2428", "line": 79, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 79, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2478", "line": 1, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2479", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2480", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2481", "line": 12, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 31}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2482", "line": 6, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2475", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2429", "line": 13, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2430", "line": 14, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2432", "line": 23, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 23, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2458", "line": 16, "column": 36, "nodeType": "2380", "messageId": "2381", "endLine": 16, "endColumn": 46}, {"ruleId": "2385", "severity": 1, "message": "2483", "line": 35, "column": 8, "nodeType": "2387", "endLine": 35, "endColumn": 53, "suggestions": "2484"}, {"ruleId": "2378", "severity": 1, "message": "2467", "line": 6, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2458", "line": 15, "column": 36, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 46}, {"ruleId": "2378", "severity": 1, "message": "2485", "line": 19, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 37}, {"ruleId": "2378", "severity": 1, "message": "2486", "line": 17, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2487", "line": 29, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 29, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2488", "line": 36, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 36, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2489", "line": 12, "column": 55, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 61}, {"ruleId": "2378", "severity": 1, "message": "2490", "line": 12, "column": 77, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 82}, {"ruleId": "2378", "severity": 1, "message": "2491", "line": 12, "column": 84, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 94}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 18, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2435", "line": 20, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 20, "endColumn": 23}, {"ruleId": "2385", "severity": 1, "message": "2436", "line": 39, "column": 31, "nodeType": "2380", "endLine": 39, "endColumn": 42}, {"ruleId": "2378", "severity": 1, "message": "2492", "line": 96, "column": 17, "nodeType": "2380", "messageId": "2381", "endLine": 96, "endColumn": 21}, {"ruleId": "2443", "severity": 1, "message": "2444", "line": 412, "column": 68, "nodeType": "2445", "messageId": "2446", "endLine": 412, "endColumn": 70}, {"ruleId": "2378", "severity": 1, "message": "2493", "line": 2, "column": 37, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 45}, {"ruleId": "2378", "severity": 1, "message": "2494", "line": 2, "column": 47, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 60}, {"ruleId": "2378", "severity": 1, "message": "2495", "line": 2, "column": 62, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 67}, {"ruleId": "2378", "severity": 1, "message": "2496", "line": 2, "column": 69, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 77}, {"ruleId": "2378", "severity": 1, "message": "2497", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 26}, {"ruleId": "2385", "severity": 1, "message": "2498", "line": 36, "column": 8, "nodeType": "2387", "endLine": 36, "endColumn": 43, "suggestions": "2499"}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 1, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 33}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2500", "line": 21, "column": 15, "nodeType": "2380", "messageId": "2381", "endLine": 21, "endColumn": 21}, {"ruleId": "2385", "severity": 1, "message": "2501", "line": 25, "column": 8, "nodeType": "2387", "endLine": 25, "endColumn": 20, "suggestions": "2502"}, {"ruleId": "2378", "severity": 1, "message": "2503", "line": 14, "column": 38, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 47}, {"ruleId": "2378", "severity": 1, "message": "2504", "line": 14, "column": 49, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 56}, {"ruleId": "2378", "severity": 1, "message": "2505", "line": 14, "column": 58, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 70}, {"ruleId": "2506", "severity": 1, "message": "2507", "line": 34, "column": 40, "nodeType": "2508", "messageId": "2509", "endLine": 34, "endColumn": 42}, {"ruleId": "2506", "severity": 1, "message": "2507", "line": 34, "column": 109, "nodeType": "2508", "messageId": "2509", "endLine": 34, "endColumn": 111}, {"ruleId": "2378", "severity": 1, "message": "2510", "line": 1, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2496", "line": 7, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2490", "line": 8, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 10}, {"ruleId": "2378", "severity": 1, "message": "2493", "line": 9, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2494", "line": 10, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 10, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2511", "line": 11, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2512", "line": 12, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2513", "line": 13, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2514", "line": 22, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 22, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2515", "line": 33, "column": 78, "nodeType": "2380", "messageId": "2381", "endLine": 33, "endColumn": 87}, {"ruleId": "2378", "severity": 1, "message": "2516", "line": 33, "column": 89, "nodeType": "2380", "messageId": "2381", "endLine": 33, "endColumn": 97}, {"ruleId": "2378", "severity": 1, "message": "2517", "line": 33, "column": 103, "nodeType": "2380", "messageId": "2381", "endLine": 33, "endColumn": 109}, {"ruleId": "2378", "severity": 1, "message": "2518", "line": 33, "column": 111, "nodeType": "2380", "messageId": "2381", "endLine": 33, "endColumn": 117}, {"ruleId": "2378", "severity": 1, "message": "2519", "line": 33, "column": 119, "nodeType": "2380", "messageId": "2381", "endLine": 33, "endColumn": 131}, {"ruleId": "2506", "severity": 1, "message": "2507", "line": 87, "column": 75, "nodeType": "2508", "messageId": "2509", "endLine": 87, "endColumn": 77}, {"ruleId": "2506", "severity": 1, "message": "2507", "line": 87, "column": 144, "nodeType": "2508", "messageId": "2509", "endLine": 87, "endColumn": 146}, {"ruleId": "2506", "severity": 1, "message": "2507", "line": 92, "column": 74, "nodeType": "2508", "messageId": "2509", "endLine": 92, "endColumn": 76}, {"ruleId": "2506", "severity": 1, "message": "2507", "line": 92, "column": 138, "nodeType": "2508", "messageId": "2509", "endLine": 92, "endColumn": 140}, {"ruleId": "2378", "severity": 1, "message": "2395", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2520", "line": 13, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 23}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 290, "column": 56, "nodeType": "2445", "messageId": "2446", "endLine": 290, "endColumn": 58}, {"ruleId": "2378", "severity": 1, "message": "2391", "line": 1, "column": 20, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2522", "line": 10, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 10, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2523", "line": 64, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 64, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2524", "line": 64, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 64, "endColumn": 39}, {"ruleId": "2378", "severity": 1, "message": "2525", "line": 6, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2526", "line": 6, "column": 31, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 37}, {"ruleId": "2378", "severity": 1, "message": "2491", "line": 6, "column": 39, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 49}, {"ruleId": "2378", "severity": 1, "message": "2495", "line": 6, "column": 51, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 56}, {"ruleId": "2378", "severity": 1, "message": "2527", "line": 6, "column": 65, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 70}, {"ruleId": "2378", "severity": 1, "message": "2417", "line": 15, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2528", "line": 15, "column": 44, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 52}, {"ruleId": "2385", "severity": 1, "message": "2529", "line": 42, "column": 8, "nodeType": "2387", "endLine": 42, "endColumn": 40, "suggestions": "2530"}, {"ruleId": "2378", "severity": 1, "message": "2511", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2427", "line": 7, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 36}, {"ruleId": "2378", "severity": 1, "message": "2462", "line": 13, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2462", "line": 15, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2427", "line": 7, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 36}, {"ruleId": "2378", "severity": 1, "message": "2462", "line": 14, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2427", "line": 7, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 36}, {"ruleId": "2378", "severity": 1, "message": "2462", "line": 13, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2531", "line": 15, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2532", "line": 19, "column": 22, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2533", "line": 1, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2533", "line": 1, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 7, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2384", "line": 1, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2382", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2534", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2535", "line": 4, "column": 26, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 44}, {"ruleId": "2378", "severity": 1, "message": "2536", "line": 9, "column": 166, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 170}, {"ruleId": "2385", "severity": 1, "message": "2537", "line": 89, "column": 8, "nodeType": "2387", "endLine": 89, "endColumn": 10, "suggestions": "2538"}, {"ruleId": "2402", "severity": 1, "message": "2403", "line": 56, "column": 37, "nodeType": "2404", "endLine": 56, "endColumn": 107}, {"ruleId": "2402", "severity": 1, "message": "2403", "line": 44, "column": 29, "nodeType": "2404", "endLine": 44, "endColumn": 98}, {"ruleId": "2385", "severity": 1, "message": "2539", "line": 29, "column": 8, "nodeType": "2387", "endLine": 29, "endColumn": 10, "suggestions": "2540"}, {"ruleId": "2378", "severity": 1, "message": "2541", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 27}, {"ruleId": "2385", "severity": 1, "message": "2542", "line": 73, "column": 8, "nodeType": "2387", "endLine": 73, "endColumn": 25, "suggestions": "2543"}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 19}, {"ruleId": "2544", "severity": 1, "message": "2545", "line": 205, "column": 24, "nodeType": "2546", "messageId": "2547", "endLine": 205, "endColumn": 25, "suggestions": "2548"}, {"ruleId": "2544", "severity": 1, "message": "2549", "line": 205, "column": 26, "nodeType": "2546", "messageId": "2547", "endLine": 205, "endColumn": 27, "suggestions": "2550"}, {"ruleId": "2544", "severity": 1, "message": "2545", "line": 207, "column": 27, "nodeType": "2546", "messageId": "2547", "endLine": 207, "endColumn": 28, "suggestions": "2551"}, {"ruleId": "2544", "severity": 1, "message": "2549", "line": 207, "column": 29, "nodeType": "2546", "messageId": "2547", "endLine": 207, "endColumn": 30, "suggestions": "2552"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 256, "column": 30, "nodeType": "2546", "messageId": "2547", "endLine": 256, "endColumn": 31, "suggestions": "2554"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 276, "column": 61, "nodeType": "2546", "messageId": "2547", "endLine": 276, "endColumn": 62, "suggestions": "2555"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 296, "column": 29, "nodeType": "2546", "messageId": "2547", "endLine": 296, "endColumn": 30, "suggestions": "2556"}, {"ruleId": "2544", "severity": 1, "message": "2557", "line": 296, "column": 31, "nodeType": "2546", "messageId": "2547", "endLine": 296, "endColumn": 32, "suggestions": "2558"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 297, "column": 51, "nodeType": "2546", "messageId": "2547", "endLine": 297, "endColumn": 52, "suggestions": "2559"}, {"ruleId": "2544", "severity": 1, "message": "2557", "line": 297, "column": 53, "nodeType": "2546", "messageId": "2547", "endLine": 297, "endColumn": 54, "suggestions": "2560"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 399, "column": 30, "nodeType": "2546", "messageId": "2547", "endLine": 399, "endColumn": 31, "suggestions": "2561"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 427, "column": 61, "nodeType": "2546", "messageId": "2547", "endLine": 427, "endColumn": 62, "suggestions": "2562"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 447, "column": 29, "nodeType": "2546", "messageId": "2547", "endLine": 447, "endColumn": 30, "suggestions": "2563"}, {"ruleId": "2544", "severity": 1, "message": "2557", "line": 447, "column": 31, "nodeType": "2546", "messageId": "2547", "endLine": 447, "endColumn": 32, "suggestions": "2564"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 448, "column": 51, "nodeType": "2546", "messageId": "2547", "endLine": 448, "endColumn": 52, "suggestions": "2565"}, {"ruleId": "2544", "severity": 1, "message": "2557", "line": 448, "column": 53, "nodeType": "2546", "messageId": "2547", "endLine": 448, "endColumn": 54, "suggestions": "2566"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 537, "column": 30, "nodeType": "2546", "messageId": "2547", "endLine": 537, "endColumn": 31, "suggestions": "2567"}, {"ruleId": "2544", "severity": 1, "message": "2553", "line": 562, "column": 61, "nodeType": "2546", "messageId": "2547", "endLine": 562, "endColumn": 62, "suggestions": "2568"}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 116, "column": 56, "nodeType": "2445", "messageId": "2446", "endLine": 116, "endColumn": 58}, {"ruleId": "2378", "severity": 1, "message": "2569", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2570", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2571", "line": 100, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 100, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2572", "line": 111, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 111, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2569", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2573", "line": 6, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2574", "line": 6, "column": 18, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 33}, {"ruleId": "2378", "severity": 1, "message": "2575", "line": 13, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2576", "line": 13, "column": 28, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 45}, {"ruleId": "2378", "severity": 1, "message": "2577", "line": 29, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 29, "endColumn": 20}, {"ruleId": "2578", "severity": 1, "message": "2579", "line": 28, "column": 74, "nodeType": "2445", "messageId": "2580", "endLine": 28, "endColumn": 75}, {"ruleId": "2581", "severity": 1, "message": "2582", "line": 12, "column": 25, "nodeType": "2404", "endLine": 12, "endColumn": 614}, {"ruleId": "2378", "severity": 1, "message": "2479", "line": 2, "column": 23, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 34}, {"ruleId": "2378", "severity": 1, "message": "2583", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2584", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2585", "line": 5, "column": 33, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 40}, {"ruleId": "2378", "severity": 1, "message": "2586", "line": 5, "column": 42, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 51}, {"ruleId": "2378", "severity": 1, "message": "2587", "line": 8, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2533", "line": 1, "column": 46, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 49}, {"ruleId": "2378", "severity": 1, "message": "2588", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2589", "line": 15, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2590", "line": 15, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 37}, {"ruleId": "2385", "severity": 1, "message": "2591", "line": 21, "column": 8, "nodeType": "2387", "endLine": 21, "endColumn": 26, "suggestions": "2592"}, {"ruleId": "2385", "severity": 1, "message": "2593", "line": 40, "column": 8, "nodeType": "2387", "endLine": 40, "endColumn": 10, "suggestions": "2594"}, {"ruleId": "2385", "severity": 1, "message": "2595", "line": 299, "column": 8, "nodeType": "2387", "endLine": 299, "endColumn": 18, "suggestions": "2596"}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 89, "column": 37, "nodeType": "2445", "messageId": "2446", "endLine": 89, "endColumn": 39}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 91, "column": 44, "nodeType": "2445", "messageId": "2446", "endLine": 91, "endColumn": 46}, {"ruleId": "2378", "severity": 1, "message": "2597", "line": 2, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2598", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2599", "line": 18, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2600", "line": 18, "column": 137, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 146}, {"ruleId": "2378", "severity": 1, "message": "2601", "line": 18, "column": 148, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 161}, {"ruleId": "2378", "severity": 1, "message": "2602", "line": 18, "column": 163, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 173}, {"ruleId": "2378", "severity": 1, "message": "2603", "line": 18, "column": 175, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 182}, {"ruleId": "2378", "severity": 1, "message": "2604", "line": 18, "column": 184, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 189}, {"ruleId": "2378", "severity": 1, "message": "2605", "line": 18, "column": 191, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 205}, {"ruleId": "2378", "severity": 1, "message": "2606", "line": 34, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 34, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2607", "line": 39, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 39, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2608", "line": 40, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 40, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2609", "line": 64, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 64, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2610", "line": 65, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 65, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2611", "line": 67, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 67, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2569", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2612", "line": 6, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2496", "line": 12, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2495", "line": 13, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 10}, {"ruleId": "2378", "severity": 1, "message": "2613", "line": 14, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2614", "line": 16, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 16, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2615", "line": 17, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2616", "line": 79, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 79, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2617", "line": 80, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 80, "endColumn": 18}, {"ruleId": "2385", "severity": 1, "message": "2618", "line": 86, "column": 8, "nodeType": "2387", "endLine": 86, "endColumn": 18, "suggestions": "2619"}, {"ruleId": "2385", "severity": 1, "message": "2620", "line": 197, "column": 8, "nodeType": "2387", "endLine": 197, "endColumn": 22, "suggestions": "2621"}, {"ruleId": "2622", "severity": 1, "message": "2623", "line": 205, "column": 91, "nodeType": "2624", "messageId": "2446", "endLine": 205, "endColumn": 95}, {"ruleId": "2385", "severity": 1, "message": "2625", "line": 206, "column": 8, "nodeType": "2387", "endLine": 206, "endColumn": 112, "suggestions": "2626"}, {"ruleId": "2378", "severity": 1, "message": "2627", "line": 3, "column": 48, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 64}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 24, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 19}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 140, "column": 37, "nodeType": "2445", "messageId": "2446", "endLine": 140, "endColumn": 39}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 142, "column": 63, "nodeType": "2445", "messageId": "2446", "endLine": 142, "endColumn": 65}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 145, "column": 71, "nodeType": "2445", "messageId": "2446", "endLine": 145, "endColumn": 73}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 153, "column": 61, "nodeType": "2445", "messageId": "2446", "endLine": 153, "endColumn": 63}, {"ruleId": "2385", "severity": 1, "message": "2628", "line": 160, "column": 8, "nodeType": "2387", "endLine": 160, "endColumn": 76, "suggestions": "2629"}, {"ruleId": "2385", "severity": 1, "message": "2630", "line": 222, "column": 8, "nodeType": "2387", "endLine": 222, "endColumn": 21, "suggestions": "2631"}, {"ruleId": "2378", "severity": 1, "message": "2632", "line": 34, "column": 53, "nodeType": "2380", "messageId": "2381", "endLine": 34, "endColumn": 62}, {"ruleId": "2378", "severity": 1, "message": "2633", "line": 35, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 35, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2634", "line": 43, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 43, "endColumn": 27}, {"ruleId": "2385", "severity": 1, "message": "2635", "line": 154, "column": 8, "nodeType": "2387", "endLine": 154, "endColumn": 120, "suggestions": "2636"}, {"ruleId": "2378", "severity": 1, "message": "2500", "line": 162, "column": 19, "nodeType": "2380", "messageId": "2381", "endLine": 162, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2637", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 35}, {"ruleId": "2378", "severity": 1, "message": "2638", "line": 4, "column": 58, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 73}, {"ruleId": "2378", "severity": 1, "message": "2639", "line": 4, "column": 102, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 117}, {"ruleId": "2378", "severity": 1, "message": "2640", "line": 4, "column": 119, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 126}, {"ruleId": "2378", "severity": 1, "message": "2391", "line": 6, "column": 31, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 37}, {"ruleId": "2378", "severity": 1, "message": "2641", "line": 8, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2642", "line": 14, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2643", "line": 17, "column": 15, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2644", "line": 18, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 8}, {"ruleId": "2378", "severity": 1, "message": "2645", "line": 26, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 9}, {"ruleId": "2378", "severity": 1, "message": "2646", "line": 27, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 27, "endColumn": 10}, {"ruleId": "2378", "severity": 1, "message": "2647", "line": 28, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 28, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2648", "line": 35, "column": 73, "nodeType": "2380", "messageId": "2381", "endLine": 35, "endColumn": 87}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 48, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 48, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2649", "line": 49, "column": 19, "nodeType": "2380", "messageId": "2381", "endLine": 49, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2650", "line": 320, "column": 48, "nodeType": "2380", "messageId": "2381", "endLine": 320, "endColumn": 52}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 349, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 349, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2651", "line": 350, "column": 19, "nodeType": "2380", "messageId": "2381", "endLine": 350, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2652", "line": 351, "column": 23, "nodeType": "2380", "messageId": "2381", "endLine": 351, "endColumn": 33}, {"ruleId": "2378", "severity": 1, "message": "2653", "line": 355, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 355, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2651", "line": 394, "column": 19, "nodeType": "2380", "messageId": "2381", "endLine": 394, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2654", "line": 394, "column": 52, "nodeType": "2380", "messageId": "2381", "endLine": 394, "endColumn": 75}, {"ruleId": "2385", "severity": 1, "message": "2655", "line": 471, "column": 8, "nodeType": "2387", "endLine": 471, "endColumn": 48, "suggestions": "2656"}, {"ruleId": "2378", "severity": 1, "message": "2533", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2657", "line": 9, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 10, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 10, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2658", "line": 11, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2659", "line": 24, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2660", "line": 32, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 32, "endColumn": 20}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 199, "column": 91, "nodeType": "2445", "messageId": "2446", "endLine": 199, "endColumn": 93}, {"ruleId": "2378", "severity": 1, "message": "2661", "line": 467, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 467, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2662", "line": 530, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 530, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2663", "line": 658, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 658, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2430", "line": 13, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 9}, {"ruleId": "2378", "severity": 1, "message": "2493", "line": 17, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2664", "line": 19, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 13}, {"ruleId": "2385", "severity": 1, "message": "2665", "line": 129, "column": 8, "nodeType": "2387", "endLine": 129, "endColumn": 18, "suggestions": "2666"}, {"ruleId": "2378", "severity": 1, "message": "2667", "line": 155, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 155, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2668", "line": 232, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 232, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2613", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2395", "line": 8, "column": 18, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2496", "line": 8, "column": 21, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2669", "line": 15, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2670", "line": 16, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 16, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2671", "line": 26, "column": 24, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 37}, {"ruleId": "2378", "severity": 1, "message": "2672", "line": 111, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 111, "endColumn": 23}, {"ruleId": "2443", "severity": 1, "message": "2444", "line": 49, "column": 108, "nodeType": "2445", "messageId": "2446", "endLine": 49, "endColumn": 110}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 22}, {"ruleId": "2385", "severity": 1, "message": "2673", "line": 40, "column": 8, "nodeType": "2387", "endLine": 40, "endColumn": 30, "suggestions": "2674"}, {"ruleId": "2402", "severity": 1, "message": "2403", "line": 223, "column": 69, "nodeType": "2404", "endLine": 227, "endColumn": 71}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 285, "column": 65, "nodeType": "2445", "messageId": "2446", "endLine": 285, "endColumn": 67}, {"ruleId": "2378", "severity": 1, "message": "2675", "line": 55, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 55, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2652", "line": 55, "column": 32, "nodeType": "2380", "messageId": "2381", "endLine": 55, "endColumn": 42}, {"ruleId": "2378", "severity": 1, "message": "2676", "line": 59, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 59, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2677", "line": 70, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 70, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2678", "line": 82, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 82, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2679", "line": 88, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 88, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2667", "line": 94, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 94, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2680", "line": 7, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2681", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 12}, {"ruleId": "2378", "severity": 1, "message": "2682", "line": 13, "column": 3, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2576", "line": 14, "column": 3, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 20}, {"ruleId": "2443", "severity": 1, "message": "2444", "line": 336, "column": 35, "nodeType": "2445", "messageId": "2446", "endLine": 336, "endColumn": 37}, {"ruleId": "2378", "severity": 1, "message": "2683", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2684", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2685", "line": 17, "column": 26, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 46}, {"ruleId": "2378", "severity": 1, "message": "2686", "line": 24, "column": 96, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 100}, {"ruleId": "2378", "severity": 1, "message": "2489", "line": 24, "column": 102, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 108}, {"ruleId": "2378", "severity": 1, "message": "2407", "line": 26, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2687", "line": 27, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 27, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 34, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 34, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2651", "line": 35, "column": 47, "nodeType": "2380", "messageId": "2381", "endLine": 35, "endColumn": 54}, {"ruleId": "2378", "severity": 1, "message": "2458", "line": 37, "column": 17, "nodeType": "2380", "messageId": "2381", "endLine": 37, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2688", "line": 91, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 91, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2689", "line": 92, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 92, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2690", "line": 98, "column": 20, "nodeType": "2380", "messageId": "2381", "endLine": 98, "endColumn": 31}, {"ruleId": "2378", "severity": 1, "message": "2691", "line": 99, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 99, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2692", "line": 99, "column": 22, "nodeType": "2380", "messageId": "2381", "endLine": 99, "endColumn": 35}, {"ruleId": "2378", "severity": 1, "message": "2693", "line": 100, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 100, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2694", "line": 100, "column": 20, "nodeType": "2380", "messageId": "2381", "endLine": 100, "endColumn": 31}, {"ruleId": "2378", "severity": 1, "message": "2695", "line": 103, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 103, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2696", "line": 104, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 104, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2697", "line": 105, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 105, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2698", "line": 106, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 106, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2699", "line": 136, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 136, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2700", "line": 137, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 137, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2701", "line": 139, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 139, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2702", "line": 139, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 139, "endColumn": 41}, {"ruleId": "2378", "severity": 1, "message": "2703", "line": 167, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 167, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2704", "line": 173, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 173, "endColumn": 33}, {"ruleId": "2385", "severity": 1, "message": "2705", "line": 286, "column": 6, "nodeType": "2387", "endLine": 286, "endColumn": 32, "suggestions": "2706"}, {"ruleId": "2378", "severity": 1, "message": "2707", "line": 364, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 364, "endColumn": 23}, {"ruleId": "2385", "severity": 1, "message": "2708", "line": 742, "column": 6, "nodeType": "2387", "endLine": 742, "endColumn": 25, "suggestions": "2709"}, {"ruleId": "2378", "severity": 1, "message": "2710", "line": 764, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 764, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2711", "line": 776, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 776, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2467", "line": 9, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2712", "line": 11, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2489", "line": 19, "column": 3, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 9}, {"ruleId": "2378", "severity": 1, "message": "2458", "line": 31, "column": 30, "nodeType": "2380", "messageId": "2381", "endLine": 31, "endColumn": 40}, {"ruleId": "2378", "severity": 1, "message": "2712", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2490", "line": 19, "column": 3, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 8}, {"ruleId": "2378", "severity": 1, "message": "2713", "line": 20, "column": 3, "nodeType": "2380", "messageId": "2381", "endLine": 20, "endColumn": 10}, {"ruleId": "2378", "severity": 1, "message": "2714", "line": 30, "column": 36, "nodeType": "2380", "messageId": "2381", "endLine": 30, "endColumn": 56}, {"ruleId": "2378", "severity": 1, "message": "2715", "line": 32, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 32, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2716", "line": 32, "column": 32, "nodeType": "2380", "messageId": "2381", "endLine": 32, "endColumn": 55}, {"ruleId": "2385", "severity": 1, "message": "2436", "line": 35, "column": 29, "nodeType": "2380", "endLine": 35, "endColumn": 40}, {"ruleId": "2385", "severity": 1, "message": "2436", "line": 35, "column": 30, "nodeType": "2380", "endLine": 35, "endColumn": 41}, {"ruleId": "2378", "severity": 1, "message": "2717", "line": 2, "column": 26, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 32}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 347, "column": 68, "nodeType": "2445", "messageId": "2446", "endLine": 347, "endColumn": 70}, {"ruleId": "2443", "severity": 1, "message": "2444", "line": 360, "column": 64, "nodeType": "2445", "messageId": "2446", "endLine": 360, "endColumn": 66}, {"ruleId": "2378", "severity": 1, "message": "2533", "line": 1, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2718", "line": 12, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2429", "line": 24, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2430", "line": 26, "column": 119, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 123}, {"ruleId": "2378", "severity": 1, "message": "2719", "line": 26, "column": 135, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 140}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 99, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 99, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2458", "line": 107, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 107, "endColumn": 19}, {"ruleId": "2385", "severity": 1, "message": "2620", "line": 217, "column": 8, "nodeType": "2387", "endLine": 217, "endColumn": 38, "suggestions": "2720"}, {"ruleId": "2385", "severity": 1, "message": "2721", "line": 67, "column": 8, "nodeType": "2387", "endLine": 67, "endColumn": 47, "suggestions": "2722"}, {"ruleId": "2378", "severity": 1, "message": "2405", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2489", "line": 17, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2396", "line": 20, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 20, "endColumn": 9}, {"ruleId": "2378", "severity": 1, "message": "2723", "line": 27, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 27, "endColumn": 32}, {"ruleId": "2385", "severity": 1, "message": "2724", "line": 122, "column": 8, "nodeType": "2387", "endLine": 122, "endColumn": 10, "suggestions": "2725"}, {"ruleId": "2378", "severity": 1, "message": "2496", "line": 6, "column": 56, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 64}, {"ruleId": "2378", "severity": 1, "message": "2526", "line": 6, "column": 73, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 79}, {"ruleId": "2378", "severity": 1, "message": "2491", "line": 6, "column": 81, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 91}, {"ruleId": "2378", "severity": 1, "message": "2726", "line": 5, "column": 60, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 66}, {"ruleId": "2378", "severity": 1, "message": "2613", "line": 5, "column": 68, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 74}, {"ruleId": "2378", "severity": 1, "message": "2727", "line": 2, "column": 42, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 51}, {"ruleId": "2378", "severity": 1, "message": "2728", "line": 5, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 8, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2651", "line": 16, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 16, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2729", "line": 17, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 1, "column": 21, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 29}, {"ruleId": "2622", "severity": 1, "message": "2730", "line": 110, "column": 5, "nodeType": "2624", "messageId": "2446", "endLine": 110, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2731", "line": 151, "column": 33, "nodeType": "2380", "messageId": "2381", "endLine": 151, "endColumn": 47}, {"ruleId": "2732", "severity": 1, "message": "2733", "line": 167, "column": 17, "nodeType": "2734", "messageId": "2735", "endLine": 177, "endColumn": 18}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 280, "column": 50, "nodeType": "2445", "messageId": "2446", "endLine": 280, "endColumn": 52}, {"ruleId": "2378", "severity": 1, "message": "2736", "line": 422, "column": 27, "nodeType": "2380", "messageId": "2381", "endLine": 422, "endColumn": 37}, {"ruleId": "2378", "severity": 1, "message": "2513", "line": 3, "column": 58, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 69}, {"ruleId": "2378", "severity": 1, "message": "2737", "line": 3, "column": 71, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 81}, {"ruleId": "2378", "severity": 1, "message": "2738", "line": 48, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 48, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2739", "line": 56, "column": 9, "nodeType": "2380", "messageId": "2381", "endLine": 56, "endColumn": 29}, {"ruleId": "2385", "severity": 1, "message": "2436", "line": 37, "column": 30, "nodeType": "2380", "endLine": 37, "endColumn": 41}, {"ruleId": "2385", "severity": 1, "message": "2436", "line": 35, "column": 32, "nodeType": "2380", "endLine": 35, "endColumn": 43}, {"ruleId": "2378", "severity": 1, "message": "2496", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2490", "line": 3, "column": 20, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2740", "line": 3, "column": 27, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 38}, {"ruleId": "2378", "severity": 1, "message": "2511", "line": 3, "column": 40, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 52}, {"ruleId": "2378", "severity": 1, "message": "2513", "line": 3, "column": 54, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 65}, {"ruleId": "2378", "severity": 1, "message": "2741", "line": 6, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2742", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2743", "line": 8, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 39}, {"ruleId": "2378", "severity": 1, "message": "2533", "line": 1, "column": 17, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2744", "line": 2, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 26}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 186, "column": 50, "nodeType": "2445", "messageId": "2446", "endLine": 186, "endColumn": 52}, {"ruleId": "2385", "severity": 1, "message": "2745", "line": 57, "column": 8, "nodeType": "2387", "endLine": 57, "endColumn": 38, "suggestions": "2746"}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2496", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2740", "line": 3, "column": 27, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 38}, {"ruleId": "2378", "severity": 1, "message": "2511", "line": 3, "column": 40, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 52}, {"ruleId": "2378", "severity": 1, "message": "2747", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2748", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2749", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2742", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2750", "line": 9, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2651", "line": 15, "column": 39, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 46}, {"ruleId": "2378", "severity": 1, "message": "2520", "line": 17, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2751", "line": 18, "column": 37, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 48}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 181, "column": 54, "nodeType": "2445", "messageId": "2446", "endLine": 181, "endColumn": 56}, {"ruleId": "2378", "severity": 1, "message": "2752", "line": 79, "column": 29, "nodeType": "2380", "messageId": "2381", "endLine": 79, "endColumn": 45}, {"ruleId": "2378", "severity": 1, "message": "2753", "line": 79, "column": 47, "nodeType": "2380", "messageId": "2381", "endLine": 79, "endColumn": 59}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 1, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2533", "line": 1, "column": 31, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 34}, {"ruleId": "2378", "severity": 1, "message": "2754", "line": 75, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 75, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2651", "line": 75, "column": 30, "nodeType": "2380", "messageId": "2381", "endLine": 75, "endColumn": 37}, {"ruleId": "2378", "severity": 1, "message": "2652", "line": 75, "column": 39, "nodeType": "2380", "messageId": "2381", "endLine": 75, "endColumn": 49}, {"ruleId": "2378", "severity": 1, "message": "2755", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2756", "line": 3, "column": 22, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2757", "line": 3, "column": 31, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 38}, {"ruleId": "2378", "severity": 1, "message": "2758", "line": 3, "column": 40, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 50}, {"ruleId": "2378", "severity": 1, "message": "2759", "line": 7, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2760", "line": 7, "column": 19, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2463", "line": 13, "column": 45, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 54}, {"ruleId": "2378", "severity": 1, "message": "2761", "line": 66, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 66, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2418", "line": 67, "column": 26, "nodeType": "2380", "messageId": "2381", "endLine": 67, "endColumn": 36}, {"ruleId": "2378", "severity": 1, "message": "2762", "line": 2, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 1, "column": 17, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2763", "line": 7, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2764", "line": 7, "column": 19, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2765", "line": 7, "column": 32, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 40}, {"ruleId": "2378", "severity": 1, "message": "2766", "line": 7, "column": 42, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 50}, {"ruleId": "2378", "severity": 1, "message": "2767", "line": 7, "column": 52, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 60}, {"ruleId": "2378", "severity": 1, "message": "2768", "line": 7, "column": 76, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 90}, {"ruleId": "2378", "severity": 1, "message": "2475", "line": 8, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 24}, {"ruleId": "2378", "severity": 1, "message": "2471", "line": 9, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2769", "line": 10, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 10, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2770", "line": 11, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2474", "line": 12, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 13, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2396", "line": 17, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 17, "endColumn": 9}, {"ruleId": "2378", "severity": 1, "message": "2771", "line": 18, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 8}, {"ruleId": "2378", "severity": 1, "message": "2642", "line": 19, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2772", "line": 20, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 20, "endColumn": 9}, {"ruleId": "2378", "severity": 1, "message": "2773", "line": 21, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 21, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2774", "line": 22, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 22, "endColumn": 9}, {"ruleId": "2378", "severity": 1, "message": "2513", "line": 23, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 23, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2511", "line": 24, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 24, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2740", "line": 25, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 25, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2490", "line": 26, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 10}, {"ruleId": "2378", "severity": 1, "message": "2495", "line": 27, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 27, "endColumn": 10}, {"ruleId": "2378", "severity": 1, "message": "2493", "line": 28, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 28, "endColumn": 13}, {"ruleId": "2378", "severity": 1, "message": "2775", "line": 29, "column": 14, "nodeType": "2380", "messageId": "2381", "endLine": 29, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2776", "line": 30, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 30, "endColumn": 11}, {"ruleId": "2378", "severity": 1, "message": "2777", "line": 32, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 32, "endColumn": 19}, {"ruleId": "2385", "severity": 1, "message": "2423", "line": 54, "column": 8, "nodeType": "2387", "endLine": 54, "endColumn": 18, "suggestions": "2778"}, {"ruleId": "2378", "severity": 1, "message": "2406", "line": 56, "column": 123, "nodeType": "2380", "messageId": "2381", "endLine": 56, "endColumn": 132}, {"ruleId": "2378", "severity": 1, "message": "2779", "line": 11, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 11, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2463", "line": 96, "column": 29, "nodeType": "2380", "messageId": "2381", "endLine": 96, "endColumn": 38}, {"ruleId": "2378", "severity": 1, "message": "2780", "line": 166, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 166, "endColumn": 18}, {"ruleId": "2385", "severity": 1, "message": "2781", "line": 17, "column": 8, "nodeType": "2387", "endLine": 17, "endColumn": 24, "suggestions": "2782"}, {"ruleId": "2385", "severity": 1, "message": "2783", "line": 17, "column": 9, "nodeType": "2784", "endLine": 17, "endColumn": 16}, {"ruleId": "2378", "severity": 1, "message": "2442", "line": 4, "column": 49, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 61}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 9, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2785", "line": 13, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2786", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2787", "line": 18, "column": 5, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2773", "line": 26, "column": 137, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 143}, {"ruleId": "2378", "severity": 1, "message": "2788", "line": 26, "column": 145, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 149}, {"ruleId": "2378", "severity": 1, "message": "2789", "line": 33, "column": 43, "nodeType": "2380", "messageId": "2381", "endLine": 33, "endColumn": 63}, {"ruleId": "2378", "severity": 1, "message": "2658", "line": 36, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 36, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2474", "line": 38, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 38, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2790", "line": 43, "column": 34, "nodeType": "2380", "messageId": "2381", "endLine": 43, "endColumn": 42}, {"ruleId": "2402", "severity": 1, "message": "2403", "line": 482, "column": 33, "nodeType": "2404", "endLine": 486, "endColumn": 35}, {"ruleId": "2378", "severity": 1, "message": "2791", "line": 509, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 509, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2571", "line": 510, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 510, "endColumn": 19}, {"ruleId": "2385", "severity": 1, "message": "2792", "line": 523, "column": 8, "nodeType": "2387", "endLine": 523, "endColumn": 118, "suggestions": "2793"}, {"ruleId": "2378", "severity": 1, "message": "2794", "line": 594, "column": 7, "nodeType": "2380", "messageId": "2381", "endLine": 594, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2795", "line": 667, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 667, "endColumn": 25}, {"ruleId": "2378", "severity": 1, "message": "2796", "line": 7, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2797", "line": 8, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2798", "line": 98, "column": 23, "nodeType": "2380", "messageId": "2381", "endLine": 98, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2790", "line": 98, "column": 34, "nodeType": "2380", "messageId": "2381", "endLine": 98, "endColumn": 42}, {"ruleId": "2385", "severity": 1, "message": "2799", "line": 352, "column": 8, "nodeType": "2387", "endLine": 352, "endColumn": 24, "suggestions": "2800"}, {"ruleId": "2378", "severity": 1, "message": "2801", "line": 383, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 383, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2787", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2474", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2481", "line": 6, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 31}, {"ruleId": "2378", "severity": 1, "message": "2802", "line": 7, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2803", "line": 22, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 22, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2804", "line": 44, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 44, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2805", "line": 110, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 110, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 1, "column": 35, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 44}, {"ruleId": "2378", "severity": 1, "message": "2776", "line": 2, "column": 21, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2806", "line": 12, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2807", "line": 76, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 76, "endColumn": 31}, {"ruleId": "2402", "severity": 1, "message": "2403", "line": 56, "column": 21, "nodeType": "2404", "endLine": 60, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 2, "column": 23, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 34}, {"ruleId": "2378", "severity": 1, "message": "2474", "line": 18, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 18, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2808", "line": 53, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 53, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2809", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 32}, {"ruleId": "2378", "severity": 1, "message": "2810", "line": 4, "column": 34, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 52}, {"ruleId": "2378", "severity": 1, "message": "2811", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2812", "line": 5, "column": 30, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 44}, {"ruleId": "2378", "severity": 1, "message": "2813", "line": 7, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2814", "line": 118, "column": 29, "nodeType": "2380", "messageId": "2381", "endLine": 118, "endColumn": 35}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 74, "column": 47, "nodeType": "2445", "messageId": "2446", "endLine": 74, "endColumn": 49}, {"ruleId": "2443", "severity": 1, "message": "2444", "line": 160, "column": 70, "nodeType": "2445", "messageId": "2446", "endLine": 160, "endColumn": 72}, {"ruleId": "2443", "severity": 1, "message": "2444", "line": 164, "column": 100, "nodeType": "2445", "messageId": "2446", "endLine": 164, "endColumn": 102}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 166, "column": 38, "nodeType": "2445", "messageId": "2446", "endLine": 166, "endColumn": 40}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 1, "column": 88, "nodeType": "2445", "messageId": "2446", "endLine": 1, "endColumn": 90}, {"ruleId": "2385", "severity": 1, "message": "2815", "line": 31, "column": 8, "nodeType": "2387", "endLine": 31, "endColumn": 33, "suggestions": "2816"}, {"ruleId": "2378", "severity": 1, "message": "2460", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2393", "line": 4, "column": 21, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 29}, {"ruleId": "2378", "severity": 1, "message": "2817", "line": 28, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 28, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2660", "line": 5, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2520", "line": 10, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 10, "endColumn": 23}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 287, "column": 56, "nodeType": "2445", "messageId": "2446", "endLine": 287, "endColumn": 58}, {"ruleId": "2378", "severity": 1, "message": "2818", "line": 7, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2601", "line": 8, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 23}, {"ruleId": "2378", "severity": 1, "message": "2819", "line": 13, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 27}, {"ruleId": "2378", "severity": 1, "message": "2820", "line": 20, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 20, "endColumn": 27}, {"ruleId": "2443", "severity": 1, "message": "2521", "line": 26, "column": 18, "nodeType": "2445", "messageId": "2446", "endLine": 26, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2821", "line": 29, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 29, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2822", "line": 15, "column": 25, "nodeType": "2380", "messageId": "2381", "endLine": 15, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2823", "line": 40, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 40, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2649", "line": 12, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 12, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2645", "line": 4, "column": 33, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 37}, {"ruleId": "2385", "severity": 1, "message": "2824", "line": 43, "column": 8, "nodeType": "2387", "endLine": 43, "endColumn": 19, "suggestions": "2825"}, {"ruleId": "2385", "severity": 1, "message": "2620", "line": 50, "column": 8, "nodeType": "2387", "endLine": 50, "endColumn": 18, "suggestions": "2826"}, {"ruleId": "2378", "severity": 1, "message": "2512", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2827", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2817", "line": 14, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2828", "line": 14, "column": 51, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 64}, {"ruleId": "2378", "severity": 1, "message": "2474", "line": 2, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2829", "line": 3, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2512", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2827", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2830", "line": 6, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2831", "line": 14, "column": 33, "nodeType": "2380", "messageId": "2381", "endLine": 14, "endColumn": 42}, {"ruleId": "2378", "severity": 1, "message": "2832", "line": 4, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 28}, {"ruleId": "2378", "severity": 1, "message": "2817", "line": 41, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 41, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 1, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 1, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2390", "line": 2, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 22}, {"ruleId": "2378", "severity": 1, "message": "2512", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2827", "line": 5, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 5, "endColumn": 20}, {"ruleId": "2378", "severity": 1, "message": "2831", "line": 13, "column": 33, "nodeType": "2380", "messageId": "2381", "endLine": 13, "endColumn": 42}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2645", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 14}, {"ruleId": "2378", "severity": 1, "message": "2833", "line": 6, "column": 8, "nodeType": "2380", "messageId": "2381", "endLine": 6, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2651", "line": 19, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 19, "endColumn": 19}, {"ruleId": "2385", "severity": 1, "message": "2834", "line": 40, "column": 8, "nodeType": "2387", "endLine": 40, "endColumn": 34, "suggestions": "2835"}, {"ruleId": "2385", "severity": 1, "message": "2836", "line": 40, "column": 9, "nodeType": "2837", "endLine": 40, "endColumn": 33}, {"ruleId": "2378", "severity": 1, "message": "2838", "line": 3, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 3, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2771", "line": 4, "column": 42, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 45}, {"ruleId": "2378", "severity": 1, "message": "2431", "line": 7, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 7, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2571", "line": 8, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 8, "endColumn": 19}, {"ruleId": "2378", "severity": 1, "message": "2649", "line": 9, "column": 13, "nodeType": "2380", "messageId": "2381", "endLine": 9, "endColumn": 17}, {"ruleId": "2378", "severity": 1, "message": "2512", "line": 4, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 4, "endColumn": 18}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 2, "column": 23, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 34}, {"ruleId": "2385", "severity": 1, "message": "2839", "line": 49, "column": 8, "nodeType": "2387", "endLine": 49, "endColumn": 16, "suggestions": "2840"}, {"ruleId": "2402", "severity": 1, "message": "2403", "line": 298, "column": 41, "nodeType": "2404", "endLine": 302, "endColumn": 43}, {"ruleId": "2378", "severity": 1, "message": "2408", "line": 2, "column": 10, "nodeType": "2380", "messageId": "2381", "endLine": 2, "endColumn": 21}, {"ruleId": "2378", "severity": 1, "message": "2449", "line": 26, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 26, "endColumn": 26}, {"ruleId": "2378", "severity": 1, "message": "2450", "line": 27, "column": 12, "nodeType": "2380", "messageId": "2381", "endLine": 27, "endColumn": 30}, {"ruleId": "2378", "severity": 1, "message": "2451", "line": 46, "column": 11, "nodeType": "2380", "messageId": "2381", "endLine": 46, "endColumn": 22}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2841"], "'resultAction' is assigned a value but never used.", "'LoadingSpinner' is defined but never used.", "'useRef' is defined but never used.", "'headerHeight' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2842"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'AdminLayout' is defined but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'isAddView' is assigned a value but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'useNavigate' is defined but never used.", "React Hook useEffect has a missing dependency: 'folder'. Either include it or remove the dependency array.", ["2843"], "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'handleClickedTracking' is assigned a value but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'showAddStudent' is assigned a value but never used.", "'setIsFilterView' is defined but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2844"], "'setExam' is defined but never used.", "'errorMsg' is assigned a value but never used.", "'getQuestionAndAnswersByAttemptAPI' is defined but never used.", "'setQuestions' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2845"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2846"], "'totalPages' is assigned a value but never used.", "'processInputForUpdate' is defined but never used.", "'useEffect' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'resetFilters' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2847"], "'setSortOrder' is defined but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2848"], "'LatexRenderer' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2849"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'setClass' is defined but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2850"], "'setDeleteMode' is assigned a value but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2851"], "'result' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2852"], "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'ExamDefaultImage' is defined but never used.", "'ChevronRight' is defined but never used.", "'Bookmark' is defined but never used.", "'CheckCircle' is defined but never used.", "'formatDate' is assigned a value but never used.", "'createdAt' is assigned a value but never used.", "'imageUrl' is assigned a value but never used.", "'isSave' is assigned a value but never used.", "'isDone' is assigned a value but never used.", "'acceptDoExam' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'LogOut' is defined but never used.", "'isModalOpen' is assigned a value but never used.", "'setIsModalOpen' is assigned a value but never used.", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2853"], "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'use' is defined but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'User' is defined but never used.", "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2854"], "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2855"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2856"], "no-useless-escape", "Unnecessary escape character: \\}.", "Literal", "unnecessaryEscape", ["2857", "2858"], "Unnecessary escape character: \\{.", ["2859", "2860"], ["2861", "2862"], ["2863", "2864"], "Unnecessary escape character: \\..", ["2865", "2866"], ["2867", "2868"], ["2869", "2870"], "Unnecessary escape character: \\).", ["2871", "2872"], ["2873", "2874"], ["2875", "2876"], ["2877", "2878"], ["2879", "2880"], ["2881", "2882"], ["2883", "2884"], ["2885", "2886"], ["2887", "2888"], ["2889", "2890"], ["2891", "2892"], "'InputSearch' is defined but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'motion' is defined but never used.", "'AnimatePresence' is defined but never used.", "'fetchUnreadCount' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'isMobile' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2893"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2894"], "React Hook useEffect has a missing dependency: 'classDetail'. Either include it or remove the dependency array.", ["2895"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'ClassImage' is defined but never used.", "'Filter' is defined but never used.", "'Loader' is defined but never used.", "'ChevronDown' is defined but never used.", "'showSortDropdown' is assigned a value but never used.", "'choice' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2896"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2897"], "no-dupe-keys", "Duplicate key 'sort'.", "ObjectExpression", "React Hook useEffect has a duplicate dependency: 'sort'. Either omit it or remove the dependency array.", ["2898"], "'resetClassDetail' is defined but never used.", "React Hook useEffect has a missing dependency: 'classCode'. Either include it or remove the dependency array.", ["2899"], "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2900"], "'startTime' is assigned a value but never used.", "'user' is assigned a value but never used.", "'remainingTimeRef' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["2901"], "'fetchExamRatingStatistics' is defined but never used.", "'saveExamForUser' is defined but never used.", "'rateExamForUser' is defined but never used.", "'setStar' is defined but never used.", "'StarIcon' is defined but never used.", "'FileText' is defined but never used.", "'QrCodeIcon' is defined but never used.", "'Pin' is defined but never used.", "'Send' is defined but never used.", "'Smile' is defined but never used.", "'Share2' is defined but never used.", "'setCurrentPage' is defined but never used.", "'exam' is assigned a value but never used.", "'view' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'loadingRatingStatistics' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleClickHistory', 'handleClickPreviewExam', and 'handleClickRanking'. Either include them or remove the dependency array.", ["2902"], "'useMemo' is defined but never used.", "'PdfViewer' is defined but never used.", "'BarChart3' is defined but never used.", "'formatTime' is defined but never used.", "'BackButton' is assigned a value but never used.", "'StatisticsSection' is assigned a value but never used.", "'codes' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2903"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2904"], "'articles' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2905"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2906"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", ["2907"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2908"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2909"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "Duplicate key 'startTime'.", "'typeOfQuestion' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'wasInError' is assigned a value but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'NavigateTimeButton' is defined but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2910"], "'UserLayout' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "'recentActivities' is assigned a value but never used.", "'systemStatus' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", "'examApi' is defined but never used.", "'setStep' is defined but never used.", "'setExamData' is defined but never used.", "'postExam' is defined but never used.", "'nextStep' is defined but never used.", "'prevStep' is defined but never used.", "'setCreatedExam' is defined but never used.", "'ImageUpload' is defined but never used.", "'UploadPdf' is defined but never used.", "'Eye' is defined but never used.", "'Plus' is defined but never used.", "'Trash2' is defined but never used.", "'Edit' is defined but never used.", "'ImageIcon' is defined but never used.", "'Upload' is defined but never used.", "'resetData' is defined but never used.", ["2911"], "'Pagination' is defined but never used.", "'staff' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'effect'. Either include it or remove the dependency array. If 'effect' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2912"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", "'classifyResult' is assigned a value but never used.", "'setQuestion' is defined but never used.", "'setSelectedIndex' is defined but never used.", "'Info' is defined but never used.", "'splitMarkdownToParts' is defined but never used.", "'examFile' is assigned a value but never used.", "'markDownExam' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isViewAdd'. Either include it or remove the dependency array.", ["2913"], "'ListQuestions' is assigned a value but never used.", "'questionCount' is assigned a value but never used.", "'questionUntil' is defined but never used.", "'useDebouncedEffect' is defined but never used.", "'examImage' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'questions'. Either include them or remove the dependency array.", ["2914"], "'examData' is assigned a value but never used.", "'reorderStatements' is defined but never used.", "'QuestionContent' is defined but never used.", "'prefixTN' is assigned a value but never used.", "'prefixDS' is assigned a value but never used.", "'fixTextResult' is assigned a value but never used.", "'handleTextareaChange' is assigned a value but never used.", "'questionId' is assigned a value but never used.", "'initialPaginationState' is defined but never used.", "'paginationReducers' is defined but never used.", "'initialFilterState' is defined but never used.", "'filterReducers' is defined but never used.", "'examCommentsApi' is defined but never used.", "'examId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'extendedOptions'. Either include it or remove the dependency array.", ["2915"], "'darkMode' is assigned a value but never used.", "'LoadingData' is defined but never used.", "'showEmojiPicker' is assigned a value but never used.", "'handleEmojiClick' is assigned a value but never used.", "'handleSend' is assigned a value but never used.", "'Medal' is defined but never used.", "'getRandomAvatar' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'thinkingMessages.length'. Either include it or remove the dependency array.", ["2916"], ["2917"], "'ReportButton' is defined but never used.", "'saveQuestions' is assigned a value but never used.", "'QuestionImage' is defined but never used.", "'NoTranslate' is defined but never used.", "'imageSize' is assigned a value but never used.", "'QuestionSectionTitle' is defined but never used.", "'EmojiPicker' is defined but never used.", "React Hook useEffect has missing dependencies: 'comment.id' and 'repliesState'. Either include them or remove the dependency array. If 'setReplies' needs the current value of 'comment.id', you can also switch to useReducer instead of useState and read 'comment.id' in the reducer.", ["2918"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "'setView' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleImageFile'. Either include it or remove the dependency array.", ["2919"], {"desc": "2920", "fix": "2921"}, {"desc": "2922", "fix": "2923"}, {"desc": "2924", "fix": "2925"}, {"desc": "2926", "fix": "2927"}, {"desc": "2928", "fix": "2929"}, {"desc": "2930", "fix": "2931"}, {"desc": "2932", "fix": "2933"}, {"desc": "2934", "fix": "2935"}, {"desc": "2936", "fix": "2937"}, {"desc": "2938", "fix": "2939"}, {"desc": "2940", "fix": "2941"}, {"desc": "2942", "fix": "2943"}, {"desc": "2944", "fix": "2945"}, {"desc": "2946", "fix": "2947"}, {"desc": "2948", "fix": "2949"}, {"desc": "2950", "fix": "2951"}, {"messageId": "2952", "fix": "2953", "desc": "2954"}, {"messageId": "2955", "fix": "2956", "desc": "2957"}, {"messageId": "2952", "fix": "2958", "desc": "2954"}, {"messageId": "2955", "fix": "2959", "desc": "2957"}, {"messageId": "2952", "fix": "2960", "desc": "2954"}, {"messageId": "2955", "fix": "2961", "desc": "2957"}, {"messageId": "2952", "fix": "2962", "desc": "2954"}, {"messageId": "2955", "fix": "2963", "desc": "2957"}, {"messageId": "2952", "fix": "2964", "desc": "2954"}, {"messageId": "2955", "fix": "2965", "desc": "2957"}, {"messageId": "2952", "fix": "2966", "desc": "2954"}, {"messageId": "2955", "fix": "2967", "desc": "2957"}, {"messageId": "2952", "fix": "2968", "desc": "2954"}, {"messageId": "2955", "fix": "2969", "desc": "2957"}, {"messageId": "2952", "fix": "2970", "desc": "2954"}, {"messageId": "2955", "fix": "2971", "desc": "2957"}, {"messageId": "2952", "fix": "2972", "desc": "2954"}, {"messageId": "2955", "fix": "2973", "desc": "2957"}, {"messageId": "2952", "fix": "2974", "desc": "2954"}, {"messageId": "2955", "fix": "2975", "desc": "2957"}, {"messageId": "2952", "fix": "2976", "desc": "2954"}, {"messageId": "2955", "fix": "2977", "desc": "2957"}, {"messageId": "2952", "fix": "2978", "desc": "2954"}, {"messageId": "2955", "fix": "2979", "desc": "2957"}, {"messageId": "2952", "fix": "2980", "desc": "2954"}, {"messageId": "2955", "fix": "2981", "desc": "2957"}, {"messageId": "2952", "fix": "2982", "desc": "2954"}, {"messageId": "2955", "fix": "2983", "desc": "2957"}, {"messageId": "2952", "fix": "2984", "desc": "2954"}, {"messageId": "2955", "fix": "2985", "desc": "2957"}, {"messageId": "2952", "fix": "2986", "desc": "2954"}, {"messageId": "2955", "fix": "2987", "desc": "2957"}, {"messageId": "2952", "fix": "2988", "desc": "2954"}, {"messageId": "2955", "fix": "2989", "desc": "2957"}, {"messageId": "2952", "fix": "2990", "desc": "2954"}, {"messageId": "2955", "fix": "2991", "desc": "2957"}, {"desc": "2992", "fix": "2993"}, {"desc": "2994", "fix": "2995"}, {"desc": "2996", "fix": "2997"}, {"desc": "2998", "fix": "2999"}, {"desc": "3000", "fix": "3001"}, {"desc": "3002", "fix": "3003"}, {"desc": "3004", "fix": "3005"}, {"desc": "3006", "fix": "3007"}, {"desc": "3008", "fix": "3009"}, {"desc": "3010", "fix": "3011"}, {"desc": "3012", "fix": "3013"}, {"desc": "3014", "fix": "3015"}, {"desc": "3016", "fix": "3017"}, {"desc": "3018", "fix": "3019"}, {"desc": "3020", "fix": "3021"}, {"desc": "3022", "fix": "3023"}, {"desc": "3024", "fix": "3025"}, {"desc": "3026", "fix": "3027"}, {"desc": "2924", "fix": "3028"}, {"desc": "3029", "fix": "3030"}, {"desc": "3031", "fix": "3032"}, {"desc": "3033", "fix": "3034"}, {"desc": "3035", "fix": "3036"}, {"desc": "3037", "fix": "3038"}, {"desc": "3039", "fix": "3040"}, {"desc": "3041", "fix": "3042"}, {"desc": "3043", "fix": "3044"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "3045", "text": "3046"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "3047", "text": "3048"}, "Update the dependencies array to be: [dispatch, folder]", {"range": "3049", "text": "3050"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "3051", "text": "3052"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "3053", "text": "3054"}, "Update the dependencies array to be: [options, selected, type]", {"range": "3055", "text": "3056"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "3057", "text": "3058"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "3059", "text": "3060"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "3061", "text": "3062"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "3063", "text": "3064"}, "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "3065", "text": "3066"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "3067", "text": "3068"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "3069", "text": "3070"}, "Update the dependencies array to be: [handlePaste]", {"range": "3071", "text": "3072"}, "Update the dependencies array to be: [maxLength]", {"range": "3073", "text": "3074"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "3075", "text": "3076"}, "removeEscape", {"range": "3077", "text": "3078"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3079", "text": "3080"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "3081", "text": "3078"}, {"range": "3082", "text": "3080"}, {"range": "3083", "text": "3078"}, {"range": "3084", "text": "3080"}, {"range": "3085", "text": "3078"}, {"range": "3086", "text": "3080"}, {"range": "3087", "text": "3078"}, {"range": "3088", "text": "3080"}, {"range": "3089", "text": "3078"}, {"range": "3090", "text": "3080"}, {"range": "3091", "text": "3078"}, {"range": "3092", "text": "3080"}, {"range": "3093", "text": "3078"}, {"range": "3094", "text": "3080"}, {"range": "3095", "text": "3078"}, {"range": "3096", "text": "3080"}, {"range": "3097", "text": "3078"}, {"range": "3098", "text": "3080"}, {"range": "3099", "text": "3078"}, {"range": "3100", "text": "3080"}, {"range": "3101", "text": "3078"}, {"range": "3102", "text": "3080"}, {"range": "3103", "text": "3078"}, {"range": "3104", "text": "3080"}, {"range": "3105", "text": "3078"}, {"range": "3106", "text": "3080"}, {"range": "3107", "text": "3078"}, {"range": "3108", "text": "3080"}, {"range": "3109", "text": "3078"}, {"range": "3110", "text": "3080"}, {"range": "3111", "text": "3078"}, {"range": "3112", "text": "3080"}, {"range": "3113", "text": "3078"}, {"range": "3114", "text": "3080"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "3115", "text": "3116"}, "Update the dependencies array to be: [handleFile]", {"range": "3117", "text": "3118"}, "Update the dependencies array to be: [classDetail, dispatch]", {"range": "3119", "text": "3120"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "3121", "text": "3122"}, "Update the dependencies array to be: [codes, dispatch, grade]", {"range": "3123", "text": "3124"}, "Update the dependencies array to be: [dispatch, search, page, sort, typeOfExam, grade, chapter, view, isClassroomExam, year, firstLoad]", {"range": "3125", "text": "3126"}, "Update the dependencies array to be: [lessonId, classDetail, sortedLessons, learningItemId, firstHandled, classCode]", {"range": "3127", "text": "3128"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "3129", "text": "3130"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, isTimeUp, resetDone, handleSubmit]", {"range": "3131", "text": "3132"}, "Update the dependencies array to be: [location.search, dispatch, firstRender, handleClickRanking, handleClickPreviewExam, handleClickHistory]", {"range": "3133", "text": "3134"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "3135", "text": "3136"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "3137", "text": "3138"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "3139", "text": "3140"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "3141", "text": "3142"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "3143", "text": "3144"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "3145", "text": "3146"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "3147", "text": "3148"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "3149", "text": "3150"}, {"range": "3151", "text": "3050"}, "Update the dependencies array to be: [delay, effect]", {"range": "3152", "text": "3153"}, "Update the dependencies array to be: [questionT<PERSON>ontent, correctAnswerTN, questionDS<PERSON>ontent, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", {"range": "3154", "text": "3155"}, "Update the dependencies array to be: [classifyResult, dispatch, questions]", {"range": "3156", "text": "3157"}, "Update the dependencies array to be: [selected, filterOptions, extendedOptions]", {"range": "3158", "text": "3159"}, "Update the dependencies array to be: [aiLoading, thinkingMessages.length]", {"range": "3160", "text": "3161"}, "Update the dependencies array to be: [dispatch, question]", {"range": "3162", "text": "3163"}, "Update the dependencies array to be: [comment.id, repliesState]", {"range": "3164", "text": "3165"}, "Update the dependencies array to be: [handleImageFile, isOpen]", {"range": "3166", "text": "3167"}, [1894, 1910], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [1469, 1479], "[dispatch, folder]", [9548, 9558], "[dispatch, fetchPdfFiles]", [2683, 2705], "[inputValue, dispatch, setSearch]", [669, 671], "[options, selected, type]", [1903, 1921], "[dispatch, fetchQuestions, params]", [2124, 2148], "[codes, question, question.class]", [2394, 2414], "[codes, exam, exam?.class]", [1569, 1614], "[dispatch, search, page, pageSize, sortOrder, classId]", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [2566, 2568], "[handlePaste]", [972, 974], "[maxLength]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [7921, 7922], "", [7921, 7921], "\\", [7923, 7924], [7923, 7923], [8024, 8025], [8024, 8024], [8026, 8027], [8026, 8026], [9835, 9836], [9835, 9835], [10623, 10624], [10623, 10623], [11254, 11255], [11254, 11254], [11256, 11257], [11256, 11256], [11326, 11327], [11326, 11326], [11328, 11329], [11328, 11328], [14248, 14249], [14248, 14248], [15420, 15421], [15420, 15420], [16026, 16027], [16026, 16026], [16028, 16029], [16028, 16028], [16098, 16099], [16098, 16098], [16100, 16101], [16100, 16100], [19010, 19011], [19010, 19010], [19956, 19957], [19956, 19956], [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [14882, 14892], "[classDetail, dispatch]", [4183, 4193], "[dispatch, limit]", [8245, 8259], "[codes, dispatch, grade]", [8578, 8682], "[dispatch, search, page, sort, typeOfExam, grade, chapter, view, isClassroomExam, year, firstLoad]", [5970, 6038], "[lessonId, classDetail, sortedLessons, learningItemId, firstHandled, classCode]", [7974, 7987], "[activeItem?.index, classDetail]", [5971, 6083], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, isTimeUp, resetDone, handleSubmit]", [19570, 19610], "[location.search, dispatch, firstRender, handleClickRanking, handleClickPreviewExam, handleClickHistory]", [6098, 6108], "[dispatch, location.search]", [1654, 1676], "[currentPage, didInit, loadReports]", [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2199, 2238], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [2393, 2403], [511, 527], "[delay, effect]", [23156, 23266], "[question<PERSON><PERSON><PERSON><PERSON>, correctAnswerTN, question<PERSON><PERSON><PERSON>nt, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", [17182, 17198], "[classifyResult, dispatch, questions]", [995, 1020], "[selected, filterOptions, extendedOptions]", [1888, 1899], "[ai<PERSON><PERSON><PERSON>, thinkingMessages.length]", [2019, 2029], "[dispatch, question]", [1560, 1586], "[comment.id, repliesState]", [2113, 2121], "[handleImageFile, isOpen]"]