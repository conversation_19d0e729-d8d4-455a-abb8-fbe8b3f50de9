[{"D:\\ToanThayBee\\frontend\\src\\index.js": "1", "D:\\ToanThayBee\\frontend\\src\\App.js": "2", "D:\\ToanThayBee\\frontend\\src\\reportWebVitals.js": "3", "D:\\ToanThayBee\\frontend\\src\\redux\\store.js": "4", "D:\\ToanThayBee\\frontend\\src\\pages\\LoginPage.jsx": "5", "D:\\ToanThayBee\\frontend\\src\\components\\ProtectedRoute.jsx": "6", "D:\\ToanThayBee\\frontend\\src\\components\\error\\NotificationDisplay.jsx": "7", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\CodeManagement.jsx": "8", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\HomePageManagement.jsx": "9", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticleManagement.jsx": "10", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticlePostPage.jsx": "11", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "12", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\questionManagement.jsx": "13", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "14", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamManagement.jsx": "15", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "16", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "17", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "18", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "19", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "20", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassManagement.jsx": "21", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\LessonManagement.jsx": "22", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "23", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "25", "D:\\ToanThayBee\\frontend\\src\\features\\sidebar\\sidebarSlice.js": "26", "D:\\ToanThayBee\\frontend\\src\\features\\user\\userSlice.js": "27", "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterSlice.js": "28", "D:\\ToanThayBee\\frontend\\src\\features\\question\\questionSlice.js": "29", "D:\\ToanThayBee\\frontend\\src\\features\\auth\\authSlice.js": "30", "D:\\ToanThayBee\\frontend\\src\\features\\state\\stateApiSlice.js": "31", "D:\\ToanThayBee\\frontend\\src\\features\\code\\codeSlice.js": "32", "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examSlice.js": "33", "D:\\ToanThayBee\\frontend\\src\\features\\image\\imageSlice.js": "34", "D:\\ToanThayBee\\frontend\\src\\features\\answer\\answerSlice.js": "35", "D:\\ToanThayBee\\frontend\\src\\features\\attempt\\attemptSlice.js": "36", "D:\\ToanThayBee\\frontend\\src\\features\\class\\classSlice.js": "37", "D:\\ToanThayBee\\frontend\\src\\features\\article\\articleSlice.js": "38", "D:\\ToanThayBee\\frontend\\src\\utils\\sanitizeInput.js": "39", "D:\\ToanThayBee\\frontend\\src\\utils\\validation.js": "40", "D:\\ToanThayBee\\frontend\\src\\features\\achievement\\achievementSlice.js": "41", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForAuthPage.jsx": "42", "D:\\ToanThayBee\\frontend\\src\\components\\button\\GoogleLoginButton.jsx": "43", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingSpinner.jsx": "44", "D:\\ToanThayBee\\frontend\\src\\layouts\\AdminLayout.jsx": "45", "D:\\ToanThayBee\\frontend\\src\\layouts\\AuthLayout.jsx": "46", "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputForAuthPage.jsx": "47", "D:\\ToanThayBee\\frontend\\src\\components\\checkBox\\AuthCheckbox.jsx": "48", "D:\\ToanThayBee\\frontend\\src\\components\\logo\\BeeMathLogo.jsx": "49", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FunctionBarAdmin.jsx": "50", "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "51", "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\AuthDropMenu.jsx": "52", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBarAttemp.jsx": "53", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreDistributionChart.jsx": "54", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddQuestionModal.jsx": "55", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddCodeModal.jsx": "56", "D:\\ToanThayBee\\frontend\\src\\components\\table\\CodeTable.jsx": "57", "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTable.jsx": "58", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ArticleTable.jsx": "59", "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutMultipleImages.jsx": "60", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AdminModal.jsx": "61", "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownEditer.jsx": "62", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\QuestionDetail.jsx": "63", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ExamDetail.jsx": "64", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\PreviewExam.jsx": "65", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ExamTable.jsx": "66", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ClassDetail.jsx": "67", "D:\\ToanThayBee\\frontend\\src\\components\\table\\UserClassTable.jsx": "68", "D:\\ToanThayBee\\frontend\\src\\components\\Footer.jsx": "69", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassTable.jsx": "70", "D:\\ToanThayBee\\frontend\\src\\components\\input\\suggestInputBarAdmin.jsx": "71", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddClassModal.jsx": "72", "D:\\ToanThayBee\\frontend\\src\\components\\image\\LearningItemIcon.jsx": "73", "D:\\ToanThayBee\\frontend\\src\\components\\YouTubePlayer.jsx": "74", "D:\\ToanThayBee\\frontend\\src\\components\\ViewDetail.jsx": "75", "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayoutHome.jsx": "76", "D:\\ToanThayBee\\frontend\\src\\utils\\formatters.js": "77", "D:\\ToanThayBee\\frontend\\src\\components\\image\\SlideShow.jsx": "78", "D:\\ToanThayBee\\frontend\\src\\components\\latex\\RenderLatex.jsx": "79", "D:\\ToanThayBee\\frontend\\src\\components\\NetworkSpeedTest.jsx": "80", "D:\\ToanThayBee\\frontend\\src\\components\\CustomSchedule.jsx": "81", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ExamRegulationModal.jsx": "82", "D:\\ToanThayBee\\frontend\\src\\components\\StudentThoughts.jsx": "83", "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputSearch.jsx": "84", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\JoinClassModal.jsx": "85", "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayout.jsx": "86", "D:\\ToanThayBee\\frontend\\src\\components\\Pagination.jsx": "87", "D:\\ToanThayBee\\frontend\\src\\components\\card\\countDownCard.jsx": "88", "D:\\ToanThayBee\\frontend\\src\\components\\image\\ClassImage.jsx": "89", "D:\\ToanThayBee\\frontend\\src\\components\\ViewPdf.jsx": "90", "D:\\ToanThayBee\\frontend\\src\\utils\\apiHandler.js": "91", "D:\\ToanThayBee\\frontend\\src\\services\\authApi.js": "92", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreSummaryTable.jsx": "93", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "94", "D:\\ToanThayBee\\frontend\\src\\components\\card\\RelatedExamCard.jsx": "95", "D:\\ToanThayBee\\frontend\\src\\components\\card\\ExamCard.jsx": "96", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreBarChart.jsx": "97", "D:\\ToanThayBee\\frontend\\src\\components\\QrCode.jsx": "98", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ScreenButton.jsx": "99", "D:\\ToanThayBee\\frontend\\src\\services\\userApi.js": "100", "D:\\ToanThayBee\\frontend\\src\\services\\questionApi.js": "101", "D:\\ToanThayBee\\frontend\\src\\components\\ViewLearning.jsx": "102", "D:\\ToanThayBee\\frontend\\src\\components\\Schedule.jsx": "103", "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderDoExamPage.jsx": "104", "D:\\ToanThayBee\\frontend\\src\\components\\achievement\\AchievementSection.jsx": "105", "D:\\ToanThayBee\\frontend\\src\\components\\article\\Breadcrumb.jsx": "106", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\FilterExamSidebar.jsx": "107", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleList.jsx": "108", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleBreadcrumb.jsx": "109", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleSidebar.jsx": "110", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleRelatedSidebar.jsx": "111", "D:\\ToanThayBee\\frontend\\src\\components\\article\\SearchBar.jsx": "112", "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementStatTable.jsx": "113", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleHeader.jsx": "114", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleContent.jsx": "115", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "116", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementImageModal.jsx": "117", "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementCategoryTable.jsx": "118", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementStatModal.jsx": "119", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementImageModal.jsx": "120", "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementImageTable.jsx": "121", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "122", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddStudentModal.jsx": "123", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementStatModal.jsx": "124", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\UserDetail.jsx": "125", "D:\\ToanThayBee\\frontend\\src\\services\\codeApi.js": "126", "D:\\ToanThayBee\\frontend\\src\\services\\imageApi.js": "127", "D:\\ToanThayBee\\frontend\\src\\components\\table\\userTable.jsx": "128", "D:\\ToanThayBee\\frontend\\src\\services\\answerApi.js": "129", "D:\\ToanThayBee\\frontend\\src\\services\\articleApi.js": "130", "D:\\ToanThayBee\\frontend\\src\\services\\examApi.js": "131", "D:\\ToanThayBee\\frontend\\src\\services\\achievementApi.js": "132", "D:\\ToanThayBee\\frontend\\src\\components\\pagination\\Pagination.jsx": "133", "D:\\ToanThayBee\\frontend\\src\\services\\attemptApi.js": "134", "D:\\ToanThayBee\\frontend\\src\\services\\classApi.js": "135", "D:\\ToanThayBee\\frontend\\src\\utils\\excelExport.js": "136", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\AdminSidebar.jsx": "137", "D:\\ToanThayBee\\frontend\\src\\components\\ParticlesBackground.jsx": "138", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "139", "D:\\ToanThayBee\\frontend\\src\\components\\image\\UploadImage.jsx": "140", "D:\\ToanThayBee\\frontend\\src\\components\\table\\StatementTableRow.jsx": "141", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ChangeDescriptionCode.jsx": "142", "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTableRow.jsx": "143", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmDeleteModal.jsx": "144", "D:\\ToanThayBee\\frontend\\src\\components\\table\\TooltipTd.jsx": "145", "D:\\ToanThayBee\\frontend\\src\\components\\UploadPdf.jsx": "146", "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutImgae.jsx": "147", "D:\\ToanThayBee\\frontend\\src\\components\\detail\\DetailTr.jsx": "148", "D:\\ToanThayBee\\frontend\\src\\utils\\question\\questionUtils.js": "149", "D:\\ToanThayBee\\frontend\\src\\components\\ScheduleModal.jsx": "150", "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderHome.jsx": "151", "D:\\ToanThayBee\\frontend\\src\\components\\header\\Header.jsx": "152", "D:\\ToanThayBee\\frontend\\src\\services\\api.js": "153", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleCard.jsx": "154", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ChapterFilters.jsx": "155", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TickSideBar.jsx": "156", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ClassFilters.jsx": "157", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmModal.jsx": "158", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ActiveFilters.jsx": "159", "D:\\ToanThayBee\\frontend\\src\\components\\article\\CategoryFilters.jsx": "160", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\UserSidebar.jsx": "161", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\HeaderSidebar.jsx": "162", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\StudentCardModal.jsx": "163", "D:\\ToanThayBee\\frontend\\src\\components\\header\\ChoiceHeader.jsx": "164", "D:\\ToanThayBee\\frontend\\src\\services\\responseInterceptor.js": "165", "D:\\ToanThayBee\\frontend\\src\\services\\requestInterceptor.js": "166", "D:\\ToanThayBee\\frontend\\src\\components\\image\\AvatarUploader.jsx": "167", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\OverViewPage.jsx": "168", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassDetailPage.jsx": "169", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\Home.jsx": "170", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassUserPage.jsx": "171", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\PracticePage.jsx": "172", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\LearningPage.jsx": "173", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\DoExamPage.jsx": "174", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ExamDetail.jsx": "175", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ScorePage.jsx": "176", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticleListPage.jsx": "177", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticlePage.jsx": "178", "D:\\ToanThayBee\\frontend\\src\\utils\\fullscreenUtils.js": "179", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\QuestionSection.jsx": "180", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ThemeToggleButton.jsx": "181", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TimeDisplay.jsx": "182", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SizeSlider.jsx": "183", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SettingsButton.jsx": "184", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ViewModeToggle.jsx": "185", "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "186", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\SpinnerDemo.jsx": "187", "D:\\ToanThayBee\\frontend\\src\\features\\questionReport\\questionReportSlice.js": "188", "D:\\ToanThayBee\\frontend\\src\\services\\questionReportApi.js": "189", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ReportButton.jsx": "190", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ReportQuestionModal.jsx": "191", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\QuestionReportManagement.jsx": "192", "D:\\ToanThayBee\\frontend\\src\\components\\utils\\NoTranslate.jsx": "193", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "194", "D:\\ToanThayBee\\frontend\\src\\features\\notification\\notificationSlice.js": "195", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "196", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "197", "D:\\ToanThayBee\\frontend\\src\\components\\breadcrumb\\Breadcrumb.jsx": "198", "D:\\ToanThayBee\\frontend\\src\\components\\article\\ModernArticleSidebar.jsx": "199", "D:\\ToanThayBee\\frontend\\src\\utils\\cacheManager.js": "200", "D:\\ToanThayBee\\frontend\\src\\services\\notificationApi.js": "201", "D:\\ToanThayBee\\frontend\\src\\components\\notification\\NotificationPanel.jsx": "202", "D:\\ToanThayBee\\frontend\\src\\features\\tuition\\tuitionSlice.js": "203", "D:\\ToanThayBee\\frontend\\src\\services\\tuitionApi.js": "204", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "205", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "206", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "207", "D:\\ToanThayBee\\frontend\\src\\components\\ClassSearchInput.jsx": "208", "D:\\ToanThayBee\\frontend\\src\\components\\UserSearchInput.jsx": "209", "D:\\ToanThayBee\\frontend\\src\\components\\PaymentModal.jsx": "210", "D:\\ToanThayBee\\frontend\\src\\components\\MultiClassSelector.jsx": "211", "D:\\ToanThayBee\\frontend\\src\\features\\attendance\\attendanceSlice.js": "212", "D:\\ToanThayBee\\frontend\\src\\services\\attendanceApi.js": "213", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\AttendancePage.jsx": "214", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "215", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "216", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "217", "D:\\ToanThayBee\\frontend\\src\\components\\attendance\\AttendanceCard.jsx": "218", "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\lessonSlice.js": "219", "D:\\ToanThayBee\\frontend\\src\\services\\lessonApi.js": "220", "D:\\ToanThayBee\\frontend\\src\\components\\team\\TeamSection.jsx": "221", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "222", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "223", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "224", "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBar.jsx": "225", "D:\\ToanThayBee\\frontend\\src\\components\\banner\\ClassBanner.jsx": "226", "D:\\ToanThayBee\\frontend\\src\\layouts\\ClassAdminLayout.jsx": "227", "D:\\ToanThayBee\\frontend\\src\\utils\\maintenanceUtils.js": "228", "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceCleaner.jsx": "229", "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceWrapper.jsx": "230", "D:\\ToanThayBee\\frontend\\src\\config\\maintenance.js": "231", "D:\\ToanThayBee\\frontend\\src\\pages\\MaintenancePage.jsx": "232", "D:\\ToanThayBee\\frontend\\src\\components\\ScrollToTop.jsx": "233", "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownPreview.jsx": "234", "D:\\ToanThayBee\\frontend\\src\\layouts\\UserAdminLayout.jsx": "235", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\UserClassManagement.jsx": "236", "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassOfUserTable.jsx": "237", "D:\\ToanThayBee\\frontend\\src\\features\\learningItem\\learningItemSlice.js": "238", "D:\\ToanThayBee\\frontend\\src\\services\\learningItemApi.js": "239", "D:\\ToanThayBee\\frontend\\src\\features\\doExam\\doExamSlice.js": "240", "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterReducer.js": "241", "D:\\ToanThayBee\\frontend\\src\\features\\pagination\\paginationReducer.js": "242", "D:\\ToanThayBee\\frontend\\src\\services\\doExamApi.js": "243", "D:\\ToanThayBee\\frontend\\src\\layouts\\404NotFound.jsx": "244", "D:\\ToanThayBee\\frontend\\src\\components\\input\\CustomSearchInput.jsx": "245", "D:\\ToanThayBee\\frontend\\src\\components\\UnpaidTuitionModal.jsx": "246", "D:\\ToanThayBee\\frontend\\src\\layouts\\ExamAdminLayout.jsx": "247", "D:\\ToanThayBee\\frontend\\src\\components\\table\\TotalComponent.jsx": "248", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForUserPage.jsx": "249", "D:\\ToanThayBee\\frontend\\src\\features\\sheet\\sheetSlice.js": "250", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "251", "D:\\ToanThayBee\\frontend\\src\\services\\sheetApi.js": "252", "D:\\ToanThayBee\\frontend\\src\\components\\ExamSearchInput.jsx": "253", "D:\\ToanThayBee\\frontend\\src\\components\\MultiLessonSelector.jsx": "254", "D:\\ToanThayBee\\frontend\\src\\components\\LessonSearchInput.jsx": "255", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "256", "D:\\ToanThayBee\\frontend\\src\\features\\calendar\\calendarSlice.js": "257", "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\index.js": "258", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\CalenderMonth.jsx": "259", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\DayView.jsx": "260", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\MonthView.jsx": "261", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\SidebarCalender.jsx": "262", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\WeekView.jsx": "263", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\TimeSlots.jsx": "264", "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\NavigateTimeButton.jsx": "265", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "266", "D:\\ToanThayBee\\frontend\\src\\constants\\UserType.js": "267", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "268", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "269", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "270", "D:\\ToanThayBee\\frontend\\src\\services\\ocrExamApi.js": "271", "D:\\ToanThayBee\\frontend\\src\\components\\table\\TableAdmin.jsx": "272", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingData.jsx": "273", "D:\\ToanThayBee\\frontend\\src\\services\\apin8n.js": "274", "D:\\ToanThayBee\\frontend\\src\\utils\\setupKatexWarningFilter.js": "275", "D:\\ToanThayBee\\frontend\\src\\features\\dashboard\\dashboardSlice.js": "276", "D:\\ToanThayBee\\frontend\\src\\features\\questionsExam\\questionsExamSlice.js": "277", "D:\\ToanThayBee\\frontend\\src\\features\\addExam\\addExamSlice.js": "278", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\AddExamAdmin.jsx": "279", "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StaffManagement.jsx": "280", "D:\\ToanThayBee\\frontend\\src\\services\\dashboardApi.js": "281", "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddImagesModal.jsx": "282", "D:\\ToanThayBee\\frontend\\src\\hooks\\useDebouncedEffect.js": "283", "D:\\ToanThayBee\\frontend\\src\\components\\layout\\AdminLayout.jsx": "284", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\RightContent.jsx": "285", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\LeftContent.jsx": "286", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\LeftContent.jsx": "287", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\RightContent.jsx": "288", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionView.jsx": "289", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\SolutionEditor.jsx": "290", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\NavigateBar.jsx": "291", "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\CompactStepHeader.jsx": "292", "D:\\ToanThayBee\\frontend\\src\\components\\input\\TextArea.jsx": "293", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\ImageView.jsx": "294", "D:\\ToanThayBee\\frontend\\src\\components\\image\\ImageDropZone.jsx": "295", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx": "296", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionContent.jsx": "297", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx": "298", "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx": "299", "D:\\ToanThayBee\\frontend\\src\\pages\\user\\question\\QuestionPage.jsx": "300", "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examDetailSlice.js": "301", "D:\\ToanThayBee\\frontend\\src\\features\\practice\\practiceSlice.js": "302", "D:\\ToanThayBee\\frontend\\src\\features\\scorePage\\scorePageSlice.js": "303", "D:\\ToanThayBee\\frontend\\src\\features\\ai\\aiSlice.js": "304", "D:\\ToanThayBee\\frontend\\src\\features\\comments\\ExamCommentsSlice.js": "305", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\BatteryLoading.jsx": "306", "D:\\ToanThayBee\\frontend\\src\\components\\button\\ActionButton.jsx": "307", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingText.jsx": "308", "D:\\ToanThayBee\\frontend\\src\\utils\\codeUtils.js": "309", "D:\\ToanThayBee\\frontend\\src\\components\\filter\\SortBar.jsx": "310", "D:\\ToanThayBee\\frontend\\src\\components\\header\\ExamOverviewHeader.jsx": "311", "D:\\ToanThayBee\\frontend\\src\\components\\header\\LearningHeader.jsx": "312", "D:\\ToanThayBee\\frontend\\src\\components\\filter\\FilterBar.jsx": "313", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamContent.jsx": "314", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamSideBar.jsx": "315", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ModalSubmitExam.jsx": "316", "D:\\ToanThayBee\\frontend\\src\\components\\Schedule1.jsx": "317", "D:\\ToanThayBee\\frontend\\src\\utils\\shareUntil.js": "318", "D:\\ToanThayBee\\frontend\\src\\components\\common\\OutsideClickWrapper.jsx": "319", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentSection.jsx": "320", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\RankingView.jsx": "321", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\PreviewView.jsx": "322", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\HistoryView.jsx": "323", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AiChatWidget.jsx": "324", "D:\\ToanThayBee\\frontend\\src\\services\\examCommentsApi.js": "325", "D:\\ToanThayBee\\frontend\\src\\services\\studentExamApi.js": "326", "D:\\ToanThayBee\\frontend\\src\\services\\aiApi.js": "327", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\MultipleChoiceQuestion.jsx": "328", "D:\\ToanThayBee\\frontend\\src\\components\\header\\ButtonHeader.jsx": "329", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ShortAnswerQuestion.jsx": "330", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\LoadingQuestions.jsx": "331", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionSectionTitle.jsx": "332", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ProgressBar.jsx": "333", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionCounter.jsx": "334", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\SubmitButton.jsx": "335", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\TrueFalseQuestion.jsx": "336", "D:\\ToanThayBee\\frontend\\src\\components\\StarRating.jsx": "337", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\LoadingCommentItem.jsx": "338", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentInput.jsx": "339", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentItem.jsx": "340", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\QuestionDropdown.jsx": "341", "D:\\ToanThayBee\\frontend\\src\\components\\loading\\OnlineLoading.jsx": "342", "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\UserInfoPanel.jsx": "343", "D:\\ToanThayBee\\frontend\\src\\components\\common\\WrapperWithTooltip.jsx": "344", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionContent.jsx": "345", "D:\\ToanThayBee\\frontend\\src\\components\\comment\\EmojiPicker.jsx": "346", "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionImage.jsx": "347", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AIWidget.jsx": "348", "D:\\ToanThayBee\\frontend\\src\\components\\ai\\FloatingAIWidget.jsx": "349"}, {"size": 837, "mtime": 1748831088160, "results": "350", "hashOfConfig": "351"}, {"size": 12232, "mtime": 1754037662792, "results": "352", "hashOfConfig": "351"}, {"size": 362, "mtime": 1740442931001, "results": "353", "hashOfConfig": "351"}, {"size": 3269, "mtime": 1754037662824, "results": "354", "hashOfConfig": "351"}, {"size": 7439, "mtime": 1752528418123, "results": "355", "hashOfConfig": "351"}, {"size": 1169, "mtime": 1753875510513, "results": "356", "hashOfConfig": "351"}, {"size": 7222, "mtime": 1753875510527, "results": "357", "hashOfConfig": "351"}, {"size": 2152, "mtime": 1751286250564, "results": "358", "hashOfConfig": "351"}, {"size": 11683, "mtime": 1748233656474, "results": "359", "hashOfConfig": "351"}, {"size": 1271, "mtime": 1744200312845, "results": "360", "hashOfConfig": "351"}, {"size": 21516, "mtime": 1751879443271, "results": "361", "hashOfConfig": "351"}, {"size": 350, "mtime": 1750332992366, "results": "362", "hashOfConfig": "351"}, {"size": 1979, "mtime": 1750250347447, "results": "363", "hashOfConfig": "351"}, {"size": 551, "mtime": 1742179063192, "results": "364", "hashOfConfig": "351"}, {"size": 2397, "mtime": 1752528418131, "results": "365", "hashOfConfig": "351"}, {"size": 19813, "mtime": 1750332992382, "results": "366", "hashOfConfig": "351"}, {"size": 2949, "mtime": 1754042630198, "results": "367", "hashOfConfig": "351"}, {"size": 1898, "mtime": 1750332992366, "results": "368", "hashOfConfig": "351"}, {"size": 9223, "mtime": 1751794342083, "results": "369", "hashOfConfig": "351"}, {"size": 275, "mtime": 1748225026676, "results": "370", "hashOfConfig": "351"}, {"size": 1739, "mtime": 1750250347447, "results": "371", "hashOfConfig": "351"}, {"size": 45070, "mtime": 1752565597538, "results": "372", "hashOfConfig": "351"}, {"size": 7884, "mtime": 1747360179971, "results": "373", "hashOfConfig": "351"}, {"size": 3341, "mtime": 1754305381617, "results": "374", "hashOfConfig": "351"}, {"size": 348, "mtime": 1749107127996, "results": "375", "hashOfConfig": "351"}, {"size": 733, "mtime": 1751794342077, "results": "376", "hashOfConfig": "351"}, {"size": 7352, "mtime": 1752528418123, "results": "377", "hashOfConfig": "351"}, {"size": 3345, "mtime": 1748243232604, "results": "378", "hashOfConfig": "351"}, {"size": 9913, "mtime": 1753875510541, "results": "379", "hashOfConfig": "351"}, {"size": 10253, "mtime": 1753875510536, "results": "380", "hashOfConfig": "351"}, {"size": 1380, "mtime": 1742271817436, "results": "381", "hashOfConfig": "351"}, {"size": 3523, "mtime": 1751794342076, "results": "382", "hashOfConfig": "351"}, {"size": 13809, "mtime": 1753875510541, "results": "383", "hashOfConfig": "351"}, {"size": 7895, "mtime": 1754037662810, "results": "384", "hashOfConfig": "351"}, {"size": 1717, "mtime": 1753875510536, "results": "385", "hashOfConfig": "351"}, {"size": 5924, "mtime": 1753875510536, "results": "386", "hashOfConfig": "351"}, {"size": 18524, "mtime": 1753875510541, "results": "387", "hashOfConfig": "351"}, {"size": 5150, "mtime": 1748424127072, "results": "388", "hashOfConfig": "351"}, {"size": 1337, "mtime": 1747742780655, "results": "389", "hashOfConfig": "351"}, {"size": 2480, "mtime": 1747742780655, "results": "390", "hashOfConfig": "351"}, {"size": 11766, "mtime": 1745568049769, "results": "391", "hashOfConfig": "351"}, {"size": 1339, "mtime": 1751437201798, "results": "392", "hashOfConfig": "351"}, {"size": 690, "mtime": 1740526054766, "results": "393", "hashOfConfig": "351"}, {"size": 1151, "mtime": 1750250347422, "results": "394", "hashOfConfig": "351"}, {"size": 673, "mtime": 1742650835143, "results": "395", "hashOfConfig": "351"}, {"size": 1495, "mtime": 1751434984402, "results": "396", "hashOfConfig": "351"}, {"size": 2276, "mtime": 1751437363838, "results": "397", "hashOfConfig": "351"}, {"size": 1228, "mtime": 1751434984402, "results": "398", "hashOfConfig": "351"}, {"size": 635, "mtime": 1744073048907, "results": "399", "hashOfConfig": "351"}, {"size": 9670, "mtime": 1752528418099, "results": "400", "hashOfConfig": "351"}, {"size": 3255, "mtime": 1752528418099, "results": "401", "hashOfConfig": "351"}, {"size": 3200, "mtime": 1743313304069, "results": "402", "hashOfConfig": "351"}, {"size": 5637, "mtime": 1747360179930, "results": "403", "hashOfConfig": "351"}, {"size": 1734, "mtime": 1743261282404, "results": "404", "hashOfConfig": "351"}, {"size": 28691, "mtime": 1744972102091, "results": "405", "hashOfConfig": "351"}, {"size": 5752, "mtime": 1751286250564, "results": "406", "hashOfConfig": "351"}, {"size": 4288, "mtime": 1752528418109, "results": "407", "hashOfConfig": "351"}, {"size": 10068, "mtime": 1752528418115, "results": "408", "hashOfConfig": "351"}, {"size": 7391, "mtime": 1752528418109, "results": "409", "hashOfConfig": "351"}, {"size": 7401, "mtime": 1747360179945, "results": "410", "hashOfConfig": "351"}, {"size": 1709, "mtime": 1752528418106, "results": "411", "hashOfConfig": "351"}, {"size": 1574, "mtime": 1744211538547, "results": "412", "hashOfConfig": "351"}, {"size": 28398, "mtime": 1749474339456, "results": "413", "hashOfConfig": "351"}, {"size": 17782, "mtime": 1753875510526, "results": "414", "hashOfConfig": "351"}, {"size": 23277, "mtime": 1754301028749, "results": "415", "hashOfConfig": "351"}, {"size": 6857, "mtime": 1752528418109, "results": "416", "hashOfConfig": "351"}, {"size": 17584, "mtime": 1750768842399, "results": "417", "hashOfConfig": "351"}, {"size": 8616, "mtime": 1752528418115, "results": "418", "hashOfConfig": "351"}, {"size": 10256, "mtime": 1744252686164, "results": "419", "hashOfConfig": "351"}, {"size": 5905, "mtime": 1752528418109, "results": "420", "hashOfConfig": "351"}, {"size": 3767, "mtime": 1752528418099, "results": "421", "hashOfConfig": "351"}, {"size": 19936, "mtime": 1748952128766, "results": "422", "hashOfConfig": "351"}, {"size": 645, "mtime": 1753875510532, "results": "423", "hashOfConfig": "351"}, {"size": 1815, "mtime": 1753875510517, "results": "424", "hashOfConfig": "351"}, {"size": 31136, "mtime": 1751794342051, "results": "425", "hashOfConfig": "351"}, {"size": 1205, "mtime": 1748942182512, "results": "426", "hashOfConfig": "351"}, {"size": 3670, "mtime": 1753875510551, "results": "427", "hashOfConfig": "351"}, {"size": 7937, "mtime": 1753875510533, "results": "428", "hashOfConfig": "351"}, {"size": 4276, "mtime": 1751879443263, "results": "429", "hashOfConfig": "351"}, {"size": 3146, "mtime": 1743125827996, "results": "430", "hashOfConfig": "351"}, {"size": 6050, "mtime": 1748239151420, "results": "431", "hashOfConfig": "351"}, {"size": 5818, "mtime": 1753875510535, "results": "432", "hashOfConfig": "351"}, {"size": 949, "mtime": 1747360179920, "results": "433", "hashOfConfig": "351"}, {"size": 2787, "mtime": 1750250347422, "results": "434", "hashOfConfig": "351"}, {"size": 4087, "mtime": 1753875510536, "results": "435", "hashOfConfig": "351"}, {"size": 1876, "mtime": 1754037662815, "results": "436", "hashOfConfig": "351"}, {"size": 3327, "mtime": 1753875510513, "results": "437", "hashOfConfig": "351"}, {"size": 2201, "mtime": 1744079807330, "results": "438", "hashOfConfig": "351"}, {"size": 6034, "mtime": 1748424127072, "results": "439", "hashOfConfig": "351"}, {"size": 841, "mtime": 1748949217083, "results": "440", "hashOfConfig": "351"}, {"size": 3005, "mtime": 1750869775159, "results": "441", "hashOfConfig": "351"}, {"size": 935, "mtime": 1745370017395, "results": "442", "hashOfConfig": "351"}, {"size": 1990, "mtime": 1743040478626, "results": "443", "hashOfConfig": "351"}, {"size": 2380, "mtime": 1743040371503, "results": "444", "hashOfConfig": "351"}, {"size": 5565, "mtime": 1745808607396, "results": "445", "hashOfConfig": "351"}, {"size": 5867, "mtime": 1753875510521, "results": "446", "hashOfConfig": "351"}, {"size": 2295, "mtime": 1747360179932, "results": "447", "hashOfConfig": "351"}, {"size": 313, "mtime": 1748229848432, "results": "448", "hashOfConfig": "351"}, {"size": 2985, "mtime": 1753875510521, "results": "449", "hashOfConfig": "351"}, {"size": 1946, "mtime": 1752528418138, "results": "450", "hashOfConfig": "351"}, {"size": 4165, "mtime": 1753875510551, "results": "451", "hashOfConfig": "351"}, {"size": 50713, "mtime": 1753875510516, "results": "452", "hashOfConfig": "351"}, {"size": 31011, "mtime": 1751004342884, "results": "453", "hashOfConfig": "351"}, {"size": 10898, "mtime": 1753875510532, "results": "454", "hashOfConfig": "351"}, {"size": 13822, "mtime": 1748237193361, "results": "455", "hashOfConfig": "351"}, {"size": 993, "mtime": 1747360179920, "results": "456", "hashOfConfig": "351"}, {"size": 10634, "mtime": 1745568049769, "results": "457", "hashOfConfig": "458"}, {"size": 2193, "mtime": 1747360179920, "results": "459", "hashOfConfig": "351"}, {"size": 1359, "mtime": 1747360179920, "results": "460", "hashOfConfig": "351"}, {"size": 2967, "mtime": 1744212069162, "results": "461", "hashOfConfig": "351"}, {"size": 4872, "mtime": 1747360179920, "results": "462", "hashOfConfig": "351"}, {"size": 902, "mtime": 1747360179930, "results": "463", "hashOfConfig": "351"}, {"size": 7151, "mtime": 1747360179961, "results": "464", "hashOfConfig": "351"}, {"size": 2245, "mtime": 1744211990050, "results": "465", "hashOfConfig": "351"}, {"size": 1641, "mtime": 1751879443263, "results": "466", "hashOfConfig": "351"}, {"size": 8511, "mtime": 1745582519057, "results": "467", "hashOfConfig": "351"}, {"size": 8211, "mtime": 1745568049750, "results": "468", "hashOfConfig": "351"}, {"size": 7421, "mtime": 1747360179955, "results": "469", "hashOfConfig": "351"}, {"size": 7894, "mtime": 1745568049750, "results": "470", "hashOfConfig": "351"}, {"size": 9990, "mtime": 1747360179947, "results": "471", "hashOfConfig": "351"}, {"size": 8563, "mtime": 1747360179961, "results": "472", "hashOfConfig": "351"}, {"size": 8784, "mtime": 1747360179947, "results": "473", "hashOfConfig": "351"}, {"size": 14565, "mtime": 1750841346217, "results": "474", "hashOfConfig": "351"}, {"size": 8094, "mtime": 1747360179947, "results": "475", "hashOfConfig": "351"}, {"size": 19265, "mtime": 1751794342061, "results": "476", "hashOfConfig": "351"}, {"size": 822, "mtime": 1751286250564, "results": "477", "hashOfConfig": "351"}, {"size": 2634, "mtime": 1754037662827, "results": "478", "hashOfConfig": "351"}, {"size": 6118, "mtime": 1752528418115, "results": "479", "hashOfConfig": "351"}, {"size": 297, "mtime": 1742720286594, "results": "480", "hashOfConfig": "351"}, {"size": 826, "mtime": 1748424127082, "results": "481", "hashOfConfig": "351"}, {"size": 3408, "mtime": 1754037662827, "results": "482", "hashOfConfig": "351"}, {"size": 4800, "mtime": 1745568049769, "results": "483", "hashOfConfig": "351"}, {"size": 4921, "mtime": 1747360179955, "results": "484", "hashOfConfig": "351"}, {"size": 1158, "mtime": 1753875510551, "results": "485", "hashOfConfig": "351"}, {"size": 4755, "mtime": 1751434984402, "results": "486", "hashOfConfig": "351"}, {"size": 10728, "mtime": 1750250347464, "results": "487", "hashOfConfig": "351"}, {"size": 9098, "mtime": 1753875510536, "results": "488", "hashOfConfig": "351"}, {"size": 411, "mtime": 1743206204710, "results": "489", "hashOfConfig": "351"}, {"size": 503, "mtime": 1744085691629, "results": "490", "hashOfConfig": "351"}, {"size": 8372, "mtime": 1752528418099, "results": "491", "hashOfConfig": "351"}, {"size": 3094, "mtime": 1742422820538, "results": "492", "hashOfConfig": "351"}, {"size": 2290, "mtime": 1744034601299, "results": "493", "hashOfConfig": "351"}, {"size": 2166, "mtime": 1742422800227, "results": "494", "hashOfConfig": "351"}, {"size": 1219, "mtime": 1747742780638, "results": "495", "hashOfConfig": "351"}, {"size": 2003, "mtime": 1742005297939, "results": "496", "hashOfConfig": "351"}, {"size": 10223, "mtime": 1754037662801, "results": "497", "hashOfConfig": "351"}, {"size": 7876, "mtime": 1747360179944, "results": "498", "hashOfConfig": "351"}, {"size": 4922, "mtime": 1748333524172, "results": "499", "hashOfConfig": "351"}, {"size": 24147, "mtime": 1754045901466, "results": "500", "hashOfConfig": "351"}, {"size": 20555, "mtime": 1748232252877, "results": "501", "hashOfConfig": "351"}, {"size": 12148, "mtime": 1748236140592, "results": "502", "hashOfConfig": "351"}, {"size": 28555, "mtime": 1753875510531, "results": "503", "hashOfConfig": "351"}, {"size": 394, "mtime": 1754219282862, "results": "504", "hashOfConfig": "351"}, {"size": 1885, "mtime": 1747360179920, "results": "505", "hashOfConfig": "351"}, {"size": 3182, "mtime": 1747360179920, "results": "506", "hashOfConfig": "351"}, {"size": 1337, "mtime": 1743870941098, "results": "507", "hashOfConfig": "458"}, {"size": 2938, "mtime": 1747360179930, "results": "508", "hashOfConfig": "351"}, {"size": 1812, "mtime": 1751879443263, "results": "509", "hashOfConfig": "351"}, {"size": 5412, "mtime": 1747360179920, "results": "510", "hashOfConfig": "351"}, {"size": 2928, "mtime": 1747360179920, "results": "511", "hashOfConfig": "351"}, {"size": 6380, "mtime": 1747381941763, "results": "512", "hashOfConfig": "351"}, {"size": 1576, "mtime": 1751799058328, "results": "513", "hashOfConfig": "351"}, {"size": 11569, "mtime": 1753875510536, "results": "514", "hashOfConfig": "351"}, {"size": 2600, "mtime": 1748846797546, "results": "515", "hashOfConfig": "351"}, {"size": 4099, "mtime": 1754218166559, "results": "516", "hashOfConfig": "351"}, {"size": 1345, "mtime": 1750250347462, "results": "517", "hashOfConfig": "351"}, {"size": 3297, "mtime": 1743823876255, "results": "518", "hashOfConfig": "351"}, {"size": 15741, "mtime": 1753875510548, "results": "519", "hashOfConfig": "351"}, {"size": 17237, "mtime": 1753875510541, "results": "520", "hashOfConfig": "351"}, {"size": 70430, "mtime": 1748942168397, "results": "521", "hashOfConfig": "351"}, {"size": 15829, "mtime": 1753875510546, "results": "522", "hashOfConfig": "351"}, {"size": 12968, "mtime": 1753875510548, "results": "523", "hashOfConfig": "351"}, {"size": 34299, "mtime": 1753875510546, "results": "524", "hashOfConfig": "351"}, {"size": 13626, "mtime": 1753875510548, "results": "525", "hashOfConfig": "351"}, {"size": 21291, "mtime": 1754037662819, "results": "526", "hashOfConfig": "351"}, {"size": 41124, "mtime": 1754219652509, "results": "527", "hashOfConfig": "351"}, {"size": 16767, "mtime": 1748846663294, "results": "528", "hashOfConfig": "351"}, {"size": 8060, "mtime": 1747360179971, "results": "529", "hashOfConfig": "351"}, {"size": 3253, "mtime": 1745808607428, "results": "530", "hashOfConfig": "351"}, {"size": 2298, "mtime": 1750250347428, "results": "531", "hashOfConfig": "351"}, {"size": 848, "mtime": 1745808607413, "results": "532", "hashOfConfig": "351"}, {"size": 1176, "mtime": 1753875510536, "results": "533", "hashOfConfig": "351"}, {"size": 1437, "mtime": 1753875510536, "results": "534", "hashOfConfig": "351"}, {"size": 3467, "mtime": 1745808607408, "results": "535", "hashOfConfig": "351"}, {"size": 1102, "mtime": 1753875510536, "results": "536", "hashOfConfig": "351"}, {"size": 2801, "mtime": 1750250347428, "results": "537", "hashOfConfig": "351"}, {"size": 7136, "mtime": 1747360179947, "results": "538", "hashOfConfig": "351"}, {"size": 2129, "mtime": 1745923751544, "results": "539", "hashOfConfig": "351"}, {"size": 1184, "mtime": 1745923490808, "results": "540", "hashOfConfig": "351"}, {"size": 924, "mtime": 1745921871581, "results": "541", "hashOfConfig": "351"}, {"size": 13380, "mtime": 1754037662804, "results": "542", "hashOfConfig": "351"}, {"size": 20572, "mtime": 1752528418131, "results": "543", "hashOfConfig": "351"}, {"size": 1135, "mtime": 1748333524174, "results": "544", "hashOfConfig": "351"}, {"size": 12544, "mtime": 1748225026681, "results": "545", "hashOfConfig": "351"}, {"size": 10859, "mtime": 1748225026673, "results": "546", "hashOfConfig": "351"}, {"size": 7652, "mtime": 1747360179932, "results": "547", "hashOfConfig": "351"}, {"size": 6276, "mtime": 1747360179932, "results": "548", "hashOfConfig": "351"}, {"size": 3589, "mtime": 1747360179932, "results": "549", "hashOfConfig": "351"}, {"size": 16791, "mtime": 1748425117389, "results": "550", "hashOfConfig": "351"}, {"size": 2123, "mtime": 1747360179986, "results": "551", "hashOfConfig": "351"}, {"size": 4129, "mtime": 1747360179986, "results": "552", "hashOfConfig": "351"}, {"size": 11963, "mtime": 1750332992366, "results": "553", "hashOfConfig": "351"}, {"size": 12245, "mtime": 1750339362394, "results": "554", "hashOfConfig": "351"}, {"size": 5589, "mtime": 1750250347464, "results": "555", "hashOfConfig": "351"}, {"size": 73138, "mtime": 1750420257824, "results": "556", "hashOfConfig": "351"}, {"size": 13612, "mtime": 1750250347459, "results": "557", "hashOfConfig": "351"}, {"size": 10116, "mtime": 1750250347459, "results": "558", "hashOfConfig": "351"}, {"size": 4152, "mtime": 1749107979109, "results": "559", "hashOfConfig": "351"}, {"size": 4762, "mtime": 1748512613872, "results": "560", "hashOfConfig": "351"}, {"size": 8134, "mtime": 1750418443077, "results": "561", "hashOfConfig": "351"}, {"size": 2524, "mtime": 1747742780631, "results": "562", "hashOfConfig": "351"}, {"size": 16049, "mtime": 1751794342065, "results": "563", "hashOfConfig": "351"}, {"size": 2002, "mtime": 1751794342102, "results": "564", "hashOfConfig": "351"}, {"size": 55806, "mtime": 1752528418131, "results": "565", "hashOfConfig": "351"}, {"size": 16429, "mtime": 1751794342096, "results": "566", "hashOfConfig": "351"}, {"size": 41991, "mtime": 1751794342083, "results": "567", "hashOfConfig": "351"}, {"size": 11446, "mtime": 1748225026675, "results": "568", "hashOfConfig": "351"}, {"size": 8222, "mtime": 1748247735813, "results": "569", "hashOfConfig": "351"}, {"size": 9160, "mtime": 1753875510541, "results": "570", "hashOfConfig": "351"}, {"size": 2115, "mtime": 1753875510551, "results": "571", "hashOfConfig": "351"}, {"size": 12880, "mtime": 1748234340710, "results": "572", "hashOfConfig": "351"}, {"size": 27641, "mtime": 1748237297926, "results": "573", "hashOfConfig": "351"}, {"size": 29009, "mtime": 1748942038625, "results": "574", "hashOfConfig": "351"}, {"size": 36682, "mtime": 1748261019419, "results": "575", "hashOfConfig": "351"}, {"size": 9569, "mtime": 1750250347410, "results": "576", "hashOfConfig": "351"}, {"size": 16393, "mtime": 1748424127070, "results": "577", "hashOfConfig": "351"}, {"size": 3992, "mtime": 1753875510541, "results": "578", "hashOfConfig": "351"}, {"size": 4028, "mtime": 1748831088180, "results": "579", "hashOfConfig": "351"}, {"size": 864, "mtime": 1748831088160, "results": "580", "hashOfConfig": "351"}, {"size": 1762, "mtime": 1748831088160, "results": "581", "hashOfConfig": "351"}, {"size": 1983, "mtime": 1751286250564, "results": "582", "hashOfConfig": "351"}, {"size": 3617, "mtime": 1748831088171, "results": "583", "hashOfConfig": "351"}, {"size": 365, "mtime": 1748942366216, "results": "584", "hashOfConfig": "351"}, {"size": 1850, "mtime": 1752528418106, "results": "585", "hashOfConfig": "351"}, {"size": 4156, "mtime": 1753875510541, "results": "586", "hashOfConfig": "351"}, {"size": 8669, "mtime": 1749108405009, "results": "587", "hashOfConfig": "351"}, {"size": 8088, "mtime": 1751794342065, "results": "588", "hashOfConfig": "351"}, {"size": 5114, "mtime": 1751004342904, "results": "589", "hashOfConfig": "351"}, {"size": 958, "mtime": 1751004342915, "results": "590", "hashOfConfig": "351"}, {"size": 19880, "mtime": 1753875510541, "results": "591", "hashOfConfig": "351"}, {"size": 388, "mtime": 1750250347440, "results": "592", "hashOfConfig": "351"}, {"size": 913, "mtime": 1751794342077, "results": "593", "hashOfConfig": "351"}, {"size": 1664, "mtime": 1750250347462, "results": "594", "hashOfConfig": "351"}, {"size": 1948, "mtime": 1751794342082, "results": "595", "hashOfConfig": "351"}, {"size": 2401, "mtime": 1750418443079, "results": "596", "hashOfConfig": "351"}, {"size": 7765, "mtime": 1750418443077, "results": "597", "hashOfConfig": "351"}, {"size": 3926, "mtime": 1752528418123, "results": "598", "hashOfConfig": "351"}, {"size": 2836, "mtime": 1750250347428, "results": "599", "hashOfConfig": "351"}, {"size": 579, "mtime": 1750250347412, "results": "600", "hashOfConfig": "351"}, {"size": 2502, "mtime": 1752528418119, "results": "601", "hashOfConfig": "351"}, {"size": 6803, "mtime": 1752528418109, "results": "602", "hashOfConfig": "351"}, {"size": 200, "mtime": 1750418807924, "results": "603", "hashOfConfig": "351"}, {"size": 4832, "mtime": 1750586864082, "results": "604", "hashOfConfig": "351"}, {"size": 3066, "mtime": 1750768842393, "results": "605", "hashOfConfig": "351"}, {"size": 4807, "mtime": 1750768842383, "results": "606", "hashOfConfig": "351"}, {"size": 4498, "mtime": 1751004342912, "results": "607", "hashOfConfig": "351"}, {"size": 4263, "mtime": 1751004342899, "results": "608", "hashOfConfig": "351"}, {"size": 72, "mtime": 1748227925877, "results": "609", "hashOfConfig": "351"}, {"size": 3878, "mtime": 1751004342884, "results": "610", "hashOfConfig": "351"}, {"size": 22243, "mtime": 1751004342890, "results": "611", "hashOfConfig": "351"}, {"size": 17942, "mtime": 1751004342890, "results": "612", "hashOfConfig": "351"}, {"size": 4331, "mtime": 1751004342891, "results": "613", "hashOfConfig": "351"}, {"size": 23770, "mtime": 1751004342891, "results": "614", "hashOfConfig": "351"}, {"size": 846, "mtime": 1751004342891, "results": "615", "hashOfConfig": "351"}, {"size": 1252, "mtime": 1751004342891, "results": "616", "hashOfConfig": "351"}, {"size": 17515, "mtime": 1752528418131, "results": "617", "hashOfConfig": "351"}, {"size": 218, "mtime": 1751434984402, "results": "618", "hashOfConfig": "351"}, {"size": 4350, "mtime": 1751794342096, "results": "619", "hashOfConfig": "351"}, {"size": 759, "mtime": 1751794342091, "results": "620", "hashOfConfig": "351"}, {"size": 4259, "mtime": 1751794342091, "results": "621", "hashOfConfig": "351"}, {"size": 680, "mtime": 1754041171135, "results": "622", "hashOfConfig": "351"}, {"size": 851, "mtime": 1752528418115, "results": "623", "hashOfConfig": "351"}, {"size": 1300, "mtime": 1753875510534, "results": "624", "hashOfConfig": "351"}, {"size": 449, "mtime": 1751794342102, "results": "625", "hashOfConfig": "351"}, {"size": 1750, "mtime": 1751879443273, "results": "626", "hashOfConfig": "351"}, {"size": 6342, "mtime": 1752528418115, "results": "627", "hashOfConfig": "351"}, {"size": 9107, "mtime": 1754037662810, "results": "628", "hashOfConfig": "351"}, {"size": 5530, "mtime": 1754037662810, "results": "629", "hashOfConfig": "351"}, {"size": 6505, "mtime": 1754041914606, "results": "630", "hashOfConfig": "351"}, {"size": 8061, "mtime": 1752528418138, "results": "631", "hashOfConfig": "351"}, {"size": 1346, "mtime": 1752528418138, "results": "632", "hashOfConfig": "351"}, {"size": 13175, "mtime": 1752528418106, "results": "633", "hashOfConfig": "351"}, {"size": 592, "mtime": 1752528418123, "results": "634", "hashOfConfig": "351"}, {"size": 1611, "mtime": 1752528418106, "results": "635", "hashOfConfig": "351"}, {"size": 7069, "mtime": 1754037662800, "results": "636", "hashOfConfig": "351"}, {"size": 28741, "mtime": 1754299894303, "results": "637", "hashOfConfig": "351"}, {"size": 43149, "mtime": 1754041861548, "results": "638", "hashOfConfig": "351"}, {"size": 20286, "mtime": 1754037662793, "results": "639", "hashOfConfig": "351"}, {"size": 9040, "mtime": 1752528418099, "results": "640", "hashOfConfig": "351"}, {"size": 6089, "mtime": 1754300386743, "results": "641", "hashOfConfig": "351"}, {"size": 593, "mtime": 1752528418090, "results": "642", "hashOfConfig": "351"}, {"size": 2173, "mtime": 1754037662793, "results": "643", "hashOfConfig": "351"}, {"size": 3352, "mtime": 1754299958143, "results": "644", "hashOfConfig": "351"}, {"size": 2297, "mtime": 1752528418090, "results": "645", "hashOfConfig": "351"}, {"size": 3061, "mtime": 1752528418099, "results": "646", "hashOfConfig": "351"}, {"size": 1511, "mtime": 1752528418099, "results": "647", "hashOfConfig": "351"}, {"size": 4044, "mtime": 1752528418099, "results": "648", "hashOfConfig": "351"}, {"size": 3600, "mtime": 1752528418099, "results": "649", "hashOfConfig": "351"}, {"size": 2305, "mtime": 1752528418099, "results": "650", "hashOfConfig": "351"}, {"size": 31528, "mtime": 1754037662821, "results": "651", "hashOfConfig": "351"}, {"size": 7652, "mtime": 1754037662810, "results": "652", "hashOfConfig": "351"}, {"size": 4259, "mtime": 1753875510541, "results": "653", "hashOfConfig": "351"}, {"size": 3528, "mtime": 1753875510541, "results": "654", "hashOfConfig": "351"}, {"size": 3017, "mtime": 1754037662810, "results": "655", "hashOfConfig": "351"}, {"size": 7599, "mtime": 1753875510541, "results": "656", "hashOfConfig": "351"}, {"size": 340, "mtime": 1753875510534, "results": "657", "hashOfConfig": "351"}, {"size": 709, "mtime": 1754037662804, "results": "658", "hashOfConfig": "351"}, {"size": 376, "mtime": 1753875510534, "results": "659", "hashOfConfig": "351"}, {"size": 139, "mtime": 1754037662827, "results": "660", "hashOfConfig": "351"}, {"size": 3054, "mtime": 1753875510529, "results": "661", "hashOfConfig": "351"}, {"size": 6757, "mtime": 1754037662804, "results": "662", "hashOfConfig": "351"}, {"size": 3645, "mtime": 1753875510532, "results": "663", "hashOfConfig": "351"}, {"size": 3362, "mtime": 1753875510529, "results": "664", "hashOfConfig": "351"}, {"size": 4615, "mtime": 1753875510509, "results": "665", "hashOfConfig": "351"}, {"size": 7086, "mtime": 1753875510509, "results": "666", "hashOfConfig": "351"}, {"size": 1904, "mtime": 1753875510509, "results": "667", "hashOfConfig": "351"}, {"size": 30960, "mtime": 1753875510513, "results": "668", "hashOfConfig": "351"}, {"size": 1206, "mtime": 1754037662832, "results": "669", "hashOfConfig": "351"}, {"size": 1178, "mtime": 1753875510525, "results": "670", "hashOfConfig": "351"}, {"size": 4133, "mtime": 1753875510525, "results": "671", "hashOfConfig": "351"}, {"size": 13910, "mtime": 1753875510528, "results": "672", "hashOfConfig": "351"}, {"size": 7570, "mtime": 1753875510528, "results": "673", "hashOfConfig": "351"}, {"size": 10269, "mtime": 1753875510527, "results": "674", "hashOfConfig": "351"}, {"size": 8712, "mtime": 1754289080905, "results": "675", "hashOfConfig": "351"}, {"size": 904, "mtime": 1753875510551, "results": "676", "hashOfConfig": "351"}, {"size": 411, "mtime": 1753875510551, "results": "677", "hashOfConfig": "351"}, {"size": 719, "mtime": 1754037662826, "results": "678", "hashOfConfig": "351"}, {"size": 3881, "mtime": 1753875510510, "results": "679", "hashOfConfig": "351"}, {"size": 740, "mtime": 1753875510530, "results": "680", "hashOfConfig": "351"}, {"size": 2747, "mtime": 1753875510511, "results": "681", "hashOfConfig": "351"}, {"size": 1760, "mtime": 1753875510509, "results": "682", "hashOfConfig": "351"}, {"size": 496, "mtime": 1753875510511, "results": "683", "hashOfConfig": "351"}, {"size": 1668, "mtime": 1753875510510, "results": "684", "hashOfConfig": "351"}, {"size": 1183, "mtime": 1753875510511, "results": "685", "hashOfConfig": "351"}, {"size": 911, "mtime": 1753875510511, "results": "686", "hashOfConfig": "351"}, {"size": 7071, "mtime": 1753875510511, "results": "687", "hashOfConfig": "351"}, {"size": 5299, "mtime": 1753875510513, "results": "688", "hashOfConfig": "351"}, {"size": 647, "mtime": 1753875510525, "results": "689", "hashOfConfig": "351"}, {"size": 1507, "mtime": 1753875510524, "results": "690", "hashOfConfig": "351"}, {"size": 8615, "mtime": 1753875510524, "results": "691", "hashOfConfig": "351"}, {"size": 4137, "mtime": 1753875510517, "results": "692", "hashOfConfig": "351"}, {"size": 576, "mtime": 1754037662804, "results": "693", "hashOfConfig": "351"}, {"size": 3550, "mtime": 1753875510529, "results": "694", "hashOfConfig": "351"}, {"size": 493, "mtime": 1753875510526, "results": "695", "hashOfConfig": "351"}, {"size": 1204, "mtime": 1753875510510, "results": "696", "hashOfConfig": "351"}, {"size": 1722, "mtime": 1753875510525, "results": "697", "hashOfConfig": "351"}, {"size": 1138, "mtime": 1753875510511, "results": "698", "hashOfConfig": "351"}, {"size": 15200, "mtime": 1754042470054, "results": "699", "hashOfConfig": "351"}, {"size": 2623, "mtime": 1754041838305, "results": "700", "hashOfConfig": "351"}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12khgps", {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1jjs0tz", {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1538", "messages": "1539", "suppressedMessages": "1540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1541", "messages": "1542", "suppressedMessages": "1543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1544", "messages": "1545", "suppressedMessages": "1546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1547", "messages": "1548", "suppressedMessages": "1549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1550", "messages": "1551", "suppressedMessages": "1552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1553", "messages": "1554", "suppressedMessages": "1555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1556", "messages": "1557", "suppressedMessages": "1558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1559", "messages": "1560", "suppressedMessages": "1561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1562", "messages": "1563", "suppressedMessages": "1564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1565", "messages": "1566", "suppressedMessages": "1567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1568", "messages": "1569", "suppressedMessages": "1570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1571", "messages": "1572", "suppressedMessages": "1573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1574", "messages": "1575", "suppressedMessages": "1576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1577", "messages": "1578", "suppressedMessages": "1579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1580", "messages": "1581", "suppressedMessages": "1582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1583", "messages": "1584", "suppressedMessages": "1585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1586", "messages": "1587", "suppressedMessages": "1588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1589", "messages": "1590", "suppressedMessages": "1591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1592", "messages": "1593", "suppressedMessages": "1594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1595", "messages": "1596", "suppressedMessages": "1597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1598", "messages": "1599", "suppressedMessages": "1600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1601", "messages": "1602", "suppressedMessages": "1603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1604", "messages": "1605", "suppressedMessages": "1606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1607", "messages": "1608", "suppressedMessages": "1609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1610", "messages": "1611", "suppressedMessages": "1612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1613", "messages": "1614", "suppressedMessages": "1615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1616", "messages": "1617", "suppressedMessages": "1618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1619", "messages": "1620", "suppressedMessages": "1621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1622", "messages": "1623", "suppressedMessages": "1624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1625", "messages": "1626", "suppressedMessages": "1627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1628", "messages": "1629", "suppressedMessages": "1630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1631", "messages": "1632", "suppressedMessages": "1633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1634", "messages": "1635", "suppressedMessages": "1636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1637", "messages": "1638", "suppressedMessages": "1639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1640", "messages": "1641", "suppressedMessages": "1642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1643", "messages": "1644", "suppressedMessages": "1645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1646", "messages": "1647", "suppressedMessages": "1648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1649", "messages": "1650", "suppressedMessages": "1651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1652", "messages": "1653", "suppressedMessages": "1654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1655", "messages": "1656", "suppressedMessages": "1657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1658", "messages": "1659", "suppressedMessages": "1660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1661", "messages": "1662", "suppressedMessages": "1663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1664", "messages": "1665", "suppressedMessages": "1666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1667", "messages": "1668", "suppressedMessages": "1669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1670", "messages": "1671", "suppressedMessages": "1672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1673", "messages": "1674", "suppressedMessages": "1675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1676", "messages": "1677", "suppressedMessages": "1678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1679", "messages": "1680", "suppressedMessages": "1681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1682", "messages": "1683", "suppressedMessages": "1684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1685", "messages": "1686", "suppressedMessages": "1687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1688", "messages": "1689", "suppressedMessages": "1690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1691", "messages": "1692", "suppressedMessages": "1693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1694", "messages": "1695", "suppressedMessages": "1696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1697", "messages": "1698", "suppressedMessages": "1699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1700", "messages": "1701", "suppressedMessages": "1702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1703", "messages": "1704", "suppressedMessages": "1705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1706", "messages": "1707", "suppressedMessages": "1708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1709", "messages": "1710", "suppressedMessages": "1711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1712", "messages": "1713", "suppressedMessages": "1714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1715", "messages": "1716", "suppressedMessages": "1717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1718", "messages": "1719", "suppressedMessages": "1720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1721", "messages": "1722", "suppressedMessages": "1723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1724", "messages": "1725", "suppressedMessages": "1726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1727", "messages": "1728", "suppressedMessages": "1729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1730", "messages": "1731", "suppressedMessages": "1732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1733", "messages": "1734", "suppressedMessages": "1735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1736", "messages": "1737", "suppressedMessages": "1738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1739", "messages": "1740", "suppressedMessages": "1741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1742", "messages": "1743", "suppressedMessages": "1744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1745", "messages": "1746", "suppressedMessages": "1747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ToanThayBee\\frontend\\src\\index.js", [], [], "D:\\ToanThayBee\\frontend\\src\\App.js", ["1748"], [], "D:\\ToanThayBee\\frontend\\src\\reportWebVitals.js", [], [], "D:\\ToanThayBee\\frontend\\src\\redux\\store.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\LoginPage.jsx", ["1749", "1750", "1751", "1752", "1753"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ProtectedRoute.jsx", ["1754"], [], "D:\\ToanThayBee\\frontend\\src\\components\\error\\NotificationDisplay.jsx", ["1755", "1756"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\CodeManagement.jsx", ["1757", "1758"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\HomePageManagement.jsx", ["1759", "1760", "1761", "1762", "1763"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticleManagement.jsx", ["1764"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\ArticlePostPage.jsx", ["1765"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1766"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\questionManagement.jsx", ["1767", "1768"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1769", "1770"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1771", "1772"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1786", "1787"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1796", "1797", "1798", "1799", "1800"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1801"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1802", "1803", "1804", "1805", "1806", "1807", "1808"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1809"], [], "D:\\ToanThayBee\\frontend\\src\\features\\sidebar\\sidebarSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\user\\userSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\question\\questionSlice.js", ["1810"], [], "D:\\ToanThayBee\\frontend\\src\\features\\auth\\authSlice.js", ["1811"], [], "D:\\ToanThayBee\\frontend\\src\\features\\state\\stateApiSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\code\\codeSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\image\\imageSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\answer\\answerSlice.js", ["1812", "1813", "1814"], [], "D:\\ToanThayBee\\frontend\\src\\features\\attempt\\attemptSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\class\\classSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\article\\articleSlice.js", ["1815"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\sanitizeInput.js", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\validation.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\achievement\\achievementSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\AdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\AuthLayout.jsx", ["1816"], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputForAuthPage.jsx", ["1817"], [], "D:\\ToanThayBee\\frontend\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825"], [], "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1826"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBarAttemp.jsx", ["1827"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddQuestionModal.jsx", ["1828"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddCodeModal.jsx", ["1829"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\CodeTable.jsx", ["1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTable.jsx", ["1838", "1839", "1840"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ArticleTable.jsx", ["1841", "1842"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutMultipleImages.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AdminModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\QuestionDetail.jsx", ["1843", "1844"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ExamDetail.jsx", ["1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\PreviewExam.jsx", ["1853", "1854", "1855", "1856", "1857", "1858"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ExamTable.jsx", ["1859"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\ClassDetail.jsx", ["1860", "1861", "1862", "1863", "1864"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\UserClassTable.jsx", ["1865", "1866", "1867"], [], "D:\\ToanThayBee\\frontend\\src\\components\\Footer.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassTable.jsx", ["1868", "1869", "1870"], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddClassModal.jsx", ["1871", "1872", "1873"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\LearningItemIcon.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\YouTubePlayer.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ViewDetail.jsx", ["1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881"], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayoutHome.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\formatters.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\SlideShow.jsx", ["1882", "1883", "1884", "1885", "1886", "1887"], [], "D:\\ToanThayBee\\frontend\\src\\components\\latex\\RenderLatex.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\NetworkSpeedTest.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\CustomSchedule.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\StudentThoughts.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\InputSearch.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\JoinClassModal.jsx", ["1888", "1889", "1890"], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\UserLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\Pagination.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\card\\countDownCard.jsx", ["1891"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\ClassImage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ViewPdf.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\apiHandler.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\authApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\card\\RelatedExamCard.jsx", ["1892", "1893", "1894", "1895", "1896"], [], "D:\\ToanThayBee\\frontend\\src\\components\\card\\ExamCard.jsx", ["1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\QrCode.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ScreenButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\userApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\questionApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ViewLearning.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\Schedule.jsx", ["1915", "1916", "1917", "1918"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderDoExamPage.jsx", ["1919", "1920", "1921", "1922"], [], "D:\\ToanThayBee\\frontend\\src\\components\\achievement\\AchievementSection.jsx", ["1923", "1924", "1925", "1926", "1927"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\Breadcrumb.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1928", "1929", "1930"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleList.jsx", ["1931"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleSidebar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\SearchBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementStatTable.jsx", ["1932", "1933"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleContent.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1934"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementCategoryTable.jsx", ["1935"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1936"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\AchievementImageTable.jsx", ["1937", "1938"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddStudentModal.jsx", ["1939", "1940"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\UserDetail.jsx", ["1941"], [], "D:\\ToanThayBee\\frontend\\src\\services\\codeApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\imageApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\userTable.jsx", ["1942", "1943", "1944"], [], "D:\\ToanThayBee\\frontend\\src\\services\\answerApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\articleApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\examApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\achievementApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\pagination\\Pagination.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\attemptApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\classApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\excelExport.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\AdminSidebar.jsx", ["1945", "1946", "1947", "1948", "1949"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ParticlesBackground.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\UploadImage.jsx", ["1950"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\StatementTableRow.jsx", ["1951"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\QuestionTableRow.jsx", ["1952"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\TooltipTd.jsx", ["1953"], [], "D:\\ToanThayBee\\frontend\\src\\components\\UploadPdf.jsx", ["1954"], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\PutImgae.jsx", ["1955"], [], "D:\\ToanThayBee\\frontend\\src\\components\\detail\\DetailTr.jsx", ["1956"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\question\\questionUtils.js", ["1957", "1958", "1959", "1960", "1961", "1962", "1963", "1964", "1965", "1966", "1967", "1968", "1969", "1970", "1971", "1972", "1973", "1974"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ScheduleModal.jsx", ["1975"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\HeaderHome.jsx", ["1976", "1977", "1978", "1979"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\Header.jsx", ["1980", "1981", "1982", "1983", "1984", "1985"], [], "D:\\ToanThayBee\\frontend\\src\\services\\api.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ArticleCard.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ChapterFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ClassFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ConfirmModal.jsx", ["1986"], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ActiveFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\CategoryFilters.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\UserSidebar.jsx", ["1987"], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1988", "1989", "1990", "1991", "1992", "1993"], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\StudentCardModal.jsx", ["1994", "1995", "1996", "1997"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\ChoiceHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\responseInterceptor.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\requestInterceptor.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\AvatarUploader.jsx", ["1998", "1999"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\OverViewPage.jsx", ["2000"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["2001", "2002"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\home\\Home.jsx", ["2003", "2004", "2005", "2006", "2007", "2008", "2009", "2010", "2011", "2012", "2013", "2014", "2015", "2016", "2017"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\ClassUserPage.jsx", ["2018", "2019", "2020", "2021", "2022", "2023", "2024", "2025", "2026", "2027"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\PracticePage.jsx", ["2028", "2029", "2030"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\class\\LearningPage.jsx", ["2031", "2032", "2033", "2034", "2035", "2036", "2037", "2038"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\DoExamPage.jsx", ["2039", "2040", "2041", "2042", "2043"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ExamDetail.jsx", ["2044", "2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056", "2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\practice\\ScorePage.jsx", ["2067", "2068", "2069", "2070", "2071", "2072", "2073", "2074", "2075", "2076"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticleListPage.jsx", ["2077", "2078", "2079", "2080", "2081", "2082", "2083"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\article\\ArticlePage.jsx", ["2084", "2085", "2086", "2087", "2088", "2089", "2090"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\fullscreenUtils.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\questionReport\\questionReportSlice.js", ["2091"], [], "D:\\ToanThayBee\\frontend\\src\\services\\questionReportApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ReportButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\QuestionReportManagement.jsx", ["2092", "2093", "2094"], [], "D:\\ToanThayBee\\frontend\\src\\components\\utils\\NoTranslate.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\notification\\notificationSlice.js", ["2095"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\article\\ModernArticleSidebar.jsx", ["2096", "2097", "2098", "2099", "2100", "2101", "2102"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\cacheManager.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\notificationApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\notification\\NotificationPanel.jsx", ["2103", "2104", "2105", "2106"], [], "D:\\ToanThayBee\\frontend\\src\\features\\tuition\\tuitionSlice.js", ["2107"], [], "D:\\ToanThayBee\\frontend\\src\\services\\tuitionApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["2108", "2109", "2110", "2111", "2112", "2113", "2114", "2115", "2116", "2117", "2118", "2119", "2120", "2121", "2122", "2123", "2124", "2125", "2126", "2127", "2128", "2129", "2130", "2131", "2132", "2133", "2134", "2135", "2136", "2137", "2138", "2139"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["2140", "2141", "2142", "2143"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["2144", "2145", "2146", "2147", "2148", "2149"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ClassSearchInput.jsx", ["2150"], [], "D:\\ToanThayBee\\frontend\\src\\components\\UserSearchInput.jsx", ["2151"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PaymentModal.jsx", ["2152"], [], "D:\\ToanThayBee\\frontend\\src\\components\\MultiClassSelector.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\attendance\\attendanceSlice.js", ["2153", "2154"], [], "D:\\ToanThayBee\\frontend\\src\\services\\attendanceApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\class\\AttendancePage.jsx", ["2155", "2156", "2157", "2158", "2159", "2160", "2161", "2162"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["2163"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["2164", "2165", "2166", "2167", "2168"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\lessonSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\lessonApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\team\\TeamSection.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["2169", "2170", "2171"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["2172", "2173"], [], "D:\\ToanThayBee\\frontend\\src\\components\\bar\\FilterBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\banner\\ClassBanner.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\ClassAdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\maintenanceUtils.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceCleaner.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\MaintenanceWrapper.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\config\\maintenance.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\MaintenancePage.jsx", ["2174", "2175"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ScrollToTop.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\UserAdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["2176", "2177", "2178"], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\ClassOfUserTable.jsx", ["2179"], [], "D:\\ToanThayBee\\frontend\\src\\features\\learningItem\\learningItemSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\learningItemApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\doExam\\doExamSlice.js", ["2180", "2181", "2182", "2183", "2184"], [], "D:\\ToanThayBee\\frontend\\src\\features\\filter\\filterReducer.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\pagination\\paginationReducer.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\doExamApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\404NotFound.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\CustomSearchInput.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\UnpaidTuitionModal.jsx", ["2185", "2186", "2187", "2188"], [], "D:\\ToanThayBee\\frontend\\src\\layouts\\ExamAdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\TotalComponent.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\sheet\\sheetSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\sheetApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ExamSearchInput.jsx", ["2189"], [], "D:\\ToanThayBee\\frontend\\src\\components\\MultiLessonSelector.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\LessonSearchInput.jsx", ["2190"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["2191", "2192", "2193", "2194", "2195", "2196", "2197", "2198"], [], "D:\\ToanThayBee\\frontend\\src\\features\\calendar\\calendarSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\lesson\\index.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\DayView.jsx", ["2199", "2200", "2201"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\MonthView.jsx", ["2202"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\SidebarCalender.jsx", ["2203"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\WeekView.jsx", ["2204", "2205", "2206", "2207", "2208", "2209", "2210", "2211", "2212", "2213", "2214", "2215"], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\TimeSlots.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", ["2216", "2217"], [], "D:\\ToanThayBee\\frontend\\src\\constants\\UserType.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["2218", "2219", "2220", "2221", "2222"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["2223", "2224", "2225", "2226", "2227", "2228"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["2229", "2230", "2231"], [], "D:\\ToanThayBee\\frontend\\src\\services\\ocrExamApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\table\\TableAdmin.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingData.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\apin8n.js", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\setupKatexWarningFilter.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\dashboard\\dashboardSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\questionsExam\\questionsExamSlice.js", ["2232"], [], "D:\\ToanThayBee\\frontend\\src\\features\\addExam\\addExamSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\exam\\AddExamAdmin.jsx", ["2233", "2234", "2235", "2236", "2237", "2238", "2239", "2240", "2241", "2242", "2243", "2244", "2245", "2246", "2247", "2248", "2249", "2250", "2251", "2252", "2253", "2254", "2255", "2256", "2257", "2258", "2259", "2260", "2261", "2262"], [], "D:\\ToanThayBee\\frontend\\src\\pages\\admin\\user\\StaffManagement.jsx", ["2263", "2264", "2265"], [], "D:\\ToanThayBee\\frontend\\src\\services\\dashboardApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\modal\\AddImagesModal.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\hooks\\useDebouncedEffect.js", ["2266", "2267"], [], "D:\\ToanThayBee\\frontend\\src\\components\\layout\\AdminLayout.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\RightContent.jsx", ["2268", "2269", "2270"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\LeftContent.jsx", ["2271"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\LeftContent.jsx", ["2272", "2273", "2274", "2275", "2276", "2277", "2278", "2279", "2280", "2281", "2282", "2283", "2284"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\RightContent.jsx", ["2285", "2286", "2287", "2288", "2289", "2290"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionView.jsx", ["2291", "2292", "2293", "2294", "2295", "2296", "2297", "2298"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\SolutionEditor.jsx", ["2299", "2300", "2301", "2302"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\NavigateBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageAddExam\\CompactStepHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\input\\TextArea.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\ImageView.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\image\\ImageDropZone.jsx", ["2303"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\QuestionContent.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx", ["2304", "2305"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\pages\\user\\question\\QuestionPage.jsx", ["2306"], [], "D:\\ToanThayBee\\frontend\\src\\features\\exam\\examDetailSlice.js", ["2307", "2308", "2309", "2310", "2311", "2312"], [], "D:\\ToanThayBee\\frontend\\src\\features\\practice\\practiceSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\scorePage\\scorePageSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\ai\\aiSlice.js", [], [], "D:\\ToanThayBee\\frontend\\src\\features\\comments\\ExamCommentsSlice.js", ["2313", "2314", "2315", "2316"], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\BatteryLoading.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\button\\ActionButton.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\LoadingText.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\utils\\codeUtils.js", ["2317"], [], "D:\\ToanThayBee\\frontend\\src\\components\\filter\\SortBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\ExamOverviewHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\LearningHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\filter\\FilterBar.jsx", ["2318"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamContent.jsx", ["2319", "2320", "2321"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ExamSideBar.jsx", ["2322"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ModalSubmitExam.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\Schedule1.jsx", ["2323", "2324"], [], "D:\\ToanThayBee\\frontend\\src\\utils\\shareUntil.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\common\\OutsideClickWrapper.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentSection.jsx", ["2325", "2326", "2327", "2328", "2329", "2330"], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\RankingView.jsx", ["2331", "2332"], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\PreviewView.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\HistoryView.jsx", ["2333"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AiChatWidget.jsx", ["2334", "2335", "2336"], [], "D:\\ToanThayBee\\frontend\\src\\services\\examCommentsApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\studentExamApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\services\\aiApi.js", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\MultipleChoiceQuestion.jsx", ["2337", "2338", "2339", "2340"], [], "D:\\ToanThayBee\\frontend\\src\\components\\header\\ButtonHeader.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ShortAnswerQuestion.jsx", ["2341", "2342", "2343", "2344", "2345", "2346"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\LoadingQuestions.jsx", ["2347", "2348"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionSectionTitle.jsx", ["2349"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\ProgressBar.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionCounter.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\SubmitButton.jsx", ["2350"], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\TrueFalseQuestion.jsx", ["2351", "2352", "2353"], [], "D:\\ToanThayBee\\frontend\\src\\components\\StarRating.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\LoadingCommentItem.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentInput.jsx", ["2354"], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\CommentItem.jsx", ["2355", "2356", "2357", "2358", "2359"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\QuestionDropdown.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\loading\\OnlineLoading.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\examDetail\\UserInfoPanel.jsx", ["2360", "2361", "2362", "2363", "2364"], [], "D:\\ToanThayBee\\frontend\\src\\components\\common\\WrapperWithTooltip.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionContent.jsx", ["2365"], [], "D:\\ToanThayBee\\frontend\\src\\components\\comment\\EmojiPicker.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\PageDoExam\\QuestionImage.jsx", [], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\AIWidget.jsx", ["2366", "2367", "2368"], [], "D:\\ToanThayBee\\frontend\\src\\components\\ai\\FloatingAIWidget.jsx", [], [], {"ruleId": "2369", "severity": 1, "message": "2370", "line": 49, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 49, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2373", "line": 4, "column": 23, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2374", "line": 8, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2375", "line": 11, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 21}, {"ruleId": "2376", "severity": 1, "message": "2377", "line": 42, "column": 8, "nodeType": "2378", "endLine": 42, "endColumn": 24, "suggestions": "2379"}, {"ruleId": "2369", "severity": 1, "message": "2380", "line": 49, "column": 15, "nodeType": "2371", "messageId": "2372", "endLine": 49, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2382", "line": 1, "column": 38, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 44}, {"ruleId": "2369", "severity": 1, "message": "2383", "line": 11, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 10, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 10, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2385", "line": 18, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 36}, {"ruleId": "2369", "severity": 1, "message": "2386", "line": 6, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2387", "line": 6, "column": 27, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 31}, {"ruleId": "2369", "severity": 1, "message": "2388", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2389", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2390", "line": 88, "column": 23, "nodeType": "2371", "messageId": "2372", "endLine": 88, "endColumn": 31}, {"ruleId": "2376", "severity": 1, "message": "2391", "line": 26, "column": 8, "nodeType": "2378", "endLine": 26, "endColumn": 19, "suggestions": "2392"}, {"ruleId": "2393", "severity": 1, "message": "2394", "line": 393, "column": 45, "nodeType": "2395", "endLine": 398, "endColumn": 47}, {"ruleId": "2369", "severity": 1, "message": "2396", "line": 1, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2397", "line": 14, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2385", "line": 15, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 36}, {"ruleId": "2369", "severity": 1, "message": "2398", "line": 2, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2399", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2400", "line": 15, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2385", "line": 15, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 36}, {"ruleId": "2369", "severity": 1, "message": "2396", "line": 1, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2401", "line": 8, "column": 37, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 48}, {"ruleId": "2369", "severity": 1, "message": "2402", "line": 11, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2403", "line": 22, "column": 18, "nodeType": "2371", "messageId": "2372", "endLine": 22, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2404", "line": 23, "column": 21, "nodeType": "2371", "messageId": "2372", "endLine": 23, "endColumn": 31}, {"ruleId": "2369", "severity": 1, "message": "2405", "line": 24, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2406", "line": 25, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 25, "endColumn": 39}, {"ruleId": "2369", "severity": 1, "message": "2407", "line": 26, "column": 30, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 49}, {"ruleId": "2369", "severity": 1, "message": "2408", "line": 27, "column": 21, "nodeType": "2371", "messageId": "2372", "endLine": 27, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2409", "line": 27, "column": 41, "nodeType": "2371", "messageId": "2372", "endLine": 27, "endColumn": 51}, {"ruleId": "2369", "severity": 1, "message": "2410", "line": 31, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 31, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2411", "line": 34, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 34, "endColumn": 35}, {"ruleId": "2369", "severity": 1, "message": "2412", "line": 38, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 38, "endColumn": 33}, {"ruleId": "2369", "severity": 1, "message": "2413", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 21}, {"ruleId": "2376", "severity": 1, "message": "2414", "line": 27, "column": 8, "nodeType": "2378", "endLine": 27, "endColumn": 18, "suggestions": "2415"}, {"ruleId": "2369", "severity": 1, "message": "2396", "line": 1, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2398", "line": 2, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2416", "line": 6, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2417", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2418", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2410", "line": 25, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 25, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2412", "line": 28, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 28, "endColumn": 33}, {"ruleId": "2369", "severity": 1, "message": "2419", "line": 31, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 31, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2420", "line": 6, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2421", "line": 9, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 18, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2423", "line": 20, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 20, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2424", "line": 26, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2425", "line": 5, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 39}, {"ruleId": "2369", "severity": 1, "message": "2420", "line": 15, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2421", "line": 16, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 16, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 24, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2423", "line": 30, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 30, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2426", "line": 34, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 34, "endColumn": 23}, {"ruleId": "2376", "severity": 1, "message": "2427", "line": 73, "column": 31, "nodeType": "2371", "endLine": 73, "endColumn": 42}, {"ruleId": "2376", "severity": 1, "message": "2428", "line": 269, "column": 8, "nodeType": "2378", "endLine": 269, "endColumn": 18, "suggestions": "2429"}, {"ruleId": "2369", "severity": 1, "message": "2396", "line": 1, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2430", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2431", "line": 39, "column": 19, "nodeType": "2371", "messageId": "2372", "endLine": 39, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2432", "line": 3, "column": 34, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 67}, {"ruleId": "2369", "severity": 1, "message": "2433", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2430", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 17}, {"ruleId": "2434", "severity": 1, "message": "2435", "line": 140, "column": 80, "nodeType": "2436", "messageId": "2437", "endLine": 140, "endColumn": 82}, {"ruleId": "2369", "severity": 1, "message": "2438", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2439", "line": 19, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2399", "line": 2, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2425", "line": 7, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 39}, {"ruleId": "2369", "severity": 1, "message": "2440", "line": 25, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 25, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2441", "line": 26, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2442", "line": 48, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 48, "endColumn": 22}, {"ruleId": "2376", "severity": 1, "message": "2443", "line": 69, "column": 8, "nodeType": "2378", "endLine": 69, "endColumn": 30, "suggestions": "2444"}, {"ruleId": "2369", "severity": 1, "message": "2445", "line": 79, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 79, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2446", "line": 87, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 87, "endColumn": 21}, {"ruleId": "2376", "severity": 1, "message": "2447", "line": 18, "column": 8, "nodeType": "2378", "endLine": 18, "endColumn": 10, "suggestions": "2448"}, {"ruleId": "2369", "severity": 1, "message": "2449", "line": 11, "column": 41, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 51}, {"ruleId": "2369", "severity": 1, "message": "2450", "line": 7, "column": 47, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 68}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 2, "column": 20, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 1, "column": 20, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2452", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2453", "line": 12, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2454", "line": 13, "column": 36, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 45}, {"ruleId": "2369", "severity": 1, "message": "2455", "line": 14, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2456", "line": 14, "column": 16, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2457", "line": 29, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 29, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2458", "line": 10, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 10, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2449", "line": 18, "column": 36, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 46}, {"ruleId": "2376", "severity": 1, "message": "2459", "line": 43, "column": 8, "nodeType": "2378", "endLine": 43, "endColumn": 26, "suggestions": "2460"}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2461", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2462", "line": 8, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 28}, {"ruleId": "2376", "severity": 1, "message": "2463", "line": 45, "column": 8, "nodeType": "2378", "endLine": 45, "endColumn": 32, "suggestions": "2464"}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2465", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2466", "line": 6, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2462", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 28}, {"ruleId": "2376", "severity": 1, "message": "2467", "line": 57, "column": 8, "nodeType": "2378", "endLine": 57, "endColumn": 28, "suggestions": "2468"}, {"ruleId": "2369", "severity": 1, "message": "2412", "line": 71, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 71, "endColumn": 33}, {"ruleId": "2369", "severity": 1, "message": "2411", "line": 75, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 75, "endColumn": 35}, {"ruleId": "2369", "severity": 1, "message": "2419", "line": 79, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 79, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2469", "line": 1, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2470", "line": 2, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2471", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2472", "line": 12, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 31}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2473", "line": 6, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2466", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2420", "line": 13, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2421", "line": 14, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2423", "line": 23, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 23, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2449", "line": 16, "column": 36, "nodeType": "2371", "messageId": "2372", "endLine": 16, "endColumn": 46}, {"ruleId": "2376", "severity": 1, "message": "2474", "line": 35, "column": 8, "nodeType": "2378", "endLine": 35, "endColumn": 53, "suggestions": "2475"}, {"ruleId": "2369", "severity": 1, "message": "2458", "line": 6, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2449", "line": 15, "column": 36, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 46}, {"ruleId": "2369", "severity": 1, "message": "2476", "line": 19, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 37}, {"ruleId": "2369", "severity": 1, "message": "2477", "line": 17, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2478", "line": 29, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 29, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2479", "line": 36, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 36, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2480", "line": 12, "column": 55, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 61}, {"ruleId": "2369", "severity": 1, "message": "2481", "line": 12, "column": 77, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 82}, {"ruleId": "2369", "severity": 1, "message": "2482", "line": 12, "column": 84, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 94}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 18, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2426", "line": 20, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 20, "endColumn": 23}, {"ruleId": "2376", "severity": 1, "message": "2427", "line": 39, "column": 31, "nodeType": "2371", "endLine": 39, "endColumn": 42}, {"ruleId": "2369", "severity": 1, "message": "2483", "line": 96, "column": 17, "nodeType": "2371", "messageId": "2372", "endLine": 96, "endColumn": 21}, {"ruleId": "2434", "severity": 1, "message": "2435", "line": 412, "column": 68, "nodeType": "2436", "messageId": "2437", "endLine": 412, "endColumn": 70}, {"ruleId": "2369", "severity": 1, "message": "2484", "line": 2, "column": 37, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 45}, {"ruleId": "2369", "severity": 1, "message": "2485", "line": 2, "column": 47, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 60}, {"ruleId": "2369", "severity": 1, "message": "2486", "line": 2, "column": 62, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 67}, {"ruleId": "2369", "severity": 1, "message": "2487", "line": 2, "column": 69, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 77}, {"ruleId": "2369", "severity": 1, "message": "2488", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 26}, {"ruleId": "2376", "severity": 1, "message": "2489", "line": 36, "column": 8, "nodeType": "2378", "endLine": 36, "endColumn": 43, "suggestions": "2490"}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 1, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 33}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2491", "line": 21, "column": 15, "nodeType": "2371", "messageId": "2372", "endLine": 21, "endColumn": 21}, {"ruleId": "2376", "severity": 1, "message": "2492", "line": 25, "column": 8, "nodeType": "2378", "endLine": 25, "endColumn": 20, "suggestions": "2493"}, {"ruleId": "2369", "severity": 1, "message": "2494", "line": 14, "column": 38, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 47}, {"ruleId": "2369", "severity": 1, "message": "2495", "line": 14, "column": 49, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 56}, {"ruleId": "2369", "severity": 1, "message": "2496", "line": 14, "column": 58, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 70}, {"ruleId": "2497", "severity": 1, "message": "2498", "line": 34, "column": 40, "nodeType": "2499", "messageId": "2500", "endLine": 34, "endColumn": 42}, {"ruleId": "2497", "severity": 1, "message": "2498", "line": 34, "column": 109, "nodeType": "2499", "messageId": "2500", "endLine": 34, "endColumn": 111}, {"ruleId": "2369", "severity": 1, "message": "2501", "line": 1, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2487", "line": 7, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2481", "line": 8, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 10}, {"ruleId": "2369", "severity": 1, "message": "2484", "line": 9, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2485", "line": 10, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 10, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2502", "line": 11, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2503", "line": 12, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2504", "line": 13, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2505", "line": 22, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 22, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2506", "line": 33, "column": 78, "nodeType": "2371", "messageId": "2372", "endLine": 33, "endColumn": 87}, {"ruleId": "2369", "severity": 1, "message": "2507", "line": 33, "column": 89, "nodeType": "2371", "messageId": "2372", "endLine": 33, "endColumn": 97}, {"ruleId": "2369", "severity": 1, "message": "2508", "line": 33, "column": 103, "nodeType": "2371", "messageId": "2372", "endLine": 33, "endColumn": 109}, {"ruleId": "2369", "severity": 1, "message": "2509", "line": 33, "column": 111, "nodeType": "2371", "messageId": "2372", "endLine": 33, "endColumn": 117}, {"ruleId": "2369", "severity": 1, "message": "2510", "line": 33, "column": 119, "nodeType": "2371", "messageId": "2372", "endLine": 33, "endColumn": 131}, {"ruleId": "2497", "severity": 1, "message": "2498", "line": 87, "column": 75, "nodeType": "2499", "messageId": "2500", "endLine": 87, "endColumn": 77}, {"ruleId": "2497", "severity": 1, "message": "2498", "line": 87, "column": 144, "nodeType": "2499", "messageId": "2500", "endLine": 87, "endColumn": 146}, {"ruleId": "2497", "severity": 1, "message": "2498", "line": 92, "column": 74, "nodeType": "2499", "messageId": "2500", "endLine": 92, "endColumn": 76}, {"ruleId": "2497", "severity": 1, "message": "2498", "line": 92, "column": 138, "nodeType": "2499", "messageId": "2500", "endLine": 92, "endColumn": 140}, {"ruleId": "2369", "severity": 1, "message": "2386", "line": 2, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2399", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2511", "line": 13, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 23}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 290, "column": 56, "nodeType": "2436", "messageId": "2437", "endLine": 290, "endColumn": 58}, {"ruleId": "2369", "severity": 1, "message": "2382", "line": 1, "column": 20, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2513", "line": 10, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 10, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2514", "line": 64, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 64, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2515", "line": 64, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 64, "endColumn": 39}, {"ruleId": "2369", "severity": 1, "message": "2516", "line": 6, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2517", "line": 6, "column": 31, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 37}, {"ruleId": "2369", "severity": 1, "message": "2482", "line": 6, "column": 39, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 49}, {"ruleId": "2369", "severity": 1, "message": "2486", "line": 6, "column": 51, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 56}, {"ruleId": "2369", "severity": 1, "message": "2518", "line": 6, "column": 65, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 70}, {"ruleId": "2369", "severity": 1, "message": "2408", "line": 15, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2519", "line": 15, "column": 44, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 52}, {"ruleId": "2376", "severity": 1, "message": "2520", "line": 42, "column": 8, "nodeType": "2378", "endLine": 42, "endColumn": 40, "suggestions": "2521"}, {"ruleId": "2369", "severity": 1, "message": "2502", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2418", "line": 7, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 36}, {"ruleId": "2369", "severity": 1, "message": "2453", "line": 13, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2453", "line": 15, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2418", "line": 7, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 36}, {"ruleId": "2369", "severity": 1, "message": "2453", "line": 14, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2418", "line": 7, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 36}, {"ruleId": "2369", "severity": 1, "message": "2453", "line": 13, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2522", "line": 15, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2523", "line": 19, "column": 22, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2524", "line": 1, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2524", "line": 1, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 7, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2375", "line": 1, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2373", "line": 2, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2525", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2526", "line": 4, "column": 26, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 44}, {"ruleId": "2369", "severity": 1, "message": "2527", "line": 9, "column": 166, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 170}, {"ruleId": "2376", "severity": 1, "message": "2528", "line": 89, "column": 8, "nodeType": "2378", "endLine": 89, "endColumn": 10, "suggestions": "2529"}, {"ruleId": "2393", "severity": 1, "message": "2394", "line": 56, "column": 37, "nodeType": "2395", "endLine": 56, "endColumn": 107}, {"ruleId": "2393", "severity": 1, "message": "2394", "line": 44, "column": 29, "nodeType": "2395", "endLine": 44, "endColumn": 98}, {"ruleId": "2376", "severity": 1, "message": "2530", "line": 29, "column": 8, "nodeType": "2378", "endLine": 29, "endColumn": 10, "suggestions": "2531"}, {"ruleId": "2369", "severity": 1, "message": "2532", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 27}, {"ruleId": "2376", "severity": 1, "message": "2533", "line": 73, "column": 8, "nodeType": "2378", "endLine": 73, "endColumn": 25, "suggestions": "2534"}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 19}, {"ruleId": "2535", "severity": 1, "message": "2536", "line": 205, "column": 24, "nodeType": "2537", "messageId": "2538", "endLine": 205, "endColumn": 25, "suggestions": "2539"}, {"ruleId": "2535", "severity": 1, "message": "2540", "line": 205, "column": 26, "nodeType": "2537", "messageId": "2538", "endLine": 205, "endColumn": 27, "suggestions": "2541"}, {"ruleId": "2535", "severity": 1, "message": "2536", "line": 207, "column": 27, "nodeType": "2537", "messageId": "2538", "endLine": 207, "endColumn": 28, "suggestions": "2542"}, {"ruleId": "2535", "severity": 1, "message": "2540", "line": 207, "column": 29, "nodeType": "2537", "messageId": "2538", "endLine": 207, "endColumn": 30, "suggestions": "2543"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 256, "column": 30, "nodeType": "2537", "messageId": "2538", "endLine": 256, "endColumn": 31, "suggestions": "2545"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 276, "column": 61, "nodeType": "2537", "messageId": "2538", "endLine": 276, "endColumn": 62, "suggestions": "2546"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 296, "column": 29, "nodeType": "2537", "messageId": "2538", "endLine": 296, "endColumn": 30, "suggestions": "2547"}, {"ruleId": "2535", "severity": 1, "message": "2548", "line": 296, "column": 31, "nodeType": "2537", "messageId": "2538", "endLine": 296, "endColumn": 32, "suggestions": "2549"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 297, "column": 51, "nodeType": "2537", "messageId": "2538", "endLine": 297, "endColumn": 52, "suggestions": "2550"}, {"ruleId": "2535", "severity": 1, "message": "2548", "line": 297, "column": 53, "nodeType": "2537", "messageId": "2538", "endLine": 297, "endColumn": 54, "suggestions": "2551"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 399, "column": 30, "nodeType": "2537", "messageId": "2538", "endLine": 399, "endColumn": 31, "suggestions": "2552"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 427, "column": 61, "nodeType": "2537", "messageId": "2538", "endLine": 427, "endColumn": 62, "suggestions": "2553"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 447, "column": 29, "nodeType": "2537", "messageId": "2538", "endLine": 447, "endColumn": 30, "suggestions": "2554"}, {"ruleId": "2535", "severity": 1, "message": "2548", "line": 447, "column": 31, "nodeType": "2537", "messageId": "2538", "endLine": 447, "endColumn": 32, "suggestions": "2555"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 448, "column": 51, "nodeType": "2537", "messageId": "2538", "endLine": 448, "endColumn": 52, "suggestions": "2556"}, {"ruleId": "2535", "severity": 1, "message": "2548", "line": 448, "column": 53, "nodeType": "2537", "messageId": "2538", "endLine": 448, "endColumn": 54, "suggestions": "2557"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 537, "column": 30, "nodeType": "2537", "messageId": "2538", "endLine": 537, "endColumn": 31, "suggestions": "2558"}, {"ruleId": "2535", "severity": 1, "message": "2544", "line": 562, "column": 61, "nodeType": "2537", "messageId": "2538", "endLine": 562, "endColumn": 62, "suggestions": "2559"}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 116, "column": 56, "nodeType": "2436", "messageId": "2437", "endLine": 116, "endColumn": 58}, {"ruleId": "2369", "severity": 1, "message": "2560", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2561", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2562", "line": 100, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 100, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2563", "line": 111, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 111, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2560", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2564", "line": 6, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2565", "line": 6, "column": 18, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 33}, {"ruleId": "2369", "severity": 1, "message": "2566", "line": 13, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2567", "line": 13, "column": 28, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 45}, {"ruleId": "2369", "severity": 1, "message": "2568", "line": 29, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 29, "endColumn": 20}, {"ruleId": "2569", "severity": 1, "message": "2570", "line": 28, "column": 74, "nodeType": "2436", "messageId": "2571", "endLine": 28, "endColumn": 75}, {"ruleId": "2572", "severity": 1, "message": "2573", "line": 12, "column": 25, "nodeType": "2395", "endLine": 12, "endColumn": 614}, {"ruleId": "2369", "severity": 1, "message": "2470", "line": 2, "column": 23, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 34}, {"ruleId": "2369", "severity": 1, "message": "2574", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2575", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2576", "line": 5, "column": 33, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 40}, {"ruleId": "2369", "severity": 1, "message": "2577", "line": 5, "column": 42, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 51}, {"ruleId": "2369", "severity": 1, "message": "2578", "line": 8, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2524", "line": 1, "column": 46, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 49}, {"ruleId": "2369", "severity": 1, "message": "2579", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2580", "line": 15, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2581", "line": 15, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 37}, {"ruleId": "2376", "severity": 1, "message": "2582", "line": 21, "column": 8, "nodeType": "2378", "endLine": 21, "endColumn": 26, "suggestions": "2583"}, {"ruleId": "2376", "severity": 1, "message": "2584", "line": 40, "column": 8, "nodeType": "2378", "endLine": 40, "endColumn": 10, "suggestions": "2585"}, {"ruleId": "2376", "severity": 1, "message": "2586", "line": 299, "column": 8, "nodeType": "2378", "endLine": 299, "endColumn": 18, "suggestions": "2587"}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 89, "column": 37, "nodeType": "2436", "messageId": "2437", "endLine": 89, "endColumn": 39}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 91, "column": 44, "nodeType": "2436", "messageId": "2437", "endLine": 91, "endColumn": 46}, {"ruleId": "2369", "severity": 1, "message": "2588", "line": 2, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2589", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2590", "line": 18, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2591", "line": 18, "column": 137, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 146}, {"ruleId": "2369", "severity": 1, "message": "2592", "line": 18, "column": 148, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 161}, {"ruleId": "2369", "severity": 1, "message": "2593", "line": 18, "column": 163, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 173}, {"ruleId": "2369", "severity": 1, "message": "2594", "line": 18, "column": 175, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 182}, {"ruleId": "2369", "severity": 1, "message": "2595", "line": 18, "column": 184, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 189}, {"ruleId": "2369", "severity": 1, "message": "2596", "line": 18, "column": 191, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 205}, {"ruleId": "2369", "severity": 1, "message": "2597", "line": 34, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 34, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2598", "line": 39, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 39, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2599", "line": 40, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 40, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2600", "line": 64, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 64, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2601", "line": 65, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 65, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2602", "line": 67, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 67, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2560", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2603", "line": 6, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2487", "line": 12, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2486", "line": 13, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 10}, {"ruleId": "2369", "severity": 1, "message": "2604", "line": 14, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2605", "line": 16, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 16, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2606", "line": 17, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2607", "line": 79, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 79, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2608", "line": 80, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 80, "endColumn": 18}, {"ruleId": "2376", "severity": 1, "message": "2609", "line": 86, "column": 8, "nodeType": "2378", "endLine": 86, "endColumn": 18, "suggestions": "2610"}, {"ruleId": "2376", "severity": 1, "message": "2611", "line": 197, "column": 8, "nodeType": "2378", "endLine": 197, "endColumn": 22, "suggestions": "2612"}, {"ruleId": "2613", "severity": 1, "message": "2614", "line": 205, "column": 91, "nodeType": "2615", "messageId": "2437", "endLine": 205, "endColumn": 95}, {"ruleId": "2376", "severity": 1, "message": "2616", "line": 206, "column": 8, "nodeType": "2378", "endLine": 206, "endColumn": 112, "suggestions": "2617"}, {"ruleId": "2369", "severity": 1, "message": "2618", "line": 3, "column": 48, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 64}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 24, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 19}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 140, "column": 37, "nodeType": "2436", "messageId": "2437", "endLine": 140, "endColumn": 39}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 142, "column": 63, "nodeType": "2436", "messageId": "2437", "endLine": 142, "endColumn": 65}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 145, "column": 71, "nodeType": "2436", "messageId": "2437", "endLine": 145, "endColumn": 73}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 153, "column": 61, "nodeType": "2436", "messageId": "2437", "endLine": 153, "endColumn": 63}, {"ruleId": "2376", "severity": 1, "message": "2619", "line": 160, "column": 8, "nodeType": "2378", "endLine": 160, "endColumn": 76, "suggestions": "2620"}, {"ruleId": "2376", "severity": 1, "message": "2621", "line": 222, "column": 8, "nodeType": "2378", "endLine": 222, "endColumn": 21, "suggestions": "2622"}, {"ruleId": "2369", "severity": 1, "message": "2623", "line": 34, "column": 53, "nodeType": "2371", "messageId": "2372", "endLine": 34, "endColumn": 62}, {"ruleId": "2369", "severity": 1, "message": "2624", "line": 35, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 35, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2625", "line": 43, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 43, "endColumn": 27}, {"ruleId": "2376", "severity": 1, "message": "2626", "line": 154, "column": 8, "nodeType": "2378", "endLine": 154, "endColumn": 120, "suggestions": "2627"}, {"ruleId": "2369", "severity": 1, "message": "2491", "line": 162, "column": 19, "nodeType": "2371", "messageId": "2372", "endLine": 162, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2628", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 35}, {"ruleId": "2369", "severity": 1, "message": "2629", "line": 4, "column": 58, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 73}, {"ruleId": "2369", "severity": 1, "message": "2630", "line": 4, "column": 102, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 117}, {"ruleId": "2369", "severity": 1, "message": "2631", "line": 4, "column": 119, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 126}, {"ruleId": "2369", "severity": 1, "message": "2382", "line": 6, "column": 31, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 37}, {"ruleId": "2369", "severity": 1, "message": "2632", "line": 8, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2633", "line": 14, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2634", "line": 17, "column": 15, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2635", "line": 18, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 8}, {"ruleId": "2369", "severity": 1, "message": "2636", "line": 26, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 9}, {"ruleId": "2369", "severity": 1, "message": "2637", "line": 27, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 27, "endColumn": 10}, {"ruleId": "2369", "severity": 1, "message": "2638", "line": 28, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 28, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2639", "line": 35, "column": 73, "nodeType": "2371", "messageId": "2372", "endLine": 35, "endColumn": 87}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 48, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 48, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2640", "line": 49, "column": 19, "nodeType": "2371", "messageId": "2372", "endLine": 49, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2641", "line": 320, "column": 48, "nodeType": "2371", "messageId": "2372", "endLine": 320, "endColumn": 52}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 349, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 349, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2642", "line": 350, "column": 19, "nodeType": "2371", "messageId": "2372", "endLine": 350, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2643", "line": 351, "column": 23, "nodeType": "2371", "messageId": "2372", "endLine": 351, "endColumn": 33}, {"ruleId": "2369", "severity": 1, "message": "2644", "line": 355, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 355, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2642", "line": 394, "column": 19, "nodeType": "2371", "messageId": "2372", "endLine": 394, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2645", "line": 394, "column": 52, "nodeType": "2371", "messageId": "2372", "endLine": 394, "endColumn": 75}, {"ruleId": "2376", "severity": 1, "message": "2646", "line": 471, "column": 8, "nodeType": "2378", "endLine": 471, "endColumn": 48, "suggestions": "2647"}, {"ruleId": "2369", "severity": 1, "message": "2524", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2648", "line": 9, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 10, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 10, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2649", "line": 11, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2650", "line": 24, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2651", "line": 32, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 32, "endColumn": 20}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 199, "column": 91, "nodeType": "2436", "messageId": "2437", "endLine": 199, "endColumn": 93}, {"ruleId": "2369", "severity": 1, "message": "2652", "line": 467, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 467, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2653", "line": 530, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 530, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2654", "line": 658, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 658, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2421", "line": 13, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 9}, {"ruleId": "2369", "severity": 1, "message": "2484", "line": 17, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2655", "line": 19, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 13}, {"ruleId": "2376", "severity": 1, "message": "2656", "line": 129, "column": 8, "nodeType": "2378", "endLine": 129, "endColumn": 18, "suggestions": "2657"}, {"ruleId": "2369", "severity": 1, "message": "2658", "line": 155, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 155, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2659", "line": 232, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 232, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2604", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2386", "line": 8, "column": 18, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2487", "line": 8, "column": 21, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2660", "line": 15, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2661", "line": 16, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 16, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2662", "line": 26, "column": 24, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 37}, {"ruleId": "2369", "severity": 1, "message": "2663", "line": 111, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 111, "endColumn": 23}, {"ruleId": "2434", "severity": 1, "message": "2435", "line": 49, "column": 108, "nodeType": "2436", "messageId": "2437", "endLine": 49, "endColumn": 110}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 22}, {"ruleId": "2376", "severity": 1, "message": "2664", "line": 40, "column": 8, "nodeType": "2378", "endLine": 40, "endColumn": 30, "suggestions": "2665"}, {"ruleId": "2393", "severity": 1, "message": "2394", "line": 223, "column": 69, "nodeType": "2395", "endLine": 227, "endColumn": 71}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 285, "column": 65, "nodeType": "2436", "messageId": "2437", "endLine": 285, "endColumn": 67}, {"ruleId": "2369", "severity": 1, "message": "2666", "line": 55, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 55, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2643", "line": 55, "column": 32, "nodeType": "2371", "messageId": "2372", "endLine": 55, "endColumn": 42}, {"ruleId": "2369", "severity": 1, "message": "2667", "line": 59, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 59, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2668", "line": 70, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 70, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2669", "line": 82, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 82, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2670", "line": 88, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 88, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2658", "line": 94, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 94, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2671", "line": 7, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2672", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 12}, {"ruleId": "2369", "severity": 1, "message": "2673", "line": 13, "column": 3, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2567", "line": 14, "column": 3, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 20}, {"ruleId": "2434", "severity": 1, "message": "2435", "line": 336, "column": 35, "nodeType": "2436", "messageId": "2437", "endLine": 336, "endColumn": 37}, {"ruleId": "2369", "severity": 1, "message": "2674", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2675", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2676", "line": 17, "column": 26, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 46}, {"ruleId": "2369", "severity": 1, "message": "2677", "line": 24, "column": 96, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 100}, {"ruleId": "2369", "severity": 1, "message": "2480", "line": 24, "column": 102, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 108}, {"ruleId": "2369", "severity": 1, "message": "2398", "line": 26, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2678", "line": 27, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 27, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 34, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 34, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2642", "line": 35, "column": 47, "nodeType": "2371", "messageId": "2372", "endLine": 35, "endColumn": 54}, {"ruleId": "2369", "severity": 1, "message": "2449", "line": 37, "column": 17, "nodeType": "2371", "messageId": "2372", "endLine": 37, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2679", "line": 91, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 91, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2680", "line": 92, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 92, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2681", "line": 98, "column": 20, "nodeType": "2371", "messageId": "2372", "endLine": 98, "endColumn": 31}, {"ruleId": "2369", "severity": 1, "message": "2682", "line": 99, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 99, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2683", "line": 99, "column": 22, "nodeType": "2371", "messageId": "2372", "endLine": 99, "endColumn": 35}, {"ruleId": "2369", "severity": 1, "message": "2684", "line": 100, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 100, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2685", "line": 100, "column": 20, "nodeType": "2371", "messageId": "2372", "endLine": 100, "endColumn": 31}, {"ruleId": "2369", "severity": 1, "message": "2686", "line": 103, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 103, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2687", "line": 104, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 104, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2688", "line": 105, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 105, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2689", "line": 106, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 106, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2690", "line": 136, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 136, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2691", "line": 137, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 137, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2692", "line": 139, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 139, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2693", "line": 139, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 139, "endColumn": 41}, {"ruleId": "2369", "severity": 1, "message": "2694", "line": 167, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 167, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2695", "line": 173, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 173, "endColumn": 33}, {"ruleId": "2376", "severity": 1, "message": "2696", "line": 286, "column": 6, "nodeType": "2378", "endLine": 286, "endColumn": 32, "suggestions": "2697"}, {"ruleId": "2369", "severity": 1, "message": "2698", "line": 364, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 364, "endColumn": 23}, {"ruleId": "2376", "severity": 1, "message": "2699", "line": 742, "column": 6, "nodeType": "2378", "endLine": 742, "endColumn": 25, "suggestions": "2700"}, {"ruleId": "2369", "severity": 1, "message": "2701", "line": 764, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 764, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2702", "line": 776, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 776, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2458", "line": 9, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2703", "line": 11, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2480", "line": 19, "column": 3, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 9}, {"ruleId": "2369", "severity": 1, "message": "2449", "line": 31, "column": 30, "nodeType": "2371", "messageId": "2372", "endLine": 31, "endColumn": 40}, {"ruleId": "2369", "severity": 1, "message": "2703", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2481", "line": 19, "column": 3, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 8}, {"ruleId": "2369", "severity": 1, "message": "2704", "line": 20, "column": 3, "nodeType": "2371", "messageId": "2372", "endLine": 20, "endColumn": 10}, {"ruleId": "2369", "severity": 1, "message": "2705", "line": 30, "column": 36, "nodeType": "2371", "messageId": "2372", "endLine": 30, "endColumn": 56}, {"ruleId": "2369", "severity": 1, "message": "2706", "line": 32, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 32, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2707", "line": 32, "column": 32, "nodeType": "2371", "messageId": "2372", "endLine": 32, "endColumn": 55}, {"ruleId": "2376", "severity": 1, "message": "2427", "line": 35, "column": 29, "nodeType": "2371", "endLine": 35, "endColumn": 40}, {"ruleId": "2376", "severity": 1, "message": "2427", "line": 35, "column": 30, "nodeType": "2371", "endLine": 35, "endColumn": 41}, {"ruleId": "2369", "severity": 1, "message": "2708", "line": 2, "column": 26, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 32}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 347, "column": 68, "nodeType": "2436", "messageId": "2437", "endLine": 347, "endColumn": 70}, {"ruleId": "2434", "severity": 1, "message": "2435", "line": 360, "column": 64, "nodeType": "2436", "messageId": "2437", "endLine": 360, "endColumn": 66}, {"ruleId": "2369", "severity": 1, "message": "2524", "line": 1, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2709", "line": 12, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2420", "line": 24, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2421", "line": 26, "column": 119, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 123}, {"ruleId": "2369", "severity": 1, "message": "2710", "line": 26, "column": 135, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 140}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 99, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 99, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2449", "line": 107, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 107, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2611", "line": 217, "column": 8, "nodeType": "2378", "endLine": 217, "endColumn": 38, "suggestions": "2711"}, {"ruleId": "2376", "severity": 1, "message": "2712", "line": 67, "column": 8, "nodeType": "2378", "endLine": 67, "endColumn": 47, "suggestions": "2713"}, {"ruleId": "2369", "severity": 1, "message": "2396", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2480", "line": 17, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2387", "line": 20, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 20, "endColumn": 9}, {"ruleId": "2369", "severity": 1, "message": "2714", "line": 27, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 27, "endColumn": 32}, {"ruleId": "2376", "severity": 1, "message": "2715", "line": 122, "column": 8, "nodeType": "2378", "endLine": 122, "endColumn": 10, "suggestions": "2716"}, {"ruleId": "2369", "severity": 1, "message": "2487", "line": 6, "column": 56, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 64}, {"ruleId": "2369", "severity": 1, "message": "2517", "line": 6, "column": 73, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 79}, {"ruleId": "2369", "severity": 1, "message": "2482", "line": 6, "column": 81, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 91}, {"ruleId": "2369", "severity": 1, "message": "2717", "line": 5, "column": 60, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 66}, {"ruleId": "2369", "severity": 1, "message": "2604", "line": 5, "column": 68, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 74}, {"ruleId": "2369", "severity": 1, "message": "2718", "line": 2, "column": 42, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 51}, {"ruleId": "2369", "severity": 1, "message": "2719", "line": 5, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 8, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2642", "line": 16, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 16, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2720", "line": 17, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 1, "column": 21, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 29}, {"ruleId": "2613", "severity": 1, "message": "2721", "line": 110, "column": 5, "nodeType": "2615", "messageId": "2437", "endLine": 110, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2722", "line": 151, "column": 33, "nodeType": "2371", "messageId": "2372", "endLine": 151, "endColumn": 47}, {"ruleId": "2723", "severity": 1, "message": "2724", "line": 167, "column": 17, "nodeType": "2725", "messageId": "2726", "endLine": 177, "endColumn": 18}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 280, "column": 50, "nodeType": "2436", "messageId": "2437", "endLine": 280, "endColumn": 52}, {"ruleId": "2369", "severity": 1, "message": "2727", "line": 422, "column": 27, "nodeType": "2371", "messageId": "2372", "endLine": 422, "endColumn": 37}, {"ruleId": "2369", "severity": 1, "message": "2504", "line": 3, "column": 58, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 69}, {"ruleId": "2369", "severity": 1, "message": "2728", "line": 3, "column": 71, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 81}, {"ruleId": "2369", "severity": 1, "message": "2729", "line": 48, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 48, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2730", "line": 56, "column": 9, "nodeType": "2371", "messageId": "2372", "endLine": 56, "endColumn": 29}, {"ruleId": "2376", "severity": 1, "message": "2427", "line": 37, "column": 30, "nodeType": "2371", "endLine": 37, "endColumn": 41}, {"ruleId": "2376", "severity": 1, "message": "2427", "line": 35, "column": 32, "nodeType": "2371", "endLine": 35, "endColumn": 43}, {"ruleId": "2369", "severity": 1, "message": "2487", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2481", "line": 3, "column": 20, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2731", "line": 3, "column": 27, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 38}, {"ruleId": "2369", "severity": 1, "message": "2502", "line": 3, "column": 40, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 52}, {"ruleId": "2369", "severity": 1, "message": "2504", "line": 3, "column": 54, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 65}, {"ruleId": "2369", "severity": 1, "message": "2732", "line": 6, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2733", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2734", "line": 8, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 39}, {"ruleId": "2369", "severity": 1, "message": "2524", "line": 1, "column": 17, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2735", "line": 2, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 26}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 186, "column": 50, "nodeType": "2436", "messageId": "2437", "endLine": 186, "endColumn": 52}, {"ruleId": "2376", "severity": 1, "message": "2736", "line": 57, "column": 8, "nodeType": "2378", "endLine": 57, "endColumn": 38, "suggestions": "2737"}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 2, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2487", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2731", "line": 3, "column": 27, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 38}, {"ruleId": "2369", "severity": 1, "message": "2502", "line": 3, "column": 40, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 52}, {"ruleId": "2369", "severity": 1, "message": "2738", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2739", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2740", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2733", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2741", "line": 9, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2642", "line": 15, "column": 39, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 46}, {"ruleId": "2369", "severity": 1, "message": "2511", "line": 17, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2742", "line": 18, "column": 37, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 48}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 181, "column": 54, "nodeType": "2436", "messageId": "2437", "endLine": 181, "endColumn": 56}, {"ruleId": "2369", "severity": 1, "message": "2743", "line": 79, "column": 29, "nodeType": "2371", "messageId": "2372", "endLine": 79, "endColumn": 45}, {"ruleId": "2369", "severity": 1, "message": "2744", "line": 79, "column": 47, "nodeType": "2371", "messageId": "2372", "endLine": 79, "endColumn": 59}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 1, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2524", "line": 1, "column": 31, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 34}, {"ruleId": "2369", "severity": 1, "message": "2745", "line": 75, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 75, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2642", "line": 75, "column": 30, "nodeType": "2371", "messageId": "2372", "endLine": 75, "endColumn": 37}, {"ruleId": "2369", "severity": 1, "message": "2643", "line": 75, "column": 39, "nodeType": "2371", "messageId": "2372", "endLine": 75, "endColumn": 49}, {"ruleId": "2369", "severity": 1, "message": "2746", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2747", "line": 3, "column": 22, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2748", "line": 3, "column": 31, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 38}, {"ruleId": "2369", "severity": 1, "message": "2749", "line": 3, "column": 40, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 50}, {"ruleId": "2369", "severity": 1, "message": "2750", "line": 7, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2751", "line": 7, "column": 19, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2454", "line": 13, "column": 45, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 54}, {"ruleId": "2369", "severity": 1, "message": "2752", "line": 66, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 66, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2409", "line": 67, "column": 26, "nodeType": "2371", "messageId": "2372", "endLine": 67, "endColumn": 36}, {"ruleId": "2369", "severity": 1, "message": "2753", "line": 2, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 1, "column": 17, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2754", "line": 7, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2755", "line": 7, "column": 19, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2756", "line": 7, "column": 32, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 40}, {"ruleId": "2369", "severity": 1, "message": "2757", "line": 7, "column": 42, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 50}, {"ruleId": "2369", "severity": 1, "message": "2758", "line": 7, "column": 52, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 60}, {"ruleId": "2369", "severity": 1, "message": "2759", "line": 7, "column": 76, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 90}, {"ruleId": "2369", "severity": 1, "message": "2466", "line": 8, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 24}, {"ruleId": "2369", "severity": 1, "message": "2462", "line": 9, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2760", "line": 10, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 10, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2761", "line": 11, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2465", "line": 12, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 13, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2387", "line": 17, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 17, "endColumn": 9}, {"ruleId": "2369", "severity": 1, "message": "2762", "line": 18, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 8}, {"ruleId": "2369", "severity": 1, "message": "2633", "line": 19, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2763", "line": 20, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 20, "endColumn": 9}, {"ruleId": "2369", "severity": 1, "message": "2764", "line": 21, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 21, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2765", "line": 22, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 22, "endColumn": 9}, {"ruleId": "2369", "severity": 1, "message": "2504", "line": 23, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 23, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2502", "line": 24, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 24, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2731", "line": 25, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 25, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2481", "line": 26, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 10}, {"ruleId": "2369", "severity": 1, "message": "2486", "line": 27, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 27, "endColumn": 10}, {"ruleId": "2369", "severity": 1, "message": "2484", "line": 28, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 28, "endColumn": 13}, {"ruleId": "2369", "severity": 1, "message": "2766", "line": 29, "column": 14, "nodeType": "2371", "messageId": "2372", "endLine": 29, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2767", "line": 30, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 30, "endColumn": 11}, {"ruleId": "2369", "severity": 1, "message": "2768", "line": 32, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 32, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2414", "line": 54, "column": 8, "nodeType": "2378", "endLine": 54, "endColumn": 18, "suggestions": "2769"}, {"ruleId": "2369", "severity": 1, "message": "2397", "line": 56, "column": 123, "nodeType": "2371", "messageId": "2372", "endLine": 56, "endColumn": 132}, {"ruleId": "2369", "severity": 1, "message": "2770", "line": 11, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 11, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2454", "line": 96, "column": 29, "nodeType": "2371", "messageId": "2372", "endLine": 96, "endColumn": 38}, {"ruleId": "2369", "severity": 1, "message": "2771", "line": 166, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 166, "endColumn": 18}, {"ruleId": "2376", "severity": 1, "message": "2772", "line": 17, "column": 8, "nodeType": "2378", "endLine": 17, "endColumn": 24, "suggestions": "2773"}, {"ruleId": "2376", "severity": 1, "message": "2774", "line": 17, "column": 9, "nodeType": "2775", "endLine": 17, "endColumn": 16}, {"ruleId": "2369", "severity": 1, "message": "2433", "line": 4, "column": 49, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 61}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 9, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2776", "line": 13, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2777", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2778", "line": 18, "column": 5, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2764", "line": 26, "column": 137, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 143}, {"ruleId": "2369", "severity": 1, "message": "2779", "line": 26, "column": 145, "nodeType": "2371", "messageId": "2372", "endLine": 26, "endColumn": 149}, {"ruleId": "2369", "severity": 1, "message": "2780", "line": 33, "column": 43, "nodeType": "2371", "messageId": "2372", "endLine": 33, "endColumn": 63}, {"ruleId": "2369", "severity": 1, "message": "2649", "line": 36, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 36, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2465", "line": 38, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 38, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2781", "line": 43, "column": 34, "nodeType": "2371", "messageId": "2372", "endLine": 43, "endColumn": 42}, {"ruleId": "2393", "severity": 1, "message": "2394", "line": 482, "column": 33, "nodeType": "2395", "endLine": 486, "endColumn": 35}, {"ruleId": "2369", "severity": 1, "message": "2782", "line": 509, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 509, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2562", "line": 510, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 510, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2783", "line": 523, "column": 8, "nodeType": "2378", "endLine": 523, "endColumn": 118, "suggestions": "2784"}, {"ruleId": "2369", "severity": 1, "message": "2785", "line": 594, "column": 7, "nodeType": "2371", "messageId": "2372", "endLine": 594, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2786", "line": 667, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 667, "endColumn": 25}, {"ruleId": "2369", "severity": 1, "message": "2787", "line": 7, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2788", "line": 8, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2789", "line": 98, "column": 23, "nodeType": "2371", "messageId": "2372", "endLine": 98, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2781", "line": 98, "column": 34, "nodeType": "2371", "messageId": "2372", "endLine": 98, "endColumn": 42}, {"ruleId": "2376", "severity": 1, "message": "2790", "line": 352, "column": 8, "nodeType": "2378", "endLine": 352, "endColumn": 24, "suggestions": "2791"}, {"ruleId": "2369", "severity": 1, "message": "2792", "line": 383, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 383, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 2, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2778", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2465", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2472", "line": 6, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 31}, {"ruleId": "2369", "severity": 1, "message": "2793", "line": 7, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2794", "line": 22, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 22, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2795", "line": 44, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 44, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2796", "line": 110, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 110, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 1, "column": 35, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 44}, {"ruleId": "2369", "severity": 1, "message": "2767", "line": 2, "column": 21, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2797", "line": 12, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2798", "line": 76, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 76, "endColumn": 31}, {"ruleId": "2393", "severity": 1, "message": "2394", "line": 56, "column": 21, "nodeType": "2395", "endLine": 60, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2399", "line": 2, "column": 23, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 34}, {"ruleId": "2369", "severity": 1, "message": "2465", "line": 18, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 18, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2799", "line": 53, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 53, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2800", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 32}, {"ruleId": "2369", "severity": 1, "message": "2801", "line": 4, "column": 34, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 52}, {"ruleId": "2369", "severity": 1, "message": "2802", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2803", "line": 5, "column": 30, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 44}, {"ruleId": "2369", "severity": 1, "message": "2804", "line": 7, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2805", "line": 118, "column": 29, "nodeType": "2371", "messageId": "2372", "endLine": 118, "endColumn": 35}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 74, "column": 47, "nodeType": "2436", "messageId": "2437", "endLine": 74, "endColumn": 49}, {"ruleId": "2434", "severity": 1, "message": "2435", "line": 160, "column": 70, "nodeType": "2436", "messageId": "2437", "endLine": 160, "endColumn": 72}, {"ruleId": "2434", "severity": 1, "message": "2435", "line": 164, "column": 100, "nodeType": "2436", "messageId": "2437", "endLine": 164, "endColumn": 102}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 166, "column": 38, "nodeType": "2436", "messageId": "2437", "endLine": 166, "endColumn": 40}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 1, "column": 88, "nodeType": "2436", "messageId": "2437", "endLine": 1, "endColumn": 90}, {"ruleId": "2376", "severity": 1, "message": "2806", "line": 31, "column": 8, "nodeType": "2378", "endLine": 31, "endColumn": 33, "suggestions": "2807"}, {"ruleId": "2369", "severity": 1, "message": "2451", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2384", "line": 4, "column": 21, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 29}, {"ruleId": "2369", "severity": 1, "message": "2808", "line": 28, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 28, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2651", "line": 5, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2511", "line": 10, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 10, "endColumn": 23}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 287, "column": 56, "nodeType": "2436", "messageId": "2437", "endLine": 287, "endColumn": 58}, {"ruleId": "2369", "severity": 1, "message": "2809", "line": 7, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2592", "line": 8, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 23}, {"ruleId": "2369", "severity": 1, "message": "2810", "line": 13, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 27}, {"ruleId": "2369", "severity": 1, "message": "2811", "line": 20, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 20, "endColumn": 27}, {"ruleId": "2434", "severity": 1, "message": "2512", "line": 26, "column": 18, "nodeType": "2436", "messageId": "2437", "endLine": 26, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2812", "line": 29, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 29, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2813", "line": 15, "column": 25, "nodeType": "2371", "messageId": "2372", "endLine": 15, "endColumn": 30}, {"ruleId": "2369", "severity": 1, "message": "2814", "line": 40, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 40, "endColumn": 26}, {"ruleId": "2369", "severity": 1, "message": "2640", "line": 12, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 12, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2636", "line": 4, "column": 33, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 37}, {"ruleId": "2376", "severity": 1, "message": "2815", "line": 43, "column": 8, "nodeType": "2378", "endLine": 43, "endColumn": 19, "suggestions": "2816"}, {"ruleId": "2376", "severity": 1, "message": "2611", "line": 50, "column": 8, "nodeType": "2378", "endLine": 50, "endColumn": 18, "suggestions": "2817"}, {"ruleId": "2369", "severity": 1, "message": "2503", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2818", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2808", "line": 14, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2819", "line": 14, "column": 51, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 64}, {"ruleId": "2369", "severity": 1, "message": "2465", "line": 2, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2820", "line": 3, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2503", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2818", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2821", "line": 6, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2822", "line": 14, "column": 33, "nodeType": "2371", "messageId": "2372", "endLine": 14, "endColumn": 42}, {"ruleId": "2369", "severity": 1, "message": "2823", "line": 4, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 28}, {"ruleId": "2369", "severity": 1, "message": "2808", "line": 41, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 41, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2399", "line": 1, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 1, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2381", "line": 2, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 22}, {"ruleId": "2369", "severity": 1, "message": "2503", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2818", "line": 5, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 5, "endColumn": 20}, {"ruleId": "2369", "severity": 1, "message": "2822", "line": 13, "column": 33, "nodeType": "2371", "messageId": "2372", "endLine": 13, "endColumn": 42}, {"ruleId": "2369", "severity": 1, "message": "2399", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 21}, {"ruleId": "2369", "severity": 1, "message": "2636", "line": 2, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 14}, {"ruleId": "2369", "severity": 1, "message": "2824", "line": 6, "column": 8, "nodeType": "2371", "messageId": "2372", "endLine": 6, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2642", "line": 19, "column": 12, "nodeType": "2371", "messageId": "2372", "endLine": 19, "endColumn": 19}, {"ruleId": "2376", "severity": 1, "message": "2825", "line": 40, "column": 8, "nodeType": "2378", "endLine": 40, "endColumn": 34, "suggestions": "2826"}, {"ruleId": "2376", "severity": 1, "message": "2827", "line": 40, "column": 9, "nodeType": "2828", "endLine": 40, "endColumn": 33}, {"ruleId": "2369", "severity": 1, "message": "2829", "line": 3, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 3, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2762", "line": 4, "column": 42, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 45}, {"ruleId": "2369", "severity": 1, "message": "2422", "line": 7, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 7, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2562", "line": 8, "column": 11, "nodeType": "2371", "messageId": "2372", "endLine": 8, "endColumn": 19}, {"ruleId": "2369", "severity": 1, "message": "2640", "line": 9, "column": 13, "nodeType": "2371", "messageId": "2372", "endLine": 9, "endColumn": 17}, {"ruleId": "2369", "severity": 1, "message": "2503", "line": 4, "column": 10, "nodeType": "2371", "messageId": "2372", "endLine": 4, "endColumn": 18}, {"ruleId": "2369", "severity": 1, "message": "2399", "line": 2, "column": 23, "nodeType": "2371", "messageId": "2372", "endLine": 2, "endColumn": 34}, {"ruleId": "2376", "severity": 1, "message": "2830", "line": 49, "column": 8, "nodeType": "2378", "endLine": 49, "endColumn": 16, "suggestions": "2831"}, {"ruleId": "2393", "severity": 1, "message": "2394", "line": 298, "column": 41, "nodeType": "2395", "endLine": 302, "endColumn": 43}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2832"], "'resultAction' is assigned a value but never used.", "'LoadingSpinner' is defined but never used.", "'useRef' is defined but never used.", "'headerHeight' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2833"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'AdminLayout' is defined but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'isAddView' is assigned a value but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'useNavigate' is defined but never used.", "React Hook useEffect has a missing dependency: 'folder'. Either include it or remove the dependency array.", ["2834"], "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'handleClickedTracking' is assigned a value but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'showAddStudent' is assigned a value but never used.", "'setIsFilterView' is defined but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2835"], "'setExam' is defined but never used.", "'errorMsg' is assigned a value but never used.", "'getQuestionAndAnswersByAttemptAPI' is defined but never used.", "'setQuestions' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2836"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2837"], "'totalPages' is assigned a value but never used.", "'processInputForUpdate' is defined but never used.", "'useEffect' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'resetFilters' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2838"], "'setSortOrder' is defined but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2839"], "'LatexRenderer' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2840"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'setClass' is defined but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2841"], "'setDeleteMode' is assigned a value but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2842"], "'result' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2843"], "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'ExamDefaultImage' is defined but never used.", "'ChevronRight' is defined but never used.", "'Bookmark' is defined but never used.", "'CheckCircle' is defined but never used.", "'formatDate' is assigned a value but never used.", "'createdAt' is assigned a value but never used.", "'imageUrl' is assigned a value but never used.", "'isSave' is assigned a value but never used.", "'isDone' is assigned a value but never used.", "'acceptDoExam' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'LogOut' is defined but never used.", "'isModalOpen' is assigned a value but never used.", "'setIsModalOpen' is assigned a value but never used.", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2844"], "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'use' is defined but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'User' is defined but never used.", "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2845"], "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2846"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2847"], "no-useless-escape", "Unnecessary escape character: \\}.", "Literal", "unnecessaryEscape", ["2848", "2849"], "Unnecessary escape character: \\{.", ["2850", "2851"], ["2852", "2853"], ["2854", "2855"], "Unnecessary escape character: \\..", ["2856", "2857"], ["2858", "2859"], ["2860", "2861"], "Unnecessary escape character: \\).", ["2862", "2863"], ["2864", "2865"], ["2866", "2867"], ["2868", "2869"], ["2870", "2871"], ["2872", "2873"], ["2874", "2875"], ["2876", "2877"], ["2878", "2879"], ["2880", "2881"], ["2882", "2883"], "'InputSearch' is defined but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'motion' is defined but never used.", "'AnimatePresence' is defined but never used.", "'fetchUnreadCount' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'isMobile' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2884"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2885"], "React Hook useEffect has a missing dependency: 'classDetail'. Either include it or remove the dependency array.", ["2886"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'ClassImage' is defined but never used.", "'Filter' is defined but never used.", "'Loader' is defined but never used.", "'ChevronDown' is defined but never used.", "'showSortDropdown' is assigned a value but never used.", "'choice' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2887"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2888"], "no-dupe-keys", "Duplicate key 'sort'.", "ObjectExpression", "React Hook useEffect has a duplicate dependency: 'sort'. Either omit it or remove the dependency array.", ["2889"], "'resetClassDetail' is defined but never used.", "React Hook useEffect has a missing dependency: 'classCode'. Either include it or remove the dependency array.", ["2890"], "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2891"], "'startTime' is assigned a value but never used.", "'user' is assigned a value but never used.", "'remainingTimeRef' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["2892"], "'fetchExamRatingStatistics' is defined but never used.", "'saveExamForUser' is defined but never used.", "'rateExamForUser' is defined but never used.", "'setStar' is defined but never used.", "'StarIcon' is defined but never used.", "'FileText' is defined but never used.", "'QrCodeIcon' is defined but never used.", "'Pin' is defined but never used.", "'Send' is defined but never used.", "'Smile' is defined but never used.", "'Share2' is defined but never used.", "'setCurrentPage' is defined but never used.", "'exam' is assigned a value but never used.", "'view' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'loadingRatingStatistics' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleClickHistory', 'handleClickPreviewExam', and 'handleClickRanking'. Either include them or remove the dependency array.", ["2893"], "'useMemo' is defined but never used.", "'PdfViewer' is defined but never used.", "'BarChart3' is defined but never used.", "'formatTime' is defined but never used.", "'BackButton' is assigned a value but never used.", "'StatisticsSection' is assigned a value but never used.", "'codes' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2894"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2895"], "'articles' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2896"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2897"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", ["2898"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2899"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2900"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "Duplicate key 'startTime'.", "'typeOfQuestion' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'wasInError' is assigned a value but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'NavigateTimeButton' is defined but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2901"], "'UserLayout' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "'recentActivities' is assigned a value but never used.", "'systemStatus' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", "'examApi' is defined but never used.", "'setStep' is defined but never used.", "'setExamData' is defined but never used.", "'postExam' is defined but never used.", "'nextStep' is defined but never used.", "'prevStep' is defined but never used.", "'setCreatedExam' is defined but never used.", "'ImageUpload' is defined but never used.", "'UploadPdf' is defined but never used.", "'Eye' is defined but never used.", "'Plus' is defined but never used.", "'Trash2' is defined but never used.", "'Edit' is defined but never used.", "'ImageIcon' is defined but never used.", "'Upload' is defined but never used.", "'resetData' is defined but never used.", ["2902"], "'Pagination' is defined but never used.", "'staff' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'effect'. Either include it or remove the dependency array. If 'effect' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2903"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", "'classifyResult' is assigned a value but never used.", "'setQuestion' is defined but never used.", "'setSelectedIndex' is defined but never used.", "'Info' is defined but never used.", "'splitMarkdownToParts' is defined but never used.", "'examFile' is assigned a value but never used.", "'markDownExam' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isViewAdd'. Either include it or remove the dependency array.", ["2904"], "'ListQuestions' is assigned a value but never used.", "'questionCount' is assigned a value but never used.", "'questionUntil' is defined but never used.", "'useDebouncedEffect' is defined but never used.", "'examImage' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'questions'. Either include them or remove the dependency array.", ["2905"], "'examData' is assigned a value but never used.", "'reorderStatements' is defined but never used.", "'QuestionContent' is defined but never used.", "'prefixTN' is assigned a value but never used.", "'prefixDS' is assigned a value but never used.", "'fixTextResult' is assigned a value but never used.", "'handleTextareaChange' is assigned a value but never used.", "'questionId' is assigned a value but never used.", "'initialPaginationState' is defined but never used.", "'paginationReducers' is defined but never used.", "'initialFilterState' is defined but never used.", "'filterReducers' is defined but never used.", "'examCommentsApi' is defined but never used.", "'examId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'extendedOptions'. Either include it or remove the dependency array.", ["2906"], "'darkMode' is assigned a value but never used.", "'LoadingData' is defined but never used.", "'showEmojiPicker' is assigned a value but never used.", "'handleEmojiClick' is assigned a value but never used.", "'handleSend' is assigned a value but never used.", "'Medal' is defined but never used.", "'getRandomAvatar' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'thinkingMessages.length'. Either include it or remove the dependency array.", ["2907"], ["2908"], "'ReportButton' is defined but never used.", "'saveQuestions' is assigned a value but never used.", "'QuestionImage' is defined but never used.", "'NoTranslate' is defined but never used.", "'imageSize' is assigned a value but never used.", "'QuestionSectionTitle' is defined but never used.", "'EmojiPicker' is defined but never used.", "React Hook useEffect has missing dependencies: 'comment.id' and 'repliesState'. Either include them or remove the dependency array. If 'setReplies' needs the current value of 'comment.id', you can also switch to useReducer instead of useState and read 'comment.id' in the reducer.", ["2909"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "'setView' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleImageFile'. Either include it or remove the dependency array.", ["2910"], {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"desc": "2921", "fix": "2922"}, {"desc": "2923", "fix": "2924"}, {"desc": "2925", "fix": "2926"}, {"desc": "2927", "fix": "2928"}, {"desc": "2929", "fix": "2930"}, {"desc": "2931", "fix": "2932"}, {"desc": "2933", "fix": "2934"}, {"desc": "2935", "fix": "2936"}, {"desc": "2937", "fix": "2938"}, {"desc": "2939", "fix": "2940"}, {"desc": "2941", "fix": "2942"}, {"messageId": "2943", "fix": "2944", "desc": "2945"}, {"messageId": "2946", "fix": "2947", "desc": "2948"}, {"messageId": "2943", "fix": "2949", "desc": "2945"}, {"messageId": "2946", "fix": "2950", "desc": "2948"}, {"messageId": "2943", "fix": "2951", "desc": "2945"}, {"messageId": "2946", "fix": "2952", "desc": "2948"}, {"messageId": "2943", "fix": "2953", "desc": "2945"}, {"messageId": "2946", "fix": "2954", "desc": "2948"}, {"messageId": "2943", "fix": "2955", "desc": "2945"}, {"messageId": "2946", "fix": "2956", "desc": "2948"}, {"messageId": "2943", "fix": "2957", "desc": "2945"}, {"messageId": "2946", "fix": "2958", "desc": "2948"}, {"messageId": "2943", "fix": "2959", "desc": "2945"}, {"messageId": "2946", "fix": "2960", "desc": "2948"}, {"messageId": "2943", "fix": "2961", "desc": "2945"}, {"messageId": "2946", "fix": "2962", "desc": "2948"}, {"messageId": "2943", "fix": "2963", "desc": "2945"}, {"messageId": "2946", "fix": "2964", "desc": "2948"}, {"messageId": "2943", "fix": "2965", "desc": "2945"}, {"messageId": "2946", "fix": "2966", "desc": "2948"}, {"messageId": "2943", "fix": "2967", "desc": "2945"}, {"messageId": "2946", "fix": "2968", "desc": "2948"}, {"messageId": "2943", "fix": "2969", "desc": "2945"}, {"messageId": "2946", "fix": "2970", "desc": "2948"}, {"messageId": "2943", "fix": "2971", "desc": "2945"}, {"messageId": "2946", "fix": "2972", "desc": "2948"}, {"messageId": "2943", "fix": "2973", "desc": "2945"}, {"messageId": "2946", "fix": "2974", "desc": "2948"}, {"messageId": "2943", "fix": "2975", "desc": "2945"}, {"messageId": "2946", "fix": "2976", "desc": "2948"}, {"messageId": "2943", "fix": "2977", "desc": "2945"}, {"messageId": "2946", "fix": "2978", "desc": "2948"}, {"messageId": "2943", "fix": "2979", "desc": "2945"}, {"messageId": "2946", "fix": "2980", "desc": "2948"}, {"messageId": "2943", "fix": "2981", "desc": "2945"}, {"messageId": "2946", "fix": "2982", "desc": "2948"}, {"desc": "2983", "fix": "2984"}, {"desc": "2985", "fix": "2986"}, {"desc": "2987", "fix": "2988"}, {"desc": "2989", "fix": "2990"}, {"desc": "2991", "fix": "2992"}, {"desc": "2993", "fix": "2994"}, {"desc": "2995", "fix": "2996"}, {"desc": "2997", "fix": "2998"}, {"desc": "2999", "fix": "3000"}, {"desc": "3001", "fix": "3002"}, {"desc": "3003", "fix": "3004"}, {"desc": "3005", "fix": "3006"}, {"desc": "3007", "fix": "3008"}, {"desc": "3009", "fix": "3010"}, {"desc": "3011", "fix": "3012"}, {"desc": "3013", "fix": "3014"}, {"desc": "3015", "fix": "3016"}, {"desc": "3017", "fix": "3018"}, {"desc": "2915", "fix": "3019"}, {"desc": "3020", "fix": "3021"}, {"desc": "3022", "fix": "3023"}, {"desc": "3024", "fix": "3025"}, {"desc": "3026", "fix": "3027"}, {"desc": "3028", "fix": "3029"}, {"desc": "3030", "fix": "3031"}, {"desc": "3032", "fix": "3033"}, {"desc": "3034", "fix": "3035"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "3036", "text": "3037"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "3038", "text": "3039"}, "Update the dependencies array to be: [dispatch, folder]", {"range": "3040", "text": "3041"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "3042", "text": "3043"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "3044", "text": "3045"}, "Update the dependencies array to be: [options, selected, type]", {"range": "3046", "text": "3047"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "3048", "text": "3049"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "3050", "text": "3051"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "3052", "text": "3053"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "3054", "text": "3055"}, "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "3056", "text": "3057"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "3058", "text": "3059"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "3060", "text": "3061"}, "Update the dependencies array to be: [handlePaste]", {"range": "3062", "text": "3063"}, "Update the dependencies array to be: [maxLength]", {"range": "3064", "text": "3065"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "3066", "text": "3067"}, "removeEscape", {"range": "3068", "text": "3069"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3070", "text": "3071"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "3072", "text": "3069"}, {"range": "3073", "text": "3071"}, {"range": "3074", "text": "3069"}, {"range": "3075", "text": "3071"}, {"range": "3076", "text": "3069"}, {"range": "3077", "text": "3071"}, {"range": "3078", "text": "3069"}, {"range": "3079", "text": "3071"}, {"range": "3080", "text": "3069"}, {"range": "3081", "text": "3071"}, {"range": "3082", "text": "3069"}, {"range": "3083", "text": "3071"}, {"range": "3084", "text": "3069"}, {"range": "3085", "text": "3071"}, {"range": "3086", "text": "3069"}, {"range": "3087", "text": "3071"}, {"range": "3088", "text": "3069"}, {"range": "3089", "text": "3071"}, {"range": "3090", "text": "3069"}, {"range": "3091", "text": "3071"}, {"range": "3092", "text": "3069"}, {"range": "3093", "text": "3071"}, {"range": "3094", "text": "3069"}, {"range": "3095", "text": "3071"}, {"range": "3096", "text": "3069"}, {"range": "3097", "text": "3071"}, {"range": "3098", "text": "3069"}, {"range": "3099", "text": "3071"}, {"range": "3100", "text": "3069"}, {"range": "3101", "text": "3071"}, {"range": "3102", "text": "3069"}, {"range": "3103", "text": "3071"}, {"range": "3104", "text": "3069"}, {"range": "3105", "text": "3071"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "3106", "text": "3107"}, "Update the dependencies array to be: [handleFile]", {"range": "3108", "text": "3109"}, "Update the dependencies array to be: [classDetail, dispatch]", {"range": "3110", "text": "3111"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "3112", "text": "3113"}, "Update the dependencies array to be: [codes, dispatch, grade]", {"range": "3114", "text": "3115"}, "Update the dependencies array to be: [dispatch, search, page, sort, typeOfExam, grade, chapter, view, isClassroomExam, year, firstLoad]", {"range": "3116", "text": "3117"}, "Update the dependencies array to be: [lessonId, classDetail, sortedLessons, learningItemId, firstHandled, classCode]", {"range": "3118", "text": "3119"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "3120", "text": "3121"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, isTimeUp, resetDone, handleSubmit]", {"range": "3122", "text": "3123"}, "Update the dependencies array to be: [location.search, dispatch, firstRender, handleClickRanking, handleClickPreviewExam, handleClickHistory]", {"range": "3124", "text": "3125"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "3126", "text": "3127"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "3128", "text": "3129"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "3130", "text": "3131"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "3132", "text": "3133"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "3134", "text": "3135"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "3136", "text": "3137"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "3138", "text": "3139"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "3140", "text": "3141"}, {"range": "3142", "text": "3041"}, "Update the dependencies array to be: [delay, effect]", {"range": "3143", "text": "3144"}, "Update the dependencies array to be: [questionT<PERSON>ontent, correctAnswerTN, questionDS<PERSON>ontent, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", {"range": "3145", "text": "3146"}, "Update the dependencies array to be: [classifyResult, dispatch, questions]", {"range": "3147", "text": "3148"}, "Update the dependencies array to be: [selected, filterOptions, extendedOptions]", {"range": "3149", "text": "3150"}, "Update the dependencies array to be: [aiLoading, thinkingMessages.length]", {"range": "3151", "text": "3152"}, "Update the dependencies array to be: [dispatch, question]", {"range": "3153", "text": "3154"}, "Update the dependencies array to be: [comment.id, repliesState]", {"range": "3155", "text": "3156"}, "Update the dependencies array to be: [handleImageFile, isOpen]", {"range": "3157", "text": "3158"}, [1894, 1910], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [1469, 1479], "[dispatch, folder]", [9548, 9558], "[dispatch, fetchPdfFiles]", [2683, 2705], "[inputValue, dispatch, setSearch]", [669, 671], "[options, selected, type]", [1903, 1921], "[dispatch, fetchQuestions, params]", [2124, 2148], "[codes, question, question.class]", [2394, 2414], "[codes, exam, exam?.class]", [1569, 1614], "[dispatch, search, page, pageSize, sortOrder, classId]", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [2566, 2568], "[handlePaste]", [972, 974], "[maxLength]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [7921, 7922], "", [7921, 7921], "\\", [7923, 7924], [7923, 7923], [8024, 8025], [8024, 8024], [8026, 8027], [8026, 8026], [9835, 9836], [9835, 9835], [10623, 10624], [10623, 10623], [11254, 11255], [11254, 11254], [11256, 11257], [11256, 11256], [11326, 11327], [11326, 11326], [11328, 11329], [11328, 11328], [14248, 14249], [14248, 14248], [15420, 15421], [15420, 15420], [16026, 16027], [16026, 16026], [16028, 16029], [16028, 16028], [16098, 16099], [16098, 16098], [16100, 16101], [16100, 16100], [19010, 19011], [19010, 19010], [19956, 19957], [19956, 19956], [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [14882, 14892], "[classDetail, dispatch]", [4183, 4193], "[dispatch, limit]", [8245, 8259], "[codes, dispatch, grade]", [8578, 8682], "[dispatch, search, page, sort, typeOfExam, grade, chapter, view, isClassroomExam, year, firstLoad]", [5970, 6038], "[lessonId, classDetail, sortedLessons, learningItemId, firstHandled, classCode]", [7974, 7987], "[activeItem?.index, classDetail]", [5971, 6083], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, isTimeUp, resetDone, handleSubmit]", [19570, 19610], "[location.search, dispatch, firstRender, handleClickRanking, handleClickPreviewExam, handleClickHistory]", [6098, 6108], "[dispatch, location.search]", [1654, 1676], "[currentPage, didInit, loadReports]", [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2199, 2238], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [2393, 2403], [511, 527], "[delay, effect]", [23156, 23266], "[question<PERSON><PERSON><PERSON><PERSON>, correctAnswerTN, question<PERSON><PERSON><PERSON>nt, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", [17182, 17198], "[classifyResult, dispatch, questions]", [995, 1020], "[selected, filterOptions, extendedOptions]", [1888, 1899], "[ai<PERSON><PERSON><PERSON>, thinkingMessages.length]", [2019, 2029], "[dispatch, question]", [1560, 1586], "[comment.id, repliesState]", [2113, 2121], "[handleImageFile, isOpen]"]