/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { betaLibrariesDocumentsDelete } from "../funcs/betaLibrariesDocumentsDelete.js";
import { betaLibrariesDocumentsExtractedTextSignedUrl } from "../funcs/betaLibrariesDocumentsExtractedTextSignedUrl.js";
import { betaLibrariesDocumentsGet } from "../funcs/betaLibrariesDocumentsGet.js";
import { betaLibrariesDocumentsGetSignedUrl } from "../funcs/betaLibrariesDocumentsGetSignedUrl.js";
import { betaLibrariesDocumentsList } from "../funcs/betaLibrariesDocumentsList.js";
import { betaLibrariesDocumentsReprocess } from "../funcs/betaLibrariesDocumentsReprocess.js";
import { betaLibrariesDocumentsStatus } from "../funcs/betaLibrariesDocumentsStatus.js";
import { betaLibrariesDocumentsTextContent } from "../funcs/betaLibrariesDocumentsTextContent.js";
import { betaLibrariesDocumentsUpdate } from "../funcs/betaLibrariesDocumentsUpdate.js";
import { betaLibrariesDocumentsUpload } from "../funcs/betaLibrariesDocumentsUpload.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
import { unwrapAsync } from "../types/fp.js";

export class Documents extends ClientSDK {
  /**
   * List document in a given library.
   *
   * @remarks
   * Given a library, lists the document that have been uploaded to that library.
   */
  async list(
    request: operations.LibrariesDocumentsListV1Request,
    options?: RequestOptions,
  ): Promise<components.ListDocumentOut> {
    return unwrapAsync(betaLibrariesDocumentsList(
      this,
      request,
      options,
    ));
  }

  /**
   * Upload a new document.
   *
   * @remarks
   * Given a library, upload a new document to that library. It is queued for processing, it status will change it has been processed. The processing has to be completed in order be discoverable for the library search
   */
  async upload(
    request: operations.LibrariesDocumentsUploadV1Request,
    options?: RequestOptions,
  ): Promise<components.DocumentOut> {
    return unwrapAsync(betaLibrariesDocumentsUpload(
      this,
      request,
      options,
    ));
  }

  /**
   * Retrieve the metadata of a specific document.
   *
   * @remarks
   * Given a library and a document in this library, you can retrieve the metadata of that document.
   */
  async get(
    request: operations.LibrariesDocumentsGetV1Request,
    options?: RequestOptions,
  ): Promise<components.DocumentOut> {
    return unwrapAsync(betaLibrariesDocumentsGet(
      this,
      request,
      options,
    ));
  }

  /**
   * Update the metadata of a specific document.
   *
   * @remarks
   * Given a library and a document in that library, update the name of that document.
   */
  async update(
    request: operations.LibrariesDocumentsUpdateV1Request,
    options?: RequestOptions,
  ): Promise<components.DocumentOut> {
    return unwrapAsync(betaLibrariesDocumentsUpdate(
      this,
      request,
      options,
    ));
  }

  /**
   * Delete a document.
   *
   * @remarks
   * Given a library and a document in that library, delete that document. The document will be deleted from the library and the search index.
   */
  async delete(
    request: operations.LibrariesDocumentsDeleteV1Request,
    options?: RequestOptions,
  ): Promise<void> {
    return unwrapAsync(betaLibrariesDocumentsDelete(
      this,
      request,
      options,
    ));
  }

  /**
   * Retrieve the text content of a specific document.
   *
   * @remarks
   * Given a library and a document in that library, you can retrieve the text content of that document if it exists. For documents like pdf, docx and pptx the text content results from our processing using Mistral OCR.
   */
  async textContent(
    request: operations.LibrariesDocumentsGetTextContentV1Request,
    options?: RequestOptions,
  ): Promise<components.DocumentTextContent> {
    return unwrapAsync(betaLibrariesDocumentsTextContent(
      this,
      request,
      options,
    ));
  }

  /**
   * Retrieve the processing status of a specific document.
   *
   * @remarks
   * Given a library and a document in that library, retrieve the processing status of that document.
   */
  async status(
    request: operations.LibrariesDocumentsGetStatusV1Request,
    options?: RequestOptions,
  ): Promise<components.ProcessingStatusOut> {
    return unwrapAsync(betaLibrariesDocumentsStatus(
      this,
      request,
      options,
    ));
  }

  /**
   * Retrieve the signed URL of a specific document.
   *
   * @remarks
   * Given a library and a document in that library, retrieve the signed URL of a specific document.The url will expire after 30 minutes and can be accessed by anyone with the link.
   */
  async getSignedUrl(
    request: operations.LibrariesDocumentsGetSignedUrlV1Request,
    options?: RequestOptions,
  ): Promise<string> {
    return unwrapAsync(betaLibrariesDocumentsGetSignedUrl(
      this,
      request,
      options,
    ));
  }

  /**
   * Retrieve the signed URL of text extracted from a given document.
   *
   * @remarks
   * Given a library and a document in that library, retrieve the signed URL of text extracted. For documents that are sent to the OCR this returns the result of the OCR queries.
   */
  async extractedTextSignedUrl(
    request: operations.LibrariesDocumentsGetExtractedTextSignedUrlV1Request,
    options?: RequestOptions,
  ): Promise<string> {
    return unwrapAsync(betaLibrariesDocumentsExtractedTextSignedUrl(
      this,
      request,
      options,
    ));
  }

  /**
   * Reprocess a document.
   *
   * @remarks
   * Given a library and a document in that library, reprocess that document, it will be billed again.
   */
  async reprocess(
    request: operations.LibrariesDocumentsReprocessV1Request,
    options?: RequestOptions,
  ): Promise<void> {
    return unwrapAsync(betaLibrariesDocumentsReprocess(
      this,
      request,
      options,
    ));
  }
}
