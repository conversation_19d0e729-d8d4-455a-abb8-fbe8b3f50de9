"use strict";

export default {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert("question", [
      {
        class: "10",
        content: "<PERSON><PERSON><PERSON><PERSON> phương trình bậc hai: x² - 5x + 6 = 0",
        typeOfQuestion: "TN",
        correctAnswer: "B",
        solution: "Phương trình có hai nghiệm x = 2 và x = 3.",
        difficulty: "TH",
        chapter: "12C1",
        description: "Câu hỏi về phương trình bậc hai",
        solutionUrl: "https://example.com/solution1",
        imageUrl: "https://example.com/image1.jpg",
        solutionImageUrl: "https://example.com/solution_image1.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "11",
        content: "Tính đạo hàm của hàm số f(x) = x³ + 2x + 1",
        typeOfQuestion: "TLN",
        correctAnswer: "f'(x) = 3x² + 2",
        solution: "Đạo hàm của x³ là 3x², của 2x là 2, của 1 là 0.",
        difficulty: "VD",
        chapter: "12C2",
        description: "Câu hỏi đạo hàm cơ bản",
        solutionUrl: "https://example.com/solution2",
        imageUrl: "https://example.com/image2.jpg",
        solutionImageUrl: "https://example.com/solution_image2.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "12",
        content: "Hàm số nào sau đây đồng biến trên khoảng (0; +∞)?",
        typeOfQuestion: "DS",
        correctAnswer: "y = x³",
        solution: "Vì đạo hàm luôn dương trên khoảng (0; +∞).",
        difficulty: "TH",
        chapter: "12C3",
        description: "Tính chất hàm số",
        solutionUrl: "https://example.com/solution3",
        imageUrl: "https://example.com/image3.jpg",
        solutionImageUrl: "https://example.com/solution_image3.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "10",
        content: "Tính giá trị biểu thức: √(49) + 3²",
        typeOfQuestion: "TN",
        correctAnswer: "16",
        solution: "√(49) = 7, 3² = 9 → Tổng = 16.",
        difficulty: "TH",
        chapter: "12C1",
        description: "Biểu thức căn và mũ",
        solutionUrl: "https://example.com/solution4",
        imageUrl: "https://example.com/image4.jpg",
        solutionImageUrl: "https://example.com/solution_image4.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "12",
        content: "Tìm giới hạn lim(x→∞) (1 + 1/x)^x",
        typeOfQuestion: "TLN",
        correctAnswer: "e",
        solution: "Đây là giới hạn đặc trưng, kết quả là số e.",
        difficulty: "Khó",
        chapter: "12C4",
        description: "Giới hạn đặc biệt",
        solutionUrl: "https://example.com/solution5",
        imageUrl: "https://example.com/image5.jpg",
        solutionImageUrl: "https://example.com/solution_image5.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "11",
        content: "Cho cấp số cộng có u₁ = 2, công sai d = 3. Tính u₅.",
        typeOfQuestion: "TN",
        correctAnswer: "14",
        solution: "u₅ = u₁ + (5-1)d = 2 + 4*3 = 14",
        difficulty: "VD",
        chapter: "12C3",
        description: "Cấp số cộng",
        solutionUrl: "https://example.com/solution6",
        imageUrl: "https://example.com/image6.jpg",
        solutionImageUrl: "https://example.com/solution_image6.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "12",
        content: "Hàm số y = ln(x) xác định khi nào?",
        typeOfQuestion: "DS",
        correctAnswer: "Khi x > 0",
        solution: "Hàm log tự nhiên chỉ xác định với x > 0.",
        difficulty: "TH",
        chapter: "12C2",
        description: "Miền xác định của hàm logarit",
        solutionUrl: "https://example.com/solution7",
        imageUrl: "https://example.com/image7.jpg",
        solutionImageUrl: "https://example.com/solution_image7.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "10",
        content: "Số nghiệm của phương trình sin(x) = 0 trong [0; 2π] là:",
        typeOfQuestion: "TN",
        correctAnswer: "3",
        solution: "Các nghiệm là 0, π, 2π.",
        difficulty: "TH",
        chapter: "12C4",
        description: "Phương trình lượng giác cơ bản",
        solutionUrl: "https://example.com/solution8",
        imageUrl: "https://example.com/image8.jpg",
        solutionImageUrl: "https://example.com/solution_image8.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "11",
        content: "Cho hàm số f(x) = x². Tính tích phân từ 0 đến 2.",
        typeOfQuestion: "TLN",
        correctAnswer: "8/3",
        solution: "∫₀² x² dx = [x³/3]₀² = (8/3 - 0) = 8/3",
        difficulty: "Khó",
        chapter: "12C5",
        description: "Tính tích phân cơ bản",
        solutionUrl: "https://example.com/solution9",
        imageUrl: "https://example.com/image9.jpg",
        solutionImageUrl: "https://example.com/solution_image9.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        class: "12",
        content: "Tính đạo hàm cấp hai của y = x⁴",
        typeOfQuestion: "TLN",
        correctAnswer: "12x²",
        solution: "y' = 4x³ ⇒ y'' = 12x²",
        difficulty: "VD",
        chapter: "12C1",
        description: "Đạo hàm bậc cao",
        solutionUrl: "https://example.com/solution10",
        imageUrl: "https://example.com/image10.jpg",
        solutionImageUrl: "https://example.com/solution_image10.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete("question", null, {});
  },
};
