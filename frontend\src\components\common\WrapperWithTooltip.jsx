import React from "react";

const Tooltip = ({ title, children }) => {
    return (
        <div className="relative group inline-block">
            {children}
            <div className="absolute top-full left-1/2 -translate-x-1/2 mt-2 w-max px-2 py-1 rounded bg-gray-800 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 pointer-events-none">
                {title}
            </div>
        </div>
    );
};

export default Tooltip;
