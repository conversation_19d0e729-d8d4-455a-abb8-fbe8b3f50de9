import StarRating from "src/components/StarRating";
import { FileText, Eye, Pin, Share2, Star as StarIcon } from "lucide-react";
import LoadingText from "src/components/loading/LoadingText";
import { useSelector, useDispatch } from "react-redux";
import { saveExamForUser, rateExamForUser } from "src/features/exam/examDetailSlice";
import { setStar } from "src/features/exam/examDetailSlice";
import { useEffect } from "react";
import { fetchExamRatingStatistics } from "src/features/exam/examDetailSlice";
import { shareContent } from "src/utils/shareUntil";
import { useNavigate } from "react-router-dom";
const ExamOverviewHeader = ({ exam, star = true, examStatus = true, title = "" }) => {
    const { loading, ratingStatistics } = useSelector((state) => state.examDetail);
    const { loadingRatingStatistics } = useSelector((state) => state.examDetail);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const handleSaveExam = () => {
        dispatch(saveExamForUser({ examId: exam.id }));
    };

    const handleRatingChange = async (newRating) => {
        dispatch(setStar(newRating));
        dispatch(rateExamForUser({ examId: exam.id, star: newRating }));
    };

    const handleShareExam = async () => {
        shareContent({
            title: `${exam?.name} - Luyện đề trực tuyến`,
            text: `Hãy cùng làm đề thi "${exam?.name}" trên ToanThayBee!`,
            url: window.location.href
        });
    };

    useEffect(() => {
        if (exam?.id) {
            dispatch(fetchExamRatingStatistics(exam.id));
        }
    }, [dispatch, exam?.id]);


    return (
        <>
            <div className="flex flex-col gap-2">
                <div className="w-full flex flex-col md:flex-row gap-2 justify-between items-center">
                    <div className="items-start justify-start flex flex-wrap gap-2">
                        <FileText className="text-sky-600" />
                        <LoadingText loading={loading} w="w-48">
                            <p
                                onClick={() => navigate(`/practice/exam/${exam?.id}`)}
                                className="text-lg font-semibold hover:underline cursor-pointer">{exam?.name}</p>
                            {examStatus && (
                                <>
                                    <p className={`py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 ${exam?.acceptDoExam ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>
                                        {exam?.acceptDoExam ? 'Hoạt động' : 'Kết thúc'}
                                    </p>
                                    <p className={`py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 ${ratingStatistics?.studentStatus?.isDone ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>
                                        {ratingStatistics?.studentStatus?.isDone ? 'Đã làm' : 'Chưa làm'}
                                    </p>
                                </>
                            )}
                        </LoadingText>
                    </div>
                    <LoadingText loading={loadingRatingStatistics} w="w-80">
                        <div className="flex md:flex-row flex-col md:w-fit w-full gap-2">
                            <button
                                onClick={handleSaveExam}
                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 py-[3px] px-3 rounded-md border transition-all
                                    ${ratingStatistics?.studentStatus?.isSave
                                        ? "border-blue-500 bg-blue-50 text-blue-600 hover:bg-blue-100"
                                        : "border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200"
                                    }`}
                            >
                                <Pin size={16} />
                                <span>{ratingStatistics?.studentStatus?.isSave ? "Đã lưu" : "Lưu đề"}</span>
                            </button>
                            <button
                                onClick={handleShareExam}
                                className="flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 py-[3px] px-3 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200"
                            >
                                <Share2 size={16} />
                                <span>Chia sẻ</span>
                            </button>
                            <button
                                // onClick={onClick}
                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200`}
                            >
                                <Eye size={16} />
                                <span>Số lượt làm bài: {ratingStatistics?.isDoneCount}</span>
                            </button>
                            <button
                                // onClick={onClick}
                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200`}
                            >
                                <StarIcon size={16} className="text-yellow-500" />
                                <span className="text-xs font-medium">{ratingStatistics?.avgStar || 0} ({ratingStatistics?.starCount || 0})</span>
                            </button>
                        </div>
                    </LoadingText>
                </div>
                {star && (
                    <LoadingText loading={loadingRatingStatistics} w="w-80">
                        <div className="flex flex-row items-start">
                            <StarRating
                                initialRating={ratingStatistics?.studentStatus?.star || 0}
                                onChange={handleRatingChange}
                                size={16}
                                showValue={false}
                                disabled={loadingRatingStatistics}
                            />
                        </div>
                    </LoadingText>
                )}
            </div>
            <hr className="my-6 border-gray-300" />
        </>
    )
}

export default ExamOverviewHeader;
