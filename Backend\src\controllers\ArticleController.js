import db from '../models/index.js';
import UserType from '../constants/UserType.js';
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import { Op, or } from "sequelize";
import { uploadPdfToFirebase, deletePdfFromFirebase } from "../utils/pdfUpload.js"

export const getArticle = async (req, res) => {
    try {
        // Lấy các tham số từ query
        const { page = 1, limit = 10, class: classCode, type, chapter, search } = req.query;

        // Chuyển đổi page và limit thành số
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);

        // Tính offset cho phân trang
        const offset = (pageNumber - 1) * limitNumber;

        // Xây dựng điều kiện tìm kiếm
        const whereConditions = {};

        // Thêm điều kiện lọc theo lớp nếu có
        if (classCode) {
            whereConditions.class = classCode;
        }

        // Thêm điều kiện lọc theo loại bài viết nếu có
        if (type) {
            whereConditions.type = type;
        }

        // Thêm điều kiện lọc theo chương nếu có
        if (chapter) {
            whereConditions.chapter = chapter;
        }

        // Thêm điều kiện tìm kiếm theo từ khóa nếu có
        if (search) {
            whereConditions[db.Sequelize.Op.or] = [
                { title: { [db.Sequelize.Op.like]: `%${search}%` } },
                { content: { [db.Sequelize.Op.like]: `%${search}%` } },
            ];
        }

        // Thực hiện truy vấn với điều kiện và phân trang
        const { count, rows: articles } = await db.Article.findAndCountAll({
            where: whereConditions,
            order: [['createdAt', 'DESC']], // Sắp xếp theo ngày tạo mới nhất
            limit: limitNumber,
            offset: offset
        });

        // Tính toán thông tin phân trang
        const totalPages = Math.ceil(count / limitNumber);
        const hasNextPage = pageNumber < totalPages;
        const hasPrevPage = pageNumber > 1;

        // Trả về kết quả
        res.status(200).json({
            message: 'Danh sách bài viết',
            data: articles,
            pagination: {
                total: count,
                totalPages,
                currentPage: pageNumber,
                limit: limitNumber,
                hasNextPage,
                hasPrevPage
            }
        });
    } catch (error) {
        console.error('Error in getArticle:', error);
        res.status(500).json({
            message: 'Đã xảy ra lỗi khi lấy danh sách bài viết',
            error: error.message
        });
    }
}

export const getNewestArticle = async (req, res) => {
    const article = await db.Article.findAll({
        order: [['createdAt', 'DESC']], // Sắp xếp theo ngày tạo mới nhất
        limit: 3
    });
    res.status(200).json({
        message: 'Danh sách bài viết mới nhất',
        data: article
    })
}

export const countArticleByType = async (req, res) => {
    const types = ['SE', 'LT', 'KT'];

    const counts = await Promise.all(
        types.map(async (type) => {
            const count = await db.Article.count({ where: { type } });
            return { type, count };
        })
    );

    res.status(200).json({
        message: 'Số lượng bài viết theo type',
        data: counts
    });

};

export const getArticleById = async (req, res) => {
    const { id } = req.params

    const article = await db.Article.findOne({
        where: {
            id: id
        }
    })

    if (!article) {
        return res.status(404).json({
            message: 'Không tìm thấy bài viết'
        })
    }

    res.status(200).json({
        message: 'Chi tiết bài viết',
        data: article
    })
}

export const putArticle = async (req, res) => {
    await db.Article.update(req.body, {
        where: {
            id: req.params.id
        }
    })
    res.status(200).json({
        message: 'Cập nhật bài viết thành công'
    })
}

export const postArticle = async (req, res) => {
    await db.Article.create(req.body)
    res.status(201).json({
        message: 'Tạo bài viết thành công'
    })
}

export const deleteArticle = async (req, res) => {
    await db.Article.destroy({
        where: {
            id: req.params.id
        }
    })
    res.status(200).json({
        message: 'Xóa bài viết thành công',
        data: req.params.id
    })
}
