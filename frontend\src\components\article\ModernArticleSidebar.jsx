import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Search, BookOpen, GraduationCap, FileText, Tag } from 'lucide-react';
import LoadingSpinner from '../loading/LoadingSpinner';
import { fetchArticles, getArticleTypeCount } from 'src/features/article/articleSlice';
import { fetchCodesByType } from 'src/features/code/codeSlice';
import { motion } from "framer-motion";

const ButtonSidebar = ({ choice, onClick, value, text, icon, isOpen, count = null }) => {
    const isActive = choice === value;
    const Icon = icon;

    return (
        <button
            onClick={onClick}
            className={`cursor-pointer self-stretch p-2 ${isActive
                ? 'bg-sky-100 text-sky-700 font-medium'
                : 'hover:bg-gray-100 text-gray-700'
                } rounded-lg inline-flex w-full justify-start items-center gap-3 transition-colors`}
        >
            <div className={`flex justify-center items-center p-2 ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-100 text-gray-600'} rounded-full transition-colors`}>
                <Icon size={16} />
            </div>
            <motion.div
                initial={false}
                animate={{
                    opacity: isOpen ? 1 : 0,
                    width: isOpen ? '100%' : 0,
                }}
                transition={{
                    duration: 0.2,
                    ease: [0.25, 0.1, 0.25, 1.0],
                }}
                className="flex flex-row w-full items-center justify-between gap-2"
            >
                <p className="text-sm font-medium text-start truncate w-full">{text}</p>
                {count !== null && (
                    <div className={`px-2 py-1 text-xs rounded-full ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-200 text-gray-700'} font-medium min-w-[1.5rem] text-center`}>
                        {count}
                    </div>
                )}
            </motion.div>
        </button>
    );
};

/**
 * Modern Article Sidebar Component
 * Self-contained component with internal state management
 */
const ModernArticleSidebar = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { articles, loading, pagination, articleCount } = useSelector(state => state.articles);
    const { codes } = useSelector(state => state.codes);

    // Handle pagination
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
        fetchArticlesWithFilters(search, pageNumber);
    };

    // Internal state management
    const [selectedType, setSelectedType] = useState("");
    const [selectedClass, setSelectedClass] = useState("");
    const [selectedChapter, setSelectedChapter] = useState("");
    const [search, setSearch] = useState("");
    const [choice, setChoice] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const timeoutRef = useRef(null);

    // Fetch codes on component mount
    useEffect(() => {
        dispatch(fetchCodesByType(["article type", "grade", "chapter"]));
        // Initial fetch of articles
        dispatch(getArticleTypeCount())
        dispatch(fetchArticles({ page: 1, limit: 10 }));
    }, [dispatch]);

    // Helper functions
    const getTypeDescription = (typeCode) => {
        if (!typeCode || !codes || !codes["article type"]) return typeCode;
        const type = codes["article type"].find(t => t.code === typeCode);
        return type ? type.description : typeCode;
    };

    const getClassDescription = (classCode) => {
        if (!classCode || !codes || !codes["grade"]) return classCode;
        const grade = codes["grade"].find(g => g.code === classCode);
        return grade ? grade.description : classCode;
    };

    const getChapterDescription = (chapterCode) => {
        if (!chapterCode || !codes || !codes["chapter"]) return chapterCode;
        const chapter = codes["chapter"].find(c => c.code === chapterCode);
        return chapter ? chapter.description : chapterCode;
    };

    // Check if the selected class is a chapter code class (10C1, 11C1, 12C1)
    const isChapterCodeClass = selectedClass === "10C1" || selectedClass === "11C1" || selectedClass === "12C1";

    // Sync choice with selectedType
    useEffect(() => {
        if (!selectedType) {
            setChoice(0);
        } else if (codes && codes["article type"]) {
            const typeIndex = codes["article type"].findIndex(type => type.code === selectedType);
            setChoice(typeIndex + 1);
        }
    }, [selectedType, codes]);

    // Reset chapter filter when class changes
    useEffect(() => {
        if (!isChapterCodeClass) {
            setSelectedChapter("");
        }
    }, [selectedClass, isChapterCodeClass]);

    // Function to fetch articles with current filters
    const fetchArticlesWithFilters = (searchTerm = search, page = 1) => {
        const apiParams = {
            page,
            limit: 10
        };

        if (searchTerm) apiParams.search = searchTerm;
        if (selectedType) apiParams.type = selectedType;
        if (selectedClass) apiParams.class = selectedClass;
        if (selectedChapter) apiParams.chapter = selectedChapter;

        // Dispatch action to fetch articles
        dispatch(fetchArticles(apiParams));

        // Update URL with current filters
        updateURL(apiParams, page);
    };

    // Update URL with current filters and page
    const updateURL = (params, page = 1) => {
        const urlParams = new URLSearchParams();
        if (params.search) urlParams.append("search", params.search);
        if (params.type) urlParams.append("type", params.type);
        if (params.class) urlParams.append("class", params.class);
        if (params.chapter) urlParams.append("chapter", params.chapter);
        if (page > 1) urlParams.append("page", page.toString());

        navigate(`/articles?${urlParams.toString()}`);
    };

    // Debounced search function
    const performDebouncedSearch = (searchTerm) => {
        // Clear existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        // Set new timeout for search only
        timeoutRef.current = setTimeout(() => {
            setCurrentPage(1);
            fetchArticlesWithFilters(searchTerm, 1);
        }, 1000);
    };

    // Cleanup timeout on unmount
    React.useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    // Handle search input change (with debounce)
    const handleSearchChange = (e) => {
        const value = e.target.value;
        setSearch(value);
        performDebouncedSearch(value);
    };



    // Reset all filters
    const resetAllFilters = () => {
        setSearch("");
        setSelectedType("");
        setSelectedClass("");
        setSelectedChapter("");
        setChoice(0);
        setCurrentPage(1);

        // Fetch all articles without filters
        dispatch(fetchArticles({ page: 1, limit: 10 }));

        // Navigate to articles page without params
        navigate("/articles");
    };

    // Toggle type selection (immediate API call)
    const handleSelectType = (typeCode) => {
        // Calculate the new type value
        const newTypeValue = typeCode === selectedType ? "" : typeCode;

        // Update the state
        setSelectedType(newTypeValue);

        // Update choice state
        if (!newTypeValue) {
            setChoice(0);
        } else if (codes && codes["article type"]) {
            const typeIndex = codes["article type"].findIndex(type => type.code === typeCode);
            setChoice(typeIndex + 1);
        }

        // Reset to page 1 and fetch immediately
        setCurrentPage(1);

        // Create API params with new type value
        const apiParams = {
            page: 1,
            limit: 10
        };

        if (search) apiParams.search = search;
        if (newTypeValue) apiParams.type = newTypeValue;
        if (selectedClass) apiParams.class = selectedClass;
        if (selectedChapter) apiParams.chapter = selectedChapter;

        // Fetch articles immediately (no debounce for filters)
        dispatch(fetchArticles(apiParams));
        updateURL(apiParams, 1);
    };

    // Toggle class selection (immediate API call)
    const handleSelectClass = (classCode) => {
        // Calculate the new class value
        const newClassValue = classCode === selectedClass ? "" : classCode;

        // Update the state
        setSelectedClass(newClassValue);

        // Reset chapter if class changes
        const newChapterValue = (selectedChapter && newClassValue !== selectedClass) ? "" : selectedChapter;
        if (newChapterValue !== selectedChapter) {
            setSelectedChapter(newChapterValue);
        }

        // Reset to page 1 and fetch immediately
        setCurrentPage(1);

        // Create API params with new class value
        const apiParams = {
            page: 1,
            limit: 10
        };

        if (search) apiParams.search = search;
        if (selectedType) apiParams.type = selectedType;
        if (newClassValue) apiParams.class = newClassValue;
        if (newChapterValue) apiParams.chapter = newChapterValue;

        // Fetch articles immediately (no debounce for filters)
        dispatch(fetchArticles(apiParams));
        updateURL(apiParams, 1);
    };

    // Toggle chapter selection (immediate API call)
    const handleSelectChapter = (chapterCode) => {
        // Calculate the new chapter value
        const newChapterValue = chapterCode === selectedChapter ? "" : chapterCode;

        // Update the state
        setSelectedChapter(newChapterValue);

        // Reset to page 1 and fetch immediately
        setCurrentPage(1);

        // Create API params with new chapter value
        const apiParams = {
            page: 1,
            limit: 10
        };

        if (search) apiParams.search = search;
        if (selectedType) apiParams.type = selectedType;
        if (selectedClass) apiParams.class = selectedClass;
        if (newChapterValue) apiParams.chapter = newChapterValue;

        // Fetch articles immediately (no debounce for filters)
        dispatch(fetchArticles(apiParams));
        updateURL(apiParams, 1);
    };

    return (
        <div className="sticky top-20 py-4 px-2 w-[300px] h-[90vh] overflow-y-auto hide-scrollbar hidden lg:block">
            <div className="inline-flex w-full flex-row justify-center items-center">
                <div className="text-center truncate text-zinc-900 text-xl font-semibold font-bevietnam">
                    Bộ lọc bài viết
                </div>
            </div>
            <hr className="w-full h-[1px] bg-neutral-200 my-4" />

            {/* Filter Categories */}
            <div className="self-stretch text-sm w-full rounded-md flex flex-col justify-start items-start gap-1">
                <ButtonSidebar
                    choice={choice}
                    onClick={() => {
                        setChoice(0);
                        resetAllFilters();
                    }}
                    value={0}
                    text="Tất cả bài viết"
                    icon={FileText}
                    isOpen={true}
                    count={articleCount[0].count + articleCount[1].count + articleCount[2].count}
                />

                {/* Article Types */}
                {codes && codes["article type"] && codes["article type"].map((type, index) => (
                    <ButtonSidebar
                        key={type.code}
                        choice={choice}
                        onClick={() => {
                            setChoice(index + 1);
                            handleSelectType(type.code);
                        }}
                        value={index + 1}
                        text={type.description}
                        icon={Tag}
                        isOpen={true}
                        count={articleCount.find((article) => article.type === type.code).count}
                    />
                ))}
            </div>

            {/* Search Section */}
            <div className="mt-6">
                <h3 className="text-sm font-semibold text-gray-700 mb-3">Tìm kiếm</h3>
                <div className="relative w-full">
                    <input
                        type="text"
                        value={search}
                        onChange={handleSearchChange}
                        placeholder="Tìm kiếm bài viết..."
                        className="w-full h-10 pl-10 pr-10 text-sm text-gray-700 placeholder-gray-400 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-400 focus:border-sky-400 transition-all duration-150"
                    />
                    <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        <Search size={18} className="text-gray-400" />
                    </div>
                    {loading && (
                        <div className="absolute inset-y-0 right-3 flex items-center">
                            <LoadingSpinner color="border-black" size="1.25rem" />
                        </div>
                    )}
                </div>
            </div>

            {/* Class Filter */}
            {codes && codes["grade"] && (
                <div className="mt-6">
                    <h3 className="text-sm font-semibold text-gray-700 mb-3">Lớp học</h3>
                    <div className="space-y-1">
                        {codes["grade"].map((grade) => (
                            <button
                                key={grade.code}
                                onClick={() => handleSelectClass(grade.code)}
                                className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                                    selectedClass === grade.code
                                        ? 'bg-sky-100 text-sky-700 font-medium'
                                        : 'hover:bg-gray-100 text-gray-700'
                                }`}
                            >
                                <div className="flex items-center gap-2">
                                    <GraduationCap className="w-4 h-4" />
                                    <span>{grade.description}</span>
                                </div>
                            </button>
                        ))}
                    </div>
                </div>
            )}

            {/* Chapter Filter - Only show when chapter code class is selected */}
            {selectedClass && codes && codes["chapter"] && (
                <div className="mt-6">
                    <h3 className="text-sm font-semibold text-gray-700 mb-3">Chương</h3>
                    <div className="space-y-1">
                        {codes["chapter"]
                            .filter((chapter) => chapter.code.startsWith(selectedClass) && chapter.code.length === 4)
                            .map((chapter) => (
                                <button
                                    key={chapter.code}
                                    onClick={() => handleSelectChapter(chapter.code)}
                                    className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                                        selectedChapter === chapter.code
                                            ? 'bg-sky-100 text-sky-700 font-medium'
                                            : 'hover:bg-gray-100 text-gray-700'
                                    }`}
                                >
                                    <div className="flex items-center gap-2">
                                        <BookOpen className="w-4 h-4 shrink-0" />
                                        <span>{chapter.description}</span>
                                    </div>
                                </button>
                            ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default ModernArticleSidebar;
