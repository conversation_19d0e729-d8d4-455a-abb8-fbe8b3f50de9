import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import PostCodeRequest from '../dtos/requests/code/PostCodeRequest.js'
import * as CodeController from '../controllers/CodeController.js'
import PutCodeRequest from '../dtos/requests/code/PutCodeRequest.js'

const router = express.Router()

router.get('/v1/admin/code',
    requireRoles(Roles.JustAdmin),
    async<PERSON>and<PERSON>(CodeController.getAllCode)
)

router.get('/v1/admin/code/type',
    asyncHandler(CodeController.getCodeByType)
)

router.get('/v1/admin/chapter/code',
    async<PERSON>and<PERSON>(CodeController.getCodeChapter)
)

router.get('/v1/admin/code/:code',
    requireRoles(Roles.JustAdmin),
    as<PERSON><PERSON><PERSON><PERSON>(CodeController.getCodeByCode)
)

router.post('/v1/admin/code',
    requireRoles(Roles.JustAdmin),
    validate(PostCodeRequest),
    asyncHandler(CodeController.postCode)
)
router.put('/v1/admin/code/:code',
    requireRoles(Roles.JustAdmin),
    validate(PutCodeRequest),
    asyncHandler(CodeController.putCode)
)

export default router