# @lottiefiles/dotlottie-web

![npm](https://img.shields.io/npm/v/@lottiefiles/dotlottie-web)
![npm bundle size](https://img.shields.io/bundlephobia/minzip/@lottiefiles/dotlottie-web)
![npm](https://img.shields.io/npm/dw/@lottiefiles/dotlottie-web)
![jsDelivr hits (npm scoped)](https://img.shields.io/jsdelivr/npm/hw/%40lottiefiles/dotlottie-web)
![NPM](https://img.shields.io/npm/l/@lottiefiles/dotlottie-web)

<p align="center">
  <img src="https://user-images.githubusercontent.com/23125742/201124166-c2a0bc2a-018b-463b-b291-944fb767b5c2.png" alt="dotLottie Logo" />
</p>

## Table of Contents

* [Introduction](#introduction)
  * [What is dotLottie?](#what-is-dotlottie)
* [Documentation](#documentation)
* [Supported Platforms](#supported-platforms)
* [Live Examples](#live-examples)
* [Contributing](#contributing)
* [Issues](#issues)

## Introduction

`@lottiefiles/dotlottie-web` is a JavaScript library for rendering [Lottie](https://lottiefiles.github.io/lottie-docs/) and [dotLottie](https://dotlottie.io) animations in Node.js and web environments. It provides a simple and intuitive API for loading, playing, and controlling animations, as well as advanced features like interactivity and theming.

### What is dotLottie?

dotLottie is an open-source file format that bundles one or more Lottie animations along with their assets into a single, compressed .lottie file. It uses ZIP compression for efficient storage and distribution. The format also supports advanced features like interactivity and theming, making it a powerful tool for creating dynamic and interactive animations.

[Learn more about dotLottie](https://dotlottie.io/).

## Documentation

To get started with `@lottiefiles/dotlottie-web`, follow the [documentation here](https://developers.lottiefiles.com/docs/dotlottie-player/dotlottie-web/).

## Supported Platforms

`@lottiefiles/dotlottie-web` is an isomorphic library designed to work in both browser and Node.js environments. It supports Node.js version 18 and higher, as well as all major web browsers.

## Live Examples

Explore these CodePen examples to see `@lottiefiles/dotlottie-web` in action:

* [Getting Started](https://codepen.io/lottiefiles/pen/JjzJZmL)
* [Controlling Animation Playback](https://codepen.io/lottiefiles/pen/dyrRKwg)
* [Dynamic Animation Loading](https://codepen.io/lottiefiles/pen/JjzJZgB)
* [dotLottie Worker](https://codepen.io/lottiefiles/pen/VwJZPrQ)
* [Multi Animations .lottie file](https://codepen.io/lottiefiles/pen/wvOxdRa)
* [Advanced Animation Layout](https://codepen.io/lottiefiles/pen/LYvZveR)
* [Named Markers](https://codepen.io/lottiefiles/pen/RwOROKp)
* [dotLottie Theming](https://codepen.io/lottiefiles/pen/BaEzEeq)
* [dotLottie Interactivity](https://codepen.io/lottiefiles/pen/VwJvdRN)

## Contributing

We welcome contributions! Please see our [contributing guidelines](../../CONTRIBUTING.md) for more details on how to get started with the project locally.

## Issues

Encountered a problem or have a feature request? Log an issue in our [issues](https://github.com/LottieFiles/dotlottie-web/issues) tab. You can also browse through older issues and discussions to find solutions to common problems.
