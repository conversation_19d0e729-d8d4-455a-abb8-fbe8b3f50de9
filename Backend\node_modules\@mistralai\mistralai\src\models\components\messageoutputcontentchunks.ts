/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DocumentURLChunk,
  DocumentURLChunk$inboundSchema,
  DocumentURLChunk$Outbound,
  DocumentURLChunk$outboundSchema,
} from "./documenturlchunk.js";
import {
  ImageURLChunk,
  ImageURLChunk$inboundSchema,
  ImageURLChunk$Outbound,
  ImageURLChunk$outboundSchema,
} from "./imageurlchunk.js";
import {
  TextChunk,
  TextChunk$inboundSchema,
  TextChunk$Outbound,
  TextChunk$outboundSchema,
} from "./textchunk.js";
import {
  ToolFileChunk,
  ToolFileChunk$inboundSchema,
  ToolFileChunk$Outbound,
  ToolFileChunk$outboundSchema,
} from "./toolfilechunk.js";
import {
  ToolReferenceChunk,
  ToolReferenceChunk$inboundSchema,
  ToolReferenceChunk$Outbound,
  ToolReferenceChunk$outboundSchema,
} from "./toolreferencechunk.js";

export type MessageOutputContentChunks =
  | TextChunk
  | ImageURLChunk
  | DocumentURLChunk
  | ToolFileChunk
  | ToolReferenceChunk;

/** @internal */
export const MessageOutputContentChunks$inboundSchema: z.ZodType<
  MessageOutputContentChunks,
  z.ZodTypeDef,
  unknown
> = z.union([
  TextChunk$inboundSchema,
  ImageURLChunk$inboundSchema,
  DocumentURLChunk$inboundSchema,
  ToolFileChunk$inboundSchema,
  ToolReferenceChunk$inboundSchema,
]);

/** @internal */
export type MessageOutputContentChunks$Outbound =
  | TextChunk$Outbound
  | ImageURLChunk$Outbound
  | DocumentURLChunk$Outbound
  | ToolFileChunk$Outbound
  | ToolReferenceChunk$Outbound;

/** @internal */
export const MessageOutputContentChunks$outboundSchema: z.ZodType<
  MessageOutputContentChunks$Outbound,
  z.ZodTypeDef,
  MessageOutputContentChunks
> = z.union([
  TextChunk$outboundSchema,
  ImageURLChunk$outboundSchema,
  DocumentURLChunk$outboundSchema,
  ToolFileChunk$outboundSchema,
  ToolReferenceChunk$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageOutputContentChunks$ {
  /** @deprecated use `MessageOutputContentChunks$inboundSchema` instead. */
  export const inboundSchema = MessageOutputContentChunks$inboundSchema;
  /** @deprecated use `MessageOutputContentChunks$outboundSchema` instead. */
  export const outboundSchema = MessageOutputContentChunks$outboundSchema;
  /** @deprecated use `MessageOutputContentChunks$Outbound` instead. */
  export type Outbound = MessageOutputContentChunks$Outbound;
}

export function messageOutputContentChunksToJSON(
  messageOutputContentChunks: MessageOutputContentChunks,
): string {
  return JSON.stringify(
    MessageOutputContentChunks$outboundSchema.parse(messageOutputContentChunks),
  );
}

export function messageOutputContentChunksFromJSON(
  jsonString: string,
): SafeParseResult<MessageOutputContentChunks, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MessageOutputContentChunks$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MessageOutputContentChunks' from JSON`,
  );
}
