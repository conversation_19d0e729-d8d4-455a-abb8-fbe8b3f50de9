/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  JsonSchema,
  JsonSchema$inboundSchema,
  JsonSchema$Outbound,
  JsonSchema$outboundSchema,
} from "./jsonschema.js";
import {
  ResponseFormats,
  ResponseFormats$inboundSchema,
  ResponseFormats$outboundSchema,
} from "./responseformats.js";

export type ResponseFormat = {
  /**
   * An object specifying the format that the model must output. Setting to `{ "type": "json_object" }` enables JSON mode, which guarantees the message the model generates is in JSON. When using JSON mode you MUST also instruct the model to produce JSON yourself with a system or a user message.
   */
  type?: ResponseFormats | undefined;
  jsonSchema?: JsonSchema | null | undefined;
};

/** @internal */
export const ResponseFormat$inboundSchema: z.ZodType<
  ResponseFormat,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ResponseFormats$inboundSchema.optional(),
  json_schema: z.nullable(JsonSchema$inboundSchema).optional(),
}).transform((v) => {
  return remap$(v, {
    "json_schema": "jsonSchema",
  });
});

/** @internal */
export type ResponseFormat$Outbound = {
  type?: string | undefined;
  json_schema?: JsonSchema$Outbound | null | undefined;
};

/** @internal */
export const ResponseFormat$outboundSchema: z.ZodType<
  ResponseFormat$Outbound,
  z.ZodTypeDef,
  ResponseFormat
> = z.object({
  type: ResponseFormats$outboundSchema.optional(),
  jsonSchema: z.nullable(JsonSchema$outboundSchema).optional(),
}).transform((v) => {
  return remap$(v, {
    jsonSchema: "json_schema",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseFormat$ {
  /** @deprecated use `ResponseFormat$inboundSchema` instead. */
  export const inboundSchema = ResponseFormat$inboundSchema;
  /** @deprecated use `ResponseFormat$outboundSchema` instead. */
  export const outboundSchema = ResponseFormat$outboundSchema;
  /** @deprecated use `ResponseFormat$Outbound` instead. */
  export type Outbound = ResponseFormat$Outbound;
}

export function responseFormatToJSON(responseFormat: ResponseFormat): string {
  return JSON.stringify(ResponseFormat$outboundSchema.parse(responseFormat));
}

export function responseFormatFromJSON(
  jsonString: string,
): SafeParseResult<ResponseFormat, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ResponseFormat$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ResponseFormat' from JSON`,
  );
}
