'use strict';
/** @type {import('sequelize-cli').Migration} */
export default {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addIndex('user', ['username'], {
      name: 'idx_user_username',
      unique: true,
    });

    await queryInterface.addIndex('user', ['phone'], {
      name: 'idx_user_phone',
    });

    await queryInterface.addIndex('user', ['userType'], {
      name: 'idx_user_userType',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('user', 'idx_user_username');
    await queryInterface.removeIndex('user', 'idx_user_phone');
    await queryInterface.removeIndex('user', 'idx_user_userType');
  }
};
