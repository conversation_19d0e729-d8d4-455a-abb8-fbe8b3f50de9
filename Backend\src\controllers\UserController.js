import db from "../models/index.js"
import UserResponse from "../dtos/responses/user/UserResponse.js"
import ADUserResponse from "../dtos/responses/user/ADUserResponse.js"
import bcrypt from "bcrypt"
import UserType from "../constants/UserType.js"
import StudentClassStatus from "../constants/StudentClassStatus.js"
import jwt from "jsonwebtoken"
import { ref, uploadBytesResumable, getDownloadURL, deleteObject, getStorage } from 'firebase/storage'
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import { Op, where, fn, col, Sequelize } from "sequelize"
import { parseExcel, sanitizeExcelUser } from '../utils/excelParser.js';
import * as userService from '../services/user.service.js';
import * as classService from '../services/class.service.js';
import * as googleSheetsService from '../services/googleSheets.service.js';
import * as learningItemService from '../services/learningItem.service.js';
import { createAttendance } from '../services/attendance.service.js';
import dotenv from 'dotenv'
dotenv.config()

export const registerUser = async (req, res) => {
    // Bắt đầu transaction
    const transaction = await db.sequelize.transaction();

    try {
        // Tạo người dùng mới
        const newUser = await userService.createNewUser(req.body, UserType.STUDENT, transaction);
        // console.log('New user created:', newUser);

        // Kiểm tra nếu có classIds được truyền vào
        const { classIds } = req.body;
        let addedToClasses = []
        if (classIds && Array.isArray(classIds) && classIds.length > 0) {
            // Thêm học sinh vào các lớp học với trạng thái JOINED
            for (const classId of classIds) {
                try {
                    // Kiểm tra lớp học có tồn tại không
                    const classInfo = await classService.getClassById(classId, { transaction });
                    if (classInfo) {
                        await classService.createStudentInClass(newUser.id, classId, StudentClassStatus.JOINED, transaction);
                        await learningItemService.createStatusesForStudent(newUser.id, classId, transaction)
                        addedToClasses.push(classInfo.name)
                    }
                } catch (classError) {
                    console.error(`Lỗi khi thêm học sinh vào lớp ${classId}:`, classError);
                    // Không throw lỗi ở đây để tiếp tục với lớp tiếp theo
                    // Nhưng cũng không nên bỏ qua lỗi hoàn toàn
                    if (classError.message.includes('transaction')) {
                        // Nếu lỗi liên quan đến transaction, throw để rollback
                        throw classError;
                    }
                }
            }
        }

        const lessonIds = req.body.lessonIds || [];
        if (lessonIds && Array.isArray(lessonIds) && lessonIds.length > 0) {
            for (const lessonId of lessonIds) {
                try {

                    const attendance = await createAttendance(newUser.id, lessonId, null, 'present', 'hs mới', transaction);
                    if (!attendance) {
                        throw new Error(`Không thể tạo điểm danh cho học sinh ${newUser.id} trong buổi học ${lessonId}`);
                    }
                } catch (attendanceError) {
                    console.error(`Lỗi khi tạo điểm danh cho học sinh ${newUser.id} trong
    buổi học ${lessonId}:`, attendanceError.message);
                    // Không throw lỗi ở đây để tiếp tục với lesson tiếp theo
                    if (attendanceError.message.includes('transaction')) {
                        // Nếu lỗi liên quan đến transaction, throw để rollback
                        throw attendanceError;
                    }
                }
            }
        }

        // Commit transaction
        await transaction.commit();

        // Thêm user vào Google Sheets (không blocking)
        try {
            const userListSheets = await googleSheetsService.getUserListSheets();
            for (const sheet of userListSheets) {
                try {
                    await googleSheetsService.addUserToSheet(newUser, sheet.id, addedToClasses);
                    console.log(`Đã thêm user ${newUser.id} vào sheet ${sheet.title}`);
                } catch (sheetError) {
                    console.error(`Lỗi khi thêm user vào sheet ${sheet.title}:`, sheetError.message);
                    // Không throw lỗi để không ảnh hưởng đến response chính
                }
            }
        } catch (sheetsError) {
            console.error('Lỗi khi xử lý Google Sheets:', sheetsError.message);
            // Không throw lỗi để không ảnh hưởng đến response chính
        }

        return res.status(201).json({
            message: 'Thêm người dùng thành công',
            user: new UserResponse(newUser)
        });
    } catch (err) {
        // Rollback transaction nếu có lỗi
        if (!transaction.finished) {
            await transaction.rollback();
        }
        return res.status(400).json({ message: err.message });
    }
}

export const createUser = async (req, res) => {
    try {
        const newUser = await userService.createNewUser(req.body, req.body.userType)
        return res.status(201).json({
            message: 'Thêm người dùng thành công',
            user: new UserResponse(newUser)
        })
    } catch (err) {
        return res.status(400).json({ message: err.message })
    }
}

export const bulkRegister = async (req, res) => {
    try {
        const rawUsers = parseExcel(req.file.path);
        const users = rawUsers.map(sanitizeExcelUser);
        const result = await userService.createUserBulk(users);

        res.status(200).json({ message: 'Đăng ký hàng loạt thành công', result });
    } catch (err) {
        res.status(500).json({ message: 'Lỗi xử lý file Excel', error: err.message });
    }
};

const trimValue = (str) => {
    return str.toString().trim();
}

export const login = async (req, res) => {
    const username = trimValue(req.body.username);
    const password = trimValue(req.body.password);

    if (!username || !password) {
        return res.status(400).json({ message: 'Vui lòng nhập tài khoản và mật khẩu' });
    }

    // console.log('username', username)
    // console.log('password', password)

    const user = await userService.findUserByUsername(username);
    if (!user) {
        return res.status(403).json({ message: 'Tài khoản hoặc mật khẩu không đúng' });
    }
    console.log('user', user.id, user.lastName, user.firstName, user.userType)

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch && user.password !== password) {
        return res.status(403).json({ message: 'Tài khoản hoặc mật khẩu không đúng' });
    }

    // Tạo token với JWT
    const token = jwt.sign(
        { id: user.id },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '30d' }
    );

    // Cập nhật token hiện tại cho user trong database
    await userService.updateUser(user.id, { currentToken: token });

    // Set token vào HttpOnly cookie
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');

    // Set cookie và trả về response
    res.cookie('token', token, {
        httpOnly: true,
        secure: true,
        sameSite: 'none',
        path: '/',
        maxAge: 24 * 60 * 60 * 1000
    });

    return res.status(200).json({
        message: 'Đăng nhập thành công',
        user: new UserResponse(user),
        token,
    });
};

export const checkLogin = async (req, res) => {
    try {
        // Lấy token từ cookie hoặc từ Authorization header
        let token = req.cookies.token;

        if (!token && req.headers.authorization) {
            const authHeader = req.headers.authorization;
            if (authHeader.startsWith('Bearer ')) {
                token = authHeader.split(' ')[1]; // Lấy phần sau "Bearer"
            }
        }

        if (!token) {
            return res.status(401).json({ message: 'Chưa đăng nhập' });
        }

        // Giải mã token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Tìm user trong database
        const user = await userService.findUserById(decoded.id);

        if (!user) {
            return res.status(404).json({ message: 'Người dùng không tồn tại' });
        }

        if (!user.currentToken) {
            return res.status(403).json({ message: 'Phiên đăng nhập không hợp lệ' });
        }

        if (user.currentToken !== token && user.userType !== UserType.ADMIN) {
            return res.status(403).json({ message: 'Phiên đăng nhập không hợp lệ' });
        }

        return res.status(200).json({
            message: 'Người dùng đã đăng nhập',
            user: new UserResponse(user)
        });
    } catch (error) {
        return res.status(403).json({ message: 'Phiên đăng nhập không hợp lệ', error: error.message });
    }
};

const updateUserUntil = async (id, data, res, forbiddenFields = ['username', 'password', 'userType', 'avatarUrl']) => {

    const updatedData = Object.keys(data)
        .filter(key => !forbiddenFields.includes(key))
        .reduce((obj, key) => {
            obj[key] = data[key]
            return obj
        }, {})

    if (Object.keys(updatedData).length === 0) {
        return res.status(400).json({ message: 'Không có trường hợp lệ để cập nhật.' })
    }

    const updated = await userService.updateUser(id, updatedData)

    if (!updated) {
        return res.status(404).json({ message: 'Người dùng không tồn tại' })
    }

    const updatedUser = await userService.findUserById(id)
    return res.status(200).json({ message: 'Cập nhật người dùng thành công', data: new UserResponse(updatedUser) })
}

export const updateUserInfo = async (req, res) => {
    const user = req.user

    return await updateUserUntil(user.id, req.body, res)
}

export const putUser = async (req, res) => {
    const { id } = req.params

    return await updateUserUntil(id, req.body, res, ['userType', 'avatarUrl'])
}

export const logout = async (req, res) => {
    const user = req.user

    if (!user) {
        return res.status(401).json({ message: 'Không xác định được người dùng.' })
    }

    // Cập nhật currentToken của người dùng trong DB thành null
    await userService.updateUser(user.id, { currentToken: null })

    // Xoá cookie chứa token khỏi trình duyệt
    res.clearCookie('token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production', // chỉ áp dụng HTTPS ở môi trường production
        sameSite: 'strict',
    })

    return res.status(200).json({ message: 'Đăng xuất thành công.' })
}


export const changeUserType = async (req, res) => {
    const { id } = req.params
    const { userType } = req.body
    // console.log('changeUserType', id, userType)
    const updated = await userService.updateUser(id, { userType, currentToken: null })
    if (!updated) {
        return res.status(404).json({ message: 'Người dùng không tồn tại' })
    }

    const updatedUser = await userService.findUserById(id)
    return res.status(200).json({
        message: 'Cập nhật role người dùng thành công',
        data: new UserResponse(updatedUser)
    })
}

export const changePassword = async (req, res) => {
    const user = req.user
    const oldPassword = trimValue(req.body.oldPassword)
    const newPassword = trimValue(req.body.newPassword)
    const confirmPassword = trimValue(req.body.confirmPassword)

    if (!oldPassword || !newPassword || !confirmPassword) {
        return res.status(400).json({ message: 'Vui lòng điền đầy đủ các trường.' })
    }

    if (newPassword !== confirmPassword) {
        return res.status(400).json({ message: 'Mật khẩu xác nhận không khớp.' })
    }

    await userService.updateUser(user.id, { password: newPassword, currentToken: null })

    return res.status(200).json({ message: 'Đổi mật khẩu thành công. Vui lòng đăng nhập lại.' })
}

export const getUserById = async (req, res) => {
    const { id } = req.params
    const userDetail = await db.User.findByPk(id)

    if (!userDetail) {
        return res.status(404).json({ message: 'Người dùng không tồn tại' })
    }
    return res.status(200).json({
        message: 'Chi tiết người dùng',
        user: new ADUserResponse(userDetail)
        // userDetail
    })
}

export const updateAvatar = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const { id } = req.user

    const user = await userService.findUserById(id)

    if (!user) {
        await transaction.rollback()
        return res.status(404).json({ message: 'Người dùng không tồn tại' })
    }

    const oldAvatarUrl = user.avatarUrl
    const newAvatarFile = req.file

    if (!newAvatarFile) {
        await transaction.rollback()
        return res.status(400).json({ message: 'Vui lòng chọn ảnh để tải lên.' })
    }
    const newAvartarUrl = await uploadImage(newAvatarFile, 'avatar');

    if (!newAvartarUrl) {
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi khi tải ảnh mới lên.' })
    }

    const updated = await userService.updateUser(id, { avatarUrl: newAvartarUrl }, transaction)

    if (!updated) {
        await cleanupUploadedFiles([newAvartarUrl])
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi khi cập nhật avatar.' })
    }

    if (oldAvatarUrl) {
        try {
            await cleanupUploadedFiles([oldAvatarUrl])
            console.log(`Đã xóa ảnh cũ: ${oldAvatarUrl}`)
        } catch (err) {
            console.error(`Lỗi khi xóa ảnh cũ: ${oldAvatarUrl}`, err)
            await cleanupUploadedFiles([newAvartarUrl])
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi xóa ảnh cũ.', error: err.message })
        }
    }

    await transaction.commit()

    return res.status(200).json({
        message: 'Cập nhật avartar thành công.',
        oldAvatarUrl,
        newAvartarUrl,
    })
}


export const getAllUsers = async (req, res) => {
    // Lấy các tham số từ query
    const sortOrder = req.query.sortOrder || 'DESC';
    const search = req.query.search || '';
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;

    // Lấy filter parameters
    const graduationYear = req.query.graduationYear ? parseInt(req.query.graduationYear, 10) : null;
    const classFilter = req.query.class || null;

    // Gọi service để lấy danh sách học sinh
    const result = await userService.getAllStudents({
        search: search.trim(),
        page,
        limit,
        sortOrder,
        graduationYear,
        classFilter
    });

    // Trả về kết quả
    return res.status(200).json({
        message: 'Lấy danh sách người dùng thành công',
        ...result,
        graduationYear,
        classFilter
    });
};

export const getAllStaff = async (req, res) => {
    // Lấy các tham số từ query
    const sortOrder = req.query.sortOrder || 'DESC';
    const search = req.query.search || '';
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;

    // Gọi service để lấy danh sách học sinh
    const result = await userService.getAllStaff({
        search: search.trim(),
        page,
        limit,
        sortOrder
    });

    // Trả về kết quả
    return res.status(200).json({
        message: 'Lấy danh sách nhân viên thành công',
        ...result
    });
};

export const findUsers = async (req, res) => {
    try {
        const { search } = req.query;
        const limit = 20; // Giới hạn 20 kết quả

        // Nếu không có tham số search, trả về 20 user được cập nhật gần đây nhất
        if (!search || search.trim() === '') {
            const recentUsers = await db.User.findAll({
                where: {
                    isActive: true,
                    userType: UserType.STUDENT,
                },
                order: [['updatedAt', 'DESC']],
                limit,
                attributes: { exclude: ['password', 'currentToken'] }
            });

            return res.status(200).json({
                message: 'Lấy danh sách người dùng gần đây thành công',
                data: recentUsers.map(user => new UserResponse(user))
            });
        }

        // Tìm kiếm theo nhiều tiêu chí
        const searchTerm = search.trim();
        // console.log(searchTerm);
        // Kiểm tra xem searchTerm có phải là số không (để tìm theo ID)
        const isNumeric = !isNaN(searchTerm) && !isNaN(parseFloat(searchTerm));
        // console.log(isNumeric);
        // Tạo điều kiện tìm kiếm
        const whereCondition = {
            isActive: true,
            userType: UserType.STUDENT,
            [Op.or]: [
                // Tìm theo ID nếu searchTerm là số
                ...(isNumeric ? [{ id: parseInt(searchTerm, 10) }] : []),

                // Tìm theo firstName hoặc lastName
                { firstName: { [Op.like]: `%${searchTerm}%` } },
                { lastName: { [Op.like]: `%${searchTerm}%` } },

                // Tìm theo firstName + lastName (kết hợp)
                Sequelize.where(
                    Sequelize.fn('CONCAT',
                        Sequelize.col('firstName'),
                        ' ',
                        Sequelize.col('lastName')
                    ),
                    { [Op.like]: `%${searchTerm}%` }
                ),

                // Tìm theo lastName + firstName (kết hợp)
                Sequelize.where(
                    Sequelize.fn('CONCAT',
                        Sequelize.col('lastName'),
                        ' ',
                        Sequelize.col('firstName')
                    ),
                    { [Op.like]: `%${searchTerm}%` }
                )
            ]
        };

        // console.log(whereCondition);

        // Thực hiện tìm kiếm
        const users = await db.User.findAll({
            where: whereCondition,
            limit,
            attributes: { exclude: ['password', 'currentToken'] },
            order: [['updatedAt', 'DESC']]
        });

        return res.status(200).json({
            message: 'Tìm kiếm người dùng thành công',
            data: users.map(user => new UserResponse(user)),
            totalItems: users.length
        });
    } catch (error) {
        console.error('Lỗi khi tìm kiếm người dùng:', error);
        return res.status(500).json({
            message: 'Lỗi server khi tìm kiếm người dùng',
            error: error.message
        });
    }
}

export const getUsersByClass = async (req, res) => {
    const classId = req.params.classId;
    const search = req.query.search || '';
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const sortOrder = req.query.sortOrder || 'ASC';

    // Kiểm tra xem lớp học có tồn tại không
    const classInfo = await classService.getClassById(classId);
    if (!classInfo) {
        return res.status(404).json({ message: 'Lớp học không tồn tại' });
    }

    // Lấy danh sách người dùng trong lớp học
    const result = await userService.getAllStudentsByClass(classId, {
        search,
        page,
        limit,
        sortOrder
    });

    return res.status(200).json({
        message: 'Lấy danh sách người dùng trong lớp học thành công',
        data: result
    });
};



export const getUserMe = async (req, res) => {
    try {
        // Lấy token từ cookie và header
        const token = req.cookies.token || null;

        const authHeader = req.headers.authorization || '';
        const headerToken = authHeader.startsWith('Bearer ') ? authHeader.split(' ')[1] : null;

        // Xác định nguồn token
        let tokenSource = 'none';
        if (token) tokenSource = 'cookie';
        else if (headerToken) tokenSource = 'header';

        return res.status(200).json({
            message: 'Thông tin xác thực',
            hasTokenInCookie: !!token,
            hasTokenInHeader: !!headerToken,
            tokenSource
        });
    } catch (error) {
        console.error('Lỗi khi kiểm tra token:', error);
        return res.status(500).json({ message: 'Lỗi server.', error: error.message });
    }
};

export const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        const currentUser = req.user;

        // Sử dụng service để xóa user
        const result = await userService.deleteUserById(parseInt(id), currentUser);

        return res.status(200).json({
            message: 'Xóa người dùng thành công',
            deletedUserId: result.deletedUserId,
            deletedUserName: result.deletedUserName
        });

    } catch (error) {
        console.error('Lỗi khi xóa người dùng:', error);

        // Xử lý các loại lỗi khác nhau
        if (error.message === 'Người dùng không tồn tại') {
            return res.status(404).json({ message: error.message });
        }

        if (error.message === 'Không thể xóa chính mình' ||
            error.message === 'Chỉ admin mới có thể xóa admin hoặc giáo viên') {
            return res.status(403).json({ message: error.message });
        }

        return res.status(500).json({
            message: 'Lỗi server khi xóa người dùng',
            error: error.message
        });
    }
};

export const deleteAllInactiveUsers = async (req, res) => {
    const currentUser = req.user;
    const result = await userService.deleteAllInactiveUsers(currentUser);
    return res.status(200).json({
        message: 'Xóa tất cả người dùng không hoạt động thành công',
        deletedCount: result.deletedCount
    });
}

export const addGraduationYearToUsers = async (req, res) => {
    const users = await db.User.findAll();

    let updatedCount = 0;
    let skippedCount = 0;

    for (const user of users) {
        let expectedGraduationYear = null;

        if (user.class === "10") expectedGraduationYear = 2028;
        else if (user.class === "11") expectedGraduationYear = 2027;
        else if (user.class === "12") expectedGraduationYear = 2026;

        // Nếu lớp không nằm trong 10–12 thì bỏ qua
        if (!expectedGraduationYear) continue;

        // Nếu graduationYear đã đúng thì bỏ qua
        if (user.graduationYear === expectedGraduationYear) {
            skippedCount++;
            console.log(`🟡 User ${user.id} (${user.class}) đã đúng graduationYear: ${expectedGraduationYear}, bỏ qua.`);
            continue;
        }

        try {
            await user.update({ graduationYear: expectedGraduationYear });
            updatedCount++;
            console.log(`✅ User ${user.id} (${user.class}) cập nhật graduationYear: ${expectedGraduationYear}`);
        } catch (err) {
            console.error(`❌ Lỗi khi cập nhật user ${user.id}:`, err.message);
        }
    }

    return res.status(200).json({
        message: 'Cập nhật năm tốt nghiệp hoàn tất.',
        totalUsers: users.length,
        updatedUsers: updatedCount,
        skippedUsers: skippedCount
    });
};


