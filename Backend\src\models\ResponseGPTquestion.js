'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
    class ResponseGPTQuestions extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            ResponseGPTQuestions.belongsTo(models.Question, { foreignKey: 'questionId', as: 'question' })
        }
    }
    ResponseGPTQuestions.init({
        messageId: DataTypes.INTEGER,
        questionId: DataTypes.INTEGER,
        response: DataTypes.TEXT,
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE,
    }, {
        sequelize,
        modelName: 'ResponseGPTQuestions',
        tableName: 'responseGPTQuestions',
        timestamps: false,
    })
    return ResponseGPTQuestions
}