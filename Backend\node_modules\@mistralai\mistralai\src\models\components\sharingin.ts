/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  EntityType,
  EntityType$inboundSchema,
  EntityType$outboundSchema,
} from "./entitytype.js";
import {
  ShareEnum,
  ShareEnum$inboundSchema,
  ShareEnum$outboundSchema,
} from "./shareenum.js";

export type SharingIn = {
  orgId: string;
  level: ShareEnum;
  /**
   * The id of the entity (user, workspace or organization) to share with
   */
  shareWithUuid: string;
  /**
   * The type of entity, used to share a library.
   */
  shareWithType: EntityType;
};

/** @internal */
export const SharingIn$inboundSchema: z.ZodType<
  SharingIn,
  z.ZodTypeDef,
  unknown
> = z.object({
  org_id: z.string(),
  level: ShareEnum$inboundSchema,
  share_with_uuid: z.string(),
  share_with_type: EntityType$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "org_id": "orgId",
    "share_with_uuid": "shareWithUuid",
    "share_with_type": "shareWithType",
  });
});

/** @internal */
export type SharingIn$Outbound = {
  org_id: string;
  level: string;
  share_with_uuid: string;
  share_with_type: string;
};

/** @internal */
export const SharingIn$outboundSchema: z.ZodType<
  SharingIn$Outbound,
  z.ZodTypeDef,
  SharingIn
> = z.object({
  orgId: z.string(),
  level: ShareEnum$outboundSchema,
  shareWithUuid: z.string(),
  shareWithType: EntityType$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    orgId: "org_id",
    shareWithUuid: "share_with_uuid",
    shareWithType: "share_with_type",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace SharingIn$ {
  /** @deprecated use `SharingIn$inboundSchema` instead. */
  export const inboundSchema = SharingIn$inboundSchema;
  /** @deprecated use `SharingIn$outboundSchema` instead. */
  export const outboundSchema = SharingIn$outboundSchema;
  /** @deprecated use `SharingIn$Outbound` instead. */
  export type Outbound = SharingIn$Outbound;
}

export function sharingInToJSON(sharingIn: SharingIn): string {
  return JSON.stringify(SharingIn$outboundSchema.parse(sharingIn));
}

export function sharingInFromJSON(
  jsonString: string,
): SafeParseResult<SharingIn, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => SharingIn$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'SharingIn' from JSON`,
  );
}
