/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const ToolChoiceEnum = {
  Auto: "auto",
  None: "none",
  Any: "any",
  Required: "required",
} as const;
export type ToolChoiceEnum = ClosedEnum<typeof ToolChoiceEnum>;

/** @internal */
export const ToolChoiceEnum$inboundSchema: z.ZodNativeEnum<
  typeof ToolChoiceEnum
> = z.nativeEnum(ToolChoiceEnum);

/** @internal */
export const ToolChoiceEnum$outboundSchema: z.ZodNativeEnum<
  typeof ToolChoiceEnum
> = ToolChoiceEnum$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolChoiceEnum$ {
  /** @deprecated use `ToolChoiceEnum$inboundSchema` instead. */
  export const inboundSchema = ToolChoiceEnum$inboundSchema;
  /** @deprecated use `ToolChoiceEnum$outboundSchema` instead. */
  export const outboundSchema = ToolChoiceEnum$outboundSchema;
}
