import AdminLayout from "../../../layouts/AdminLayout";
import FunctionBarAdmin from "../../../components/bar/FunctionBarAdmin";
import { useParams } from "react-router-dom";
import { fetchExamQuestionsWithoutPagination } from "../../../features/question/questionSlice";
import { useNavigate } from "react-router-dom";
import AdminModal from "../../../components/modal/AdminModal";
import AddQuestionModal from "../../../components/modal/AddQuestionModal";
import { setIsAddView } from "../../../features/filter/filterSlice";
import PreviewExam from "../../../components/detail/PreviewExam";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import ExamAdminLayout from "src/layouts/ExamAdminLayout";

const PreviewExamAdmin = () => {
    const { examId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { questionsExam } = useSelector((state) => state.questions);
    const { exam } = useSelector((state) => state.exams);

    useEffect(() => {
        dispatch(fetchExamQuestionsWithoutPagination({ id: examId }));
    }, [dispatch, examId]);

    const handleClickedDetail = () => {
        navigate(`/admin/exam-management/${examId}`);
    }
    const handleClickedQuestions = () => {
        navigate(`/admin/exam-management/${examId}/questions`);
    }
    const handleClickedTracking = () => {
        navigate(`/admin/exam-management/${examId}/tracking`);
    }
    return (
        <ExamAdminLayout>
            <div className="flex-1 overflow-hidden p-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                    <PreviewExam exam={exam} questions={questionsExam} />
                </div>
            </div>
        </ExamAdminLayout>
    )
}

export default PreviewExamAdmin;
