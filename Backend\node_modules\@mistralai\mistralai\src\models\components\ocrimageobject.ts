/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type OCRImageObject = {
  /**
   * Image ID for extracted image in a page
   */
  id: string;
  /**
   * X coordinate of top-left corner of the extracted image
   */
  topLeftX: number | null;
  /**
   * Y coordinate of top-left corner of the extracted image
   */
  topLeftY: number | null;
  /**
   * X coordinate of bottom-right corner of the extracted image
   */
  bottomRightX: number | null;
  /**
   * Y coordinate of bottom-right corner of the extracted image
   */
  bottomRightY: number | null;
  /**
   * Base64 string of the extracted image
   */
  imageBase64?: string | null | undefined;
  /**
   * Annotation of the extracted image in json str
   */
  imageAnnotation?: string | null | undefined;
};

/** @internal */
export const OCRImageObject$inboundSchema: z.ZodType<
  OCRImageObject,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  top_left_x: z.nullable(z.number().int()),
  top_left_y: z.nullable(z.number().int()),
  bottom_right_x: z.nullable(z.number().int()),
  bottom_right_y: z.nullable(z.number().int()),
  image_base64: z.nullable(z.string()).optional(),
  image_annotation: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "top_left_x": "topLeftX",
    "top_left_y": "topLeftY",
    "bottom_right_x": "bottomRightX",
    "bottom_right_y": "bottomRightY",
    "image_base64": "imageBase64",
    "image_annotation": "imageAnnotation",
  });
});

/** @internal */
export type OCRImageObject$Outbound = {
  id: string;
  top_left_x: number | null;
  top_left_y: number | null;
  bottom_right_x: number | null;
  bottom_right_y: number | null;
  image_base64?: string | null | undefined;
  image_annotation?: string | null | undefined;
};

/** @internal */
export const OCRImageObject$outboundSchema: z.ZodType<
  OCRImageObject$Outbound,
  z.ZodTypeDef,
  OCRImageObject
> = z.object({
  id: z.string(),
  topLeftX: z.nullable(z.number().int()),
  topLeftY: z.nullable(z.number().int()),
  bottomRightX: z.nullable(z.number().int()),
  bottomRightY: z.nullable(z.number().int()),
  imageBase64: z.nullable(z.string()).optional(),
  imageAnnotation: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    topLeftX: "top_left_x",
    topLeftY: "top_left_y",
    bottomRightX: "bottom_right_x",
    bottomRightY: "bottom_right_y",
    imageBase64: "image_base64",
    imageAnnotation: "image_annotation",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OCRImageObject$ {
  /** @deprecated use `OCRImageObject$inboundSchema` instead. */
  export const inboundSchema = OCRImageObject$inboundSchema;
  /** @deprecated use `OCRImageObject$outboundSchema` instead. */
  export const outboundSchema = OCRImageObject$outboundSchema;
  /** @deprecated use `OCRImageObject$Outbound` instead. */
  export type Outbound = OCRImageObject$Outbound;
}

export function ocrImageObjectToJSON(ocrImageObject: OCRImageObject): string {
  return JSON.stringify(OCRImageObject$outboundSchema.parse(ocrImageObject));
}

export function ocrImageObjectFromJSON(
  jsonString: string,
): SafeParseResult<OCRImageObject, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OCRImageObject$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OCRImageObject' from JSON`,
  );
}
