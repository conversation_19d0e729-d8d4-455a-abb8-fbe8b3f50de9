import React from 'react';
import LatexRenderer from '../latex/RenderLatex';
import QuestionImage from './QuestionImage';
import { Bookmark } from 'lucide-react';
import ReportButton from '../button/ReportButton';
import NoTranslate from '../utils/NoTranslate';
import { useSelector } from "react-redux";
import QuestionContent from './QuestionContent';
import { useDispatch } from "react-redux";
import { setErrorMessage } from "src/features/state/stateApiSlice";
import { submitAnswerWithAttempt, setAnswers } from "src/features/doExam/doExamSlice";

const MultipleChoiceQuestion = ({ question, index }) => {
    const { darkMode, fontSize, prefixStatements, saveQuestions, isTimeUp, attemptId, answersTN } = useSelector((state) => state.doExam);
    const dispatch = useDispatch();

    const handleSelectAnswerTN = (questionId, statementId, type) => {
        // Không cho phép làm bài nếu đã hết thời gian
        if (isTimeUp) {
            dispatch(setErrorMessage("Đ<PERSON> hết thời gian làm bài. Không thể thay đổi câu trả lời!"));
            return;
        }

        const newAnswer = {
            questionId,
            answerContent: statementId,
            typeOfQuestion: type,
        };

        dispatch(setAnswers(newAnswer));

        dispatch(submitAnswerWithAttempt({
            questionId,
            answerContent: statementId,
            type,
            attemptId
        }));
    };

    const isSelected = (questionId, statementId) => {
        const isSelected = answersTN.some(
            (ans) =>
                ans.questionId === questionId &&
                ans.answerContent &&
                String(ans.answerContent) === String(statementId)
        );

        return isSelected;
    };

    return (
        <div
            key={question.id + "TN"}
            className={`flex flex-col avoid-page-break gap-2 rounded-md p-3 transition
        `}
        >
            <QuestionContent question={question} index={index} />

            <div className="flex flex-col gap-2">
                {question.statements.map((statement, statementIndex) => (
                    <div key={statement.id} className="flex items-center gap-2 pb-3">
                        <input
                            type="radio"
                            name={`question-${question.id}`}
                            value={statement.id}
                            checked={isSelected(question.id, statement.id)}
                            onChange={() =>
                                handleSelectAnswerTN(question.id, statement.id, question.typeOfQuestion)
                            }
                            disabled={isTimeUp}
                            className={`w-4 h-4 accent-sky-600 ${isTimeUp ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
                        />

                        <div className="flex flex-col">
                            <div className="flex items-center gap-2">
                                <p className="font-bold" style={{ fontSize: `${fontSize}px` }}>
                                    <NoTranslate>{prefixStatements[statementIndex]}</NoTranslate>
                                </p>
                                <LatexRenderer text={statement.content} className="break-words" style={{ fontSize: `${fontSize}px` }} />
                            </div>

                            <QuestionImage
                                imageUrl={statement.imageUrl}
                                isStatement={true}
                                altText="statement image"
                            />
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

export default MultipleChoiceQuestion;