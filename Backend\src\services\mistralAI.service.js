import { mistralClient } from '../config/mistralAIConfig.js';

/**
 * Perform OCR on base64 content of PDF or image.
 * @param {string} base64Data - Base64-encoded file content (without prefix)
 * @param {string} fileType - 'pdf', 'png', 'jpg', or 'jpeg'
 * @param {boolean} includeImageBase64 - Whether to include image data in response
 * @returns {Promise<any>}
 */
export const performOcr = async (base64Data, fileType = 'pdf', includeImageBase64 = true) => {
    try {
        const supportedTypes = {
            pdf: 'application/pdf',
            png: 'image/png',
            jpg: 'image/jpeg',
            jpeg: 'image/jpeg',
        };

        const normalizedType = fileType.toLowerCase();
        const mimeType = supportedTypes[normalizedType];

        if (!mimeType) {
            throw new Error(`Unsupported file type: ${fileType}`);
        }

        const isPdf = normalizedType === 'pdf';
        const dataUrl = `data:${mimeType};base64,${base64Data}`;

        const result = await mistralClient.ocr.process({
            model: 'mistral-ocr-latest',
            document: {
                type: isPdf ? 'document_url' : 'image_url',
                [isPdf ? 'documentUrl' : 'imageUrl']: dataUrl,
            },
            includeImageBase64,
        });

        return result;

    } catch (error) {
        console.error('Error during OCR:', error.message);
        throw error;
    }
};
