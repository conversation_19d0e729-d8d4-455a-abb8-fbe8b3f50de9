/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const LegacyJobMetadataOutObject = {
  JobMetadata: "job.metadata",
} as const;
export type LegacyJobMetadataOutObject = ClosedEnum<
  typeof LegacyJobMetadataOutObject
>;

export type LegacyJobMetadataOut = {
  /**
   * The approximated time (in seconds) for the fine-tuning process to complete.
   */
  expectedDurationSeconds?: number | null | undefined;
  /**
   * The cost of the fine-tuning job.
   */
  cost?: number | null | undefined;
  /**
   * The currency used for the fine-tuning job cost.
   */
  costCurrency?: string | null | undefined;
  /**
   * The number of tokens consumed by one training step.
   */
  trainTokensPerStep?: number | null | undefined;
  /**
   * The total number of tokens used during the fine-tuning process.
   */
  trainTokens?: number | null | undefined;
  /**
   * The total number of tokens in the training dataset.
   */
  dataTokens?: number | null | undefined;
  estimatedStartTime?: number | null | undefined;
  deprecated?: boolean | undefined;
  details: string;
  /**
   * The number of complete passes through the entire training dataset.
   */
  epochs?: number | null | undefined;
  /**
   * The number of training steps to perform. A training step refers to a single update of the model weights during the fine-tuning process. This update is typically calculated using a batch of samples from the training dataset.
   */
  trainingSteps?: number | null | undefined;
  object?: LegacyJobMetadataOutObject | undefined;
};

/** @internal */
export const LegacyJobMetadataOutObject$inboundSchema: z.ZodNativeEnum<
  typeof LegacyJobMetadataOutObject
> = z.nativeEnum(LegacyJobMetadataOutObject);

/** @internal */
export const LegacyJobMetadataOutObject$outboundSchema: z.ZodNativeEnum<
  typeof LegacyJobMetadataOutObject
> = LegacyJobMetadataOutObject$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LegacyJobMetadataOutObject$ {
  /** @deprecated use `LegacyJobMetadataOutObject$inboundSchema` instead. */
  export const inboundSchema = LegacyJobMetadataOutObject$inboundSchema;
  /** @deprecated use `LegacyJobMetadataOutObject$outboundSchema` instead. */
  export const outboundSchema = LegacyJobMetadataOutObject$outboundSchema;
}

/** @internal */
export const LegacyJobMetadataOut$inboundSchema: z.ZodType<
  LegacyJobMetadataOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  expected_duration_seconds: z.nullable(z.number().int()).optional(),
  cost: z.nullable(z.number()).optional(),
  cost_currency: z.nullable(z.string()).optional(),
  train_tokens_per_step: z.nullable(z.number().int()).optional(),
  train_tokens: z.nullable(z.number().int()).optional(),
  data_tokens: z.nullable(z.number().int()).optional(),
  estimated_start_time: z.nullable(z.number().int()).optional(),
  deprecated: z.boolean().default(true),
  details: z.string(),
  epochs: z.nullable(z.number()).optional(),
  training_steps: z.nullable(z.number().int()).optional(),
  object: LegacyJobMetadataOutObject$inboundSchema.default("job.metadata"),
}).transform((v) => {
  return remap$(v, {
    "expected_duration_seconds": "expectedDurationSeconds",
    "cost_currency": "costCurrency",
    "train_tokens_per_step": "trainTokensPerStep",
    "train_tokens": "trainTokens",
    "data_tokens": "dataTokens",
    "estimated_start_time": "estimatedStartTime",
    "training_steps": "trainingSteps",
  });
});

/** @internal */
export type LegacyJobMetadataOut$Outbound = {
  expected_duration_seconds?: number | null | undefined;
  cost?: number | null | undefined;
  cost_currency?: string | null | undefined;
  train_tokens_per_step?: number | null | undefined;
  train_tokens?: number | null | undefined;
  data_tokens?: number | null | undefined;
  estimated_start_time?: number | null | undefined;
  deprecated: boolean;
  details: string;
  epochs?: number | null | undefined;
  training_steps?: number | null | undefined;
  object: string;
};

/** @internal */
export const LegacyJobMetadataOut$outboundSchema: z.ZodType<
  LegacyJobMetadataOut$Outbound,
  z.ZodTypeDef,
  LegacyJobMetadataOut
> = z.object({
  expectedDurationSeconds: z.nullable(z.number().int()).optional(),
  cost: z.nullable(z.number()).optional(),
  costCurrency: z.nullable(z.string()).optional(),
  trainTokensPerStep: z.nullable(z.number().int()).optional(),
  trainTokens: z.nullable(z.number().int()).optional(),
  dataTokens: z.nullable(z.number().int()).optional(),
  estimatedStartTime: z.nullable(z.number().int()).optional(),
  deprecated: z.boolean().default(true),
  details: z.string(),
  epochs: z.nullable(z.number()).optional(),
  trainingSteps: z.nullable(z.number().int()).optional(),
  object: LegacyJobMetadataOutObject$outboundSchema.default("job.metadata"),
}).transform((v) => {
  return remap$(v, {
    expectedDurationSeconds: "expected_duration_seconds",
    costCurrency: "cost_currency",
    trainTokensPerStep: "train_tokens_per_step",
    trainTokens: "train_tokens",
    dataTokens: "data_tokens",
    estimatedStartTime: "estimated_start_time",
    trainingSteps: "training_steps",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LegacyJobMetadataOut$ {
  /** @deprecated use `LegacyJobMetadataOut$inboundSchema` instead. */
  export const inboundSchema = LegacyJobMetadataOut$inboundSchema;
  /** @deprecated use `LegacyJobMetadataOut$outboundSchema` instead. */
  export const outboundSchema = LegacyJobMetadataOut$outboundSchema;
  /** @deprecated use `LegacyJobMetadataOut$Outbound` instead. */
  export type Outbound = LegacyJobMetadataOut$Outbound;
}

export function legacyJobMetadataOutToJSON(
  legacyJobMetadataOut: LegacyJobMetadataOut,
): string {
  return JSON.stringify(
    LegacyJobMetadataOut$outboundSchema.parse(legacyJobMetadataOut),
  );
}

export function legacyJobMetadataOutFromJSON(
  jsonString: string,
): SafeParseResult<LegacyJobMetadataOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LegacyJobMetadataOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LegacyJobMetadataOut' from JSON`,
  );
}
