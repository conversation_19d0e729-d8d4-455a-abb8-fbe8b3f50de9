export const initialPaginationState = {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
    sortOrder: 'DESC',
};

export const paginationReducers = {
    setCurrentPage: (state, action) => {
        state.pagination.page = action.payload;
    },
    setLimit: (state, action) => {
        state.pagination.pageSize = action.payload;
        state.pagination.page = 1; // Reset to first page when limit changes
    },
    setSortOrder: (state) => {
        const currentOrder = state.pagination.sortOrder?.toUpperCase() || "ASC";
        state.pagination.sortOrder = currentOrder === "ASC" ? "DESC" : "ASC";
    },
    setPagination: (state, action) => {
        state.pagination = {
            ...state.pagination,
            ...action.payload,
        };
    },
    resetPagination: (state) => {
        state.pagination = { ...initialPaginationState };
    }
};
