/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FunctionCall,
  FunctionCall$inboundSchema,
  FunctionCall$Outbound,
  FunctionCall$outboundSchema,
} from "./functioncall.js";
import {
  ToolTypes,
  ToolTypes$inboundSchema,
  ToolTypes$outboundSchema,
} from "./tooltypes.js";

export type ToolCall = {
  id?: string | undefined;
  type?: ToolTypes | undefined;
  function: FunctionCall;
  index?: number | undefined;
};

/** @internal */
export const ToolCall$inboundSchema: z.ZodType<
  ToolCall,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string().default("null"),
  type: ToolTypes$inboundSchema.optional(),
  function: FunctionCall$inboundSchema,
  index: z.number().int().default(0),
});

/** @internal */
export type ToolCall$Outbound = {
  id: string;
  type?: string | undefined;
  function: FunctionCall$Outbound;
  index: number;
};

/** @internal */
export const ToolCall$outboundSchema: z.ZodType<
  ToolCall$Outbound,
  z.ZodTypeDef,
  ToolCall
> = z.object({
  id: z.string().default("null"),
  type: ToolTypes$outboundSchema.optional(),
  function: FunctionCall$outboundSchema,
  index: z.number().int().default(0),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolCall$ {
  /** @deprecated use `ToolCall$inboundSchema` instead. */
  export const inboundSchema = ToolCall$inboundSchema;
  /** @deprecated use `ToolCall$outboundSchema` instead. */
  export const outboundSchema = ToolCall$outboundSchema;
  /** @deprecated use `ToolCall$Outbound` instead. */
  export type Outbound = ToolCall$Outbound;
}

export function toolCallToJSON(toolCall: ToolCall): string {
  return JSON.stringify(ToolCall$outboundSchema.parse(toolCall));
}

export function toolCallFromJSON(
  jsonString: string,
): SafeParseResult<ToolCall, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolCall$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolCall' from JSON`,
  );
}
