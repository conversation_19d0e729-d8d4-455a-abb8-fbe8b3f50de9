/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type ImageURL = {
  url: string;
  detail?: string | null | undefined;
};

/** @internal */
export const ImageURL$inboundSchema: z.ZodType<
  ImageURL,
  z.ZodTypeDef,
  unknown
> = z.object({
  url: z.string(),
  detail: z.nullable(z.string()).optional(),
});

/** @internal */
export type ImageURL$Outbound = {
  url: string;
  detail?: string | null | undefined;
};

/** @internal */
export const ImageURL$outboundSchema: z.ZodType<
  ImageURL$Outbound,
  z.ZodTypeDef,
  ImageURL
> = z.object({
  url: z.string(),
  detail: z.nullable(z.string()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ImageURL$ {
  /** @deprecated use `ImageURL$inboundSchema` instead. */
  export const inboundSchema = ImageURL$inboundSchema;
  /** @deprecated use `ImageURL$outboundSchema` instead. */
  export const outboundSchema = ImageURL$outboundSchema;
  /** @deprecated use `ImageURL$Outbound` instead. */
  export type Outbound = ImageURL$Outbound;
}

export function imageURLToJSON(imageURL: ImageURL): string {
  return JSON.stringify(ImageURL$outboundSchema.parse(imageURL));
}

export function imageURLFromJSON(
  jsonString: string,
): SafeParseResult<ImageURL, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ImageURL$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ImageURL' from JSON`,
  );
}
