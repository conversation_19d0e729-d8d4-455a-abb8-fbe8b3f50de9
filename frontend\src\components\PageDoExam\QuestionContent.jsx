import React from 'react';
import LatexRenderer from '../latex/RenderLatex';
import QuestionImage from './QuestionImage';
import { Bookmark } from 'lucide-react';
import ReportButton from '../button/ReportButton';
import NoTranslate from '../utils/NoTranslate';
import { useSelector } from "react-redux";

export const QuestionContent = ({ question, index }) => {
    const { fontSize } = useSelector((state) => state.doExam);
    return (
        <div className="flex flex-col gap-2 mb-4">
            {/* Thông tin câu hỏi */}
            <div className="flex justify-between items-center">
                <div className='flex gap-2'>
                    <p className="font-bold" style={{ fontSize: `${fontSize + 2}px` }}>
                        <NoTranslate>Câu {index + 1}:</NoTranslate>
                    </p>
                    <ReportButton questionId={question.id} />
                </div>
            </div>
            <LatexRenderer text={question.content} className="" style={{ fontSize: `${fontSize}px` }} />
            <QuestionImage
                imageUrl={question.imageUrl}
            />
        </div>
    )
}

export default QuestionContent;