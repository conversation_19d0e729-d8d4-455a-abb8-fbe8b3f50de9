import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { fetchAttemptByStudentId } from "../../features/attempt/attemptSlice";
import Pagination from "../Pagination";
import { setCurrentPage } from "../../features/filter/filterSlice";
import LoadingText from "../loading/LoadingText";
import { History, Clock, Calendar, FileText } from "lucide-react";
const HistoryView = ({ examId }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { exam } = useSelector((state) => state.examDetail);
    const { attempts } = useSelector((state) => state.attempts);
    const { currentPage, limit } = useSelector((state) => state.filter);
    const { loading } = useSelector((state) => state.states);

    const totalItems = attempts.length;
    const [paginatedAttempts, setPaginatedAttempts] = useState([]);

    const handlePageChange = (page) => {
        dispatch(setCurrentPage(page));
    };

    useEffect(() => {
        const attemptsFake = attempts?.slice(
            (currentPage - 1) * limit,
            currentPage * limit
        );
        setPaginatedAttempts(attemptsFake);
    }, [attempts, currentPage, limit]);

    useEffect(() => {
        dispatch(fetchAttemptByStudentId({ examId }));
    }, [dispatch, examId]);

    if (loading) {
        return (
            <div className="flex flex-col border border-gray-300 rounded-md">
                <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                    <div className="flex items-center gap-2">
                        <History size={16} className="text-sky-600" />
                        <p className="font-Inter text-sm font-semibold">Lịch sử làm bài</p>
                    </div>
                    <div className="flex items-center gap-2 text-gray-500">
                        <FileText size={16} className="flex-shrink-0" />
                        <LoadingText loading={true} w="w-16" h="h-3" />
                    </div>
                </div>

                {/* Content Skeleton */}
                <div className="p-4">
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                            <thead>
                                <tr className="border-b border-gray-200">
                                    <th className="text-left py-3 px-2 font-medium text-gray-600">Lần</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Điểm</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Thời gian làm</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Thời gian nộp</th>
                                </tr>
                            </thead>
                            <tbody>
                                {[...Array(3)].map((_, index) => (
                                    <tr key={index} className="border-b border-gray-100">
                                        <td className="py-3 px-2">
                                            <LoadingText loading={true} w="w-16" h="h-4" />
                                        </td>
                                        <td className="py-3 px-2 text-center">
                                            <LoadingText loading={true} w="w-12" h="h-5" />
                                        </td>
                                        <td className="py-3 px-2 text-center">
                                            <div className="flex items-center justify-center gap-1">
                                                <Clock size={14} className="text-gray-400" />
                                                <LoadingText loading={true} w="w-16" h="h-4" />
                                            </div>
                                        </td>
                                        <td className="py-3 px-2 text-center">
                                            <div className="flex items-center justify-center gap-1">
                                                <Calendar size={14} className="text-gray-400" />
                                                <LoadingText loading={true} w="w-20" h="h-4" />
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            {/* Header */}
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <div className="flex items-center gap-2">
                    <History size={16} className="text-sky-600" />
                    <p className="font-Inter text-sm font-semibold">Lịch sử làm bài</p>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                    <FileText size={16} className="flex-shrink-0" />
                    <p className="text-xs">{paginatedAttempts.length} lần làm bài</p>
                </div>
            </div>

            {/* Content */}
            <div className="p-4">
                {paginatedAttempts?.length > 0 ? (
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                            <thead>
                                <tr className="border-b border-gray-200">
                                    <th className="text-left py-3 px-2 font-medium text-gray-600">Lần</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Điểm</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Thời gian làm</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Thời gian nộp</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedAttempts.map((attempt, index) => {
                                    const idx = index + 1 + (currentPage - 1) * limit;
                                    return (
                                        <tr
                                            onClick={() => navigate(`/practice/exam/attempt/${attempt.id}/score`)}
                                            key={attempt.id}
                                            className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
                                        >
                                            <td className="py-3 px-2">
                                                <span className="font-medium text-gray-700">Lần {idx}</span>
                                            </td>
                                            <td className="py-3 px-2 text-center">
                                                <span className="font-semibold text-lg text-green-600">
                                                    {attempt.score !== null ? attempt.score : "Chưa có"}
                                                </span>
                                            </td>
                                            <td className="py-3 px-2 text-center">
                                                <div className="flex items-center justify-center gap-1">
                                                    <Clock size={14} className="text-gray-400" />
                                                    <span className="text-gray-600 text-sm">
                                                        {attempt.duration || "N/A"}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="py-3 px-2 text-center">
                                                <div className="flex items-center justify-center gap-1">
                                                    <Calendar size={14} className="text-gray-400" />
                                                    <span className="text-gray-600 text-sm">
                                                        {attempt.endTime
                                                            ? new Date(attempt.endTime).toLocaleString("vi-VN")
                                                            : "Chưa nộp"}
                                                    </span>
                                                </div>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        <History size={48} className="mx-auto mb-3 text-gray-300" />
                        <p>Bạn chưa có lịch sử làm bài nào.</p>
                    </div>
                )}

                {totalItems > limit && (
                    <div className="mt-6 flex justify-center border-t border-gray-200 pt-4">
                        <Pagination
                            currentPage={currentPage}
                            totalItems={totalItems}
                            limit={limit}
                            onPageChange={handlePageChange}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default HistoryView;
