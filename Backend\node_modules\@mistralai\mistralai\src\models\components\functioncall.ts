/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type Arguments = { [k: string]: any } | string;

export type FunctionCall = {
  name: string;
  arguments: { [k: string]: any } | string;
};

/** @internal */
export const Arguments$inboundSchema: z.ZodType<
  Arguments,
  z.ZodTypeDef,
  unknown
> = z.union([z.record(z.any()), z.string()]);

/** @internal */
export type Arguments$Outbound = { [k: string]: any } | string;

/** @internal */
export const Arguments$outboundSchema: z.ZodType<
  Arguments$Outbound,
  z.ZodTypeDef,
  Arguments
> = z.union([z.record(z.any()), z.string()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Arguments$ {
  /** @deprecated use `Arguments$inboundSchema` instead. */
  export const inboundSchema = Arguments$inboundSchema;
  /** @deprecated use `Arguments$outboundSchema` instead. */
  export const outboundSchema = Arguments$outboundSchema;
  /** @deprecated use `Arguments$Outbound` instead. */
  export type Outbound = Arguments$Outbound;
}

export function argumentsToJSON(value: Arguments): string {
  return JSON.stringify(Arguments$outboundSchema.parse(value));
}

export function argumentsFromJSON(
  jsonString: string,
): SafeParseResult<Arguments, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Arguments$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Arguments' from JSON`,
  );
}

/** @internal */
export const FunctionCall$inboundSchema: z.ZodType<
  FunctionCall,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.string(),
  arguments: z.union([z.record(z.any()), z.string()]),
});

/** @internal */
export type FunctionCall$Outbound = {
  name: string;
  arguments: { [k: string]: any } | string;
};

/** @internal */
export const FunctionCall$outboundSchema: z.ZodType<
  FunctionCall$Outbound,
  z.ZodTypeDef,
  FunctionCall
> = z.object({
  name: z.string(),
  arguments: z.union([z.record(z.any()), z.string()]),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionCall$ {
  /** @deprecated use `FunctionCall$inboundSchema` instead. */
  export const inboundSchema = FunctionCall$inboundSchema;
  /** @deprecated use `FunctionCall$outboundSchema` instead. */
  export const outboundSchema = FunctionCall$outboundSchema;
  /** @deprecated use `FunctionCall$Outbound` instead. */
  export type Outbound = FunctionCall$Outbound;
}

export function functionCallToJSON(functionCall: FunctionCall): string {
  return JSON.stringify(FunctionCall$outboundSchema.parse(functionCall));
}

export function functionCallFromJSON(
  jsonString: string,
): SafeParseResult<FunctionCall, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FunctionCall$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FunctionCall' from JSON`,
  );
}
