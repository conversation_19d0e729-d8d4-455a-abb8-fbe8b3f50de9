/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  MessageInputEntry,
  MessageInputEntry$inboundSchema,
  MessageInputEntry$Outbound,
  MessageInputEntry$outboundSchema,
} from "./messageinputentry.js";
import {
  MessageOutputEntry,
  MessageOutputEntry$inboundSchema,
  MessageOutputEntry$Outbound,
  MessageOutputEntry$outboundSchema,
} from "./messageoutputentry.js";

export type MessageEntries = MessageInputEntry | MessageOutputEntry;

/** @internal */
export const MessageEntries$inboundSchema: z.ZodType<
  MessageEntries,
  z.ZodTypeDef,
  unknown
> = z.union([
  MessageInputEntry$inboundSchema,
  MessageOutputEntry$inboundSchema,
]);

/** @internal */
export type MessageEntries$Outbound =
  | MessageInputEntry$Outbound
  | MessageOutputEntry$Outbound;

/** @internal */
export const MessageEntries$outboundSchema: z.ZodType<
  MessageEntries$Outbound,
  z.ZodTypeDef,
  MessageEntries
> = z.union([
  MessageInputEntry$outboundSchema,
  MessageOutputEntry$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageEntries$ {
  /** @deprecated use `MessageEntries$inboundSchema` instead. */
  export const inboundSchema = MessageEntries$inboundSchema;
  /** @deprecated use `MessageEntries$outboundSchema` instead. */
  export const outboundSchema = MessageEntries$outboundSchema;
  /** @deprecated use `MessageEntries$Outbound` instead. */
  export type Outbound = MessageEntries$Outbound;
}

export function messageEntriesToJSON(messageEntries: MessageEntries): string {
  return JSON.stringify(MessageEntries$outboundSchema.parse(messageEntries));
}

export function messageEntriesFromJSON(
  jsonString: string,
): SafeParseResult<MessageEntries, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MessageEntries$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MessageEntries' from JSON`,
  );
}
