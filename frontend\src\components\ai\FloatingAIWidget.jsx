import React, { useState } from 'react';
import { Bot, X } from 'lucide-react';
import AIWidget from './AIWidget';

const FloatingAIWidget = ({ onImageUploaded }) => {
    const [isVisible, setIsVisible] = useState(false);

    return (
        <>
            {/* Floating Button */}
            {!isVisible && (
                <button
                    onClick={() => setIsVisible(true)}
                    className="fixed bottom-6 right-6 z-50 w-14 h-14 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group"
                    title="Mở AI Widget"
                >
                    <Bot className="w-6 h-6" />
                    <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                </button>
            )}

            {/* Floating AI Widget Panel */}
            {isVisible && (
                <div className="fixed bottom-6 right-6 z-50 w-96 max-h-[80vh] bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden">
                    {/* Header */}
                    <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
                        <div className="flex items-center gap-2">
                            <Bot className="w-5 h-5" />
                            <h3 className="font-semibold text-sm">AI Widget</h3>
                        </div>
                        <button
                            onClick={() => setIsVisible(false)}
                            className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                            title="Đóng"
                        >
                            <X className="w-4 h-4" />
                        </button>
                    </div>

                    {/* Content */}
                    <div className="max-h-[calc(80vh-60px)] overflow-y-auto">
                        <AIWidget
                            onImageUploaded={onImageUploaded}
                            className="border-0 shadow-none"
                            forceOpen={true}
                        />
                    </div>
                </div>
            )}

            {/* Backdrop */}
            {isVisible && (
                <div 
                    className="fixed inset-0 z-40 bg-black bg-opacity-10"
                    onClick={() => setIsVisible(false)}
                />
            )}
        </>
    );
};

export default FloatingAIWidget;
