import db from '../models/index.js';
import { Op } from 'sequelize';
import * as classService from './class.service.js';
import * as lessonService from './lesson.service.js';
import { deletePdfFromFirebase } from '../utils/pdfUpload.js';
import LearningItemType from '../constants/LearningItemType.js';
import ResponseDataPagination from '../dtos/responses/pagination/PaginationResponse.js';


/**
 * L<PERSON>y danh sách LearningItem của sinh viên trong khoảng thời gian nhất định
 * @param {number|string} studentId - ID của sinh viên
 * @param {string|Date} startTime - Thời gian bắt đầu (ISO string hoặc Date object)
 * @param {string|Date} endTime - Thời gian kết thúc (ISO string hoặc Date object)
 * @param {object|null} transaction - Optional transaction object của Sequelize
 * @returns {Promise<Array>} - <PERSON><PERSON> sách StudentStudyStatus kèm LearningItem
 */
export const getLearningItemByTime = async (studentId, startTime, endTime, transaction = null) => {
    if (!studentId || !startTime || !endTime) {
        throw new Error('Thiếu thông tin studentId, startTime hoặc endTime');
    }

    const learningItems = await db.StudentStudyStatus.findAll({
        where: {
            studentId,
        },
        include: [
            {
                model: db.LearningItem,
                as: 'learningItem',
                where: {
                    deadline: {
                        [Op.between]: [new Date(startTime), new Date(endTime)],
                    },
                },
            },
        ],
        transaction,
    });

    return learningItems;
};


export const checkUrlDuplicate = async (id, url) => {
    const existingItem = await db.LearningItem.findOne({ where: { id: { [Op.ne]: id }, url } });
    return existingItem ? true : false;
};

export const deleteAllLearningItemByLessonId = async (lessonIds, transaction = null) => {
    try {
        const options = { where: { lessonId: { [Op.in]: lessonIds } } };

        if (transaction) {
            options.transaction = transaction;
        }

        const learningItems = await db.LearningItem.findAll(options);

        // Lưu danh sách file cần xóa sau khi database thành công
        const filesToDelete = [];
        for (const learningItem of learningItems) {
            if (learningItem.typeOfLearningItem === LearningItemType.DOCUMENT && learningItem.url) {
                const check = await checkUrlDuplicate(learningItem.id, learningItem.url);
                if (!check) {
                    filesToDelete.push(learningItem.url);
                }
            }
        }

        // Xóa trong database trước
        const deleted = await db.LearningItem.destroy(options);

        if (!deleted) {
            throw new Error("Xóa mục học tập không thành công!");
        }

        // Chỉ xóa file trên Firebase sau khi database đã thành công
        // Nếu có transaction, sẽ xóa file sau khi transaction commit
        if (filesToDelete.length > 0 && !transaction) {
            for (const fileUrl of filesToDelete) {
                try {
                    await deletePdfFromFirebase(fileUrl);
                } catch (firebaseError) {
                    console.error("Lỗi khi xóa file trên Firebase:", firebaseError);
                    // Không throw error vì database đã thành công
                }
            }
        }

        // Trả về thông tin file cần xóa để controller xử lý sau khi commit transaction
        return {
            success: true,
            filesToDelete: filesToDelete
        };
    } catch (error) {
        console.error("Xóa mục học tập thất bại:", error);
        throw error;
    }
};


export const deleteLearningItem = async (learningItemId, transaction = null) => {
    try {
        const options = { where: { id: learningItemId } };

        if (transaction) {
            options.transaction = transaction;
        }

        const learningItem = await db.LearningItem.findOne(options);

        if (!learningItem) {
            throw new Error("Không tìm thấy mục học tập để xóa!");
        }

        // Lưu thông tin file để xóa sau khi database thành công
        let fileToDelete = null;
        if (learningItem.typeOfLearningItem === LearningItemType.DOCUMENT && learningItem.url) {
            const check = await checkUrlDuplicate(learningItem.id, learningItem.url);
            if (!check) {
                fileToDelete = learningItem.url;
            }
        }

        // Xóa trong database trước
        const deleted = await db.LearningItem.destroy(options);

        if (!deleted) {
            throw new Error("Xóa mục học tập không thành công!");
        }

        // Chỉ xóa file trên Firebase sau khi database đã thành công
        // Nếu có transaction, sẽ xóa file sau khi transaction commit
        if (fileToDelete && !transaction) {
            try {
                await deletePdfFromFirebase(fileToDelete);
            } catch (firebaseError) {
                console.error("Lỗi khi xóa file trên Firebase:", firebaseError);
                // Không throw error vì database đã thành công
            }
        }

        // Trả về thông tin file cần xóa để controller xử lý sau khi commit transaction
        return {
            success: true,
            fileToDelete: fileToDelete
        };
    } catch (error) {
        console.error("Xóa mục học tập thất bại:", error);
        throw error;
    }
};

export const getAllLearningItemIdByClassId = async (classId, transaction = null) => {
    const lessons = await lessonService.getLessonByClassId(classId, {
        includeLearningItems: true,
        transaction,
    });

    const learningItems = lessons.flatMap(lesson => lesson.learningItems || []);
    return learningItems.map(item => item.id);
};

export const createStatusesForStudent = async (studentId, classId, transaction) => {

    const learningItemIds = await getAllLearningItemIdByClassId(classId, transaction);

    const studentStudyStatuses = learningItemIds.map(learningItemId => ({
        studentId,
        learningItemId,
        isDone: false,
        studyTime: null,
    }));

    await db.StudentStudyStatus.bulkCreate(studentStudyStatuses, { transaction });
};

export const getUncompletedLearningItemService = async (studentId, page = 1, limit = 10) => {
    const offset = (page - 1) * limit;

    const { count, rows: uncompletedItems } = await db.StudentStudyStatus.findAndCountAll({
        where: { studentId, isDone: false },
        include: [
            {
                model: db.LearningItem,
                as: 'learningItem',
                include: [
                    {
                        model: db.Lesson,
                        as: 'lesson',
                        attributes: ['id', 'name'],
                        include: [
                            {
                                model: db.Class,
                                as: 'class',
                                attributes: ['name', 'class_code']
                            }
                        ]
                    },
                ],
            },
        ],
        order: [['createdAt', 'DESC']],
        offset,
        limit
    });

    return new ResponseDataPagination(uncompletedItems, {
        page,
        pageSize: limit,
        total: count,
        totalPages: Math.ceil(count / limit),
    });
};