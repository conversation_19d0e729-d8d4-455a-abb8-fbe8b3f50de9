import { useDispatch, useSelector } from "react-redux";
import QuestionSectionTitle from "./QuestionSectionTitle";
import LoadingQuestions from "./LoadingQuestions";
import { useEffect, useState } from "react";
import TrueFalseQuestion from "./TrueFalseQuestion";
import MultipleChoiceQuestion from "./MultipleChoiceQuestion";
import ShortAnswerQuestion from "./ShortAnswerQuestion";
import { setView } from "src/features/doExam/doExamSlice";
import SubmitButton from "./SubmitButton";

const Question = ({ question, index }) => {
    if (question.typeOfQuestion === "TN") {
        return (
            <MultipleChoiceQuestion question={question} index={index} />
        )
    } else if (question.typeOfQuestion === "DS") {
        return (
            <TrueFalseQuestion question={question} index={index} />
        )
    } else if (question.typeOfQuestion === "TLN") {
        return (
            <ShortAnswerQuestion question={question} index={index} />
        )
    }
}

const QuestionSection = ({ title, questions, questionRefs }) => {
    const { darkMode } = useSelector((state) => state.doExam);
    return (
        <>
            <div className="flex flex-col gap-4 ">
                {questions.map((question, index) => (
                    <div
                        key={question.id} // ✅ cần thêm key
                        ref={el => questionRefs.current[question.id] = el}
                        id={`question-${question.id}`}
                    >
                        <Question question={question} index={index} />
                        <hr className="border-gray-200 dark:border-gray-700" />
                    </div>
                ))}
            </div>
        </>
    )
}

const sectionList = [
    { key: "TN", title: "Phần I - Trắc nghiệm", short: "TN" },
    { key: "DS", title: "Phần II - Đúng sai", short: "DS" },
    { key: "TLN", title: "Phần III - Trả lời ngắn", short: "TLN" },
];

const NavigateQuestionSection = () => {
    const dispatch = useDispatch();
    const { view, darkMode } = useSelector((state) => state.doExam);

    return (
        <div className={`flex text-sm w-full border-b ${darkMode ? "border-gray-700 bg-gray-800" : "border-gray-200 bg-white"}`}>
            {sectionList.map((item) => (
                <button
                    key={item.key}
                    onClick={() => dispatch(setView(item.key))}
                    className={`flex-1 py-2 px-3 text-center cursor-pointer transition-colors duration-200 first:border-r last:border-l
      ${view === item.key
                            ? `font-semibold ${darkMode ? "text-white border-b-2 border-white" : "text-blue-700 border-b-2 border-blue-500"}`
                            : `${darkMode ? "text-gray-400" : "text-gray-600"} border-b border-gray-300`
                        }`}
                >
                    <QuestionSectionTitle title={item.title} short={item.short} />
                </button>
            ))}
        </div>
    );
};


const ExamContent = ({ questionRefs }) => {
    const { darkMode, loadingQuestions, view, questionTN, questionDS, questionTLN } = useSelector((state) => state.doExam);

    return (
        <div
            className={`rounded border border-gray-200 flex h-fit flex-1 flex-col pb-4 overflow-y-auto shadow  gap-4 ${darkMode ? 'bg-slate-800 text-white' : 'bg-white text-black'
                }`}
        >
            <NavigateQuestionSection />

            <div className="flex flex-col px-4">
                {loadingQuestions ? (
                    <LoadingQuestions />
                ) : (
                    <>
                        <div className="sm:hidden block">
                            <SubmitButton />

                        </div>
                        {view === "TN" && (
                            <QuestionSection title="Phần I - Trắc nghiệm" questions={questionTN} questionRefs={questionRefs} />
                        )}
                        {view === "DS" && (
                            <QuestionSection title="Phần II - Đúng sai" questions={questionDS} questionRefs={questionRefs} />
                        )}
                        {view === "TLN" && (
                            <QuestionSection title="Phần III - Trả lời ngắn" questions={questionTLN} questionRefs={questionRefs} />
                        )}
                    </>
                )}
            </div>

        </div>

    )
}

export default ExamContent;
