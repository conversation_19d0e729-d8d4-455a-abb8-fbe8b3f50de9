import api from "./api";

// [GET] <PERSON><PERSON><PERSON> tất cả buổi học từ các lớp mà người dùng đã tham gia (status = JS và class status = LHD)
export const getUserAttendedLessonsAPI = async (userId) => {
    try {
        const response = await api.get(`/v1/admin/lesson/attended/${userId}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

// [GET] Lấy chi tiết buổi học theo ID
export const getLessonByIdAPI = async (lessonId) => {
    try {
        const response = await api.get(`/v1/lesson/${lessonId}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const findLessonsAPI = (search) => {
    return api.get("/v1/admin/lesson/search", {
        params: {
            search,
        }
    });
}

// [GET] L<PERSON>y danh sách buổi học theo classId
export const getLessonsByClassIdAPI = async (classId) => {
    try {
        const response = await api.get(`/v1/user/lesson/class/${classId}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

// [POST] Tạo buổi học mới (dành cho giáo viên/admin)
export const createLessonAPI = async (lessonData) => {
    try {
        const response = await api.post('/v1/lesson', lessonData);
        return response.data;
    } catch (error) {
        throw error;
    }
};

// [PUT] Cập nhật buổi học (dành cho giáo viên/admin)
export const updateLessonAPI = async (lessonId, lessonData) => {
    try {
        const response = await api.put(`/v1/lesson/${lessonId}`, lessonData);
        return response.data;
    } catch (error) {
        throw error;
    }
};

// [DELETE] Xóa buổi học (dành cho giáo viên/admin)
export const deleteLessonAPI = async (lessonId) => {
    try {
        const response = await api.delete(`/v1/lesson/${lessonId}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getRecentLessonsByUserAPI = async () => {
    return await api.get('/v1/user/recent/lessons');
};
