import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import Roles from '../constants/Roles.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'
import PostQuestionRequest from '../dtos/requests/question/PostQuestionRequest.js'
import PutQuestionRequest from '../dtos/requests/question/PutQuestionRequest.js'
import * as QuestionController from '../controllers/QuestionController.js'

const router = express.Router()

router.get('/v1/admin/question',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    asyncHandler(QuestionController.getQuestion)
)
router.get('/v1/user/question/exam/:examId',
    requireRoles(Roles.JustStudent),
    asyncHandler(QuestionController.getQuestionByExamId)
)
router.get('/v1/admin/question/:id',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    asyncHandler(QuestionController.getQuestionById)
)

router.get('/v1/user/question/search',
    requireRoles(Roles.JustStudent),
    asyncHandler(QuestionController.findQuestions)
)

router.get('/v1/user/question/:id',
    requireRoles(Roles.JustStudent),
    asyncHandler(QuestionController.getQuestionById)
)



router.post('/v1/admin/question',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    // validate(PostQuestionRequest),
    uploadGoogleImageMiddleware.fields([
        { name: 'questionImage', maxCount: 1 },
        { name: 'statementImages', maxCount: 4 },
        { name: 'solutionImage', maxCount: 1 }
    ]),
    asyncHandler(QuestionController.postQuestion)
)

router.put('/v1/admin/question/:id',
    // validate(PutQuestionRequest),
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    asyncHandler(QuestionController.putQuestion)
)

router.put('/v1/admin/question/:id/image',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    uploadGoogleImageMiddleware.single('questionImage'),
    asyncHandler(QuestionController.putQuestionImage)
)

router.put('/v1/admin/question/:id/solutionImage',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    uploadGoogleImageMiddleware.single('solutionImage'),
    asyncHandler(QuestionController.putQuestionSolutionImage)
)
router.put('/v1/admin/exam/:examId/questions',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    asyncHandler(QuestionController.putQuestionsExam)
)

router.delete('/v1/admin/question/:id',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    asyncHandler(QuestionController.deleteQuestion)
)
router.delete('/v1/admin/question/:id/image',
    requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    asyncHandler(QuestionController.deleteQuestionImage)
)

router.put('/v1/admin/rename-image/question',
    // requireRoles(Roles.JustAdmin),
    asyncHandler(QuestionController.renameImageQuestion)
)


export default router
