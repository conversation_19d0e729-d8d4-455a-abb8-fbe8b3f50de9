import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as examApi from "../../services/examApi";
import { apiHandler } from "../../utils/apiHandler";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";
import * as studentExamApi from "../../services/studentExamApi";
import * as examCommentsApi from "../../services/examCommentsApi";


export const fetchPublicExamById = createAsyncThunk(
    "examDetail/fetchPublicExamById",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, examApi.getExamPublic, id, () => { }, false, false);
    }
);

export const saveExamForUser = createAsyncThunk(
    "examDetail/saveExamForUser",
    async ({ examId }, { dispatch }) => {
        return await apiHandler(dispatch, studentExamApi.saveExamForUserAPI, { examId }, () => { }, true, false, false, false);
    }
);

export const fetchRelatedExams = createAsyncThunk(
    "exams/fetchRelatedExams",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => { }, false, false);
    }
);

// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết
export const fetchRelatedExamsIfNeeded = createAsyncThunk(
    "exams/fetchRelatedExamsIfNeeded",
    async (id, { dispatch, getState }) => {
        const state = getState();
        const { relatedExams, lastFetchedRelatedExams } = state.exams;

        // Kiểm tra xem đã có dữ liệu trong cache chưa
        const hasData = relatedExams[id] && relatedExams[id].length > 0;

        // Kiểm tra thời gian cache (5 phút = 300000ms)
        const now = Date.now();
        const lastFetched = lastFetchedRelatedExams[id] || 0;
        const isCacheValid = now - lastFetched < 300000;

        // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache
        if (hasData && isCacheValid) {
            // Cập nhật state.exams để hiển thị dữ liệu cache
            return { data: relatedExams[id] };
        }

        // Nếu không có dữ liệu hoặc cache hết hạn, gọi API
        return await dispatch(fetchRelatedExams(id)).unwrap();
    }
);

export const fetchExamRatingStatistics = createAsyncThunk(
    "examDetail/fetchExamRatingStatistics",
    async (examId, { dispatch }) => {
        return await apiHandler(dispatch, studentExamApi.getExamRatingStatistics, examId, () => { }, false, false);
    }
);

export const rateExamForUser = createAsyncThunk(
    "examDetail/rateExamForUser",
    async ({ examId, star }, { dispatch }) => {
        return await apiHandler(dispatch, studentExamApi.rateExamForUser, { examId, star }, () => { }, true, false, false, false);
    }
);

export const getTotalScoreExam = createAsyncThunk(
    "examDetail/getTotalScoreExam",
    async (examId, { dispatch }) => {
        return await apiHandler(dispatch, examApi.getTotalScoreExamAPI, examId, () => { }, false, false);
    }
);

const examDetailSlice = createSlice({
    name: "examDetail",
    initialState: {
        exam: null,
        loading: false,
        relatedExams: [],
        loadingRelatedExams: false,
        view: 'detail',
        ratingStatistics: null,
        loadingRatingStatistics: false,
        totalScore: null,
        loadingTotalScore: false,
    },
    reducers: {
        setView: (state, action) => {
            state.view = action.payload;
        },
        setStar: (state, action) => {
            if (!state.ratingStatistics || !state.ratingStatistics.studentStatus) return;
            state.ratingStatistics.studentStatus.star = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchPublicExamById.pending, (state) => {
                state.exam = null;
                state.loading = true;
            })
            .addCase(fetchPublicExamById.fulfilled, (state, action) => {
                if (action.payload) {
                    state.exam = action.payload.data;
                }
                state.loading = false;
            })
            .addCase(fetchPublicExamById.rejected, (state) => {
                state.exam = null;
                state.loading = false;
            })
            .addCase(saveExamForUser.fulfilled, (state, action) => {
                if (action.payload) {
                    const { examId, isSave } = action.payload.data;

                    if (state.ratingStatistics) {
                        state.ratingStatistics.studentStatus.isSave = isSave;
                        state.ratingStatistics.isSaveCount = isSave ? state.ratingStatistics.isSaveCount + 1 : state.ratingStatistics.isSaveCount - 1;
                    }
                }
            })
            .addCase(fetchRelatedExams.pending, (state) => {
                state.loadingRelatedExams = true;
            })
            .addCase(fetchRelatedExams.fulfilled, (state, action) => {
                if (action.payload) {
                    state.relatedExams = action.payload.data;
                }
                state.loadingRelatedExams = false;
            })
            .addCase(fetchRelatedExams.rejected, (state) => {
                state.loadingRelatedExams = false;
            })
            .addCase(fetchRelatedExamsIfNeeded.pending, (state) => {
                state.loadingRelatedExams = true;
            })
            .addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {
                if (action.payload) {
                    state.relatedExams = action.payload.data;
                }
                state.loadingRelatedExams = false;
            })
            .addCase(fetchRelatedExamsIfNeeded.rejected, (state) => {
                state.loadingRelatedExams = false;
            })
            .addCase(fetchExamRatingStatistics.pending, (state) => {
                state.loadingRatingStatistics = true;
            })
            .addCase(fetchExamRatingStatistics.fulfilled, (state, action) => {
                if (action.payload) {
                    state.ratingStatistics = action.payload.data;
                }
                state.loadingRatingStatistics = false;
            })
            .addCase(fetchExamRatingStatistics.rejected, (state) => {
                state.loadingRatingStatistics = false;
            })
            .addCase(rateExamForUser.fulfilled, (state, action) => {
                if (action.payload) {
                    const { avgStar, starCount } = action.payload.data;
                    if (state.ratingStatistics) {
                        state.ratingStatistics.avgStar = avgStar;
                        state.ratingStatistics.starCount = starCount;
                    }
                }
            })
            .addCase(getTotalScoreExam.pending, (state) => {
                state.loadingTotalScore = true;
            })
            .addCase(getTotalScoreExam.fulfilled, (state, action) => {
                if (action.payload) {
                    state.totalScore = action.payload.data;
                }
                state.loadingTotalScore = false;
            })
            .addCase(getTotalScoreExam.rejected, (state) => {
                state.loadingTotalScore = false;
            })
    },
});

export const { setView, setStar } = examDetailSlice.actions;
export default examDetailSlice.reducer;
