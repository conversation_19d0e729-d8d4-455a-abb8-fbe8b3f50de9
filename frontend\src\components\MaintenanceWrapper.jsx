import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import MaintenancePage from '../pages/MaintenancePage';
import { isMaintenanceModeActive, shouldBypassMaintenance } from '../utils/maintenanceUtils';

const MaintenanceWrapper = ({ children }) => {
    const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
    const location = useLocation();

    useEffect(() => {
        // Check maintenance status on component mount and route changes
        const checkMaintenance = () => {
            const maintenanceStatus = isMaintenanceModeActive();

            // Check if current route should bypass maintenance
            const shouldBypass = shouldBypassMaintenance(location.pathname);

            setIsMaintenanceMode(maintenanceStatus && !shouldBypass);
        };

        checkMaintenance();

        // Listen for storage changes (in case maintenance mode is toggled from another tab)
        const handleStorageChange = (e) => {
            if (e.key === 'maintenanceMode') {
                checkMaintenance();
            }
        };

        window.addEventListener('storage', handleStorageChange);

        // Check maintenance status periodically (every 30 seconds)
        const interval = setInterval(checkMaintenance, 30000);

        return () => {
            window.removeEventListener('storage', handleStorageChange);
            clearInterval(interval);
        };
    }, [location.pathname]);

    // If maintenance mode is enabled, show maintenance page
    if (isMaintenanceMode) {
        return <MaintenancePage />;
    }

    // Otherwise, render the normal app
    return children;
};

export default MaintenanceWrapper;
