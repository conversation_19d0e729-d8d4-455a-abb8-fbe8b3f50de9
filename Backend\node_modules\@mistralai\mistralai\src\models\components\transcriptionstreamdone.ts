/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  TranscriptionSegmentChunk,
  TranscriptionSegmentChunk$inboundSchema,
  TranscriptionSegmentChunk$Outbound,
  TranscriptionSegmentChunk$outboundSchema,
} from "./transcriptionsegmentchunk.js";
import {
  UsageInfo,
  UsageInfo$inboundSchema,
  UsageInfo$Outbound,
  UsageInfo$outboundSchema,
} from "./usageinfo.js";

export const TranscriptionStreamDoneType = {
  TranscriptionDone: "transcription.done",
} as const;
export type TranscriptionStreamDoneType = ClosedEnum<
  typeof TranscriptionStreamDoneType
>;

export type TranscriptionStreamDone = {
  model: string;
  text: string;
  segments?: Array<TranscriptionSegmentChunk> | undefined;
  usage: UsageInfo;
  type?: TranscriptionStreamDoneType | undefined;
  language: string | null;
  additionalProperties?: { [k: string]: any };
};

/** @internal */
export const TranscriptionStreamDoneType$inboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamDoneType
> = z.nativeEnum(TranscriptionStreamDoneType);

/** @internal */
export const TranscriptionStreamDoneType$outboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamDoneType
> = TranscriptionStreamDoneType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamDoneType$ {
  /** @deprecated use `TranscriptionStreamDoneType$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamDoneType$inboundSchema;
  /** @deprecated use `TranscriptionStreamDoneType$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamDoneType$outboundSchema;
}

/** @internal */
export const TranscriptionStreamDone$inboundSchema: z.ZodType<
  TranscriptionStreamDone,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    model: z.string(),
    text: z.string(),
    segments: z.array(TranscriptionSegmentChunk$inboundSchema).optional(),
    usage: UsageInfo$inboundSchema,
    type: TranscriptionStreamDoneType$inboundSchema.default(
      "transcription.done",
    ),
    language: z.nullable(z.string()),
  }).catchall(z.any()),
  "additionalProperties",
  true,
);

/** @internal */
export type TranscriptionStreamDone$Outbound = {
  model: string;
  text: string;
  segments?: Array<TranscriptionSegmentChunk$Outbound> | undefined;
  usage: UsageInfo$Outbound;
  type: string;
  language: string | null;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const TranscriptionStreamDone$outboundSchema: z.ZodType<
  TranscriptionStreamDone$Outbound,
  z.ZodTypeDef,
  TranscriptionStreamDone
> = z.object({
  model: z.string(),
  text: z.string(),
  segments: z.array(TranscriptionSegmentChunk$outboundSchema).optional(),
  usage: UsageInfo$outboundSchema,
  type: TranscriptionStreamDoneType$outboundSchema.default(
    "transcription.done",
  ),
  language: z.nullable(z.string()),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamDone$ {
  /** @deprecated use `TranscriptionStreamDone$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamDone$inboundSchema;
  /** @deprecated use `TranscriptionStreamDone$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamDone$outboundSchema;
  /** @deprecated use `TranscriptionStreamDone$Outbound` instead. */
  export type Outbound = TranscriptionStreamDone$Outbound;
}

export function transcriptionStreamDoneToJSON(
  transcriptionStreamDone: TranscriptionStreamDone,
): string {
  return JSON.stringify(
    TranscriptionStreamDone$outboundSchema.parse(transcriptionStreamDone),
  );
}

export function transcriptionStreamDoneFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionStreamDone, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionStreamDone$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionStreamDone' from JSON`,
  );
}
