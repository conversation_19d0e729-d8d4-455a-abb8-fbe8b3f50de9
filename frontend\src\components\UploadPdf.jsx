import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { setSuccessMessage, setErrorMessage } from "../features/state/stateApiSlice";
import { Upload, Trash2, FileText, X } from "lucide-react";

const UploadPdfForm = ({
    id,
    onSubmit,
    loading,
    setPdf = () => { },
    deleteButton = true,
    compact = false,
    className = '',
    showSubmitButton = true,
}) => {
    const dispatch = useDispatch();
    const [pdfFile, setPdfFile] = useState(null);
    const [isDragging, setIsDragging] = useState(false);

    // Generate unique input id for each instance
    const inputId = `pdf-input-${id || 'default'}-${Math.random().toString(36).slice(2, 11)}`;

    const handleFile = (file) => {
        if (file && file.type === "application/pdf") {
            setPdfFile(file);
        } else {
            dispatch(setErrorMessage("❌ Vui lòng chọn một file PDF hợp lệ."));
        }
    };

    useEffect(() => {
        setPdf(pdfFile);
    }, [pdfFile, setPdf]);

    const handleFileChange = (e) => {
        handleFile(e.target.files[0]);
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            handleFile(e.dataTransfer.files[0]);
            e.dataTransfer.clearData();
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Gọi hàm onSubmit từ prop và truyền vào dữ liệu cần thiết
        if (onSubmit) {
            await onSubmit({ id, pdfFile });
        }
    };

    const handleRemoveFile = () => {
        setPdfFile(null);
    };

    // Compact mode for space-optimized layouts
    if (compact) {
        return (
            <div className={`relative ${className}`}>
                {!pdfFile ? (
                    <div
                        onClick={() => document.getElementById(inputId).click()}
                        onDrop={handleDrop}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        className={`w-full h-full p-4 min-h-[3rem] flex flex-col items-center justify-center cursor-pointer rounded border-2 border-dashed transition-all duration-200 ${
                            isDragging
                                ? "border-blue-400 bg-blue-50"
                                : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
                        }`}
                    >
                        <FileText className="w-4 h-4 text-gray-400 mb-1" />
                        <span className="text-xs text-gray-600">Chọn PDF</span>
                    </div>
                ) : (
                    <div className="relative group">
                        <div className="flex items-center space-x-2 p-2 bg-white border border-gray-200 rounded">
                            <FileText className="w-4 h-4 text-red-600" />
                            <span className="text-xs text-gray-700 truncate flex-1">
                                {pdfFile.name}
                            </span>
                        </div>
                        <button
                            type="button"
                            onClick={handleRemoveFile}
                            className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        >
                            <X className="w-3 h-3" />
                        </button>
                    </div>
                )}
                <input
                    id={inputId}
                    type="file"
                    accept="application/pdf"
                    onChange={handleFileChange}
                    className="hidden"
                />
            </div>
        );
    }

    // Standard mode
    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onClick={() => document.getElementById(inputId).click()}
                className={`flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${
                    className || 'w-full min-h-[8rem]'
                } ${
                    isDragging
                        ? "border-blue-400 bg-blue-50"
                        : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
                }`}
            >
                {pdfFile ? (
                    <div className="relative group w-full">
                        <div className="flex items-center justify-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg">
                            <FileText className="w-8 h-8 text-red-600" />
                            <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                    {pdfFile.name}
                                </p>
                                <p className="text-xs text-gray-500">
                                    {(pdfFile.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                            </div>
                        </div>
                        <button
                            type="button"
                            onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveFile();
                            }}
                            className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md"
                        >
                            <Trash2 className="w-4 h-4" />
                        </button>
                    </div>
                ) : (
                    <>
                        <div className="flex flex-col items-center text-center space-y-2">
                            <div className="p-2 bg-gray-200 rounded-full">
                                <FileText className="w-6 h-6 text-gray-600" />
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-gray-700">
                                    Chọn file PDF hoặc kéo thả vào đây
                                </p>
                                <p className="text-xs text-gray-500">
                                    Chỉ hỗ trợ file PDF
                                </p>
                            </div>
                        </div>
                        <button
                            type="button"
                            className="mt-3 px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
                        >
                            Chọn file PDF
                        </button>
                    </>
                )}
                <input
                    id={inputId}
                    type="file"
                    accept="application/pdf"
                    onChange={handleFileChange}
                    className="hidden"
                />
            </div>

            {showSubmitButton && (
                <div className="flex justify-end gap-3">
                    {pdfFile ? (
                        <button
                            type="submit"
                            disabled={loading}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        >
                            {loading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    Đang tải...
                                </>
                            ) : (
                                <>
                                    <Upload className="w-4 h-4" />
                                    Tải lên
                                </>
                            )}
                        </button>
                    ) : (deleteButton && (
                        <button
                            type="submit"
                            disabled={loading}
                            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        >
                            {loading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    Đang xóa...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="w-4 h-4" />
                                    Xóa
                                </>
                            )}
                        </button>
                    ))}
                </div>
            )}
        </form>
    );
};

export default UploadPdfForm;
