import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as LearningItemApi from "../../services/learningItemApi";
import { apiHandler } from "../../utils/apiHandler";


export const getUncompletedLearningItem = createAsyncThunk(
    "learningItems/getUncompletedLearningItem",
    async ({ page = 1, limit = 10 }, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getUncompletedLearningItemAPI, { page, limit }, () => { }, false, false, false, false);
    }
);

export const getLearningItemWeekend = createAsyncThunk(
    "learningItems/getLearningItemWeekend",
    async ({ startOfWeek, endOfWeek }, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getLearningItemWeekendAPI, { startOfWeek, endOfWeek }, () => { }, false, false, false, false);
    }
);

export const getLearningItemDay = createAsyncThunk(
    "learningItems/getLearningItemDay",
    async (day, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getLearningItemDayAPI, day, () => { }, false, false, false, false);
    }
);

export const getLearningItemMonth = createAsyncThunk(
    "learningItems/getLearningItemMonth",
    async ({ firstDay, lastDay }, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getLearningItemMonthAPI, { firstDay, lastDay }, () => { }, false, false, false, false);
    }
);

const initialState = {
    learningItems: [],
    learningItemsWeekend: [],
    learningItemsDay: [],
    learningItemsMonth: [],
    loading: false,


    pagination: {
        page: 1,
        limit: 10,
        totalItems: 0,
        totalPages: 0
    }
};

const learningItemSlice = createSlice({
    name: "learningItems",
    initialState,
    reducers: {
        resetLearningItems: (state) => {
            state.learningItems = [];
            state.pagination = {
                page: 1,
                pageSize: 10,
                total: 0,
                totalPages: 0
            };
        },
        setCurrentPageLT: (state, action) => {
            state.pagination.page = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getUncompletedLearningItem.pending, (state) => {
                state.loading = true;
                state.learningItems = [];
            })
            .addCase(getUncompletedLearningItem.fulfilled, (state, action) => {
                let { data } = action.payload;
                if (!action.payload || !action.payload.data) {
                    state.loading = false;
                    return;
                }

                state.loading = false;
                state.learningItems = data.data;
                state.pagination = data.pagination;
            })
            .addCase(getUncompletedLearningItem.rejected, (state) => {
                state.loading = false;
            })
            .addCase(getLearningItemWeekend.pending, (state) => {
                state.learningItemsWeekend = [];
            })
            .addCase(getLearningItemWeekend.fulfilled, (state, action) => {
                let { data } = action.payload;
                if (!action.payload || !action.payload.data) {
                    return;
                }

                state.learningItemsWeekend = data;
                // console.log("getLearningItemWeekend", state.learningItemsWeekend);
            })
            .addCase(getLearningItemWeekend.rejected, (state) => {
                state.learningItemsWeekend = [];
            })
            .addCase(getLearningItemDay.pending, (state) => {
                state.learningItemsDay = [];
            })
            .addCase(getLearningItemDay.fulfilled, (state, action) => {
                let { data } = action.payload;

                if (!data) {
                    return;
                }
                // console.log("getLearningItemDay", data);
                state.learningItemsDay = data;
                // console.log("getLearningItemDay", state.learningItems);
            })
            .addCase(getLearningItemDay.rejected, (state) => {
                state.learningItemsDay = [];
            })
            .addCase(getLearningItemMonth.pending, (state) => {
                state.learningItemsMonth = [];
            })
            .addCase(getLearningItemMonth.fulfilled, (state, action) => {
                let { data } = action.payload;

                if (!data) {
                    return;
                }
                // console.log("getLearningItemMonth", data);
                state.learningItemsMonth = data;
                // console.log("getLearningItemMonth", state.learningItems);
            })
            .addCase(getLearningItemMonth.rejected, (state) => {
                state.learningItemsMonth = [];
            });
    }

});

export const { resetLearningItems, setCurrentPageLT } = learningItemSlice.actions;

export default learningItemSlice.reducer;
