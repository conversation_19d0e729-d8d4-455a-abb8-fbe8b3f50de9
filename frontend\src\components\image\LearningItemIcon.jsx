import { FileText, PenTool, Play } from "lucide-react";

const LearningItemIcon = ({ type, size = "w-5 h-5 " }) => {
    switch (type) {
        case "BTVN":
            return (
                <PenTool size={14} className="text-green-500" />
            );
        case "VID":
            return (
                <Play size={14} className="text-red-500" />
            );
        case "DOC":
            return (
                <FileText size={14} className="text-blue-500" />
            );
        default:
            return <FileText size={14} className="text-gray-500" />;
    }
};

export default LearningItemIcon;