{"name": "@lottiefiles/dotlottie-react", "version": "0.14.4", "type": "module", "description": "React wrapper around the dotlottie-web library", "repository": {"type": "git", "url": "git+https://github.com/LottieFiles/dotlottie-web.git", "directory": "packages/react"}, "homepage": "https://github.com/LottieFiles/dotlottie-web#readme", "bugs": "https://github.com/LottieFiles/dotlottie-web/issues", "author": "<PERSON><PERSON>F<PERSON>", "contributors": ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "main": "dist/index.js", "module": "dist/index.js", "browser": "dist/browser/index.js", "types": "dist/index.d.ts", "files": ["dist"], "keywords": ["<PERSON><PERSON><PERSON>", "lottie", "player", "animation", "web", "canvas", "javascript", "react", "thorvg"], "peerDependencies": {"react": "^17 || ^18 || ^19"}, "dependencies": {"@lottiefiles/dotlottie-web": "0.49.0"}, "devDependencies": {"@testing-library/user-event": "^14.5.2", "@types/react": "^18", "@vitejs/plugin-react": "^4.2.1", "@vitest/browser": "2.1.0-beta.5", "@vitest/coverage-istanbul": "2.1.0-beta.5", "cross-env": "7.0.3", "playwright": "^1.52.0", "react": "^18", "tsup": "8.3.5", "typescript": "5.0.4", "vitest": "2.1.0-beta.5", "vitest-browser-react": "^0.0.4"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint --fix .", "stats:eslint": "cross-env TIMING=1 eslint .", "stats:ts": "tsc -p tsconfig.build.json --extendedDiagnostics", "test": "vitest run --browser.headless", "test:coverage": "vitest run --browser.headless --coverage", "test:watch": "vitest", "type-check": "tsc --noEmit"}}