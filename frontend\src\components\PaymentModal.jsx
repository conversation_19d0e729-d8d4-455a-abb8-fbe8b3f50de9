import React, { useState } from 'react';
import { X, Copy, Check, QrCode } from 'lucide-react';
// import QRCode from 'qrcode.react';
import QRHOCPHI from 'src/assets/images/qrhocphi.jpg';

const PaymentModal = ({ isOpen, onClose, paymentInfo }) => {
  const [copiedAccount, setCopiedAccount] = useState(null);
  const [activeTab, setActiveTab] = useState('mbbank');

  if (!isOpen) return null;

  const bankAccounts = [
    {
      id: 'mbbank',
      name: 'MB Bank',
      accountName: 'Ong <PERSON> Ng<PERSON>',
      accountNumber: '**********',
      qrValue: `Ong Khac Ngoc - MB Bank - ********** - ${paymentInfo?.description || 'Thanh toan hoc phi'}`
    },
  ];

  const handleCopy = (text, accountId) => {
    navigator.clipboard.writeText(text);
    setCopiedAccount(accountId);
    setTimeout(() => setCopiedAccount(null), 2000);
  };

  const activeBank = bankAccounts.find(bank => bank.id === activeTab);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-4 sm:p-6 border-b flex-shrink-0">
          <h2 className="text-base sm:text-lg font-semibold text-gray-800">Thông tin thanh toán</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors p-1"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content - Scrollable */}
        <div className="p-4 sm:p-6 overflow-y-auto flex-1">
          {/* Payment Info */}
          {paymentInfo && (
            <div className="mb-4 sm:mb-6 bg-blue-50 p-3 sm:p-4 rounded-lg space-y-3 sm:space-y-4">
              <h3 className="text-sm sm:text-base font-medium text-gray-800 mb-2">Thông tin học phí</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <p className="text-xs sm:text-sm text-gray-500">Tháng:</p>
                  <p className="text-sm sm:text-base font-medium">{paymentInfo.month}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-gray-500">Số tiền cần thanh toán:</p>
                  <p className="text-sm sm:text-base font-medium text-red-600 break-words">Liên hệ anh Triệu Minh qua zalo 0399520768 để biết số tiền</p>
                </div>
                <div className="sm:col-span-2">
                  <p className="text-xs sm:text-sm text-gray-500">Ghi chú:</p>
                  <p className="text-sm sm:text-base font-medium break-words">{paymentInfo.note}</p>
                </div>
              </div>
              <div>
                <p className="text-xs sm:text-sm text-gray-500">Nội dung chuyển khoản:</p>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1">
                  <p className="text-sm sm:text-base font-medium break-all flex-1">{paymentInfo.description}</p>
                  <button
                    onClick={() => handleCopy(paymentInfo.description, 'description')}
                    className="text-blue-600 hover:text-blue-800 p-2 sm:p-1 self-start sm:self-auto"
                    title="Sao chép"
                  >
                    {copiedAccount === 'description' ? <Check size={16} /> : <Copy size={16} />}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Bank Tabs - Responsive */}
          <div className="border-b border-gray-200 mb-4 overflow-x-auto">
            <div className="flex min-w-max sm:min-w-0">
              {bankAccounts.map(bank => (
                <button
                  key={bank.id}
                  onClick={() => setActiveTab(bank.id)}
                  className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === bank.id
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                    }`}
                >
                  {bank.name}
                </button>
              ))}
            </div>
          </div>

          {/* Bank Account Info - Responsive Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            <div className="order-2 lg:order-1">
              <h3 className="text-sm sm:text-base font-medium text-gray-800 mb-3 sm:mb-4">Thông tin tài khoản</h3>
              <div className="space-y-3 sm:space-y-4">
                <div>
                  <p className="text-xs sm:text-sm text-gray-500">Ngân hàng:</p>
                  <p className="text-sm sm:text-base font-medium">{activeBank.name}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-gray-500">Tên tài khoản:</p>
                  <p className="text-sm sm:text-base font-medium break-words">{activeBank.accountName}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-gray-500">Số tài khoản:</p>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                    <p className="text-sm sm:text-base font-medium">{activeBank.accountNumber}</p>
                    <button
                      onClick={() => handleCopy(activeBank.accountNumber, activeBank.id)}
                      className="text-blue-600 hover:text-blue-800 p-2 sm:p-1 self-start sm:self-auto"
                      title="Sao chép số tài khoản"
                    >
                      {copiedAccount === activeBank.id ? <Check size={16} /> : <Copy size={16} />}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* QR Code Section - Responsive */}
            <div className="flex flex-col items-center justify-center order-1 lg:order-2 py-4 lg:py-0">
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                {/* <QRCode
                  value={activeBank.qrValue}
                  size={180}
                  level="H"
                  includeMargin={true}
                  renderAs="svg"
                /> */}
                <div className="w-32 h-32 sm:w-40 sm:h-40 lg:w-44 lg:h-44 bg-gray-100 flex items-center justify-center rounded">
                  <img src={QRHOCPHI} alt="QR Code" className="w-full h-full object-contain" />
                </div>
              </div>
              <p className="mt-2 text-xs sm:text-sm text-gray-500 text-center">Quét mã QR để thanh toán</p>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-xs sm:text-sm text-gray-500">Nội dung chuyển khoản:</p>
            <p className="text-sm sm:text-base font-medium break-all">{paymentInfo?.description || 'Thanh toán học phí'}</p>
          </div>

          <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-yellow-50 rounded-lg">
            <p className="text-xs sm:text-sm text-yellow-800">
              <strong>Lưu ý:</strong> Sau khi chuyển khoản, vui lòng chụp màn hình biên lai và gửi cho quản lý qua Zalo để xác nhận thanh toán. Số điện thoại: 0399520768.
            </p>
          </div>
        </div>

        {/* Footer - Responsive */}
        <div className="p-4 sm:p-6 border-t bg-gray-50 flex justify-end flex-shrink-0">
          <button
            onClick={onClose}
            className="px-4 sm:px-6 py-2 sm:py-3 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm sm:text-base min-h-[44px] sm:min-h-0"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
