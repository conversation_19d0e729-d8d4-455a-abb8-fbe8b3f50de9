const ActionButton = ({ icon: Icon, title, shortTitle, isActive = false, onClick }) => {
    return (
        <button
            onClick={onClick}
            className={`w-fit px-2 py-[7px] rounded-md text-xs font-medium transition-colors whitespace-nowrap
                ${isActive
                    ? 'bg-sky-100 text-sky-700 border border-sky-300'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}
            `}
        >
            <Icon className="w-4 h-4 inline mr-1 lg:mr-2" />
            <span className="hidden sm:inline">{title}</span>
            <span className="sm:hidden">{shortTitle}</span>
        </button>
    );
};

export default ActionButton;
