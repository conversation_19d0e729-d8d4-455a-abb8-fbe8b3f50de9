import express from 'express';
import asyncHandler from '../middlewares/asyncHandler.js';
import UserType from '../constants/UserType.js';
import { requireRoles } from '../middlewares/jwtMiddleware.js';
import CronController from '../controllers/CronController.js';

const router = express.Router();

/**
 * Cron job management routes
 * T<PERSON>t cả routes này yêu cầu admin privileges
 */

// Get cron job status and information
router.get('/v1/admin/cron/status',
    requireRoles([UserType.ADMIN]),
    asyncHandler(CronController.getCronStatus)
);

// Get user deactivation statistics
router.get('/v1/admin/cron/deactivation-stats',
    requireRoles([UserType.ADMIN]),
    asyncHandler(CronController.getDeactivationStats)
);

// Preview users that will be deactivated
router.get('/v1/admin/cron/preview-deactivation',
    requireRoles([UserType.ADMIN]),
    asyncHandler(CronController.previewDeactivation)
);

// Manually trigger user deactivation
router.post('/v1/admin/cron/deactivate-users',
    requireRoles([UserType.ADMIN]),
    asyncHandler(CronController.triggerUserDeactivation)
);

export default router;
