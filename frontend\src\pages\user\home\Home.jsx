import UserLayoutHome from "../../../layouts/UserLayoutHome";
import TeacherImage from "../../../assets/images/teacherImage.jpg";
import CountDownCard from "../../../components/card/countDownCard";
import Footer from "../../../components/Footer";
import { motion } from "framer-motion";
import SlideShow from "../../../components/image/SlideShow";
import CustomSchedule from "../../../components/CustomSchedule";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
import StudentThoughts from "../../../components/StudentThoughts";
import { checkLogin } from "../../../features/auth/authSlice";
import { fetchClassesPublic } from "../../../features/class/classSlice";
import { fetchCodesByType } from "../../../features/code/codeSlice";

import { fetchImagesFolders } from "../../../features/image/imageSlice";
import MessageIcon from "../../../assets/icons/64px-Facebook_Messenger_logo_2020.svg.png";
import { CalendarClock, Flag, Award, BookOpen, Camera, CalendarDays, Video, FileText, PenTool, Calculator, Users, Brain, GraduationCap, Lightbulb, MessageCircle, HelpCircle, Contact, Phone, HeadphonesIcon, Headphones } from "lucide-react";
import AchievementSection from "../../../components/achievement/AchievementSection";
import TeamSection from "../../../components/team/TeamSection";



import tongon from "../../../assets/images/tongon.jpg";
import luyende from "../../../assets/images/luyende.jpg";

import calender12 from "../../../assets/images/calender12.jpg";
import calender11 from "../../../assets/images/calender11.jpg";
import calender10 from "../../../assets/images/calender10.jpg";
import calender9 from "../../../assets/images/calender9.jpg";



import banner304151 from "../../../assets/images/304151.jpg";
import banner304152 from "../../../assets/images/304152.jpg";
import banner304153 from "../../../assets/images/304153.jpg";


const calenderSlides = [luyende, tongon, calender12, calender11, calender10, calender9];
const bannerSlides = [banner304152, banner304153];

// Student thoughts data
const studentThoughts2022 = [
    { content: "\"Thầy Bee dạy rất dễ hiểu, giúp em từ học sinh trung bình trở thành học sinh khá giỏi môn Toán.\"", student: "Nguyễn Đức Dương" },
    { content: "\"Nhờ phương pháp giảng dạy của thầy, em đã đạt 9.0 trong kỳ thi THPT Quốc gia.\"", student: "Nguyễn Hoàng Anh" },
    { content: "\"Các bài giảng của thầy luôn đi từ cơ bản đến nâng cao, giúp em tiến bộ rất nhiều.\"", student: "Trần Minh Phương" },
];

const studentThoughts2023 = [
    { content: "\"Thầy không chỉ dạy kiến thức mà còn truyền cảm hứng cho chúng em yêu thích môn Toán.\"", student: "Phạm Thị Minh Trang" },
    { content: "\"Nhờ thầy mà em đã vượt qua nỗi sợ hãi với môn Toán và đạt điểm cao trong kỳ thi.\"", student: "Nguyễn Quốc Cường" },
    { content: "\"Phương pháp giải nhanh của thầy giúp em tiết kiệm rất nhiều thời gian trong các bài thi.\"", student: "Ngô Hương Giang" },
];

const studentThoughts2024 = [
    { content: "\"Thầy luôn tạo không khí học tập vui vẻ nhưng hiệu quả, giúp em tiến bộ rất nhiều.\"", student: "Đỗ Hoàng Dũng" },
    { content: "\"Cách thầy phân tích bài toán rất logic và dễ hiểu, giúp em tự tin hơn khi làm bài.\"", student: "Hoàng Việt Hoàng" },
    { content: "\"Em rất biết ơn thầy đã giúp em đạt được ước mơ vào trường đại học mong muốn.\"", student: "Nguyễn Minh Đức" },
];



// Vietnam flag colors
const vnRedColor = "#E30A17";
const vnYellowColor = "#FFFF00";

const MomentCard = ({ year, images, icon: Icon = CalendarDays, color = "text-sky-500", interval = 4000, thoughts, reverse = false }) => {
    // Create slideshow component
    const SlideShowCard = () => (
        <div className="relative bg-white shadow-xl rounded-xl p-4">
            {/* Băng dính trái */}
            <div className="absolute top-0 left-4 w-12 h-5 bg-yellow-200 rotate-[-10deg] rounded-sm shadow-md z-10"></div>

            {/* Băng dính phải */}
            <div className="absolute top-0 right-4 w-12 h-5 bg-yellow-200 rotate-[10deg] rounded-sm shadow-md z-10"></div>

            {/* Tiêu đề */}
            <div className={`flex items-center gap-2 text-lg sm:text-xl font-bold font-cubano ${color} mb-2 justify-center`}>
                <Icon className="w-5 h-5" />
                {year}
            </div>

            {/* Slide ảnh */}
            <div className="w-full rounded-lg overflow-hidden border-4 border-white shadow-md">
                <SlideShow interval={interval} images={images} h="h-[15rem] sm:h-[18rem] lg:h-[22rem]" />
            </div>
        </div>
    );

    // Create thoughts component
    const ThoughtsCard = () => (
        <div className="h-full">
            <StudentThoughts thoughts={thoughts || []} />
        </div>
    );

    return (
        <div className="w-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                {reverse ? (
                    // Reversed layout: Thoughts on left, Slideshow on right
                    <>
                        <ThoughtsCard />
                        <SlideShowCard />
                    </>
                ) : (
                    // Default layout: Slideshow on left, Thoughts on right
                    <>
                        <SlideShowCard />
                        <ThoughtsCard />
                    </>
                )}
            </div>
        </div>
    );
};

const BannerWrapper = ({ bannerImages }) => {
    const [isPortrait, setIsPortrait] = useState(false);

    useEffect(() => {
        if (bannerImages?.length > 0) {
            const img = new Image();
            img.src = bannerImages[0];
            img.onload = () => {
                setIsPortrait(img.height > img.width);
            };
        }
    }, [bannerImages]);

    return (
        <div className="flex items-center h-full">
            <motion.div
                initial={{ opacity: 0, y: -50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1.2, delay: 0.3 }}
                className={`w-full ${isPortrait ? 'max-w-[25rem]' : 'max-w-[50rem]'} flex h-full lg:justify-center shadow-xl items-center justify-center`}
            >
                <SlideShow interval={4000} images={bannerImages} h="h-fit" />
            </motion.div>
        </div>
    );
};

const Home = () => {
    const { user } = useSelector((state) => state.auth);
    const { imagesHome } = useSelector((state) => state.images);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [bannerImages, setBannerImages] = useState([]);
    const [calendarImages, setCalendarImages] = useState([]);
    const [momentImages2022, setMomentImages2022] = useState([]);
    const [momentImages2023, setMomentImages2023] = useState([]);
    const [momentImages2024, setMomentImages2024] = useState([]);
    const [teamImages, setTeamImages] = useState([]);


    useEffect(() => {
        if (!user) {
            dispatch(checkLogin());
        }
    }, [user, dispatch]);

    useEffect(() => {
        dispatch(fetchClassesPublic());
        dispatch(fetchCodesByType(["class status", "year", "dow", "duration"]));

        dispatch(fetchImagesFolders(["home_banner", "home_calendar", "home_moment_2022", "home_moment_2023", "home_moment_2024", "home_team"]));
    }, [dispatch]);

    useEffect(() => {
        if (imagesHome) {
            const bannerImgs = imagesHome[0]
            const calendarImgs = imagesHome[1]
            const moment2022Imgs = imagesHome[2]
            const moment2023Imgs = imagesHome[3]
            const moment2024Imgs = imagesHome[4]
            const teamImgs = imagesHome[5]
            setBannerImages(bannerImgs);
            setCalendarImages(calendarImgs);
            setMomentImages2022(moment2022Imgs);
            setMomentImages2023(moment2023Imgs);
            setMomentImages2024(moment2024Imgs);
            setTeamImages(teamImgs);
        }
    }, [imagesHome]);


    return (
        <UserLayoutHome>
            <div className="relative ">
                {/* Holiday Banner for April 30th/May 1st */}
                {/*

                */}
                {/* <section
                    style={{
                        background: `linear-gradient(to right, ${vnRedColor}, ${vnYellowColor})`,
                    }}
                    className="w-full py-2 sm:py-4 text-white"
                >
                    <div className="max-w-screen-xl mx-auto px-2 sm:px-4 flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0">
                        <div className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 text-center md:text-left">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                                <path d="M16 2.5H2C1.46957 2.5 0.960859 2.71071 0.585786 3.08579C0.210714 3.46086 0 3.96957 0 4.5L0 13.5C0 14.0304 0.210714 14.5391 0.585786 14.9142C0.960859 15.2893 1.46957 15.5 2 15.5H16C16.5304 15.5 17.0391 15.2893 17.4142 14.9142C17.7893 14.5391 18 14.0304 18 13.5V4.5C18 3.96957 17.7893 3.46086 17.4142 3.08579C17.0391 2.71071 16.5304 2.5 16 2.5Z" fill="#DA251D" />
                                <path d="M9.8766 8.0183L9.0001 5.3208L8.1236 8.0183H5.2876L7.5821 9.6848L6.7056 12.3823L9.0001 10.7153L11.2946 12.3823L10.4181 9.6848L12.7126 8.0183H9.8766Z" fill="#FFFF00" />
                            </svg>
                            <span className="font-bold text-sm sm:text-base">
                                Kỷ niệm 50 năm Ngày Giải phóng miền Nam,
                                <br className="md:hidden" />
                                thống nhất đất nước
                            </span>
                        </div>

                        <div className="flex items-center text-xs sm:text-sm">
                            <span className="ml-2 text-red-700">30/4/1975 – 30/4/2025</span>
                        </div>
                    </div>
                </section> */}


                <section
                    id="home"
                    className="w-full px-4 py-16 bg-gradient-to-br from-sky-50 via-white to-indigo-50 overflow-hidden"
                >
                    <div className="max-w-screen-xl mx-auto">
                        <div className="flex flex-col-reverse lg:flex-row justify-center items-center gap-12 lg:gap-16">
                            {/* Left Side - Content */}
                            <motion.div
                                className="w-full lg:w-1/2 flex justify-center items-center lg:justify-start lg:items-start"
                                initial={{ opacity: 0, x: -50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                                viewport={{ once: true }}
                            >
                                <div className="space-y-8 text-center lg:text-left">
                                    {/* Badge */}
                                    <motion.div
                                        className="inline-block px-4 py-2 bg-sky-100 text-sky-700 rounded-full text-sm font-medium"
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        whileInView={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.5, delay: 0.2 }}
                                        viewport={{ once: true }}
                                    >
                                        Học toán hiệu quả
                                    </motion.div>

                                    {/* Main Title */}
                                    <motion.div
                                        className="text-5xl sm:text-6xl lg:text-7xl font-normal font-cubano leading-tight"
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.8, delay: 0.3 }}
                                        viewport={{ once: true }}
                                    >
                                        <span className="text-gray-900">Học </span>
                                        <span className="text-sky-500">thầy Bee</span>
                                        <br />
                                        <span className="text-gray-900">Toán easy</span>
                                    </motion.div>

                                    {/* Description */}
                                    <motion.p
                                        className="text-gray-600 text-lg lg:text-xl max-w-lg"
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: 0.5 }}
                                        viewport={{ once: true }}
                                    >
                                        Khám phá phương pháp học toán hiệu quả với đội ngũ giáo viên giàu kinh nghiệm và tài liệu chất lượng cao.
                                    </motion.p>

                                    {/* CTA Button */}
                                    <motion.div
                                        className="flex justify-center lg:justify-start"
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: 0.6 }}
                                        viewport={{ once: true }}
                                    >
                                        <motion.button
                                            onClick={() => user ? navigate("/practice") : navigate("/login")}
                                            className="px-8 py-4 bg-gradient-to-r from-sky-500 to-indigo-500 rounded-full shadow-lg hover:shadow-xl text-white text-lg font-semibold transition-all duration-300"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            Vào học ngay
                                        </motion.button>
                                    </motion.div>

                                    {/* Credentials */}
                                    <motion.div
                                        className="space-y-3"
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: 0.7 }}
                                        viewport={{ once: true }}
                                    >
                                        <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wide">Giáo viên tại</h4>
                                        <div className="flex flex-col space-y-2 text-sm lg:text-base">
                                            <motion.a
                                                href="https://vuihoc.vn"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="font-semibold text-teal-600 hover:text-teal-700 transition-colors duration-300"
                                                whileHover={{ x: 5 }}
                                            >
                                                → Vuihoc.vn
                                            </motion.a>
                                            <motion.a
                                                href="https://hocmai.vn"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="font-semibold text-purple-600 hover:text-purple-700 transition-colors duration-300"
                                                whileHover={{ x: 5 }}
                                            >
                                                → Hệ thống Giáo dục HOCMAI
                                            </motion.a>
                                            <motion.a
                                                href="https://anhxtanh.edu.vn"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="font-semibold text-rose-600 hover:text-rose-700 transition-colors duration-300"
                                                whileHover={{ x: 5 }}
                                            >
                                                → THPT Anhxtanh – Hà Nội
                                            </motion.a>
                                        </div>
                                    </motion.div>
                                </div>
                            </motion.div>

                            {/* Right Side - Banner */}
                            <motion.div
                                className="w-full lg:w-1/2"
                                initial={{ opacity: 0, x: 50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                viewport={{ once: true }}
                            >
                                <BannerWrapper bannerImages={bannerImages} />
                            </motion.div>
                        </div>
                    </div>
                </section>





                {/* Holiday Special Section */}


                {/* 🔽 Tính năng Section */}
                <section id="features" className="w-full px-4 py-20 bg-white overflow-hidden">
                    <div className="max-w-screen-xl mx-auto">
                        <motion.div
                            className="text-center mb-12"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                        >
                            <motion.div
                                className="inline-block px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium mb-3"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                                viewport={{ once: true }}
                            >
                                Học tập hiệu quả
                            </motion.div>
                            <motion.h2
                                className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                viewport={{ once: true }}
                            >
                                Tính năng nổi bật
                            </motion.h2>
                            <motion.p
                                className="text-gray-600 max-w-2xl mx-auto text-lg"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                viewport={{ once: true }}
                            >
                                Khám phá các tính năng đa dạng giúp việc học toán trở nên dễ dàng, hiệu quả và thú vị hơn bao giờ hết.
                            </motion.p>
                        </motion.div>

                        <motion.div
                            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.5 }}
                        >
                            {/* Feature 1 */}
                            <motion.div
                                className="bg-gradient-to-br from-indigo-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-indigo-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.6 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-indigo-600 flex items-center justify-center mb-5 mx-auto group-hover:bg-indigo-700 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <BookOpen className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Tài liệu học tập</h3>
                                <p className="text-gray-600 text-center">Truy cập kho tài liệu phong phú với đầy đủ lý thuyết và bài tập theo từng chương trình học.</p>
                            </motion.div>

                            {/* Feature 2 */}
                            <motion.div
                                className="bg-gradient-to-br from-purple-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-purple-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.7 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-purple-600 flex items-center justify-center mb-5 mx-auto group-hover:bg-purple-700 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <Video className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Video bài giảng</h3>
                                <p className="text-gray-600 text-center">Xem các video bài giảng chất lượng cao với phương pháp giảng dạy dễ hiểu và trực quan.</p>
                            </motion.div>

                            {/* Feature 3 */}
                            <motion.div
                                className="bg-gradient-to-br from-blue-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-blue-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.8 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-blue-600 flex items-center justify-center mb-5 mx-auto group-hover:bg-blue-700 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <FileText className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Đề thi & Bài tập</h3>
                                <p className="text-gray-600 text-center">Luyện tập với ngân hàng đề thi và bài tập đa dạng từ cơ bản đến nâng cao.</p>
                            </motion.div>

                            {/* Feature 4 */}
                            <motion.div
                                className="bg-gradient-to-br from-pink-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-pink-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.9 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-pink-600 flex items-center justify-center mb-5 mx-auto group-hover:bg-pink-700 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <PenTool className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Luyện thi online</h3>
                                <p className="text-gray-600 text-center">Tham gia các bài kiểm tra trực tuyến với đánh giá chi tiết và phản hồi ngay lập tức.</p>
                            </motion.div>
                        </motion.div>

                        <motion.div
                            className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 1.0 }}
                        >
                            {/* Feature 5 */}
                            <motion.div
                                className="bg-gradient-to-br from-amber-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-amber-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 1.1 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-amber-500 flex items-center justify-center mb-5 mx-auto group-hover:bg-amber-600 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <Calculator className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Công cụ tính toán</h3>
                                <p className="text-gray-600 text-center">Sử dụng các công cụ tính toán thông minh giúp giải quyết bài toán nhanh chóng và chính xác.</p>
                            </motion.div>

                            {/* Feature 6 */}
                            <motion.div
                                className="bg-gradient-to-br from-teal-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-teal-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 1.2 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-teal-600 flex items-center justify-center mb-5 mx-auto group-hover:bg-teal-700 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <Users className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Lớp học đa dạng</h3>
                                <p className="text-gray-600 text-center">Tham gia các lớp học tập để trao đổi kiến thức và giải đáp thắc mắc cùng bạn bè.</p>
                            </motion.div>

                            {/* Feature 7 */}
                            <motion.div
                                className="bg-gradient-to-br from-rose-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-rose-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 1.3 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-rose-600 flex items-center justify-center mb-5 mx-auto group-hover:bg-rose-700 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <Brain className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Phương pháp học</h3>
                                <p className="text-gray-600 text-center">Tiếp cận các phương pháp học tập hiệu quả và kỹ thuật ghi nhớ giúp việc học toán dễ dàng hơn.</p>
                            </motion.div>

                            {/* Feature 8 */}
                            <motion.div
                                className="bg-gradient-to-br from-violet-50 to-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-violet-100 group cursor-pointer"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 1.4 }}
                                whileHover={{ scale: 1.02, y: -5 }}
                                onClick={() => navigate('/features')}
                            >
                                <motion.div
                                    className="w-16 h-16 rounded-2xl bg-violet-600 flex items-center justify-center mb-5 mx-auto group-hover:bg-violet-700 transition-colors rotate-3 shadow-md"
                                    whileHover={{ rotate: 6, scale: 1.1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <GraduationCap className="w-8 h-8 text-white" />
                                </motion.div>
                                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">Tư vấn đại học</h3>
                                <p className="text-gray-600 text-center">Nhận tư vấn về định hướng nghề nghiệp và chiến lược ôn thi đại học hiệu quả.</p>
                            </motion.div>
                        </motion.div>
                    </div>
                </section>

                {/* 🔽 Thành tích Section */}
                <section id="achievements">
                    <AchievementSection />
                </section>

                {/* 🔽 Đội ngũ trợ giảng Section */}
                <section id="team">
                    <TeamSection teamImages={teamImages} />
                </section>

                {/* 🔽 Khoảnh khắc Section */}
                <section id="moments" className="w-full px-4 py-16 bg-gradient-to-br from-sky-50 via-white to-cyan-50">
                    <div className="max-w-screen-xl mx-auto">
                        {/* Header */}
                        <motion.div
                            className="text-center mb-12"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                        >
                            <motion.div
                                className="inline-block px-4 py-2 bg-sky-100 text-sky-700 rounded-full text-sm font-medium mb-4"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                                viewport={{ once: true }}
                            >
                                Kỷ niệm đáng nhớ
                            </motion.div>
                            <motion.h2
                                className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4 flex items-center justify-center gap-3"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                viewport={{ once: true }}
                            >
                                <Camera className="w-8 h-8 text-sky-600" />
                                Khoảnh khắc lớp 12
                            </motion.h2>
                            <motion.p
                                className="text-gray-600 max-w-2xl mx-auto text-lg"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                viewport={{ once: true }}
                            >
                                Cùng nhìn lại những kỷ niệm đáng nhớ của các lớp học qua từng năm. Những khoảnh khắc đẹp, những buổi học vui vẻ và những thành tích đáng tự hào của các bạn học sinh.
                            </motion.p>
                        </motion.div>

                        {/* Moment Cards */}
                        <div className="space-y-16">
                            {/* 2022-2023 */}
                            <motion.div
                                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.5 }}
                            >
                                {/* Left - Images */}
                                <motion.div
                                    className="order-2 lg:order-1"
                                    initial={{ opacity: 0, x: -50 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.6 }}
                                >
                                    <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-sky-100">
                                        <div className="absolute inset-0 bg-gradient-to-br from-pink-50/20 to-rose-50/20 z-10 pointer-events-none"></div>

                                        <div className="absolute top-6 left-6 w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-500 rounded-full flex items-center justify-center z-20 shadow-lg">
                                            <CalendarDays className="w-8 h-8 text-white" />
                                        </div>

                                        <div className="absolute top-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
                                            2022 - 2023
                                        </div>

                                        <div className="w-full aspect-square">
                                            <SlideShow
                                                key="2022-2023"
                                                interval={4000}
                                                images={momentImages2022}
                                                h="h-full"
                                            />
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Right - Content */}
                                <motion.div
                                    className="order-1 lg:order-2 space-y-6"
                                    initial={{ opacity: 0, x: 50 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.8 }}
                                >
                                    <div>
                                        <h3 className="text-2xl font-bold text-gray-900 mb-3">Năm học 2022 - 2023</h3>
                                        <p className="text-gray-600 text-lg leading-relaxed">
                                            Những khoảnh khắc đầu tiên của hành trình học tập. Từ những buổi học đầu tiên đến những thành tích đáng tự hào.
                                        </p>
                                    </div>

                                    {/* Student Thoughts */}
                                    <div className="space-y-4">
                                        {studentThoughts2022.map((thought, index) => (
                                            <motion.div
                                                key={index}
                                                className="bg-white p-4 rounded-xl shadow-md border border-pink-100"
                                                initial={{ opacity: 0, y: 20 }}
                                                whileInView={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.4, delay: 1.0 + index * 0.1 }}
                                            >
                                                <p className="text-gray-700 italic">"{thought.content}"</p>
                                                <p className="text-pink-600 font-semibold text-sm mt-2">- {thought.author}</p>
                                            </motion.div>
                                        ))}
                                    </div>
                                </motion.div>
                            </motion.div>

                            {/* 2023-2024 */}
                            <motion.div
                                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.5 }}
                            >
                                {/* Left - Content */}
                                <motion.div
                                    className="order-1 space-y-6"
                                    initial={{ opacity: 0, x: -50 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.6 }}
                                >
                                    <div>
                                        <h3 className="text-2xl font-bold text-gray-900 mb-3">Năm học 2023 - 2024</h3>
                                        <p className="text-gray-600 text-lg leading-relaxed">
                                            Một năm đầy thành tích và kỷ niệm. Những bước tiến vượt bậc trong học tập và phát triển bản thân.
                                        </p>
                                    </div>

                                    {/* Student Thoughts */}
                                    <div className="space-y-4">
                                        {studentThoughts2023.map((thought, index) => (
                                            <motion.div
                                                key={index}
                                                className="bg-white p-4 rounded-xl shadow-md border border-yellow-100"
                                                initial={{ opacity: 0, y: 20 }}
                                                whileInView={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.4, delay: 1.0 + index * 0.1 }}
                                            >
                                                <p className="text-gray-700 italic">"{thought.content}"</p>
                                                <p className="text-yellow-600 font-semibold text-sm mt-2">- {thought.author}</p>
                                            </motion.div>
                                        ))}
                                    </div>
                                </motion.div>

                                {/* Right - Images */}
                                <motion.div
                                    className="order-2"
                                    initial={{ opacity: 0, x: 50 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.8 }}
                                >
                                    <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-sky-100">
                                        <div className="absolute inset-0 bg-gradient-to-br from-yellow-50/20 to-amber-50/20 z-10 pointer-events-none"></div>

                                        <div className="absolute top-6 left-6 w-16 h-16 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center z-20 shadow-lg">
                                            <Award className="w-8 h-8 text-white" />
                                        </div>

                                        <div className="absolute top-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
                                            2023 - 2024
                                        </div>

                                        <div className="w-full aspect-square">
                                            <SlideShow
                                                key="2023-2024"
                                                interval={5000}
                                                images={momentImages2023}
                                                h="h-full"
                                            />
                                        </div>
                                    </div>
                                </motion.div>
                            </motion.div>

                            {/* 2024-2025 */}
                            <motion.div
                                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.5 }}
                            >
                                {/* Left - Images */}
                                <motion.div
                                    className="order-2 lg:order-1"
                                    initial={{ opacity: 0, x: -50 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.6 }}
                                >
                                    <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-sky-100">
                                        <div className="absolute inset-0 bg-gradient-to-br from-sky-50/20 to-cyan-50/20 z-10 pointer-events-none"></div>

                                        <div className="absolute top-6 left-6 w-16 h-16 bg-gradient-to-br from-sky-400 to-cyan-500 rounded-full flex items-center justify-center z-20 shadow-lg">
                                            <Camera className="w-8 h-8 text-white" />
                                        </div>

                                        <div className="absolute top-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
                                            2024 - 2025
                                        </div>

                                        <div className="w-full aspect-square">
                                            <SlideShow
                                                key="2024-2025"
                                                interval={3000}
                                                images={momentImages2024}
                                                h="h-full"
                                            />
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Right - Content */}
                                <motion.div
                                    className="order-1 lg:order-2 space-y-6"
                                    initial={{ opacity: 0, x: 50 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.8 }}
                                >
                                    <div>
                                        <h3 className="text-2xl font-bold text-gray-900 mb-3">Năm học 2024 - 2025</h3>
                                        <p className="text-gray-600 text-lg leading-relaxed">
                                            Năm học hiện tại với những dự án mới và mục tiêu cao hơn. Tiếp tục hành trình chinh phục tri thức.
                                        </p>
                                    </div>

                                    {/* Student Thoughts */}
                                    <div className="space-y-4">
                                        {studentThoughts2024.map((thought, index) => (
                                            <motion.div
                                                key={index}
                                                className="bg-white p-4 rounded-xl shadow-md border border-sky-100"
                                                initial={{ opacity: 0, y: 20 }}
                                                whileInView={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.4, delay: 1.0 + index * 0.1 }}
                                            >
                                                <p className="text-gray-700 italic">"{thought.content}"</p>
                                                <p className="text-sky-600 font-semibold text-sm mt-2">- {thought.author}</p>
                                            </motion.div>
                                        ))}
                                    </div>
                                </motion.div>
                            </motion.div>
                        </div>
                    </div>
                </section>


                <section id="schedule" className="w-full px-4 py-16 bg-gradient-to-br from-emerald-50 via-white to-green-50">
                    <div className="max-w-screen-xl mx-auto">
                        {/* Header */}
                        <motion.div
                            className="text-center mb-12"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                        >
                            <motion.div
                                className="inline-block px-4 py-2 bg-emerald-100 text-emerald-700 rounded-full text-sm font-medium mb-4"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                                viewport={{ once: true }}
                            >
                                Thời khóa biểu
                            </motion.div>
                            <motion.h2
                                className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4 flex items-center justify-center gap-3"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                viewport={{ once: true }}
                            >
                                <CalendarClock className="w-8 h-8 text-emerald-600" />
                                Lịch học 2024 - 2025
                            </motion.h2>
                            <motion.p
                                className="text-gray-600 max-w-2xl mx-auto text-lg"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                viewport={{ once: true }}
                            >
                                Thông tin chi tiết về lịch học các lớp trong năm học 2024-2025. Xem lịch học để sắp xếp thời gian học tập hiệu quả và không bỏ lỡ buổi học nào.
                            </motion.p>
                        </motion.div>

                        {/* Main Content */}
                        <motion.div
                            className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.5 }}
                            viewport={{ once: true }}
                        >
                            {/* Left Side - Calendar Images */}
                            <motion.div
                                className="order-2 lg:order-1"
                                initial={{ opacity: 0, x: -50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.6 }}
                                viewport={{ once: true }}
                            >
                                <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-emerald-100">
                                    {/* Decorative gradient overlay */}
                                    <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/20 to-green-50/20 z-10 pointer-events-none"></div>

                                    {/* Decorative icon */}
                                    <div className="absolute top-6 left-6 w-16 h-16 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full flex items-center justify-center z-20 shadow-lg">
                                        <CalendarClock className="w-8 h-8 text-white" />
                                    </div>

                                    {/* Year badge */}
                                    <div className="absolute top-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
                                        2024 - 2025
                                    </div>

                                    {/* Image counter */}
                                    <div className="absolute bottom-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
                                        {calendarImages && calendarImages.length > 0 ? `1 / ${calendarImages.length}` : '0 / 0'}
                                    </div>

                                    {/* Calendar Images - Landscape aspect ratio */}
                                    <div className="w-full aspect-[4/3]">
                                        <SlideShow
                                            key="schedule-2024-2025"
                                            interval={4000}
                                            images={calendarImages}
                                            h="h-full"
                                            objectFit="object-contain"
                                        />
                                    </div>

                                    {/* View All Schedule Button */}
                                    <div className="absolute bottom-6 left-6 right-6 z-30">
                                        <motion.button
                                            onClick={() => navigate('/schedule')}
                                            className="w-full inline-flex items-center justify-center gap-3 px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-500 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                                            initial={{ opacity: 0, y: 20 }}
                                            whileInView={{ opacity: 1, y: 0 }}
                                            transition={{ duration: 0.6, delay: 1.0 }}
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                        >
                                            <CalendarClock size={20} />
                                            <span className="text-sm sm:text-base">Xem lịch học chi tiết</span>
                                            <span className="text-lg">→</span>
                                        </motion.button>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Right Side - Schedule Content */}
                            <motion.div
                                className="order-1 lg:order-2 space-y-8"
                                initial={{ opacity: 0, x: 50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.8 }}
                                viewport={{ once: true }}
                            >
                                {/* Schedule Info */}
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 1.0 }}
                                >
                                    <h3 className="text-2xl font-bold text-gray-900 mb-4">Thông tin lịch học</h3>
                                    <p className="text-gray-600 text-lg leading-relaxed mb-6">
                                        Lịch học được thiết kế linh hoạt, phù hợp với thời gian của học sinh. Các buổi học được sắp xếp khoa học để đảm bảo hiệu quả học tập tối ưu.
                                    </p>
                                </motion.div>

                                {/* Schedule Features */}
                                <motion.div
                                    className="grid grid-cols-1 sm:grid-cols-2 gap-4"
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 1.2 }}
                                >
                                    <motion.div
                                        className="bg-white p-4 rounded-xl shadow-md border border-emerald-100 hover:shadow-lg transition-all duration-300"
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        whileInView={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.4, delay: 1.3 }}
                                        whileHover={{ scale: 1.02 }}
                                    >
                                        <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-3">
                                            <CalendarClock className="w-5 h-5 text-emerald-600" />
                                        </div>
                                        <h4 className="font-semibold text-gray-800 mb-2">Lịch linh hoạt</h4>
                                        <p className="text-gray-600 text-sm">Thời gian học có thể điều chỉnh theo nhu cầu</p>
                                    </motion.div>

                                    <motion.div
                                        className="bg-white p-4 rounded-xl shadow-md border border-emerald-100 hover:shadow-lg transition-all duration-300"
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        whileInView={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.4, delay: 1.4 }}
                                        whileHover={{ scale: 1.02 }}
                                    >
                                        <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-3">
                                            <Users className="w-5 h-5 text-emerald-600" />
                                        </div>
                                        <h4 className="font-semibold text-gray-800 mb-2">Lớp nhỏ</h4>
                                        <p className="text-gray-600 text-sm">Tối đa 15 học sinh mỗi lớp để đảm bảo chất lượng</p>
                                    </motion.div>

                                    <motion.div
                                        className="bg-white p-4 rounded-xl shadow-md border border-emerald-100 hover:shadow-lg transition-all duration-300"
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        whileInView={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.4, delay: 1.5 }}
                                        whileHover={{ scale: 1.02 }}
                                    >
                                        <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-3">
                                            <BookOpen className="w-5 h-5 text-emerald-600" />
                                        </div>
                                        <h4 className="font-semibold text-gray-800 mb-2">Tài liệu đầy đủ</h4>
                                        <p className="text-gray-600 text-sm">Cung cấp đầy đủ tài liệu và bài tập</p>
                                    </motion.div>

                                    <motion.div
                                        className="bg-white p-4 rounded-xl shadow-md border border-emerald-100 hover:shadow-lg transition-all duration-300"
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        whileInView={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.4, delay: 1.6 }}
                                        whileHover={{ scale: 1.02 }}
                                    >
                                        <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-3">
                                            <Headphones className="w-5 h-5 text-emerald-600" />
                                        </div>
                                        <h4 className="font-semibold text-gray-800 mb-2">Hỗ trợ 24/7</h4>
                                        <p className="text-gray-600 text-sm">Giải đáp thắc mắc mọi lúc mọi nơi</p>
                                    </motion.div>
                                </motion.div>

                                {/* Custom Schedule Component */}
                                <motion.div
                                    className="bg-gradient-to-r from-emerald-600 to-green-600 p-6 rounded-xl text-white"
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 1.8 }}
                                >
                                    <h4 className="text-xl font-bold mb-4">Đăng ký tư vấn lịch học</h4>
                                    <p className="text-emerald-100 mb-4">
                                        Liên hệ với chúng tôi để được tư vấn lịch học phù hợp nhất với thời gian và nhu cầu của bạn.
                                    </p>
                                    <div className="flex flex-wrap gap-2 mb-4">
                                        <span className="px-3 py-1 bg-white/20 rounded-full text-sm">Tư vấn miễn phí</span>
                                        <span className="px-3 py-1 bg-white/20 rounded-full text-sm">Lịch linh hoạt</span>
                                        <span className="px-3 py-1 bg-white/20 rounded-full text-sm">Học thử miễn phí</span>
                                    </div>
                                    <CustomSchedule />
                                </motion.div>
                            </motion.div>
                        </motion.div>
                    </div>
                </section>


                <div className="fixed bottom-4 right-0 z-10 flex flex-col gap-2">
                    <ContactButton />

                    <CountDownCard
                        targetTime={new Date("2025-06-26T23:59:59")}
                        title="Kì thi THPT quốc gia"
                    />
                </div>

            </div>

            
        </UserLayoutHome>
    );
};

export default Home;
const zaloIconUrl = "https://upload.wikimedia.org/wikipedia/commons/9/91/Icon_of_Zalo.svg";
const facebookIconUrl = "https://upload.wikimedia.org/wikipedia/commons/b/b9/2023_Facebook_icon.svg"

const ContactButton = () => {
    const [isOpen, setIsOpen] = useState(false);
    const menuRef = useRef(null);

    // Function to toggle the menu open/closed
    const toggleMenu = () => {
        setIsOpen(!isOpen);
    };

    // Function to handle clicks outside the menu to close it
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        // Add event listener when the menu is open
        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Clean up the event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    return (
        <div className="flex flex-col justify-center items-end mr-1" ref={menuRef}>
            <div
                onClick={toggleMenu}
                className="w-14 h-14 rounded-full bg-gradient-to-r from-sky-400 to-sky-500 flex items-center justify-center shadow-xl cursor-pointer animate-pulse"
            >
                <Headphones className="w-6 h-6 text-white" />

                {isOpen && (
                    <div className="absolute bottom-[11rem] flex flex-col gap-2 items-end">

                        <a
                            href="https://m.me/9349151345145048"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 hover:scale-110 transition-transform"
                        >
                            <img src={MessageIcon} alt="Messenger" className="w-10 object-contain" />
                        </a>
                        <a
                            href="https://zalo.me/0333726202"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 hover:scale-110 transition-transform"
                        >
                            <img src={zaloIconUrl} alt="Zalo" className="w-10 object-contain" />
                        </a>
                        <a
                            href="https://facebook.com/loptoanthaybee"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 hover:scale-110 transition-transform"
                        >
                            <img src={facebookIconUrl} alt="Facebook" className="w-10 object-contain" />
                        </a>
                    </div>
                )}
            </div>
        </div>
    );
};
