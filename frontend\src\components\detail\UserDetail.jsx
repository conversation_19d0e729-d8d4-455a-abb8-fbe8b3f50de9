import { use, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { fetchUserById, putUser, putUserType, deleteUser } from "../../features/user/userSlice";
import { useNavigate } from "react-router-dom";
import { fetchCodesByType } from "../../features/code/codeSlice";
import { validationUser } from "../../utils/validation";
import { setSuccessMessage, setErrorMessage } from "../../features/state/stateApiSlice";
import DetailTr from "./DetailTr";
import ConfirmModal from "../modal/ConfirmModal";
import UserAdminLayout from "../../layouts/UserAdminLayout";
import UserType from "src/constants/UserType";
import LoadingData from "../loading/LoadingData";

const UserDetail = ({ userId }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { student } = useSelector((state) => state.users);
    const { user } = useSelector((state) => state.auth);
    const { codes } = useSelector((state) => state.codes);

    const [studentData, setStudentData] = useState(null)
    const [showDeleteModal, setShowDeleteModal] = useState(false)

    useEffect(() => {
        dispatch(fetchCodesByType(["user type", "student status", "grade", "highSchool"]));
    }, [dispatch]);

    const { loading } = useSelector((state) => state.states);

    useEffect(() => {
        dispatch(fetchUserById(userId))
    }, [dispatch, userId]);

    useEffect(() => {
        if (student) {
            // console.log(student)
            setStudentData({ ...student })
        }
    }, [student]);


    const handlePutUser = () => {
        // Validate graduation year before submitting
        let graduationYear = null;
        if (studentData.graduationYear) {
            if (typeof studentData.graduationYear === 'string') {
                // If it's a string, validate it's a complete 4-digit year
                if (/^\d{4}$/.test(studentData.graduationYear)) {
                    const year = parseInt(studentData.graduationYear);
                    if (year >= 2020 && year <= 2030) {
                        graduationYear = year;
                    } else {
                        dispatch(setErrorMessage("Năm tốt nghiệp phải từ 2020 đến 2030"));
                        return;
                    }
                } else {
                    dispatch(setErrorMessage("Năm tốt nghiệp phải là năm hợp lệ (4 chữ số)"));
                    return;
                }
            } else {
                graduationYear = studentData.graduationYear;
            }
        }

        const data = {
            lastName: studentData.lastName,
            firstName: studentData.firstName,
            phone: studentData.phone ? studentData.phone : null,
            class: studentData.class,
            highSchool: studentData.highSchool,
            graduationYear: graduationYear,
            isActive: studentData.isActive,
            password: studentData.password,
            username: studentData.username
        };

        if (!validationUser(data, dispatch)) return;
        if (JSON.stringify(student) === JSON.stringify(studentData)) {
            dispatch(setSuccessMessage("Không có thay đổi nào được thực hiện"));
            return;
        } else {
            if (user.userType === "AD" || user.userType === "GV") {
                if (studentData.userType !== student.userType) {
                    dispatch(putUserType({ id: student.id, type: studentData.userType }));
                }
            }
            dispatch(putUser({ id: student.id, user: data }));
        }

    }

    const handleDeleteUser = async () => {
        try {
            await dispatch(deleteUser(student.id)).unwrap();
            setShowDeleteModal(false);
            navigate('/admin/student-management');
        } catch (error) {
            console.error('Error deleting user:', error);
            // Error message đã được xử lý trong apiHandler
        }
    };

    return (
        <UserAdminLayout>
            {/* Content */}
            <div className="flex-1 p-6 pb-20">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col">

                    <LoadingData
                        loading={loading}
                        isNoData={student ? false : true}
                        loadText="Đang tải thông tin người dùng"
                        noDataText="Không có thông tin người dùng."
                    >
                        {/* Details Table */}

                        <div className="flex-shrink-0 p-6 border-b border-gray-200 ">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông tin chi tiết</h3>
                        </div>
                        <div className="p-6">

                            <div className="overflow-x-auto">
                                <div className="flex-grow h-full overflow-y-auto hide-scrollbar">
                                    <table className="w-full border-collapse h-full border border-[#E7E7ED]">
                                        <thead className="bg-[#F6FAFD]">
                                            <tr className="border border-[#E7E7ED]">
                                                <th className="p-3 text-[#202325] text-md font-bold font-['Be_Vietnam_Pro'] leading-[18px] w-64">Thuộc tính</th>
                                                <th className="p-3 text-[#202325] text-md font-bold font-['Be_Vietnam_Pro'] leading-[18px]">Chi tiết</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <DetailTr
                                                title="ID"
                                                value={studentData?.id}
                                                type={0}
                                            />
                                            <DetailTr
                                                title="Họ và tên đệm"
                                                value={studentData?.lastName}
                                                type={1}
                                                required={true}
                                                placeholder={"Nhập họ và tên đệm"}
                                                onChange={(e) => setStudentData({ ...studentData, lastName: e.target.value })}
                                            />
                                            <DetailTr
                                                title="Tên"
                                                value={studentData?.firstName}
                                                type={1}
                                                required={true}
                                                placeholder={"Nhập tên"}
                                                onChange={(e) => setStudentData({ ...studentData, firstName: e.target.value })}
                                            />
                                            {(user.userType === "GV" || user.userType === "AD") ? (
                                                <DetailTr
                                                    title="Kiểu"
                                                    value={studentData?.userType}
                                                    type={3}
                                                    options={Array.isArray(codes["user type"]) ? codes["user type"] : []}
                                                    onChange={(option) => setStudentData({ ...studentData, userType: option })}
                                                    required={true}
                                                />
                                            ) : (
                                                <DetailTr
                                                    title="Kiểu"
                                                    value={studentData?.userType}
                                                    type={0}
                                                    required={true}
                                                />
                                            )}

                                            <DetailTr
                                                title="Tài khoản (tên + sdt HS)"
                                                value={studentData?.username}
                                                type={1}
                                                required={true}
                                                placeholder={"Nhập tài khoản"}
                                                onChange={(e) => setStudentData({ ...studentData, username: e.target.value })}
                                            />


                                            <DetailTr
                                                title="Mật khẩu / sdt HS"
                                                value={studentData?.password}
                                                type={1}
                                                required={true}
                                                placeholder={"Nhập mật khẩu"}
                                                onChange={(e) => setStudentData({ ...studentData, password: e.target.value })}
                                            />

                                            <DetailTr
                                                title={"Giới tính"}
                                                value={studentData?.gender}
                                                valueText={studentData?.gender ? "Nam" : "Nữ"}
                                                type={0}
                                                required={true}
                                            />
                                            <DetailTr
                                                title="Số điện thoại"
                                                value={studentData?.phone}
                                                valueText={studentData?.phone ? studentData?.phone : "Chưa cập nhật"}
                                                type={1}
                                                required={true}
                                                placeholder={"Nhập số điện thoại"}
                                                onChange={(e) => setStudentData({ ...studentData, phone: e.target.value })}
                                            />
                                            <DetailTr
                                                title="Lớp"
                                                value={studentData?.class}
                                                type={3}
                                                options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                                                onChange={(option) => setStudentData({ ...studentData, class: option })}
                                                required={true}
                                            />
                                            <DetailTr
                                                title="Trường học"
                                                value={studentData?.highSchool}
                                                valueText={studentData?.highSchool ? studentData?.highSchool : "Chưa cập nhật"}
                                                type={1}
                                                required={true}
                                                placeholder={"Nhập Trường học"}
                                                onChange={(e) => setStudentData({ ...studentData, highSchool: e.target.value })}
                                            />
                                            <DetailTr
                                                title="Năm tốt nghiệp"
                                                value={studentData?.graduationYear || ''}
                                                valueText={studentData?.graduationYear ? studentData?.graduationYear.toString() : "Chưa cập nhật"}
                                                type={1}
                                                required={false}
                                                placeholder={"VD: 2025, 2026, 2027..."}
                                                onChange={(e) => {
                                                    const value = e.target.value;

                                                    // Allow empty string (for clearing/backspace)
                                                    if (value === '') {
                                                        setStudentData({ ...studentData, graduationYear: null });
                                                        return;
                                                    }

                                                    // Allow partial input while typing (don't validate incomplete numbers)
                                                    if (/^\d{1,4}$/.test(value)) {
                                                        // If it's a complete 4-digit year, validate range
                                                        if (value.length === 4) {
                                                            const year = parseInt(value);
                                                            if (year >= 2020 && year <= 2030) {
                                                                setStudentData({ ...studentData, graduationYear: year });
                                                            }
                                                            // If invalid range, don't update but allow the input to show
                                                        } else {
                                                            // For partial input (1-3 digits), store as string temporarily
                                                            setStudentData({ ...studentData, graduationYear: value });
                                                        }
                                                    }
                                                    // Ignore non-numeric input
                                                }}
                                            />
                                            <DetailTr
                                                title="Trạng thái hoạt động"
                                                value={studentData?.isActive}
                                                valueText={studentData?.isActive ? "Hoạt động" : "Không hoạt động"}
                                                type={5}
                                                required={true}
                                                onChange={(checked) => setStudentData({ ...studentData, isActive: checked })}
                                            />
                                            <DetailTr
                                                title="Ngày tham gia"
                                                valueText={new Date(studentData?.createdAt).toLocaleDateString()}
                                                type={0}
                                            />
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            {/* Save Button */}
                            <div className="p-6 border-t border-gray-200">
                                <div className="flex w-full justify-between">
                                    {/* Nút xóa - chỉ hiển thị cho admin và teacher */}
                                    {(user.userType === UserType.ADMIN || user.userType === UserType.CLASSMANAGEMENT || user.userType === UserType.HUMANRESOURCEMANAGEMENT || user.userType === UserType.TEACHER) && (
                                        <button
                                            type="button"
                                            onClick={() => setShowDeleteModal(true)}
                                            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center gap-2"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                <path d="M3 6h18"></path>
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                <path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"></path>
                                            </svg>
                                            Xóa người dùng
                                        </button>
                                    )}

                                    <div className="flex gap-2">
                                        <button
                                            type="button"
                                            onClick={handlePutUser}
                                            className="px-4 py-2 bg-slate-700 text-white rounded-lg hover:bg-slate-800 transition-colors"
                                        >
                                            Lưu
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </LoadingData>
                </div>
            </div>

            {/* Modal xác nhận xóa */}
            {showDeleteModal && (
                <ConfirmModal
                    isOpen={showDeleteModal}
                    onClose={() => setShowDeleteModal(false)}
                    onConfirm={handleDeleteUser}
                    title="Xác nhận xóa người dùng"
                    message={`Bạn có chắc chắn muốn xóa người dùng "${studentData?.lastName} ${studentData?.firstName}"? Hành động này không thể hoàn tác.`}
                    confirmText="Xóa"
                    cancelText="Hủy"
                    type="danger"
                />
            )}
        </UserAdminLayout>
    );
};

export default UserDetail;
