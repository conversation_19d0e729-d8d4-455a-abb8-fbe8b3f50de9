import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MessageCircle, ChevronDown, RefreshCcw } from "lucide-react";
import OutsideClickWrapper from "../common/OutsideClickWrapper";


const QuestionDropdown = ({ userQuestion, selectedMessageId, aiLoading, aiResponse, handleAskAI }) => {
    const [open, setOpen] = useState(false);

    const handleSelect = (messageId) => {
        setOpen(false);
        handleAskAI(parseInt(messageId));
    };

    return (
        <div className="relative w-full dropdown1Ref">
            <button
                onClick={() => setOpen(!open)}
                disabled={aiLoading}
                className={`w-full flex justify-between items-center p-2 text-sm bg-white border rounded-md shadow-sm hover:border-gray-400 transition-all ${aiLoading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
                    }`}
            >
                <span>
                    {selectedMessageId
                        ? userQuestion[selectedMessageId]
                        : "Chọn câu hỏi AI..."}
                </span>
                <ChevronDown size={14} />
            </button>

            <AnimatePresence>
                {open && (
                    <OutsideClickWrapper
                        ignoreOutsideClick="dropdown1Ref"
                        onClickOutside={() => setOpen(false)}
                    >
                        <motion.div
                            initial={{ opacity: 0, y: 4 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 4 }}
                            transition={{ duration: 0.15 }}
                            className="absolute z-10 mt-2 w-full bg-white border border-gray-200 rounded-md shadow-lg overflow-hidden"
                        >
                            {Object.entries(userQuestion).map(([messageId, questionText]) => {
                                const selected = selectedMessageId === parseInt(messageId);
                                return (
                                    <button
                                        key={messageId}
                                        onClick={() => handleSelect(messageId)}
                                        disabled={aiLoading}
                                        className={`w-full text-left px-4 py-2 text-sm flex items-center gap-2 hover:bg-gray-50 transition-all ${selected && aiResponse
                                            ? "bg-sky-50 text-sky-700"
                                            : "text-gray-700"
                                            } ${aiLoading ? "opacity-50 cursor-not-allowed" : ""}`}
                                    >
                                        <div
                                            className={`p-1 rounded-full flex items-center justify-center ${selected && aiResponse
                                                ? "bg-sky-100 text-sky-600"
                                                : "bg-gray-100 text-gray-500"
                                                }`}
                                        >
                                            <MessageCircle size={12} />
                                        </div>
                                        <span>{questionText}</span>

                                        {aiLoading && selected && (
                                            <RefreshCcw
                                                size={12}
                                                className="ml-auto text-sky-600 animate-spin"
                                            />
                                        )}
                                    </button>
                                );
                            })}
                        </motion.div>
                    </OutsideClickWrapper>
                )}
            </AnimatePresence>
        </div>
    );
};

export default QuestionDropdown;
