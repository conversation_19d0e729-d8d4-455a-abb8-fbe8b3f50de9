'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('user', 'graduationYear', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'Năm tốt nghiệp của học sinh'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('user', 'graduationYear');
  }
};
