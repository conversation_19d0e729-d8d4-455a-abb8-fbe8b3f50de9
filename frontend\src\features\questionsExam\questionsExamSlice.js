import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as examApi from "../../services/examApi";
import { apiHandler } from "../../utils/apiHandler";
import * as questionApi from "../../services/questionApi";

export const fetchExamQuestionsWithoutPagination = createAsyncThunk(
    "questions/fetchExamQuestionsWithExam",
    async ({ id }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, { id, pageSize: 1000, sortOrder: 'DESC' }, (data) => {
        }, false, false);
    }
);

export const putQuestionsExam = createAsyncThunk(
    "questions/putQuestionsExam",
    async ({ examId, questions }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.putQuestionsExamAPI, { examId, questions }, (data) => {
        }, true, false);
    }
);

const initialState = {
    questionsExam: [],
    loading: false,
    loadingPut: false,
    view: 'question',
    showAddImagesModal: false,
    folder: "questionImage",
    selectedId: 0,
    newQuestion: {
        content: "",
        description: "",
        typeOfQuestion: "TN",
        class: "10",
        chapter: null,
        difficulty: null,
        solution: "",
        correctAnswer: null,
        solutionUrl: "",
        ExamQuestions: {
            order: 0,
        },
        isNewQuestion: true,
        statements: [
            {
                content: "",
                isCorrect: false,
                difficulty: null,
                order: 0,
                isNewStatement: true,
            },
            {
                content: "",
                isCorrect: false,
                difficulty: null,
                order: 1,
                isNewStatement: true,
            },
            {
                content: "",
                isCorrect: false,
                difficulty: null,
                order: 2,
                isNewStatement: true,
            },
            {
                content: "",
                isCorrect: false,
                difficulty: null,
                order: 3,
                isNewStatement: true,
            },
        ]
    },
}

const questionsExamSlice = createSlice({
    name: "questionsExam",
    initialState,
    reducers: {
        setQuestionsExam: (state, action) => {
            state.questionsExam = action.payload;
        },
        addStatement: (state, action) => {
            const questionIndex = state.questionsExam.findIndex(q => q.id === state.selectedId);
            if (questionIndex === -1) return;

            const question = state.questionsExam[questionIndex];
            const newStatement = {
                content: "",
                isCorrect: false,
                difficulty: null,
                order: question.statements.length,
                isNewStatement: true,
            };
            question.statements.push(newStatement);
        },
        setNewQuestion: (state, action) => {
            state.newQuestion = action.payload;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        },
        setViewRightContent: (state, action) => {
            state.view = action.payload;
        },
        setSelectedId: (state, action) => {
            state.selectedId = action.payload;
        },
        setQuestions: (state, action) => {
            const question = action.payload;
            const index = state.questionsExam.findIndex(q => q.id === question.id);
            if (index !== -1) {
                state.questionsExam[index] = question;
            } else {
                state.questionsExam.push(question);
            }
        },
        setAllQuestions: (state, action) => {
            state.questionsExam = action.payload;
        },
        deleteQuestion: (state, action) => {
            state.questionsExam = state.questionsExam.filter(q => q.id !== action.payload);

            state.questionsExam = state.questionsExam.map((question, index) => ({
                ...question,
                ExamQuestions: {
                    ...question.ExamQuestions,
                    order: index
                }
            }));
            state.selectedId = state.questionsExam[0]?.id || 0;
            console.log("selectedId", state.selectedId);
        },
        addQuestion: (state) => {
            const maxId = Math.max(...state.questionsExam.map(q => q.id)) || 0;
            state.newQuestion.id = maxId + 1;
            const newQuestions = [state.newQuestion, ...state.questionsExam];
            for (let i = 0; i < newQuestions.length; i++) {
                newQuestions[i].ExamQuestions.order = i;
            }
            state.questionsExam = newQuestions;
            state.selectedId = state.newQuestion.id;
            state.newQuestion = initialState.newQuestion;
        },
        reorderQuestions: (state, action) => {
            const { oldIndex, newIndex } = action.payload;
            if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 &&
                oldIndex < state.questionsExam.length && newIndex < state.questionsExam.length) {

                // Tạo mảng mới với thứ tự đã thay đổi
                const newQuestions = [...state.questionsExam];
                const [movedQuestion] = newQuestions.splice(oldIndex, 1);
                newQuestions.splice(newIndex, 0, movedQuestion);

                // Cập nhật thuộc tính order cho tất cả câu hỏi
                const updatedQuestions = newQuestions.map((question, index) => ({
                    ...question,
                    ExamQuestions: {
                        ...question.ExamQuestions,
                        order: index
                    }
                }));

                state.questionsExam = updatedQuestions;
            }
        },
        reorderStatements: (state, action) => {
            const { questionId, oldIndex, newIndex } = action.payload;

            const questionIndex = state.questionsExam.findIndex(q => q.id === questionId);
            if (questionIndex === -1) return;

            const question = state.questionsExam[questionIndex];
            if (!question.statements || oldIndex === newIndex) return;

            if (oldIndex >= 0 && newIndex >= 0 &&
                oldIndex < question.statements.length && newIndex < question.statements.length) {

                // Tạo mảng mới với thứ tự đã thay đổi
                const newStatements = [...question.statements];
                const [movedStatement] = newStatements.splice(oldIndex, 1);
                newStatements.splice(newIndex, 0, movedStatement);
                // console.log("question", question.statements);
                // Cập nhật thuộc tính order cho tất cả statements
                const updatedStatements = newStatements.map((statement, index) => ({
                    ...statement,
                    order: index
                }));

                // Cập nhật question với statements mới
                const updatedQuestion = {
                    ...question,
                    statements: updatedStatements
                };

                state.questionsExam[questionIndex] = updatedQuestion;
            }
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchExamQuestionsWithoutPagination.pending, (state) => {
                state.loading = true;
                state.questionsExam = [];
                state.selectedId = 0;
            })
            .addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {
                if (action.payload) {
                    state.questionsExam = action.payload.data;
                    state.selectedId = action.payload.data[0]?.id || 0;
                }
                // console.log(state.questionsExam)
                state.loading = false;
            })
            .addCase(fetchExamQuestionsWithoutPagination.rejected, (state) => {
                state.questionsExam = [];
                state.selectedId = 0;
                state.loading = false;
            })
            .addCase(putQuestionsExam.pending, (state) => {
                state.loadingPut = true;
            })
            .addCase(putQuestionsExam.fulfilled, (state, action) => {
                state.loadingPut = false;
            })
            .addCase(putQuestionsExam.rejected, (state) => {
                state.loadingPut = false;
            })
    },
});

export const {
    setQuestionsExam,
    setLoading,
    setViewRightContent,
    setSelectedId,
    setQuestions,
    addQuestion,
    reorderQuestions,
    reorderStatements,
    setNewQuestion,
    deleteQuestion,
    addStatement,
    setAllQuestions,
} = questionsExamSlice.actions;
export default questionsExamSlice.reducer;
