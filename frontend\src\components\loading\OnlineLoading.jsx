import React from 'react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

const typeLoading = {
    AI: "https://lottie.host/fd3fc689-ebea-4104-9077-00539b3e98d3/yLqM3hTSpY.lottie",
    DEFAULT: "https://lottie.host/af496ab8-9afa-428a-b40a-49ca925c66e8/76C6xOJjme.lottie",
}

const OnlineLoading = ({ type = "DEFAULT", className = "" }) => {
    return (
        <DotLottieReact
            src={typeLoading[type]}
            loop
            autoplay
            className={className}
        />
    );
};

export default OnlineLoading;
