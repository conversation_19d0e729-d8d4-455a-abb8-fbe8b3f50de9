import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { getAllUsersAPI, getUserByIdAPI, putUserAPI, putUserTypeAPI, putUserStatusAPI, getUserClassesAPI, findUsersAPI, deleteUserAP<PERSON>, getAllStaffAPI } from "../../services/userApi";
// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";

export const fetchUsers = createAsyncThunk(
    "users/fetchUsers",
    async ({ search, currentPage, limit, sortOrder, graduationYear, classFilter }, { dispatch }) => {
        return await apiHandler(dispatch, getAllUsersAPI, { search, currentPage, limit, sortOrder, graduationYear, classFilter }, (data) => {
        }, true, false);
    }
);

export const fetchStaff = createAsyncThunk(
    "users/fetchStaff",
    async ({ search, currentPage, limit, sortOrder }, { dispatch }) => {
        return await apiHandler(dispatch, getAllStaffAPI, { search, currentPage, limit, sortOrder }, (data) => {
        }, true, false);
    }
);

export const fetchUsersWithoutPagination = createAsyncThunk(
    "users/fetchUsersWithoutPagination",
    async ({ limit = 1000, sortOrder = 'DESC', graduationYear, classFilter }, { dispatch }) => {
        return await apiHandler(dispatch, getAllUsersAPI, { limit, sortOrder, graduationYear, classFilter }, (data) => {
        }, false, false, false, false);
    }
);

export const fetchUserById = createAsyncThunk(
    "users/fetchUserById",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, getUserByIdAPI, id, () => { }, true, false);
    }
);

export const findUsers = createAsyncThunk(
    "users/findUsers",
    async (search, { dispatch }) => {
        return await apiHandler(dispatch, findUsersAPI, search, () => { }, false, false, false, false);
    }
);

export const fetchUserClasses = createAsyncThunk(
    "users/fetchUserClasses",
    async ({ id, search, currentPage, limit, sortOrder }, { dispatch }) => {
        return await apiHandler(dispatch, getUserClassesAPI, { id, search, currentPage, limit, sortOrder }, (data) => {
            // dispatch(setCurrentPage(data.currentPage));
            // dispatch(setTotalPages(data.totalPages));
            // dispatch(setTotalItems(data.totalItems));
        }, true, false, true);
    }
);

export const putUser = createAsyncThunk(
    "users/putUser",
    async ({ id, user }, { dispatch }) => {
        return await apiHandler(dispatch, putUserAPI, { id, user }, () => { }, true);
    }
);

export const putUserType = createAsyncThunk(
    "users/putUserType",
    async ({ id, type }, { dispatch }) => {
        return await apiHandler(dispatch, putUserTypeAPI, { id, type }, () => { }, true);
    }
);

export const putUserStatus = createAsyncThunk(
    "users/putUserStatus",
    async ({ id, status }, { dispatch }) => {
        return await apiHandler(dispatch, putUserStatusAPI, { id, status }, () => { }, true);
    }
);

export const deleteUser = createAsyncThunk(
    "users/deleteUser",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, deleteUserAPI, id, () => { }, true);
    }
);


const userSlice = createSlice({
    name: "users",
    initialState: {
        users: [],
        student: null,
        loadingUser: false,
        foundUsers: [],
        search: "",
        graduationYearFilter: null,
        classFilter: null,
        staff: [],
        pagination: { ...initialPaginationState },
        ...initialFilterState,
    },
    reducers: {
        setUser: (state, action) => {
            state.student = action.payload;
        },
        setGraduationYearFilter: (state, action) => {
            state.graduationYearFilter = action.payload;
        },
        setClassFilter: (state, action) => {
            state.classFilter = action.payload;
        },
        ...paginationReducers,
        ...filterReducers,
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchUsers.pending, (state) => {
                state.users = [];
                state.loading = true;
            })

            .addCase(fetchUsers.fulfilled, (state, action) => {
                if (action.payload) {
                    state.users = action.payload.data;
                    state.pagination = action.payload.pagination;
                    state.graduationYearFilter = action.payload.graduationYear || null;
                    state.classFilter = action.payload.classFilter || null;
                }
                state.loading = false;
            })
            .addCase(fetchUsers.rejected, (state) => {
                state.users = [];
                state.loading = false;
            })
            .addCase(fetchUserById.pending, (state) => {
                state.student = null;
            })
            .addCase(fetchUserById.fulfilled, (state, action) => {
                if (action.payload) {
                    state.student = action.payload.user;
                }
            })
            .addCase(fetchUserById.rejected, () => {
                // Không cần xử lý gì vì lỗi đã được add vào stateApiSlice
            })
            .addCase(fetchUserClasses.pending, (state) => {
                state.users = [];
                state.loading = true;
            })
            .addCase(fetchUserClasses.fulfilled, (state, action) => {
                // console.log("fetchUserClasses", action.payload.data);
                if (action.payload.data) {
                    // console.log("fetchUserClasses", action.payload.data);
                    state.users = action.payload.data.data;
                    state.pagination = action.payload.data.pagination;
                }
                state.loading = false;
            })
            .addCase(fetchUserClasses.rejected, (state) => {
                state.loading = false;
            })
            .addCase(findUsers.pending, (state) => {
                state.foundUsers = [];
            })
            .addCase(findUsers.fulfilled, (state, action) => {
                if (action.payload) {
                    state.foundUsers = action.payload.data;
                }
            })
            .addCase(fetchStaff.pending, (state) => {
                state.staff = [];
                state.loading = true;
            })
            .addCase(fetchStaff.fulfilled, (state, action) => {
                if (action.payload) {
                    state.staff = action.payload.data;
                    state.pagination = action.payload.pagination;
                }
                state.loading = false;
            })
            .addCase(fetchStaff.rejected, (state) => {
                state.staff = [];
                state.loading = false;
            })

    },
});

export const { setUser, setClassFilter, setGraduationYearFilter, setSearch, setSortOrder, setCurrentPage, setLimit, setPagination, setTotalPages } = userSlice.actions;
export default userSlice.reducer;
