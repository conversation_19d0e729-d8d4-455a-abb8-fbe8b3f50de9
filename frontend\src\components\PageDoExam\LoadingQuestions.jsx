
import { useSelector } from "react-redux";
import LoadingText from "../loading/LoadingText";
import QuestionSectionTitle from "./QuestionSectionTitle";


const LoadingQuestion = () => {
    const { darkMode } = useSelector((state) => state.doExam);
    return (
        <div className="flex flex-col gap-2">
            <LoadingText
                loading={true}
                w="w-60"
                color={`${darkMode ? "bg-white" : "bg-gray-200"}`}
            />
            <LoadingText
                loading={true}
                w="w-48"
                color={`${darkMode ? "bg-white" : "bg-gray-200"}`}
            />
            <LoadingText
                loading={true}
                w="w-48"
                color={`${darkMode ? "bg-white" : "bg-gray-200"}`}
            />
            <LoadingText
                loading={true}
                w="w-48"
                color={`${darkMode ? "bg-white" : "bg-gray-200"}`}
            />
            <LoadingText
                loading={true}
                w="w-48"
                color={`${darkMode ? "bg-white" : "bg-gray-200"}`}
            />
        </div>
    )
}

const LoadingQuestions = () => {
    const { darkMode } = useSelector((state) => state.doExam);

    const renderSection = ( count) => (
        <>
            <div className="flex flex-col gap-4">
                {Array.from({ length: count }).map((_, idx) => (
                    <LoadingQuestion key={idx} />
                ))}
            </div>
        </>
    );

    return (
        <div className="flex flex-col w-full items-start justify-start gap-4">
            {renderSection(4)}
        </div>
    );
};

export default LoadingQuestions;