/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type OCRUsageInfo = {
  /**
   * Number of pages processed
   */
  pagesProcessed: number;
  /**
   * Document size in bytes
   */
  docSizeBytes?: number | null | undefined;
};

/** @internal */
export const OCRUsageInfo$inboundSchema: z.ZodType<
  OCRUsageInfo,
  z.ZodTypeDef,
  unknown
> = z.object({
  pages_processed: z.number().int(),
  doc_size_bytes: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    "pages_processed": "pagesProcessed",
    "doc_size_bytes": "docSizeBytes",
  });
});

/** @internal */
export type OCRUsageInfo$Outbound = {
  pages_processed: number;
  doc_size_bytes?: number | null | undefined;
};

/** @internal */
export const OCRUsageInfo$outboundSchema: z.ZodType<
  OCRUsageInfo$Outbound,
  z.ZodTypeDef,
  OCRUsageInfo
> = z.object({
  pagesProcessed: z.number().int(),
  docSizeBytes: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    pagesProcessed: "pages_processed",
    docSizeBytes: "doc_size_bytes",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OCRUsageInfo$ {
  /** @deprecated use `OCRUsageInfo$inboundSchema` instead. */
  export const inboundSchema = OCRUsageInfo$inboundSchema;
  /** @deprecated use `OCRUsageInfo$outboundSchema` instead. */
  export const outboundSchema = OCRUsageInfo$outboundSchema;
  /** @deprecated use `OCRUsageInfo$Outbound` instead. */
  export type Outbound = OCRUsageInfo$Outbound;
}

export function ocrUsageInfoToJSON(ocrUsageInfo: OCRUsageInfo): string {
  return JSON.stringify(OCRUsageInfo$outboundSchema.parse(ocrUsageInfo));
}

export function ocrUsageInfoFromJSON(
  jsonString: string,
): SafeParseResult<OCRUsageInfo, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OCRUsageInfo$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OCRUsageInfo' from JSON`,
  );
}
