import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { debounce } from 'lodash';
import { X } from 'lucide-react';
import { findLessons } from '../features/lesson/lessonSlice';

/**
 * Component for searching and selecting classes
 * 
 * @param {Object} props
 * @param {string} props.value - The current search term
 * @param {string} props.lessonId - The ID of the selected lesson
 * @param {function} props.onChange - Callback when search term changes
 * @param {function} props.onSelect - Callback when a class is selected
 * @param {function} props.onClear - Callback when selection is cleared
 * @param {string} props.placeholder - Placeholder text for the input
 * @param {string} props.className - Additional CSS classes
 */
const LessonSearchInput = ({
    value = '',
    selectedLessonId = '',
    onChange,
    onSelect,
    onClear,
    placeholder = 'Tìm kiếm buổi học...',
    className = 'w-full'
}) => {
    const dispatch = useDispatch();
    const [searchTerm, setSearchTerm] = useState(value);
    const [showDropdown, setShowDropdown] = useState(false);
    const { lessonsSearch } = useSelector((state) => state.lessons);
    const dropdownRef = useRef(null);

    // Handle lesson search with debounce
    const handleLessonSearch = useCallback(
        debounce((searchTerm) => {
            dispatch(findLessons(searchTerm))
        }, 1000),
        [dispatch]
    );

    // Handle input change
    const handleInputChange = (e) => {
        const value = e.target.value;
        setSearchTerm(value);
        if (onChange) onChange(value);
        handleLessonSearch(value);
        setShowDropdown(true);
    };

    // Handle class selection
    const handleSelectLesson = (lessonItem) => {
        if (onSelect) onSelect(lessonItem);
        setShowDropdown(false);
    };

    // Handle clear selection
    const handleClearSelection = () => {
        setSearchTerm('');
        if (onClear) onClear();
    };

    // Update local state when props change
    useEffect(() => {
        setSearchTerm(value);
    }, [value]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            <input
                type="text"
                placeholder={placeholder}
                value={searchTerm}
                onChange={handleInputChange}
                onFocus={() => setShowDropdown(true)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
            {showDropdown && (
                lessonsSearch.length > 0 ? (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {lessonsSearch.map((lessonItem) => (
                            <div
                                key={lessonItem.id}
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => handleSelectLesson(lessonItem)}
                            >
                                <div className="font-medium">{lessonItem.name} - {lessonItem.class.name}</div>
                                <div className="text-xs text-gray-500">
                                    Ngày học {new Date(lessonItem.day).toLocaleDateString('vi-VN')} | Năm: {lessonItem.class.academicYear}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        <div className="px-4 py-2 text-gray-500">Không tìm thấy buổi học</div>
                    </div>
                )
            )}
            {selectedLessonId && (
                <button
                    onClick={handleClearSelection}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                    <X size={16} />
                </button>
            )}
        </div>
    );
};

export default LessonSearchInput;
