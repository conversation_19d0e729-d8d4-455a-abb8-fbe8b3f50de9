// src/layouts/AuthLayout.jsx
import { BeeMathLogo } from '../components/logo/BeeMathLogo';
import backgroundImage from '../assets/images/anh-nen1.jpg'; // Import ảnh từ src/assets
import ParticlesBackground from '../components/ParticlesBackground';

const AuthLayout = ({ children }) => {
    return (
        <div className="relative w-screen bg-white h-screen flex items-center justify-center 
                         overflow-hidden"
        // style={{
        //     backgroundImage: `url(${backgroundImage})`, // Sử dụng ảnh từ src/assets
        //     backgroundSize: 'cover',
        //     backgroundPosition: 'center',
        // }}
        >
            <ParticlesBackground />

            <div className="absolute top-6 left-6 z-50 flex items-center gap-3">
                {/* Logo */}
                <BeeMathLogo className="w-9 h-9 shrink-0" />

                {/* Tên và slogan */}
                <div className="flex flex-col leading-tight">
                    <span className="text-base font-semibold text-gray-800 font-inter tracking-wide">
                        Toan<PERSON>hay<PERSON>ee
                    </span>
                    <span className="text-xs text-gray-500 font-medium font-inter tracking-tight">
                        Học Toán dễ dàng hơn mỗi ngày
                    </span>
                </div>
            </div>


            {children}
        </div>
    );
};

export default AuthLayout;
