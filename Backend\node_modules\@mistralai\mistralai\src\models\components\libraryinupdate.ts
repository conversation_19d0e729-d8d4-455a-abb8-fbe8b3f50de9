/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibraryInUpdate = {
  name?: string | null | undefined;
  description?: string | null | undefined;
};

/** @internal */
export const LibraryInUpdate$inboundSchema: z.ZodType<
  LibraryInUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
});

/** @internal */
export type LibraryInUpdate$Outbound = {
  name?: string | null | undefined;
  description?: string | null | undefined;
};

/** @internal */
export const LibraryInUpdate$outboundSchema: z.ZodType<
  LibraryInUpdate$Outbound,
  z.ZodTypeDef,
  LibraryInUpdate
> = z.object({
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibraryInUpdate$ {
  /** @deprecated use `LibraryInUpdate$inboundSchema` instead. */
  export const inboundSchema = LibraryInUpdate$inboundSchema;
  /** @deprecated use `LibraryInUpdate$outboundSchema` instead. */
  export const outboundSchema = LibraryInUpdate$outboundSchema;
  /** @deprecated use `LibraryInUpdate$Outbound` instead. */
  export type Outbound = LibraryInUpdate$Outbound;
}

export function libraryInUpdateToJSON(
  libraryInUpdate: LibraryInUpdate,
): string {
  return JSON.stringify(LibraryInUpdate$outboundSchema.parse(libraryInUpdate));
}

export function libraryInUpdateFromJSON(
  jsonString: string,
): SafeParseResult<LibraryInUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibraryInUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibraryInUpdate' from JSON`,
  );
}
