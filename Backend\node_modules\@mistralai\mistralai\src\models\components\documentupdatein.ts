/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type DocumentUpdateIn = {
  name?: string | null | undefined;
};

/** @internal */
export const DocumentUpdateIn$inboundSchema: z.ZodType<
  DocumentUpdateIn,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.nullable(z.string()).optional(),
});

/** @internal */
export type DocumentUpdateIn$Outbound = {
  name?: string | null | undefined;
};

/** @internal */
export const DocumentUpdateIn$outboundSchema: z.ZodType<
  DocumentUpdateIn$Outbound,
  z.ZodTypeDef,
  DocumentUpdateIn
> = z.object({
  name: z.nullable(z.string()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DocumentUpdateIn$ {
  /** @deprecated use `DocumentUpdateIn$inboundSchema` instead. */
  export const inboundSchema = DocumentUpdateIn$inboundSchema;
  /** @deprecated use `DocumentUpdateIn$outboundSchema` instead. */
  export const outboundSchema = DocumentUpdateIn$outboundSchema;
  /** @deprecated use `DocumentUpdateIn$Outbound` instead. */
  export type Outbound = DocumentUpdateIn$Outbound;
}

export function documentUpdateInToJSON(
  documentUpdateIn: DocumentUpdateIn,
): string {
  return JSON.stringify(
    DocumentUpdateIn$outboundSchema.parse(documentUpdateIn),
  );
}

export function documentUpdateInFromJSON(
  jsonString: string,
): SafeParseResult<DocumentUpdateIn, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DocumentUpdateIn$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DocumentUpdateIn' from JSON`,
  );
}
