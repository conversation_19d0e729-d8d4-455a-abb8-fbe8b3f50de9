/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const EmbeddingDtype = {
  Float: "float",
  Int8: "int8",
  Uint8: "uint8",
  Binary: "binary",
  Ubinary: "ubinary",
} as const;
export type EmbeddingDtype = ClosedEnum<typeof EmbeddingDtype>;

/** @internal */
export const EmbeddingDtype$inboundSchema: z.ZodNativeEnum<
  typeof EmbeddingDtype
> = z.nativeEnum(EmbeddingDtype);

/** @internal */
export const EmbeddingDtype$outboundSchema: z.ZodNativeEnum<
  typeof EmbeddingDtype
> = EmbeddingDtype$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace EmbeddingDtype$ {
  /** @deprecated use `EmbeddingDtype$inboundSchema` instead. */
  export const inboundSchema = EmbeddingDtype$inboundSchema;
  /** @deprecated use `EmbeddingDtype$outboundSchema` instead. */
  export const outboundSchema = EmbeddingDtype$outboundSchema;
}
