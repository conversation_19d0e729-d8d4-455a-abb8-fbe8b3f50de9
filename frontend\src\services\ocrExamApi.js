import api from "./api";

export const ocrPdfWithMistralAPI = async (file) => {
    const formData = new FormData();
    formData.append("pdf", file);
    const response = await api.post("/v1/AI/handle/exam/pdf", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
}

export const ocrImageWithMistralAPI = async (file) => {
    const formData = new FormData();
    formData.append("image", file);
    const response = await api.post("/v1/AI/ocr/image", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
}
