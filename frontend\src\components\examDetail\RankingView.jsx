import { useSelector, useDispatch } from "react-redux";
import { useEffect } from "react";
import { fetchAttemptsByExamId } from "../../features/attempt/attemptSlice";
import Pagination from "../Pagination";
import { setCurrentPage } from "../../features/filter/filterSlice";
import iconAvatarDefault0 from "../../assets/icons/student.png";
import iconAvatarDefault1 from "../../assets/icons/teenager.png";
import iconAvatarDefault2 from "../../assets/icons/teenager1.png";
import iconAvatarDefault3 from "../../assets/icons/teenager2.png";
import iconAvatarDefault4 from "../../assets/icons/teenager3.png";
import iconAvatarDefault5 from "../../assets/icons/teenager4.png";
import iconAvatarDefault6 from "../../assets/icons/teenager5.png";
import iconAvatarDefault7 from "../../assets/icons/teenager6.png";
import LoadingText from "../loading/LoadingText";
import { Award, Trophy, Medal, Clock } from "lucide-react";
import UserInfoPanel from "./UserInfoPanel";

const RankingView = ({ examId }) => {
    const dispatch = useDispatch();
    const { attempts } = useSelector((state) => state.attempts);
    const { user } = useSelector((state) => state.auth);
    const { currentPage, totalItems, limit } = useSelector((state) => state.filter);
    const { loading } = useSelector((state) => state.states);

    const handlePageChange = (page) => {
        dispatch(setCurrentPage(page));
    };

    const avatarIcons = [
        iconAvatarDefault0,
        iconAvatarDefault1,
        iconAvatarDefault2,
        iconAvatarDefault3,
        iconAvatarDefault4,
        iconAvatarDefault5,
        iconAvatarDefault6,
        iconAvatarDefault7,
    ];

    const getRandomAvatar = () => {
        const index = Math.floor(Math.random() * avatarIcons.length);
        return avatarIcons[index];
    };

    useEffect(() => {
        dispatch(fetchAttemptsByExamId({ examId, currentPage }));
    }, [dispatch, examId, currentPage]);

    if (loading) {
        return (
            <div className="flex flex-col border border-gray-300 rounded-md">
                <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                    <div className="flex items-center gap-2">
                        <Award size={16} className="text-sky-600" />
                        <p className="font-Inter text-sm font-semibold">Bảng xếp hạng</p>
                    </div>
                    <div className="flex items-center gap-2 text-gray-500">
                        <Trophy size={16} className="flex-shrink-0" />
                        <LoadingText loading={true} w="w-20" h="h-3" />
                    </div>
                </div>

                {/* Content Skeleton */}
                <div className="p-4">
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                            <thead>
                                <tr className="border-b border-gray-200">
                                    <th className="text-left py-3 px-2 font-medium text-gray-600">Hạng</th>
                                    <th className="text-left py-3 px-2 font-medium text-gray-600">Học sinh</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Điểm</th>
                                    <th className="text-center py-3 px-2 font-medium text-gray-600">Thời gian</th>
                                </tr>
                            </thead>
                            <tbody>
                                {[...Array(5)].map((_, index) => (
                                    <tr key={index} className="border-b border-gray-100">
                                        <td className="py-3 px-2">
                                            <div className="flex items-center gap-2">
                                                <LoadingText loading={true} w="w-8" h="h-4" />
                                            </div>
                                        </td>
                                        <td className="py-3 px-2">
                                            <div className="flex items-center gap-3">
                                                <LoadingText loading={true} w="w-8" h="h-8" rounded="rounded-full" />
                                                <div className="flex flex-col gap-1">
                                                    <LoadingText loading={true} w="w-24" h="h-4" />
                                                </div>
                                            </div>
                                        </td>
                                        <td className="py-3 px-2 text-center">
                                            <LoadingText loading={true} w="w-12" h="h-5" />
                                        </td>
                                        <td className="py-3 px-2 text-center">
                                            <div className="flex items-center justify-center gap-1">
                                                <Clock size={14} className="text-gray-400" />
                                                <LoadingText loading={true} w="w-16" h="h-4" />
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <>
            {/* User Info Panel */}
            <div div className="mb-4" >
                <UserInfoPanel examId={examId} />
            </div >
            <div className="flex flex-col border border-gray-300 rounded-md">

                {/* Header */}
                <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                    <div className="flex items-center gap-2">
                        <Award size={16} className="text-sky-600" />
                        <p className="font-Inter text-sm font-semibold">Bảng xếp hạng</p>
                    </div>
                    <div className="flex items-center gap-2 text-gray-500">
                        <Trophy size={16} className="flex-shrink-0" />
                        <p className="text-xs">Top {attempts.length} học sinh</p>
                    </div>
                </div>

                {/* Content */}
                <div className="p-4">



                    {attempts?.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b border-gray-200">
                                        <th className="text-left py-3 px-2 font-medium text-gray-600">Hạng</th>
                                        <th className="text-left py-3 px-2 font-medium text-gray-600">Học sinh</th>
                                        <th className="text-center py-3 px-2 font-medium text-gray-600">Điểm</th>
                                        <th className="text-center py-3 px-2 font-medium text-gray-600">Thời gian</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {attempts.map((attempt, index) => {
                                        const rank = (currentPage - 1) * limit + index + 1;
                                        const isCurrentUser = attempt.student?.id === user?.id;

                                        return (
                                            <tr
                                                key={attempt.id}
                                                className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${isCurrentUser ? "bg-sky-50 border-sky-200" : ""
                                                    }`}
                                            >
                                                <td className="py-3 px-2">
                                                    <div className="flex items-center gap-2">
                                                        {rank <= 3 && (
                                                            <span className="text-lg">
                                                                {rank === 1 ? <Trophy className="text-yellow-400" /> : rank === 2 ? <Trophy className="text-gray-400" /> : <Trophy className="text-orange-500" />}
                                                            </span>
                                                        )}
                                                        <span className={`font-medium ${isCurrentUser ? "text-sky-600" : "text-gray-700"}`}>
                                                            #{rank}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td className="py-3 px-2">
                                                    <div className="flex items-center gap-3">
                                                        {attempt.student?.avatarUrl ? (
                                                            <img
                                                                src={attempt.student?.avatarUrl}
                                                                alt="avatar"
                                                                className="w-8 h-8 rounded-full object-cover border border-gray-200"
                                                            />
                                                        ) : (
                                                            <div className="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center text-white bg-gray-300 font-bold border border-gray-200">
                                                                {attempt.student?.firstName?.[0]}{attempt.student?.lastName?.[0]}
                                                            </div>
                                                        )}
                                                        <div className="flex flex-col">
                                                            <span className={`font-medium ${isCurrentUser ? "text-sky-600" : "text-gray-700"}`}>
                                                                {attempt.student?.lastName} {attempt.student?.firstName}
                                                            </span>
                                                            {isCurrentUser && (
                                                                <span className="text-xs bg-sky-100 text-sky-600 px-2 py-0.5 rounded-full w-fit">
                                                                    Bạn
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="py-3 px-2 text-center">
                                                    <span className={`font-semibold text-lg ${isCurrentUser ? "text-sky-600" : "text-green-600"}`}>
                                                        {attempt.score !== null ? attempt.score : "Chưa có"}
                                                    </span>
                                                </td>
                                                <td className="py-3 px-2 text-center">
                                                    <div className="flex items-center justify-center gap-1">
                                                        <Clock size={14} className="text-gray-400" />
                                                        <span className="text-gray-600 text-sm">
                                                            {attempt.duration}
                                                        </span>
                                                    </div>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            <Trophy size={48} className="mx-auto mb-3 text-gray-300" />
                            <p>Chưa có ai tham gia bài thi này.</p>
                        </div>
                    )}

                    {totalItems > limit && (
                        <div className="mt-6 flex justify-center border-t border-gray-200 pt-4">
                            <Pagination
                                currentPage={currentPage}
                                totalItems={totalItems}
                                limit={limit}
                                onPageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default RankingView;
