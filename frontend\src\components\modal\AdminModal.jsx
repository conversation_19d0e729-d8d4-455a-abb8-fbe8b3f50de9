import { useDispatch } from "react-redux";
import { setIsAddView } from "src/features/filter/filterSlice";

const AdminModal = ({ isOpen, onClose, children, headerText }) => {
    const dispatch = useDispatch();

    if (!isOpen) return null;

    // Hàm xử lý khi click vào overlay
    const handleOverlayClick = () => {
        dispatch(setIsAddView(false));
        if (onClose) onClose();
    };

    return (
        <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]"
            onClick={handleOverlayClick}
        >
            {/* Modal content */}
            <div
                className="bg-white shadow-lg relative flex flex-col rounded-xl h-[80vh] w-[80vw]"
                onClick={(e) => e.stopPropagation()} // ❗ Chặn click lan xuống overlay
            >
                {/* Header */}
                <div className="flex justify-between items-center p-6 bg-[#253F61] rounded-t-xl">
                    <div className="text-white text-xl font-medium font-bevietnam">
                        {headerText}
                    </div>
                    <button
                        onClick={onClose}
                        className="text-white text-2xl font-bold hover:text-red-500 transition"
                    >
                        &times;
                    </button>
                </div>

                {/* Content */}
                <div className="px-[2rem] pb-[2.25rem] pt-3 w-full h-full mb-5 overflow-y-auto hide-scrollbar">
                    {children}
                </div>
            </div>
        </div>
    );
};

export default AdminModal;
