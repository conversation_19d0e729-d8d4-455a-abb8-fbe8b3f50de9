import OutsideClickWrapper from "../common/OutsideClickWrapper";
import { useState, useEffect } from "react";
import { Filter, ChevronDown, Check } from "lucide-react";

const SortBar = ({ selected, onChange, sortOptions = [] }) => {
    const [showSortDropdown, setShowSortDropdown] = useState(false);
    const [selectedOption, setSelectedOption] = useState(
        sortOptions.find(opt => opt.value === selected) || sortOptions[0]
    );

    const toggleDropdown = () => setShowSortDropdown(prev => !prev);

    const handleOptionClick = (option) => {
        onChange?.(option.value);
        setSelectedOption(option);
        setShowSortDropdown(false);
    };

    // Cập nhật selectedOption khi prop selected thay đổi từ bên ngoài
    useEffect(() => {
        const updatedOption = sortOptions.find(opt => opt.value === selected);
        if (updatedOption) setSelectedOption(updatedOption);
    }, [selected, sortOptions]);

    return (
        <div className="min-w-[170px] relative inline-block text-left">
            <div
                onClick={toggleDropdown}
                className="flex sortDropdownRef items-center justify-between gap-1 cursor-pointer border border-gray-300 rounded-md px-2 py-[7px] text-gray-700 hover:border-sky-500 hover:bg-sky-50 transition-all duration-150"
            >
                <div className="flex items-center gap-1">
                    <Filter size={16} className="text-sky-600" />
                    <span className="text-xs">
                        Sắp xếp:{" "}
                        <span className="font-semibold text-gray-800">
                            {selectedOption?.label || "Mới nhất"}
                        </span>
                    </span>
                </div>
                <ChevronDown size={14} />
            </div>

            {showSortDropdown && (
                <OutsideClickWrapper
                    ignoreOutsideClick="sortDropdownRef"
                    onClickOutside={() => setShowSortDropdown(false)}
                    className="absolute z-10 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-md right-0"
                >
                    {sortOptions.map((option) => (
                        <div
                            key={option.value}
                            onClick={() => handleOptionClick(option)}
                            className={`px-3 py-2 text-sm text-gray-700 hover:bg-sky-100 cursor-pointer flex items-center justify-between ${selected === option.value
                                    ? "bg-sky-50 font-medium text-sky-700"
                                    : ""
                                }`}
                        >
                            {option.label}
                            {selected === option.value && <Check size={14} />}
                        </div>
                    ))}
                </OutsideClickWrapper>
            )}
        </div>
    );
};

export default SortBar;
