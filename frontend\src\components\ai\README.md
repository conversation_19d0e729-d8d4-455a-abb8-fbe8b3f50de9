# AI Widget Documentation

## Overview
The AI Widget is a comprehensive component that provides OCR (Optical Character Recognition) functionality and image upload capabilities for exam administration pages.

## Features

### 1. Image Upload Methods
- **Drag and Drop**: Users can drag image files directly onto the upload area
- **Ctrl+V Paste**: Users can paste images from clipboard using Ctrl+V
- **File Selection**: Traditional file picker interface

### 2. OCR Processing
- Uses the `/v1/AI/ocr/image` API endpoint from MistralAI
- Processes uploaded images to extract text content
- Returns markdown-formatted text with LaTeX support
- **Important**: OCR response includes `base64Images` array that contains processed images

### 3. Display Components
- **Image Preview**: Shows the uploaded image before processing
- **OCR Text Input**: Editable textarea with extracted text
- **LaTeX Preview**: Real-time LaTeX rendering using RenderLatex component
- **Upload Button**: Saves **OCR-processed base64Images** to Firebase storage (not original uploaded image)

### 4. Integration
- Seamlessly integrates with existing exam administration interface
- Follows current design patterns and styling
- Uses existing Redux state management

## Usage

### Basic Implementation
```jsx
import AIWidget from '../ai/AIWidget';

// In your component
<AIWidget 
    onImageUploaded={(imageUrl) => {
        // Handle uploaded image URL
        console.log('Image uploaded:', imageUrl);
    }}
    className="w-full"
/>
```

### Props
- `onImageUploaded`: Callback function called when image is successfully uploaded
- `className`: Additional CSS classes for styling

## File Structure
```
frontend/src/components/ai/
├── AIWidget.jsx          # Main AI Widget component
└── README.md            # This documentation
```

## Dependencies
- React hooks (useState, useRef, useEffect)
- Redux (useDispatch, useSelector)
- Lucide React icons
- RenderLatex component
- uploadBase64Images function
- ocrImageWithMistralAPI service

## API Integration

### OCR Endpoint
- **URL**: `/v1/AI/ocr/image`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Body**: FormData with 'image' field
- **Response**: `{ markdown: string, base64Images: string[] }`

### Upload Workflow
1. User uploads image (drag-drop, paste, or file selection)
2. OCR processes the image and returns `{ markdown, base64Images }`
3. User can edit the extracted markdown text
4. **Upload button uploads the `base64Images` from OCR response** (not original image)
5. Uses existing `uploadBase64Images` function with **folder**: "questionImage"
6. **Returns**: Array of uploaded image URLs from processed images

## Styling
The component uses Tailwind CSS classes and follows the existing design system:
- Purple gradient for the main button
- Consistent spacing and typography
- Responsive design
- Loading states with spinners
- Error and success feedback

## Error Handling
- File type validation (JPEG, PNG only)
- File size validation (10MB limit)
- API error handling with user feedback
- Loading states for better UX

## Integration Points

### AddExamAdmin.jsx
- Added to Step 2 form (content creation step)
- Integrates with existing base64Images array
- Positioned after OCR section

### QuestionOfExamAdmin.jsx
- Added to LeftContent component
- Positioned after header section
- Available for question creation workflow

## Future Enhancements
- Support for additional image formats
- Batch image processing
- OCR confidence scoring
- Text correction suggestions
- Integration with question auto-generation
