'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
  class StudentExamAttempt extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      StudentExamAttempt.belongsTo(models.User, { foreignKey: 'studentId', as: 'student' });
      StudentExamAttempt.belongsTo(models.Exam, { foreignKey: "examId", as: "exam" });
    }

    /**
     * Check if the exam attempt has timed out based on the exam's duration
     * @returns {boolean} True if the exam has timed out, false otherwise
     */
    async hasTimedOut() {
      if (this.endTime) {
        return false; // Already submitted
      }

      const exam = await this.getExam();
      if (!exam || !exam.testDuration) {
        return false; // No duration set
      }

      const startTime = new Date(this.startTime);
      const durationMs = exam.testDuration * 60 * 1000; // Convert minutes to milliseconds
      const endTime = new Date(startTime.getTime() + durationMs);

      return new Date() > endTime;
    }

    /**
     * Automatically submit the exam if the time has run out
     * @returns {Object|null} The updated attempt if submitted, null if not
     */
    async autoSubmitIfTimedOut() {
      const hasTimedOut = await this.hasTimedOut();

      if (!hasTimedOut) {
        return null;
      }

      // If timed out and not yet submitted, submit it
      if (!this.endTime) {
        const transaction = await sequelize.transaction();

        try {
          // Set end time
          this.endTime = new Date();
          await this.save({ transaction });

          // Update student exam status
          const [status] = await sequelize.models.StudentExamStatus.findOrCreate({
            where: {
              studentId: this.studentId,
              examId: this.examId
            },
            defaults: {
              isDone: true,
              isSave: false,
              completionTime: this.endTime
            },
            transaction
          });

          if (status && !status.isDone) {
            status.isDone = true;
            status.completionTime = this.endTime;
            await status.save({ transaction });
          }

          // Calculate score
          const answers = await sequelize.models.Answer.findAll({
            where: { attemptId: this.id },
            include: {
              model: sequelize.models.Question,
              attributes: ['id', 'typeOfQuestion']
            },
            transaction
          });

          let totalScore = 0;

          for (const answer of answers) {
            const { typeOfQuestion } = answer.Question || {};
            const isCorrect = answer.result === true;

            if (typeOfQuestion === 'TN' && isCorrect) {
              totalScore += 0.25;
            } else if (typeOfQuestion === 'TLN' && isCorrect) {
              totalScore += 0.5;
            } else if (typeOfQuestion === 'DS') {
              let count = 0;
              if (!answer.answerContent || answer.answerContent == []) continue;

              try {
                const answersDS = JSON.parse(answer.answerContent);

                for (const answerDS of answersDS || []) {
                  const statement = await sequelize.models.Statement.findByPk(answerDS.statementId, { transaction });
                  if (statement && statement.isCorrect === answerDS.answer) {
                    count++;
                  }
                }

                // Tính điểm dựa vào số lượng đúng
                if (count === 1) totalScore += 0.1;
                else if (count === 2) totalScore += 0.25;
                else if (count === 3) totalScore += 0.5;
                else if (count >= 4) totalScore += 1.0;
              } catch (e) {
                console.error("Error parsing DS answer:", e);
              }
            }
          }

          this.score = parseFloat(totalScore.toFixed(2));
          await this.save({ transaction });

          await transaction.commit();

          console.log(`Auto-submitted exam attempt ${this.id} for student ${this.studentId}, score: ${this.score}`);

          return this;
        } catch (error) {
          await transaction.rollback();
          console.error("Error auto-submitting exam:", error);
          return null;
        }
      }

      return null;
    }
  }
  StudentExamAttempt.init({
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    examId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    startTime: DataTypes.DATE,
    endTime: DataTypes.DATE,
    score: DataTypes.FLOAT
  }, {
    sequelize,
    modelName: 'StudentExamAttempt',
    tableName: 'studentExamAttempt',
    timestamps: false,
  })
  return StudentExamAttempt
}