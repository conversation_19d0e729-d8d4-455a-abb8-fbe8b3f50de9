import { AlertTriangle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
// Component hiển thị thông báo 404
const NotFound = ({
    message = "Không tìm thấy nội dung bạn yêu cầu.",
    backLink = "/overview",
    backText = "Quay về trang chủ"
}) => {
    const navigate = useNavigate();

    return (
        <div className="flex flex-col justify-center items-center h-screen text-gray-600 w-full p-4 text-center">
            <AlertTriangle size={64} className="text-red-500 mb-4" />
            <h1 className="text-3xl font-bold mb-2">404 - Không tìm thấy</h1>
            <p className="text-lg mb-6">{message}</p>
            <button
                onClick={() => navigate(backLink)}
                className="px-5 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-all"
            >
                {backText}
            </button>
        </div>
    );
};

// Layout bọc quanh các trang có thể "not found"
const NotFoundLayout = ({ children, isFound, message, backLink, backText, className = "flex flex-col min-h-screen w-full", isLoading, textLoading }) => {
    if (isLoading) {
        return (
            <div className={` ${className} flex justify-center items-center`}>
                <LoadingSpinner
                    size="40"
                    showText={true}
                    text={textLoading || "Đang tải..."} />
            </div>
        );
    }


    return (
        <div className={`w-full h-full ${className}`}>
            {isFound ? (
                children
            ) : (
                <NotFound
                    message={message}
                    backLink={backLink}
                    backText={backText}
                />
            )}
        </div>
    );
};

export default NotFoundLayout;
