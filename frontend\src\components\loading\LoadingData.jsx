import LoadingSpinner from "./LoadingSpinner"
import { useNavigate } from "react-router-dom";
export const LoadingData = ({ loading, isNoData = true, loadText = "Đang tải dữ liệu", noDataText = "Không có dữ liệu.", children, IconNoData, buttonGoBack = false, routeBack = -1 }) => {
    const navigate = useNavigate();

    if (loading) return (
        <div className="flex items-center justify-center h-64">
            <LoadingSpinner color="text-gray-200" size="40" showText={true} text={loadText} />
        </div>
    )

    if (isNoData) {
        return (
            <div className="flex flex-col items-center justify-center text-center text-gray-500 h-64">
                {IconNoData && <IconNoData size={32} className="mx-auto mb-4 text-gray-300" />}
                <p className="text-sm">{noDataText}</p>
                {buttonGoBack && (
                    <button
                        onClick={() => navigate(routeBack)}
                        className="mt-4 px-3 py-1.5 text-sm bg-gray-200 hover:bg-gray-300 rounded-md text-gray-700"
                    >
                        {buttonGoBack}
                    </button>
                )}
            </div>
        );
    }

    return children

}

export default LoadingData;