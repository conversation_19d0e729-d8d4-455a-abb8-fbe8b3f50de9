import api from './api';

// 📌 L<PERSON>y danh sách điểm danh (c<PERSON> phân trang, tì<PERSON> kiếm)
export const getAttendanceListAPI = async (params) => {
    return await api.get('/v1/attendance', { params });
};


// 📌 Tạo bản ghi điểm danh cho một học sinh
export const postAttendanceAPI = async (data) => {
    return await api.post('/v1/attendance', data);
};

// 📌 Tạo điểm danh cho toàn bộ lớp theo lessonId
export const postBulkAttendanceAPI = async (data) => {
    return await api.post('/v1/attendance/bulk', data);
};

// 📌 Cập nhật trạng thái hoặc ghi chú điểm danh
export const putAttendanceAPI = async ({ attendanceId, data }) => {
    return await api.put(`/v1/attendance/${attendanceId}`, data);
};

// 📌 X<PERSON>a bản ghi điểm danh
export const deleteAttendanceAPI = async (attendanceId) => {
    return await api.delete(`/v1/attendance/${attendanceId}`);
};

// 📌 Lấy tất cả điểm danh của một user (chia theo tháng)
export const getUserAttendancesAPI = async (params = {}) => {
    return await api.get(`/v1/user/attendance`, { params });
};

export const getUserAttendancesAPIAdmin = async (userId, params = {}) => {
    return await api.get(`/v1/admin/${userId}/attendance`, { params });
};

// ========== STATISTICS APIs ==========

// 📌 Thống kê điểm danh cho một buổi học cụ thể
export const getLessonAttendanceStatisticsAPI = async (lessonId) => {
    return await api.get(`/v1/attendance/statistics/lesson/${lessonId}`);
};

// 📌 Thống kê điểm danh cho một lớp học
export const getClassAttendanceStatisticsAPI = async (classId, params = {}) => {
    return await api.get(`/v1/attendance/statistics/class/${classId}`, { params });
};

// 📌 Thống kê điểm danh tổng quan
export const getOverallAttendanceStatisticsAPI = async (params = {}) => {
    return await api.get('/v1/attendance/statistics/overall', { params });
};
