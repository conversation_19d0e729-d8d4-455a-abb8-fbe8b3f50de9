import db from '../models/index.js';
import ResponseDataPagination from '../dtos/responses/pagination/PaginationResponse.js';
import { Op, fn, col } from 'sequelize';

export const CommentService = {
    async getByExamId(examId, page = 1, pageSize = 10) {
        const offset = (page - 1) * pageSize;

        const { count, rows } = await db.ExamComments.findAndCountAll({
            where: {
                examId,
                commentId: null, // chỉ lấy comment gốc
            },
            include: [
                {
                    model: db.User,
                    as: "user",
                    attributes: ["id", "lastName", "firstName", "avatarUrl", "userType"],
                },
                {
                    model: db.ExamComments,
                    as: "replies",
                    attributes: [], // không lấy nội dung replies, chỉ dùng để đếm
                    required: false,
                },
            ],
            attributes: {
                include: [
                    [fn("COUNT", col("replies.id")), "replyCount"], // đế<PERSON> số replies
                ],
            },
            group: ["ExamComments.id", "user.id"], // group by để COUNT hoạt động
            order: [["createdAt", "DESC"]],
            limit: pageSize,
            offset,
            subQuery: false, // quan trọng để tránh sai limit/offset
        });

        return new ResponseDataPagination(rows, {
            total: count.length,
            page,
            pageSize,
            totalPages: Math.ceil(count.length / pageSize),
        });
    },

    async getRepliesByCommentId(commentId, page = 1, pageSize = 10) {
        const offset = (page - 1) * pageSize;

        const { count, rows } = await db.ExamComments.findAndCountAll({
            where: {
                commentId,
            },
            include: [
                {
                    model: db.User,
                    as: "user",
                    attributes: ["id", "lastName", "firstName", "avatarUrl", "userType"],
                },
            ],
            order: [["createdAt", "ASC"]],
            limit: pageSize,
            offset,
        });

        return new ResponseDataPagination(rows, {
            total: count.length,
            page,
            pageSize,
            totalPages: Math.ceil(count.length / pageSize),
        });
    },

    async getByCommentId(commentId) {
        return await db.ExamComments.findByPk(commentId);
    },

    async create(data) {
        return await db.ExamComments.create(data);
    },

    async update(commentId, newContent, userId) {
        const comment = await this.getByCommentId(commentId);
        this._throwIfUnauthorizedOrNotFound(comment, userId);

        comment.content = newContent;
        await comment.save();
        return comment;
    },

    async remove(commentId, userId) {
        const comment = await this.getByCommentId(commentId);
        this._throwIfUnauthorizedOrNotFound(comment, userId);

        await comment.destroy();
        return true;
    },

    _throwIfUnauthorizedOrNotFound(comment, userId) {
        if (!comment) {
            throw new Error('Bình luận không tồn tại');
        }
        if (comment.userId !== userId) {
            throw new Error('Không có quyền với bình luận này');
        }
    }
};
