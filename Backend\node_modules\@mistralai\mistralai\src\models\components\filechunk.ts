/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FileChunk = {
  type?: "file" | undefined;
  fileId: string;
};

/** @internal */
export const FileChunk$inboundSchema: z.ZodType<
  FileChunk,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: z.literal("file").default("file"),
  file_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "file_id": "fileId",
  });
});

/** @internal */
export type FileChunk$Outbound = {
  type: "file";
  file_id: string;
};

/** @internal */
export const FileChunk$outboundSchema: z.ZodType<
  FileChunk$Outbound,
  z.ZodTypeDef,
  FileChunk
> = z.object({
  type: z.literal("file").default("file" as const),
  fileId: z.string(),
}).transform((v) => {
  return remap$(v, {
    fileId: "file_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FileChunk$ {
  /** @deprecated use `FileChunk$inboundSchema` instead. */
  export const inboundSchema = FileChunk$inboundSchema;
  /** @deprecated use `FileChunk$outboundSchema` instead. */
  export const outboundSchema = FileChunk$outboundSchema;
  /** @deprecated use `FileChunk$Outbound` instead. */
  export type Outbound = FileChunk$Outbound;
}

export function fileChunkToJSON(fileChunk: FileChunk): string {
  return JSON.stringify(FileChunk$outboundSchema.parse(fileChunk));
}

export function fileChunkFromJSON(
  jsonString: string,
): SafeParseResult<FileChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FileChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FileChunk' from JSON`,
  );
}
