import api from "./api";

// Hỏi đáp AI với câu hỏi từ database
export const askQuestionWithAI = async ({ questionId, messageId = 1 }) => {
    const payload = { messageId };

    return await api.post(`/v1/ai/ask-question/${questionId}`, payload);
};

// Gọi GPT API trực tiếp (route gốc)
export const callGPT = async (messages) => {
    return await api.post('/v1/gpt', { messages });
};

export const classifyQuestions = async (questions) => {
    return await api.post('/v1/ai/classify-questions', { questions });
};

// Sửa chính tả và ký hiệu LaTeX
export const fixTextAndLatex = async (text) => {
    return await api.post('/v1/ai/fix-text-latex', { text });
};
