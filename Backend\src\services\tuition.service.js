import db from '../models/index.js';
import { Op } from 'sequelize';
import StudentClassStatus from '../constants/StudentClassStatus.js';
const { TuitionPayment, ClassTuition } = db;
/**
 * Tính toán số tiền học phí dự kiến (expectedAmount) cho một học sinh trong một tháng cụ thể
 * bằng cách kiểm tra tất cả các lớp học sinh đó đã tham gia (status = 'JS') và lấy amount từ ClassTuition
 *
 * @param {number} userId - ID của học sinh
 * @param {string} month - Th<PERSON>g tính học phí (định dạng YYYY-MM)
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn bổ sung (transaction, ...)
 * @param {Object} cache - Cache để lưu trữ dữ liệu đã truy vấn
 * @returns {Promise<number>} Tổng số tiền học phí dự kiến
 */
export const calculateExpectedAmount = async (userId, month, options = {}, cache = null) => {
    try {
        // Lấy danh sách các lớp học sinh đã tham gia với status = 'JS' (Joined Successfully)
        const studentClasses = await db.StudentClassStatus.findAll({
            where: {
                studentId: userId,
                status: StudentClassStatus.JOINED // 'JS' = Joined Successfully
            },
            include: [
                {
                    model: db.Class,
                    as: 'class',
                    attributes: ['id', 'name']
                }
            ],
            ...options
        });

        if (!studentClasses || studentClasses.length === 0) {
            return 0; // Học sinh không tham gia lớp nào
        }

        // Lấy danh sách ID của các lớp học sinh đã tham gia
        const classIds = studentClasses.map(sc => sc.classId);

        // Khởi tạo biến để lưu tổng học phí
        let totalAmount = 0;

        // Danh sách các lớp cần truy vấn (không có trong cache)
        const classIdsToQuery = [];

        // Nếu có cache, kiểm tra xem lớp nào đã có trong cache
        if (cache && cache.classTuitions) {
            for (const classId of classIds) {
                const cacheKey = `${classId}_${month}`;
                if (cache.classTuitions[cacheKey] !== undefined) {
                    // Nếu đã có trong cache, cộng vào tổng
                    totalAmount += parseFloat(cache.classTuitions[cacheKey] || 0);
                } else {
                    // Nếu chưa có trong cache, thêm vào danh sách cần truy vấn
                    classIdsToQuery.push(classId);
                }
            }
        } else {
            // Nếu không có cache, truy vấn tất cả
            classIdsToQuery.push(...classIds);
        }

        // Nếu còn lớp cần truy vấn
        if (classIdsToQuery.length > 0) {
            // Lấy học phí của các lớp trong tháng cụ thể
            const classTuitions = await ClassTuition.findAll({
                where: {
                    classId: {
                        [Op.in]: classIdsToQuery
                    },
                    month: month
                },
                ...options
            });

            // Thêm vào cache và tính tổng
            if (classTuitions && classTuitions.length > 0) {
                for (const tuition of classTuitions) {
                    const amount = parseFloat(tuition.amount || 0);
                    totalAmount += amount;

                    // Lưu vào cache nếu có
                    if (cache && cache.classTuitions) {
                        const cacheKey = `${tuition.classId}_${month}`;
                        cache.classTuitions[cacheKey] = amount;
                    }
                }
            }
        }

        return totalAmount;
    } catch (error) {
        console.error('Lỗi khi tính toán học phí dự kiến:', error);
        throw error;
    }
};

/**
 * Tính toán và cập nhật số tiền học phí dự kiến (expectedAmount) cho một khoản đóng học phí
 *
 * @param {number} tuitionPaymentId - ID của khoản đóng học phí
 * @param {Object} options - Các tùy chọn bổ sung (transaction, ...)
 * @param {Object} cache - Cache để lưu trữ dữ liệu đã truy vấn
 * @returns {Promise<Object>} Khoản đóng học phí đã được cập nhật
 */
export const updateExpectedAmountForTuitionPayment = async (tuitionPaymentId, options = {}, cache = null) => {
    try {
        // Lấy thông tin khoản đóng học phí
        const tuitionPayment = await TuitionPayment.findByPk(tuitionPaymentId, options);

        if (!tuitionPayment) {
            throw new Error('Khoản đóng học phí không tồn tại');
        }

        // Tính toán số tiền học phí dự kiến
        const expectedAmount = await calculateExpectedAmount(
            tuitionPayment.userId,
            tuitionPayment.month,
            options,
            cache
        );

        // Cập nhật số tiền học phí dự kiến
        await tuitionPayment.update({ expectedAmount, isCustom: false }, options);

        return tuitionPayment;
    } catch (error) {
        console.error('Lỗi khi cập nhật học phí dự kiến:', error);
        throw error;
    }
};

/**
 * Lấy tất cả học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
 *
 * @param {number} userId - ID của học sinh
 * @param {string} month - Tháng cần lấy học phí (định dạng YYYY-MM)
 * @param {Object} options - Các tùy chọn bổ sung (transaction, ...)
 * @returns {Promise<Array>} Danh sách học phí của các lớp
 */
export const getClassTuitionsByStudentAndMonth = async (userId, month, options = {}) => {
    try {
        // Lấy danh sách các lớp học sinh đã tham gia với status = 'JS' (Joined Successfully)
        const studentClasses = await db.StudentClassStatus.findAll({
            where: {
                studentId: userId,
                status: StudentClassStatus.JOINED // 'JS' = Joined Successfully
            },
            include: [
                {
                    model: db.Class,
                    as: 'class',
                    attributes: ['id', 'name', 'grade']
                }
            ],
            ...options
        });

        if (!studentClasses || studentClasses.length === 0) {
            return []; // Học sinh không tham gia lớp nào
        }

        // Lấy danh sách ID của các lớp học sinh đã tham gia
        const classIds = studentClasses.map(sc => sc.classId);

        // Lấy học phí của các lớp trong tháng cụ thể
        const classTuitions = await ClassTuition.findAll({
            where: {
                classId: {
                    [Op.in]: classIds
                },
                month: month
            },
            include: [
                {
                    model: db.Class,
                    as: 'class',
                    attributes: ['id', 'name', 'grade']
                }
            ],
            ...options
        });

        // Kết hợp thông tin lớp học với học phí
        const result = classTuitions.map(tuition => {
            // Tìm thông tin lớp học từ studentClasses
            const studentClass = studentClasses.find(sc => sc.classId === tuition.classId);

            return {
                id: tuition.id,
                classId: tuition.classId,
                className: tuition.class ? tuition.class.name : null,
                classGrade: tuition.class ? tuition.class.grade : null,
                month: tuition.month,
                amount: parseFloat(tuition.amount || 0),
                note: tuition.note,
                joinDate: studentClass ? studentClass.createdAt : null
            };
        });

        return result;
    } catch (error) {
        console.error('Lỗi khi lấy học phí của các lớp:', error);
        throw error;
    }
};

/**
 * Lấy thông tin học phí (isPaid) của danh sách user trong tháng của ngày lesson.
 * @param {number[]} userIds - Danh sách userId cần kiểm tra.
 * @param {Date} lessonDay - Ngày của buổi học (dùng để tính tháng/năm).
 * @returns {Promise<Map<number, boolean|null>>} - Map từ userId đến isPaid (true/false/null).
 */
export const getTuitionStatusesByUserIds = async (userIds, formattedMonth) => {
    if (!userIds || userIds.length === 0) return new Map();


    const payments = await TuitionPayment.findAll({
        where: {
            userId: { [Op.in]: userIds },
            month: formattedMonth
        },
        attributes: ['userId', 'isPaid']
    });

    const resultMap = new Map();

    // Gắn isPaid từ kết quả có sẵn
    for (const payment of payments) {
        resultMap.set(payment.userId, payment.isPaid);
    }

    // Gắn null cho những user không có dữ liệu
    for (const id of userIds) {
        if (!resultMap.has(id)) {
            resultMap.set(id, null);
        }
    }

    return resultMap;
};


export default {
    calculateExpectedAmount,
    updateExpectedAmountForTuitionPayment,
    getClassTuitionsByStudentAndMonth
};
