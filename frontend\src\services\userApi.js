import api from "./api";

export const getAllUsersAPI = ({ search = "", currentPage = 1, limit = 10, sortOrder = 'desc', graduationYear = null, gradeFilter = null, classFilter = null }) => {
    const params = {
        search,
        page: currentPage,
        limit,
        sortOrder,
    };

    // Chỉ thêm filter parameters nếu có giá trị
    if (graduationYear !== null && graduationYear !== undefined) {
        params.graduationYear = graduationYear;
    }

    if (gradeFilter !== null && gradeFilter !== undefined && gradeFilter !== '') {
        params.grade = gradeFilter;
    }

    if (classFilter !== null && classFilter !== undefined && classFilter !== '') {
        params.classId = classFilter;
    }

    return api.get("/v1/admin/user", { params });
};

export const getAllStaffAPI = ({ search = "", currentPage = 1, limit = 10, sortOrder = 'desc' }) => {
    return api.get("/v1/admin/staff", {
        params: {
            search,
            page: currentPage,
            limit,
            sortOrder,
        }
    });
};

export const getUserByIdAPI = (id) => {
    return api.get(`/v1/admin/user/${id}`);
};

export const findUsersAPI = (search) => {
    return api.get("/v1/admin/user/search", {
        params: {
            search,
        }
    });
}

export const getUserClassesAPI = ({ id, search = "", currentPage = 1, limit = 10, sortOrder = 'desc' }) => {
    return api.get(`/v1/admin/user/class/${id}`, {
        params: {
            search,
            page: currentPage,
            limit,
            sortOrder,
        }
    });
}

export const putUserAPI = ({ id, user }) => {
    return api.put(`/v1/admin/user/${id}`, user);
}

export const putUserTypeAPI = ({ id, type }) => {
    return api.put(`/v1/admin/user/${id}/user-type`, { userType: type });
}

export const putUserStatusAPI = ({ id, status }) => {
    return api.put(`/v1/admin/user/${id}/status`, { status });
}

export const deleteUserAPI = (id) => {
    return api.delete(`/v1/admin/user/${id}`);
}

