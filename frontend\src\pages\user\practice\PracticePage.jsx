import UserLayout from "../../../layouts/UserLayout";
import ExamCard from "../../../components/card/ExamCard";
import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect } from "react";
import Pagination from "../../../components/Pagination";
import {
    BookOpen,
    FileText,
    GraduationCap,
    PencilLine,
    Tag,
    Search,
    ChevronDown,
    ChevronUp,
    Info,
    Calendar
} from "lucide-react";
import { fetchPublicExams } from "../../../features/practice/practiceSlice";
import SortBar from "src/components/filter/SortBar";
import { fetchCodesByType } from "src/features/code/codeSlice";
import FilterBar from "src/components/filter/FilterBar";
import LoadingData from "src/components/loading/LoadingData";
import { setSort, setTypeOfExam, setGrade, setChapter, setView, setSearch, setLoading, setCurrentPage, setIsClassroomExam, setYear } from "src/features/practice/practiceSlice";
import useDebouncedEffect from "src/hooks/useDebouncedEffect";

const RowSideBar = ({ title, isActive, onClick, Icon }) => {
    return (
        <div
            onClick={onClick}
            className="relative w-full">
            {isActive && (
                <div className="absolute -left-2 top-1/2 -translate-y-1/2 h-[80%] w-[4px] bg-sky-600 rounded-md" />
            )}

            <div className={`w-full px-[8px] py-[6px] text-slate-800 rounded-md font-inter cursor-pointer  ${isActive ? 'bg-[#ECEEF0] font-semibold' : 'font-light hover:bg-[#F2F3F4]'}`}>
                <div className="flex flex-row items-center gap-2">
                    {Icon && <Icon size={16} className="text-gray-600 flex-shrink-0" />}
                    <span className="text-sm ">{title}</span>
                </div>
            </div>
        </div>
    );
}

const Guide = () => {
    const [showGuide, setShowGuide] = useState(false);

    const toggleGuide = () => setShowGuide((prev) => !prev);

    return (
        <div className="flex flex-col gap-2">
            <div
                className="flex flex-row px-[8px] py-[6px] hover:bg-[#F2F3F4] rounded-md text-gray-800 items-center gap-2 cursor-pointer select-none justify-between"
                onClick={toggleGuide}
            >
                <div className="flex items-center gap-2">
                    <BookOpen size={16} className="text-gray-600 flex-shrink-0" />
                    <span className="text-sm font-medium">Hướng dẫn lọc đề</span>
                </div>
                {showGuide ? (
                    <ChevronUp size={16} className="text-gray-600" />
                ) : (
                    <ChevronDown size={16} className="text-gray-600" />
                )}
            </div>

            {showGuide && (
                <div className="mt-2 text-xs text-gray-700 bg-gray-50 border border-gray-200 rounded-md p-3 space-y-2">
                    <div className="flex items-start gap-2">
                        <Info size={14} className="mt-[2px] text-sky-600" />
                        <p>
                            <strong>Loại đề:</strong> Chọn loại đề phù hợp với nhu cầu của bạn.
                        </p>
                    </div>
                    <div className="flex items-start gap-2">
                        <Info size={14} className="mt-[2px] text-sky-600" />
                        <p>
                            <strong>Chương:</strong> Lọc theo chương mà bạn muốn ôn luyện.
                        </p>
                    </div>
                    <div className="flex items-start gap-2">
                        <Info size={14} className="mt-[2px] text-sky-600" />
                        <p>
                            <strong>Khối:</strong> Hiển thị đề phù hợp với lớp bạn đang học.
                        </p>
                    </div>
                    <div className="flex items-start gap-2">
                        <Info size={14} className="mt-[2px] text-sky-600" />
                        <p>
                            <strong>Sắp xếp:</strong> Ưu tiên đề mới nhất, điểm cao, v.v.
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
};

const SideBar = () => {
    const { codes } = useSelector((state) => state.codes);
    const { grade, isClassroomExam } = useSelector((state) => state.practice);
    const dispatch = useDispatch();

    return (
        <div className="h-fit w-full md:w-1/4 lg:w-1/6 pt-4 pb-4 md:sticky top-24">
            <RowSideBar title="Tất cả đề thi" isActive={isClassroomExam === null} onClick={() => dispatch(setIsClassroomExam(null))} Icon={FileText} />
            <RowSideBar title="Đề thi trên lớp" isActive={isClassroomExam === true} onClick={() => dispatch(setIsClassroomExam(true))} Icon={GraduationCap} />
            <RowSideBar title="Đề thi tự luyện" isActive={isClassroomExam === false} onClick={() => dispatch(setIsClassroomExam(false))} Icon={PencilLine} />
            <hr className="my-4 border-gray-300" />
            <p className="ml-[8px] text-sm text-gray-800">Khối</p>
            {codes?.['grade']?.map((code) => (
                <RowSideBar key={code.code} title={code.description} isActive={grade === code.code} onClick={() => dispatch(setGrade(code.code))} Icon={GraduationCap} />
            ))}
            <hr className="my-4 border-gray-300" />
            <Guide />
        </div>
    );
}

const NavigateButton = ({ title, onClick, isActive }) => {
    return (
        <button
            onClick={onClick}
            className={`p-[6px] ${isActive ? 'border border-gray-500 bg-white font-semibold' : 'font-light'}  rounded-md text-sm  `}>
            {title}
        </button>
    );
}

const SearchBar = () => {
    const dispatch = useDispatch();
    const { search } = useSelector((state) => state.practice);
    const [searchTerm, setSearchTerm] = useState(search);

    useDebouncedEffect(() => {
        dispatch(setSearch(searchTerm));
    }, [searchTerm], 1000);

    return (
        <div className=" flex-1 flex items-center cursor-pointer border border-gray-300 rounded-md px-2 py-[7px] text-gray-600 focus-within:ring-2 focus-within:ring-sky-500">
            <Search size={16} />
            <input
                id="questionId"
                type="text"
                value={searchTerm}
                onChange={(e) => { dispatch(setLoading(true)); setSearchTerm(e.target.value) }}
                placeholder="Tìm kiếm đề thi..."
                className="flex-1 pl-2 text-xs outline-none bg-transparent"
            />
        </div>
    );
};

const ShowTotalResult = ({ total }) => {
    const { loading } = useSelector((state) => state.practice);

    return (
        <div className="text-sm text-gray-700 mt-2">
            🔎 {loading ? (
                <span className="animate-pulse text-sky-500 font-semibold">Đang tìm...</span>
            ) : (
                <>
                    Tìm thấy <span className="font-semibold text-sky-600">{total}</span> kết quả
                </>
            )}
        </div>
    );
};


const PracticePage = () => {
    const { exams, pagination, loading, search, typeOfExam, view, chapter, sort, isClassroomExam, grade, year } = useSelector((state) => state.practice);
    const dispatch = useDispatch();
    const { page, total } = pagination;
    const { user } = useSelector((state) => state.auth)
    const { codes } = useSelector((state) => state.codes);

    const [firstLoad, setFirstLoad] = useState(false);

    useEffect(() => {
        if (user?.class && !firstLoad) {
            dispatch(setGrade(user.class));
            setFirstLoad(true);
        }
    }, [user?.class, dispatch, firstLoad]);

    const [optionChapter, setOptionChapter] = useState([]);
    useEffect(() => {
        if (grade && Array.isArray(codes["chapter"])) {
            setOptionChapter(
                codes["chapter"].filter((code) => code.code.startsWith(grade))
            );
            dispatch(setChapter(''));
        } else {
            setOptionChapter([]);
        }
    }, [codes, grade]);

    useEffect(() => {
        dispatch(fetchCodesByType(['chapter', 'grade', 'exam type', 'year']));
    }, [dispatch]);

    useEffect(() => {
        if (!firstLoad) return;
        dispatch(fetchPublicExams({ search, page, sort, typeOfExam, grade, chapter, view, sort, isClassroomExam, year }));
    }, [dispatch, search, page, sort, typeOfExam, grade, chapter, view, sort, isClassroomExam, year, firstLoad]);

    return (
        <UserLayout>
            <div className="flex md:flex-row flex-col px-3 pb-3">
                <SideBar />
                <div className="flex-1 flex md:pl-4 pt-4 gap-4 flex-col pb-6">
                    <div className="flex-col md:flex-row flex gap-4">
                        <div className="bg-[#E6EAEF] rounded-md w-fit">
                            <NavigateButton title="Tất cả" isActive={view === 'all'} onClick={() => { dispatch(setView('all')) }} />
                            <NavigateButton title="Đã làm" isActive={view === 'done'} onClick={() => { dispatch(setView('done')) }} />
                            <NavigateButton title="Đã lưu" isActive={view === 'saved'} onClick={() => { dispatch(setView('saved')) }} />
                        </div>
                        <SearchBar />
                        <SortBar
                            selected={sort}
                            onChange={(value) => dispatch(setSort(value))}
                            sortOptions={[
                                { label: "Mới nhất", value: "newest" },
                                { label: "Cũ nhất", value: "oldest" },
                                { label: "A-Z", value: "az" },
                                { label: "Z-A", value: "za" },
                            ]}
                        />
                    </div>
                    <div className="flex-col md:flex-row flex gap-4">
                        <FilterBar
                            title="Loại đề"
                            Icon={Tag}
                            ignoreOutsideClick={'filterDropdownRefExamType'}
                            filterOptions={codes?.['exam type'] || []}
                            selected={typeOfExam}
                            onChange={(value) => dispatch(setTypeOfExam(value))}
                        />
                        <FilterBar
                            title={"Năm"}
                            Icon={Calendar}
                            w="80"
                            ignoreOutsideClick={'filterDropdownRefYear'}
                            filterOptions={codes?.['year'] || []}
                            selected={year}
                            onChange={(value) => dispatch(setYear(value))}
                        />
                        <FilterBar
                            title="Chương"
                            Icon={BookOpen}
                            w="80"
                            ignoreOutsideClick={'filterDropdownRefChapter'}
                            filterOptions={optionChapter}
                            selected={chapter}
                            onChange={(value) => dispatch(setChapter(value))}
                        />
                    </div>
                    <div className="flex-1 flex flex-col gap-2">
                        <ShowTotalResult total={total} />
                        <div className="flex flex-col gap-4">
                            <LoadingData
                                loading={loading}
                                isNoData={exams.length > 0 ? false : true}
                                loadText="Đang tải danh sách đề thi"
                                noDataText="Không có đề thi nào."
                                IconNoData={FileText}
                            >
                                {exams.map((exam) => (
                                    <ExamCard key={exam.id} exam={exam} />
                                ))}
                            </LoadingData>
                        </div>
                        <Pagination
                            currentPage={pagination.page}
                            totalItems={pagination.total}
                            limit={pagination.pageSize}
                            onPageChange={(page) => dispatch(setCurrentPage(page))}
                        />
                    </div>
                </div>

            </div>
        </UserLayout>
    );
};

export default PracticePage;
