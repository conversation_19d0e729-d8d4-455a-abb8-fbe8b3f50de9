/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const FunctionResultEntryObject = {
  Entry: "entry",
} as const;
export type FunctionResultEntryObject = ClosedEnum<
  typeof FunctionResultEntryObject
>;

export const FunctionResultEntryType = {
  FunctionResult: "function.result",
} as const;
export type FunctionResultEntryType = ClosedEnum<
  typeof FunctionResultEntryType
>;

export type FunctionResultEntry = {
  object?: FunctionResultEntryObject | undefined;
  type?: FunctionResultEntryType | undefined;
  createdAt?: Date | undefined;
  completedAt?: Date | null | undefined;
  id?: string | undefined;
  toolCallId: string;
  result: string;
};

/** @internal */
export const FunctionResultEntryObject$inboundSchema: z.ZodNativeEnum<
  typeof FunctionResultEntryObject
> = z.nativeEnum(FunctionResultEntryObject);

/** @internal */
export const FunctionResultEntryObject$outboundSchema: z.ZodNativeEnum<
  typeof FunctionResultEntryObject
> = FunctionResultEntryObject$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionResultEntryObject$ {
  /** @deprecated use `FunctionResultEntryObject$inboundSchema` instead. */
  export const inboundSchema = FunctionResultEntryObject$inboundSchema;
  /** @deprecated use `FunctionResultEntryObject$outboundSchema` instead. */
  export const outboundSchema = FunctionResultEntryObject$outboundSchema;
}

/** @internal */
export const FunctionResultEntryType$inboundSchema: z.ZodNativeEnum<
  typeof FunctionResultEntryType
> = z.nativeEnum(FunctionResultEntryType);

/** @internal */
export const FunctionResultEntryType$outboundSchema: z.ZodNativeEnum<
  typeof FunctionResultEntryType
> = FunctionResultEntryType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionResultEntryType$ {
  /** @deprecated use `FunctionResultEntryType$inboundSchema` instead. */
  export const inboundSchema = FunctionResultEntryType$inboundSchema;
  /** @deprecated use `FunctionResultEntryType$outboundSchema` instead. */
  export const outboundSchema = FunctionResultEntryType$outboundSchema;
}

/** @internal */
export const FunctionResultEntry$inboundSchema: z.ZodType<
  FunctionResultEntry,
  z.ZodTypeDef,
  unknown
> = z.object({
  object: FunctionResultEntryObject$inboundSchema.default("entry"),
  type: FunctionResultEntryType$inboundSchema.default("function.result"),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  completed_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  id: z.string().optional(),
  tool_call_id: z.string(),
  result: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "completed_at": "completedAt",
    "tool_call_id": "toolCallId",
  });
});

/** @internal */
export type FunctionResultEntry$Outbound = {
  object: string;
  type: string;
  created_at?: string | undefined;
  completed_at?: string | null | undefined;
  id?: string | undefined;
  tool_call_id: string;
  result: string;
};

/** @internal */
export const FunctionResultEntry$outboundSchema: z.ZodType<
  FunctionResultEntry$Outbound,
  z.ZodTypeDef,
  FunctionResultEntry
> = z.object({
  object: FunctionResultEntryObject$outboundSchema.default("entry"),
  type: FunctionResultEntryType$outboundSchema.default("function.result"),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  id: z.string().optional(),
  toolCallId: z.string(),
  result: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    completedAt: "completed_at",
    toolCallId: "tool_call_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionResultEntry$ {
  /** @deprecated use `FunctionResultEntry$inboundSchema` instead. */
  export const inboundSchema = FunctionResultEntry$inboundSchema;
  /** @deprecated use `FunctionResultEntry$outboundSchema` instead. */
  export const outboundSchema = FunctionResultEntry$outboundSchema;
  /** @deprecated use `FunctionResultEntry$Outbound` instead. */
  export type Outbound = FunctionResultEntry$Outbound;
}

export function functionResultEntryToJSON(
  functionResultEntry: FunctionResultEntry,
): string {
  return JSON.stringify(
    FunctionResultEntry$outboundSchema.parse(functionResultEntry),
  );
}

export function functionResultEntryFromJSON(
  jsonString: string,
): SafeParseResult<FunctionResultEntry, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FunctionResultEntry$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FunctionResultEntry' from JSON`,
  );
}
