'use strict';

export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('question1', 'chapter', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('question1', 'class', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('question1', 'chapter');
    await queryInterface.removeColumn('question1', 'class');
  },
};
