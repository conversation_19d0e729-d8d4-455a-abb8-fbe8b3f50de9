'use strict';

export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('question1', 'solutionImageUrl', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('question1', 'imageUrl', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('question1', 'order', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('question1', 'solutionImageUrl');
    await queryInterface.removeColumn('question1', 'imageUrl');
    await queryInterface.removeColumn('question1', 'order');
  },
};
