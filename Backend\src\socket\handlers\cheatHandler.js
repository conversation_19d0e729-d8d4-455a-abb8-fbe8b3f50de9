import db from '../../models/index.js';
import { EVENTS, ROOMS } from '../constants.js';

// Map to track recent cheat logs to prevent spam
const recentCheatLogs = new Map();

/**
 * Handle user_log event for cheat detection
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleUserLog = async (socket, io, data) => {
  const { action, code, attemptId, examId, name } = data;
  
  if (!attemptId) return;
  
  const now = Date.now();
  // ⚠️ Key duy nhất cho mỗi loại hành động và attempt
  const logKey = `${attemptId}-${code}`;

  // Nếu đã log trong vòng 10 giây, thì bỏ qua
  const lastLogged = recentCheatLogs.get(logKey);
  if (lastLogged && now - lastLogged < 10_000) {
    return;
  }

  // Cập nhật timestamp mới
  recentCheatLogs.set(logKey, now);

  console.log("🛡️ User log:",
    action,
    code,
    attemptId,
    examId,
    new Date().toISOString()
  );

  io.to(`${ROOMS.ADMIN_EXAM}${examId}`).emit(EVENTS.ADMIN_USER_LOG, {
    action,
    code,
    attemptId,
    name,
    timestamp: new Date().toISOString(),
  });

  // Uncomment to enable cheating warnings
  // if (["exit_fullscreen", "tab_blur", "copy_detected", "suspicious_key"].includes(action)) {
  //   socket.emit(EVENTS.CHEATING_WARNING, {
  //     message: `Phát hiện hành vi nghi vấn: ${action}`,
  //   });
  // }

  // Lưu log vào database
  await db.Cheat.create({
    typeOfCheat: code,
    attemptId,
  });
};
