import React from 'react';
import {
    CheckCircle,
    XCircle,
    AlertCircle,
    Clock,
    Calendar,
    Users,
    BookOpen
} from 'lucide-react';

const AttendanceCard = ({ attendance }) => {
    // Get status display
    const getStatusDisplay = (status) => {
        switch (status) {
            case 'present':
                return {
                    icon: <CheckCircle size={16} className="text-green-600" />,
                    text: 'Có mặt',
                    bgColor: 'bg-green-50',
                    textColor: 'text-green-700',
                    borderColor: 'border-green-200'
                };
            case 'absent':
                return {
                    icon: <XCircle size={16} className="text-red-600" />,
                    text: 'Vắng mặt',
                    bgColor: 'bg-red-50',
                    textColor: 'text-red-700',
                    borderColor: 'border-red-200'
                };
            case 'late':
                return {
                    icon: <AlertCircle size={16} className="text-yellow-600" />,
                    text: 'Muộn',
                    bgColor: 'bg-yellow-50',
                    textColor: 'text-yellow-700',
                    borderColor: 'border-yellow-200'
                };
            default:
                return {
                    icon: <XCircle size={16} className="text-gray-600" />,
                    text: 'Không xác định',
                    bgColor: 'bg-gray-50',
                    textColor: 'text-gray-700',
                    borderColor: 'border-gray-200'
                };
        }
    };

    // Format time
    const formatTime = (timeString) => {
        if (!timeString) return '';
        return timeString.substring(0, 5); // HH:MM
    };

    // Format date
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };

    // Format day of week
    const formatDayOfWeek = (dayOfWeek) => {
        const days = {
            'Monday': 'Thứ 2',
            'Tuesday': 'Thứ 3',
            'Wednesday': 'Thứ 4',
            'Thursday': 'Thứ 5',
            'Friday': 'Thứ 6',
            'Saturday': 'Thứ 7',
            'Sunday': 'Chủ nhật'
        };
        return days[dayOfWeek] || dayOfWeek;
    };

    const statusDisplay = getStatusDisplay(attendance.status);

    return (
        <div className={`border rounded-lg p-4 hover:shadow-md transition-all duration-200 ${statusDisplay.borderColor} ${statusDisplay.bgColor}`}>
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
                <div className="flex-1">
                    {/* Lesson Title */}
                    <div className="flex items-center gap-3 mb-3">
                        <BookOpen size={18} className="text-sky-600 flex-shrink-0" />
                        <h4 className="font-semibold text-gray-800 text-lg">
                            {attendance.lesson.name}
                        </h4>
                    </div>

                    {/* Lesson Description */}
                    {attendance.lesson.description && (
                        <p className="text-gray-600 text-sm mb-3 ml-7">
                            {attendance.lesson.description}
                        </p>
                    )}

                    {/* Class and Time Info */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                            <Users size={14} className="text-gray-400" />
                            <span className="font-medium">{attendance.lesson.class.name}</span>
                            {attendance.lesson.class.grade && (
                                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                                    Lớp {attendance.lesson.class.grade}
                                </span>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            <Calendar size={14} className="text-gray-400" />
                            <span>{formatDate(attendance.lesson.day)}</span>
                            {attendance.lesson.class.dayOfWeek1 && (
                                <span className="text-gray-500">
                                    ({formatDayOfWeek(attendance.lesson.class.dayOfWeek1)})
                                </span>
                            )}
                            {attendance.lesson.class.dayOfWeek2 && (
                                <span className="text-gray-500">
                                    ({formatDayOfWeek(attendance.lesson.class.dayOfWeek2)})
                                </span>
                            )}
                        </div>

                        {attendance.lesson.class.startTime1 && (
                            <div className="flex items-center gap-2">
                                <Clock size={14} className="text-gray-400" />
                                <span>
                                    {formatTime(attendance.lesson.class.startTime1)} - {formatTime(attendance.lesson.class.endTime1)}
                                </span>
                            </div>
                        )}

                        {attendance.lesson.class.startTime2 && (
                            <div className="flex items-center gap-2">
                                <Clock size={14} className="text-gray-400" />
                                <span>
                                    {formatTime(attendance.lesson.class.startTime2)} - {formatTime(attendance.lesson.class.endTime2)}
                                </span>
                            </div>
                        )}

                        {attendance.attendanceTime && (
                            <div className="flex items-center gap-2">
                                <Clock size={14} className="text-gray-400" />
                                <span className="text-xs">
                                    Điểm danh: {new Date(attendance.attendanceTime).toLocaleString('vi-VN', {
                                        weekday: 'long',      // Thứ Hai, Thứ Ba,...
                                        day: '2-digit',       // 27
                                        month: '2-digit',     // 05
                                        year: 'numeric',      // 2025
                                        hour: '2-digit',      // 09
                                        minute: '2-digit',    // 30
                                        hour12: false         // Định dạng 24h
                                    })}
                                </span>
                            </div>
                        )}

                    </div>

                    {/* Note */}
                    {attendance.note && (
                        <div className="mt-3 p-2 bg-gray-50 rounded-md">
                            <p className="text-sm text-gray-600 italic">
                                <span className="font-medium">Ghi chú:</span> {attendance.note}
                            </p>
                        </div>
                    )}
                </div>

                {/* Status Badge */}
                <div className="flex-shrink-0">
                    <div className={`px-4 py-2 rounded-full ${statusDisplay.bgColor} ${statusDisplay.textColor} border ${statusDisplay.borderColor} flex items-center gap-2 font-medium`}>
                        {statusDisplay.icon}
                        <span>{statusDisplay.text}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AttendanceCard;
