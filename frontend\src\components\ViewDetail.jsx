import YouTubePlayer from "./YouTubePlayer";
import { useDispatch, useSelector } from "react-redux";
import { fetchExamById, setExam } from "../features/exam/examSlice";
import { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "./loading/LoadingSpinner";
import UploadPdfForm from "./UploadPdf";
import PdfViewer from "./ViewPdf";
import { putLearningItem, putLesson, uploadLearningItemPdf } from "../features/class/classSlice";
import SuggestInputBarAdmin from "./input/suggestInputBarAdmin";
import { fetchPdfs } from "../features/image/imageSlice";
import { BookOpen, Video, FileText, Target, Calendar, Search, Save, Trash2, Clock, TrendingUp } from 'lucide-react';
import ExamSearchInput from "./ExamSearchInput";

const ViewDetail = ({ activeItem, deleteLesson, deleteLearningItem }) => {
    const { exam } = useSelector((state) => state.exams);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [itemData, setItemData] = useState(activeItem?.item || {});
    const [loadingExam, setLoadingExam] = useState(false);
    const [loadingPdfs, setLoadingPdfs] = useState(false);
    const [activeTab, setActiveTab] = useState('existing'); // 'existing' or 'upload'
    const { codes } = useSelector((state) => state.codes);
    const { loadingPut } = useSelector((state) => state.classes);
    const { pdfs } = useSelector((state) => state.images);

    const findExam = async (examCode) => {
        if (examCode && examCode.trim()) {
            setLoadingExam(true);
            await dispatch(fetchExamById(examCode.trim()));
            setLoadingExam(false);
        } else {
            // Clear exam if no code
            dispatch(setExam(null));
        }
    };

    // Debounced search function
    const debouncedFindExam = useCallback(
        (() => {
            let timeoutId;
            return (examCode) => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    findExam(examCode);
                }, 500); // 500ms delay
            };
        })(),
        [dispatch]
    );

    const fetchPdfFiles = async () => {
        setLoadingPdfs(true);
        // console.log('Fetching PDFs...');
        try {
            await dispatch(fetchPdfs('learningItemsPdf'));
        } catch (error) {
            console.error('Error fetching PDFs:', error);
        } finally {
            setLoadingPdfs(false);
        }
    };

    const handleUpload = ({ id, pdfFile }) => {
        dispatch(uploadLearningItemPdf({ learningItemId: id, pdfFile }))
            .unwrap()
            .then(() => fetchPdfFiles())
    }

    const handleDeleteItem = () => {
        if (activeItem?.type === "learningItem") {
            deleteLearningItem(itemData.id);
        } else if (activeItem?.type === "lesson") {
            deleteLesson(itemData.id)
        }
    };


    const handleSaveItem = () => {
        if (activeItem?.type === "learningItem") {
            let data;
            if (itemData.typeOfLearningItem === "BTVN") {
                data = {
                    deadline: itemData.deadline,
                    name: itemData.name,
                    url: itemData.url,
                    description: itemData.description,
                }
            } else if (itemData.typeOfLearningItem === "VID") {
                data = {
                    name: itemData.name,
                    url: itemData.url,
                    description: itemData.description,
                }
            } else if (itemData.typeOfLearningItem === "DOC") {
                data = {
                    name: itemData.name,
                    url: itemData.url,
                    description: itemData.description,
                }
            }
            dispatch(putLearningItem({ learningItemId: itemData.id, data: itemData }))
                .unwrap()
                .then(() => {
                    if (itemData.typeOfLearningItem === "DOC") {
                        fetchPdfFiles();
                    }
                })
                .catch((error) => console.error('Error saving learning item:', error));

        } else if (activeItem?.type === "lesson") {
            const data = {
                day: itemData.day,
                description: itemData.description ? itemData.description : null,
                name: itemData.name,
                chapter: itemData.chapter ? itemData.chapter : null,
            }
            dispatch(putLesson({ lessonId: itemData.id, data }));
        }
    };

    useEffect(() => {
        if (
            activeItem?.type === "learningItem" &&
            activeItem?.item?.typeOfLearningItem === "BTVN" &&
            activeItem?.item?.url
        ) {
            dispatch(fetchExamById(activeItem.item.url));
        }
        if (activeItem?.item) {
            const item = activeItem.item;
            setItemData({
                ...item,
                chapter: item.chapter ?? null
            });
        }
    }, [dispatch, activeItem]);

    const handleChange = (key, value) => {
        setItemData(prev => ({ ...prev, [key]: value }));

        // Auto search for exam when url (exam code) changes for BTVN type
        if (key === "url" && itemData.typeOfLearningItem === "BTVN") {
            debouncedFindExam(value);
        }
    };

    if (!activeItem) {
        return (
            <div className="flex-1 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                <div className="text-center">
                    <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Chọn một mục để xem chi tiết</h3>
                    <p className="text-gray-500">Nhấn vào buổi học hoặc mục học tập để xem và chỉnh sửa thông tin</p>
                </div>
            </div>
        );
    }

    const getTypeIcon = (type) => {
        switch (type) {
            case 'BTVN': return <Target size={20} className="text-blue-600" />;
            case 'VID': return <Video size={20} className="text-green-600" />;
            case 'DOC': return <FileText size={20} className="text-orange-600" />;
            default: return <BookOpen size={20} className="text-gray-600" />;
        }
    };

    const getTypeColor = (type) => {
        switch (type) {
            case 'BTVN': return 'bg-blue-50 border-blue-200';
            case 'VID': return 'bg-green-50 border-green-200';
            case 'DOC': return 'bg-orange-50 border-orange-200';
            default: return 'bg-gray-50 border-gray-200';
        }
    };

    return (
        <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col overflow-hidden">
            {/* Header */}
            <div className="flex-shrink-0 p-6 border-b border-gray-200">
                <div className="flex items-center gap-3 mb-4">
                    {activeItem?.type === "learningItem" ? (
                        <>
                            {getTypeIcon(itemData.typeOfLearningItem)}
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Chi tiết mục học tập</h2>
                                <p className="text-sm text-gray-500">
                                    {itemData.typeOfLearningItem === 'BTVN' && 'Bài tập về nhà'}
                                    {itemData.typeOfLearningItem === 'VID' && 'Video học tập'}
                                    {itemData.typeOfLearningItem === 'DOC' && 'Tài liệu học tập'}
                                </p>
                            </div>
                        </>
                    ) : (
                        <>
                            <Calendar size={20} className="text-sky-600" />
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Chi tiết buổi học</h2>
                                <p className="text-sm text-gray-500">Thông tin và cài đặt buổi học</p>
                            </div>
                        </>
                    )}
                </div>

                {/* Title Input */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        {activeItem?.type === "learningItem" ? "Tên mục học tập" : "Tên buổi học"}
                    </label>
                    <input
                        className="w-full px-3 py-2 text-lg font-semibold border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                        value={itemData.name || ''}
                        onChange={(e) => handleChange("name", e.target.value)}
                        placeholder={activeItem?.type === "learningItem" ? "Nhập tên mục học tập" : "Nhập tên buổi học"}
                    />
                </div>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
                {activeItem?.type === "learningItem" && (
                    <div className="space-y-6">
                        {/* Basic Info */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Ngày tạo</label>
                                <input
                                    type="date"
                                    readOnly
                                    className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-600"
                                    value={itemData.createdAt?.slice(0, 10) || ''}
                                />
                            </div>

                            {itemData?.typeOfLearningItem === "BTVN" && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Hạn nộp</label>
                                    <input
                                        type="date"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        value={itemData.deadline?.slice(0, 10) || ''}
                                        onChange={(e) => handleChange("deadline", e.target.value)}
                                    />
                                </div>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Mô tả
                            </label>
                            <textarea
                                name="description"
                                value={itemData.description || ''}
                                onChange={(e) => handleChange("description", e.target.value)}
                                rows={4}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                                placeholder="Nhập mô tả cho mục học tập này..."
                            />
                            <p className="text-xs text-gray-500 mt-1">
                                {(itemData.description || '').length}/500 ký tự
                            </p>
                        </div>

                        {/* Type-specific Content */}
                        {itemData?.typeOfLearningItem === "VID" && (
                            <div className={`p-4 rounded-lg border ${getTypeColor('VID')}`}>
                                <h4 className="font-medium text-green-900 flex items-center gap-2 mb-4">
                                    <Video size={16} />
                                    Video học tập
                                </h4>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Link YouTube</label>
                                        <input
                                            type="url"
                                            value={itemData.url || ""}
                                            onChange={(e) => handleChange("url", e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                            placeholder="https://www.youtube.com/watch?v=..."
                                        />
                                    </div>
                                    {itemData.url && (
                                        <div className="mt-4">
                                            <YouTubePlayer url={itemData.url} />
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {itemData?.typeOfLearningItem === "DOC" && (
                            <div className={`p-4 rounded-lg border ${getTypeColor('DOC')}`}>
                                <h4 className="font-medium text-orange-900 flex items-center gap-2 mb-4">
                                    <FileText size={16} />
                                    Tài liệu học tập
                                </h4>
                                <div className="space-y-4">
                                    {/* URL Input Field */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">URL tài liệu</label>
                                        <input
                                            type="url"
                                            value={itemData.url || ""}
                                            onChange={(e) => handleChange("url", e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                            placeholder="Nhập URL tài liệu hoặc chọn từ tab bên dưới"
                                        />
                                    </div>

                                    {/* Tab Navigation */}
                                    <div className="border-b border-gray-200">
                                        <nav className="-mb-px flex space-x-8">
                                            <button
                                                onClick={() => setActiveTab('existing')}
                                                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'existing'
                                                    ? 'border-orange-500 text-orange-600'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                                    }`}
                                            >
                                                Chọn PDF có sẵn
                                            </button>
                                            <button
                                                onClick={() => setActiveTab('upload')}
                                                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'upload'
                                                    ? 'border-orange-500 text-orange-600'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                                    }`}
                                            >
                                                Thêm PDF mới
                                            </button>
                                        </nav>
                                    </div>

                                    {/* Tab Content */}
                                    {activeTab === 'existing' ? (
                                        // PDF Suggestions
                                        loadingPdfs ? (
                                            <div className="flex justify-center py-4">
                                                <LoadingSpinner size="1.5rem" />
                                            </div>
                                        ) : pdfs && pdfs.length > 0 ? (
                                            <div className="space-y-2 max-h-60 overflow-y-auto">
                                                <p className="text-sm text-gray-600 mb-3">
                                                    Chọn một file PDF từ danh sách bên dưới:
                                                </p>
                                                {pdfs.map((pdfUrl, index) => {
                                                    const fileName = pdfUrl.split('/').pop().split('?')[0];
                                                    const displayName = decodeURIComponent(fileName.split('-').slice(1).join('-')) || fileName;
                                                    const isSelected = itemData.url === pdfUrl;

                                                    return (
                                                        <div
                                                            key={index}
                                                            onClick={() => handleChange("url", pdfUrl)}
                                                            className={`p-3 border rounded-lg cursor-pointer transition-colors ${isSelected
                                                                ? 'border-orange-500 bg-orange-100 text-orange-900'
                                                                : 'border-gray-300 bg-white hover:border-orange-300 hover:bg-orange-50'
                                                                }`}
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                <FileText size={16} className={isSelected ? 'text-orange-600' : 'text-gray-500'} />
                                                                <span className="text-sm font-medium truncate">{displayName}</span>
                                                                {isSelected && (
                                                                    <div className="ml-auto">
                                                                        <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        ) : (
                                            <div className="text-center py-4 text-gray-500">
                                                <FileText size={24} className="mx-auto mb-2 text-gray-300" />
                                                <p className="text-sm">Không có file PDF nào trong thư mục</p>
                                            </div>
                                        )
                                    ) : (
                                        // Upload Form
                                        <UploadPdfForm id={itemData.id} onSubmit={handleUpload} loading={loadingPut} />
                                    )}

                                    {/* PDF Viewer */}
                                    {itemData.url && <PdfViewer url={itemData.url} height={'800px'} />}
                                </div>
                            </div>
                        )}

                        {itemData?.typeOfLearningItem === "BTVN" && (
                            <div className={`p-4 rounded-lg border ${getTypeColor('BTVN')}`}>
                                <h4 className="font-medium text-blue-900 flex items-center gap-2 mb-4">
                                    <Target size={16} />
                                    Bài tập về nhà
                                </h4>
                                <div className="space-y-4">
                                    <div className="flex items-center gap-3">
                                        <label className="text-sm font-medium text-gray-700 w-20">Mã đề:</label>
                                        <ExamSearchInput
                                            value={itemData.url}
                                            selectedExamId={itemData.url}
                                            onChange={(value) => setItemData({ ...itemData, url: value })}
                                            onSelect={(examItem) => setItemData({ ...itemData, url: examItem.id })}
                                            onClear={() => setItemData({ ...itemData, url: '' })}
                                            className="flex-1"
                                            setExam={(exam) => { dispatch(setExam(exam)) }}
                                            clearExam={() => dispatch(setExam(null))}
                                        />
                                    </div>

                                    {(itemData.url && exam && exam != {}) ? (
                                        <div className="space-y-3 bg-white p-3 rounded border">
                                            <div className="flex justify-between">
                                                <span className="text-sm font-medium text-gray-600">Tên đề:</span>
                                                <span className="text-sm text-gray-900">{exam?.name}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm font-medium text-gray-600">Khối:</span>
                                                <span className="text-sm text-gray-900">{exam?.class}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm font-medium text-gray-600">Năm:</span>
                                                <span className="text-sm text-gray-900">{exam?.year}</span>
                                            </div>
                                        </div>
                                    ) : (
                                        <p className="text-sm text-red-600">Chưa chọn đề thi</p>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {activeItem?.type === "lesson" && (
                    <div className="space-y-6">
                        {/* Basic Info */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Ngày tạo</label>
                                <input
                                    type="date"
                                    readOnly
                                    className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-600"
                                    value={itemData.createdAt?.slice(0, 10) || ''}
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Ngày học</label>
                                <input
                                    type="date"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                    value={itemData.day?.slice(0, 10) || ''}
                                    onChange={(e) => handleChange("day", e.target.value)}
                                />
                            </div>
                        </div>

                        {/* Chapter Selection */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Chương học
                                <span className="text-gray-400 text-sm ml-2">
                                    {itemData.chapter ? `(${itemData.chapter})` : "(Không có)"}
                                </span>
                            </label>
                            <SuggestInputBarAdmin
                                key={itemData.id}
                                options={codes['chapter'] ? codes['chapter'].filter((c) => c.code.length === 4) : []}
                                selectedOption={itemData.chapter ?? null}
                                onChange={(value) => handleChange("chapter", value)}
                                placeholder="Chọn chương học"
                            />
                        </div>

                        {/* Description */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Mô tả buổi học</label>
                            <textarea
                                value={itemData.description || ''}
                                onChange={(e) => handleChange("description", e.target.value)}
                                rows={4}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 resize-none"
                                placeholder="Nhập mô tả cho buổi học này..."
                            />
                            <p className="text-xs text-gray-500 mt-1">
                                {(itemData.description || '').length}/500 ký tự
                            </p>
                        </div>

                        {/* Statistics */}
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <h4 className="font-medium text-gray-900 mb-3">Thống kê buổi học</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">Số mục học tập:</span>
                                    <span className="font-medium text-gray-900">{itemData?.learningItems?.length || 0}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">Trạng thái:</span>
                                    <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                        Đã tạo
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Footer - Fixed */}
            <div className="flex-shrink-0 p-6 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-end gap-3">
                    <button
                        onClick={handleDeleteItem}
                        disabled={loadingPut}
                        className="px-4 py-2 text-red-700 bg-red-50 border border-red-200 hover:bg-red-100 rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                    >
                        {loadingPut ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                                Đang xóa...
                            </>
                        ) : (
                            <>
                                <Trash2 size={16} />
                                Xóa
                            </>
                        )}
                    </button>
                    <button
                        onClick={handleSaveItem}
                        disabled={loadingPut}
                        className="px-6 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                    >
                        {loadingPut ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                Đang lưu...
                            </>
                        ) : (
                            <>
                                <Save size={16} />
                                Lưu thay đổi
                            </>
                        )}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ViewDetail;
