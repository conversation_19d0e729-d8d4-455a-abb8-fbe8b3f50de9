import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Tạo kết nối database
const sequelize = new Sequelize(
    process.env.DB_DEV_DATABASE,
    process.env.DB_DEV_USERNAME,
    process.env.DB_DEV_PASSWORD,
    {
        host: process.env.DB_DEV_HOST,
        port: process.env.DB_DEV_PORT,
        dialect: process.env.DB_DEV_DIALECT,
        logging: false // Tắt log SQL queries
    }
);

async function checkUserTable() {
    try {
        // Test kết nối
        await sequelize.authenticate();
        console.log('✅ Kết nối database thành công!');
        console.log('='.repeat(60));

        // 1. Lấy thông tin cấu trúc bảng user
        console.log('📋 THÔNG TIN CẤU TRÚC BẢNG USER:');
        console.log('='.repeat(60));

        const tableInfo = await sequelize.getQueryInterface().describeTable('user');

        // In thông tin tất cả các cột
        Object.keys(tableInfo).forEach(column => {
            const info = tableInfo[column];
            console.log(`📌 Cột: ${column}`);
            console.log(`   - Type: ${info.type}`);
            console.log(`   - AllowNull: ${info.allowNull}`);
            console.log(`   - DefaultValue: ${info.defaultValue}`);
            console.log(`   - PrimaryKey: ${info.primaryKey || false}`);
            console.log(`   - AutoIncrement: ${info.autoIncrement || false}`);
            console.log(`   - Unique: ${info.unique || false}`);
            console.log('   ' + '-'.repeat(40));
        });

        // 2. Chi tiết đặc biệt về cột phone
        console.log('\n📱 CHI TIẾT CỘT PHONE:');
        console.log('='.repeat(60));

        const phoneInfo = tableInfo.phone;
        if (phoneInfo) {
            console.log('📱 Thông tin cột phone:');
            console.log(`   - Kiểu dữ liệu: ${phoneInfo.type}`);
            console.log(`   - Cho phép NULL: ${phoneInfo.allowNull}`);
            console.log(`   - Giá trị mặc định: ${phoneInfo.defaultValue || 'Không có'}`);
            console.log(`   - Là Primary Key: ${phoneInfo.primaryKey || false}`);
            console.log(`   - Auto Increment: ${phoneInfo.autoIncrement || false}`);
            console.log(`   - Unique Constraint: ${phoneInfo.unique || false}`);
        } else {
            console.log('❌ Không tìm thấy cột phone trong bảng user');
        }

        // 3. Lấy thông tin về indexes
        console.log('\n🔍 THÔNG TIN INDEXES:');
        console.log('='.repeat(60));

        const indexes = await sequelize.getQueryInterface().showIndex('user');

        if (indexes && indexes.length > 0) {
            indexes.forEach((index, i) => {
                console.log(`📊 Index ${i + 1}:`);
                console.log(`   - Tên: ${index.name}`);
                console.log(`   - Unique: ${index.unique}`);
                console.log(`   - Primary: ${index.primary || false}`);
                console.log(`   - Các cột: ${index.fields.map(f => f.attribute).join(', ')}`);
                console.log('   ' + '-'.repeat(40));
            });
        } else {
            console.log('❌ Không tìm thấy index nào');
        }

        // 4. Kiểm tra constraints
        console.log('\n🔒 THÔNG TIN CONSTRAINTS:');
        console.log('='.repeat(60));

        try {
            // Query để lấy thông tin constraints từ information_schema
            const [constraints] = await sequelize.query(`
                SELECT
                    CONSTRAINT_NAME,
                    CONSTRAINT_TYPE,
                    COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = '${process.env.DB_DEV_DATABASE}'
                AND TABLE_NAME = 'user'
                AND COLUMN_NAME = 'phone'
            `);

            if (constraints && constraints.length > 0) {
                constraints.forEach((constraint, i) => {
                    console.log(`🔒 Constraint ${i + 1}:`);
                    console.log(`   - Tên: ${constraint.CONSTRAINT_NAME}`);
                    console.log(`   - Loại: ${constraint.CONSTRAINT_TYPE}`);
                    console.log(`   - Cột: ${constraint.COLUMN_NAME}`);
                    console.log('   ' + '-'.repeat(40));
                });
            } else {
                console.log('✅ Không có constraint nào cho cột phone');
            }
        } catch (error) {
            console.log('❌ Lỗi khi kiểm tra constraints:', error.message);
        }

        // 5. Kiểm tra dữ liệu mẫu
        console.log('\n📊 DỮ LIỆU MẪU (5 bản ghi đầu tiên):');
        console.log('='.repeat(60));

        const [sampleData] = await sequelize.query(`
            SELECT id, firstName, lastName, phone, username
            FROM user
            LIMIT 5
        `);

        if (sampleData && sampleData.length > 0) {
            sampleData.forEach((user, i) => {
                console.log(`👤 User ${i + 1}:`);
                console.log(`   - ID: ${user.id}`);
                console.log(`   - Tên: ${user.firstName} ${user.lastName}`);
                console.log(`   - Username: ${user.username}`);
                console.log(`   - Phone: ${user.phone}`);
                console.log('   ' + '-'.repeat(40));
            });
        } else {
            console.log('❌ Không có dữ liệu trong bảng user');
        }

        // 6. Kiểm tra phone trùng lặp
        console.log('\n🔍 KIỂM TRA PHONE TRÙNG LẶP:');
        console.log('='.repeat(60));

        const [duplicatePhones] = await sequelize.query(`
            SELECT phone, COUNT(*) as count
            FROM user
            WHERE phone IS NOT NULL
            GROUP BY phone
            HAVING COUNT(*) > 1
        `);

        if (duplicatePhones && duplicatePhones.length > 0) {
            console.log('⚠️  Tìm thấy số điện thoại trùng lặp:');
            duplicatePhones.forEach((item, i) => {
                console.log(`   ${i + 1}. Phone: ${item.phone} - Số lượng: ${item.count}`);
            });
        } else {
            console.log('✅ Không có số điện thoại trùng lặp');
        }

        console.log('\n' + '='.repeat(60));
        console.log('✅ Hoàn thành kiểm tra bảng user!');

    } catch (error) {
        console.error('❌ Lỗi:', error.message);
    } finally {
        await sequelize.close();
    }
}

// Chạy script
checkUserTable();
