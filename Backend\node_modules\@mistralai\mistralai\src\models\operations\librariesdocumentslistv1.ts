/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsListV1Request = {
  libraryId: string;
  search?: string | null | undefined;
  pageSize?: number | undefined;
  page?: number | undefined;
  sortBy?: string | undefined;
  sortOrder?: string | undefined;
};

/** @internal */
export const LibrariesDocumentsListV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsListV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  search: z.nullable(z.string()).optional(),
  page_size: z.number().int().default(100),
  page: z.number().int().default(0),
  sort_by: z.string().default("created_at"),
  sort_order: z.string().default("desc"),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "page_size": "pageSize",
    "sort_by": "sortBy",
    "sort_order": "sortOrder",
  });
});

/** @internal */
export type LibrariesDocumentsListV1Request$Outbound = {
  library_id: string;
  search?: string | null | undefined;
  page_size: number;
  page: number;
  sort_by: string;
  sort_order: string;
};

/** @internal */
export const LibrariesDocumentsListV1Request$outboundSchema: z.ZodType<
  LibrariesDocumentsListV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsListV1Request
> = z.object({
  libraryId: z.string(),
  search: z.nullable(z.string()).optional(),
  pageSize: z.number().int().default(100),
  page: z.number().int().default(0),
  sortBy: z.string().default("created_at"),
  sortOrder: z.string().default("desc"),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    pageSize: "page_size",
    sortBy: "sort_by",
    sortOrder: "sort_order",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsListV1Request$ {
  /** @deprecated use `LibrariesDocumentsListV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesDocumentsListV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsListV1Request$outboundSchema` instead. */
  export const outboundSchema = LibrariesDocumentsListV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsListV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsListV1Request$Outbound;
}

export function librariesDocumentsListV1RequestToJSON(
  librariesDocumentsListV1Request: LibrariesDocumentsListV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsListV1Request$outboundSchema.parse(
      librariesDocumentsListV1Request,
    ),
  );
}

export function librariesDocumentsListV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesDocumentsListV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesDocumentsListV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesDocumentsListV1Request' from JSON`,
  );
}
