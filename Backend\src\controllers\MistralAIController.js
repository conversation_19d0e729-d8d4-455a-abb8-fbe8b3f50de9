import { performOcr } from '../services/mistralAI.service.js';
import { encodePdfToBase64 } from '../services/pdf.service.js';
import * as gptService from '../services/gpt.service.js';
import { encodeImage } from '../services/image.service.js';

export const ocrPdfWithMistral = async (req, res) => {
    try {
        const file = req.file;
        if (!file) return res.status(400).json({ message: 'Chưa chọn file PDF.' });

        const base64 = encodePdfToBase64(req.file.buffer);
        if (!base64) return res.status(500).json({ message: 'Không thể đọc file.' });

        const ocrResponse = await performOcr(base64, 'pdf', false);

        return res.status(200).json({
            message: 'OCR thành công',
            result: ocrResponse,
        });
    } catch (err) {
        console.error('OCR lỗi:', err.message);
        return res.status(500).json({ message: '<PERSON><PERSON> thất bại', error: err.message });
    }
};

export const handleExamPdfWithAI = async (req, res) => {
    const file = req.file;
    if (!file) return res.status(400).json({ message: 'Chưa chọn file PDF.' });

    const base64 = encodePdfToBase64(req.file.buffer);
    if (!base64) return res.status(500).json({ message: 'Không thể đọc file.' });

    const result = await performOcr(base64, 'pdf', false);

    const mergedMarkdown = result.pages
        .map(page => page.markdown)
        .filter(Boolean)
        .join('\n\n---\n\n');

    const base64Images = result.pages
        .map(page => page.images.map(image => image.imageBase64))
        .flat();

    return res.status(200).json({
        message: 'Xử lý đề thi thành công',
        markdown: mergedMarkdown,
        base64Images,
    });
};

import mime from 'mime-types'; // npm install mime-types nếu chưa cài

export const ocrImageWithMistral = async (req, res) => {
    try {
        const file = req.file;
        if (!file) return res.status(400).json({ message: 'Chưa chọn file ảnh.' });

        const { base64, type } = await encodeImage(file);
        if (!base64) return res.status(500).json({ message: 'Không thể đọc file.' });

        const ocrResponse = await performOcr(base64, 'jpeg', true);

        return res.status(200).json({
            message: 'OCR thành công',
            markdown: ocrResponse.pages.map(page => page.markdown).filter(Boolean).join('\n\n---\n\n'),
            base64Images: ocrResponse.pages.map(page => page.images.map(image => image.imageBase64)).flat(),
        });

    } catch (err) {
        console.error('OCR lỗi:', err.message);
        return res.status(500).json({ message: 'OCR thất bại', error: err.message });
    }
};
