import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  fetchUserTuitionPayments,
  fetchUserTuitionSummary,

} from "src/features/tuition/tuitionSlice";
import { resetFilters } from "src/features/filter/filterSlice";
import { setCurrentPage } from "src/features/tuition/tuitionSlice";
import { formatCurrency } from "src/utils/formatters";
import Pagination from "src/components/Pagination";
import UserLayout from "src/layouts/UserLayout";
import PaymentModal from "src/components/PaymentModal";
import {
  CreditCard,
  Eye,
  FileText,
  Search,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Loader
} from "lucide-react";

const UserTuitionPayments = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { tuitionPayments, userTuitionSummary, loading } = useSelector((state) => state.tuition);
  const { page: currentPage, totalPages, total: totalItems, pageSize: limit } = useSelector(
    (state) => state.tuition.pagination
  );

  const [isOverdue, setIsOverDue] = useState(false);
  const [isPaid, setIsPaid] = useState(null);

  // Lọc học phí theo tab đang chọn
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'pending', 'paid', 'overdue'

  // State cho modal thanh toán
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  useEffect(() => {
    dispatch(
      fetchUserTuitionPayments({
        page: currentPage,
        limit,
        sortOrder: "DESC",
        overdue: isOverdue,
        isPaid,
      })
    );
  }, [dispatch, currentPage, limit, isOverdue, isPaid]);

  useEffect(() => {
    dispatch(fetchUserTuitionSummary());
  }, [dispatch]);

  const handleView = (id) => {
    navigate(`/tuition-payment/${id}`);
  };

  const handleOpenPaymentModal = (payment) => {
    setSelectedPayment({
      id: payment.id,
      month: payment.monthFormatted,
      amount: "Liên hệ anh Triệu Minh để biết số tiền", // Không còn expectedAmount/paidAmount
      note: payment.note,
      description: `${user.firstName} ${user.lastName} ${user.highSchool} HP_${payment.monthFormatted.replace(' ', '_')}_${payment.id}`
    });
    setIsPaymentModalOpen(true);
  };

  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedPayment(null);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "PAID":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
            Đã thanh toán
          </span>
        );
      case "UNPAID":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
            Chưa thanh toán
          </span>
        );
      case "OVERDUE":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
            Quá hạn
          </span>
        );
      case "PARTIAL":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
            Thanh toán một phần
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };



  // Hàm lấy biểu tượng theo trạng thái học phí
  const getPaymentIcon = (status) => {
    switch (status) {
      case 'PAID':
        return (
          <div className="p-3 bg-green-100 rounded-full">
            <CheckCircle className="w-6 h-6 text-green-600" />
          </div>
        );
      case 'UNPAID':
        return (
          <div className="p-3 bg-yellow-100 rounded-full">
            <CreditCard className="w-6 h-6 text-yellow-600" />
          </div>
        );
      case 'OVERDUE':
        return (
          <div className="p-3 bg-red-100 rounded-full">
            <AlertCircle className="w-6 h-6 text-red-600" />
          </div>
        );
      case 'PARTIAL':
        return (
          <div className="p-3 bg-blue-100 rounded-full">
            <DollarSign className="w-6 h-6 text-blue-600" />
          </div>
        );
      default:
        return (
          <div className="p-3 bg-gray-100 rounded-full">
            <CreditCard className="w-6 h-6 text-gray-600" />
          </div>
        );
    }
  };

  return (
    <UserLayout>
      <div className="container mx-auto px-4 py-4 sm:py-8 max-w-4xl">
        {/* Header Section - Responsive */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800 flex items-center gap-2">
            <CreditCard className="text-sky-600 w-5 h-5 sm:w-6 sm:h-6" />
            Học phí của tôi
          </h1>

          {/* Thống kê tổng quan - Responsive Grid */}
          <div className="grid grid-cols-3 sm:flex gap-2 sm:gap-4">
            <div className="text-xs sm:text-sm text-center sm:text-left">
              <div className="text-gray-500">Tổng khoản:</div>
              <div className="font-semibold text-gray-800">{userTuitionSummary?.totalPayments || 0}</div>
            </div>
            <div className="text-xs sm:text-sm text-center sm:text-left">
              <div className="text-gray-500">Đã thanh toán:</div>
              <div className="font-semibold text-green-600">{userTuitionSummary?.paidPayments || 0}</div>
            </div>
            <div className="text-xs sm:text-sm text-center sm:text-left">
              <div className="text-gray-500">Chưa thanh toán:</div>
              <div className="font-semibold text-red-600">{userTuitionSummary?.unpaidPayments || 0}</div>
            </div>
          </div>
        </div>

        {/* Tabs - Responsive Horizontal Scroll */}
        <div className="border-b border-gray-200 mb-6 overflow-x-auto">
          <div className="flex min-w-max sm:min-w-0">
            <button
              onClick={() => {
                setActiveTab('all');
                setIsPaid(null);
                setIsOverDue(false);
                dispatch(setCurrentPage(1));
              }}
              className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'all'
                ? 'text-sky-600 border-b-2 border-sky-600'
                : 'text-gray-500 hover:text-gray-700'
                }`}
            >
              Tất cả ({userTuitionSummary?.totalPayments || 0})
            </button>
            <button
              onClick={() => {
                setActiveTab('pending');
                setIsPaid(false);
                setIsOverDue(false);
                dispatch(setCurrentPage(1));
              }}
              className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'pending'
                ? 'text-sky-600 border-b-2 border-sky-600'
                : 'text-gray-500 hover:text-gray-700'
                }`}
            >
              Chưa thanh toán ({userTuitionSummary?.unpaidPayments || 0})
            </button>
            <button
              onClick={() => {
                setActiveTab('paid');
                setIsPaid(true);
                setIsOverDue(false);
                dispatch(setCurrentPage(1));
              }}
              className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'paid'
                ? 'text-sky-600 border-b-2 border-sky-600'
                : 'text-gray-500 hover:text-gray-700'
                }`}
            >
              Đã thanh toán ({userTuitionSummary?.paidPayments || 0})
            </button>
            <button
              onClick={() => {
                setActiveTab('overdue');
                setIsPaid(null);
                setIsOverDue(true);
                dispatch(setCurrentPage(1));
              }}
              className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'overdue'
                ? 'text-sky-600 border-b-2 border-sky-600'
                : 'text-gray-500 hover:text-gray-700'
                }`}
            >
              Quá hạn ({userTuitionSummary?.overduePayments || 0})
            </button>
          </div>
        </div>

        {/* Danh sách học phí */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {loading ? (
            <div className="p-8 text-center text-gray-500">
              <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
              <p>Đang tải thông tin học phí...</p>
            </div>
          ) : tuitionPayments.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <CreditCard size={40} className="mx-auto mb-4 text-gray-300" />
              <p>Không tìm thấy khoản học phí nào.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {tuitionPayments.map((payment) => (
                <div
                  key={payment.id}
                  className={`p-4 transition-colors ${payment.isOverdue ? 'bg-red-50' : !payment.isPaid ? 'bg-yellow-50' : ''}`}
                >
                  <div className="flex gap-4">
                    {getPaymentIcon(payment.isPaid ? 'PAID' : payment.isOverdue ? 'OVERDUE' : 'UNPAID')}
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h4 className="text-base font-semibold text-gray-800">
                          Học phí {payment.monthFormatted}
                        </h4>
                        <span className="text-sm text-gray-500">
                          Hạn: {new Date(payment.dueDate).toLocaleDateString("vi-VN")}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <p className="text-xs text-gray-500">Ngày thanh toán</p>
                          <p className="text-sm font-medium">
                            {payment.paymentDate
                              ? new Date(payment.paymentDate).toLocaleDateString("vi-VN")
                              : "Chưa thanh toán"
                            }
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Ghi chú</p>
                          <p className="text-sm font-medium text-gray-600">
                            {payment.note || "Không có ghi chú"}
                          </p>
                        </div>
                      </div>

                      <div className="mt-2">
                        <p className="text-xs text-gray-500">Trạng thái</p>
                        <div className="mt-1">{getStatusBadge(payment.isPaid ? 'PAID' : payment.isOverdue ? 'OVERDUE' : 'UNPAID')}</div>
                      </div>

                      <div className="flex justify-end items-center mt-3">
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleView(payment.id)}
                            className="flex items-center gap-1 text-sm text-sky-600 hover:text-sky-700 px-3 py-1.5 bg-sky-50 hover:bg-sky-100 rounded-md transition-colors"
                            title="Xem chi tiết"
                          >
                            <Eye size={16} />
                            <span>Xem chi tiết</span>
                          </button>

                          {!payment.isPaid && (
                            <button
                              onClick={() => handleOpenPaymentModal(payment)}
                              className="flex items-center gap-1 text-sm text-green-600 hover:text-green-700 px-3 py-1.5 bg-green-50 hover:bg-green-100 rounded-md transition-colors"
                              title="Thanh toán"
                            >
                              <FileText size={16} />
                              <span>Thanh toán</span>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Phân trang */}
          <div className="p-4 border-t border-gray-100">

            <Pagination
              currentPage={currentPage}
              totalItems={totalItems}
              limit={limit}
              onPageChange={(p) => dispatch(setCurrentPage(p))}
            />
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handleClosePaymentModal}
        paymentInfo={selectedPayment}
      />
    </UserLayout>
  );
};

export default UserTuitionPayments;
