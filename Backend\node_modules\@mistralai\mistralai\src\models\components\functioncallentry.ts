/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FunctionCallEntryArguments,
  FunctionCallEntryArguments$inboundSchema,
  FunctionCallEntryArguments$Outbound,
  FunctionCallEntryArguments$outboundSchema,
} from "./functioncallentryarguments.js";

export const FunctionCallEntryObject = {
  Entry: "entry",
} as const;
export type FunctionCallEntryObject = ClosedEnum<
  typeof FunctionCallEntryObject
>;

export const FunctionCallEntryType = {
  FunctionCall: "function.call",
} as const;
export type FunctionCallEntryType = ClosedEnum<typeof FunctionCallEntryType>;

export type FunctionCallEntry = {
  object?: FunctionCallEntryObject | undefined;
  type?: FunctionCallEntryType | undefined;
  createdAt?: Date | undefined;
  completedAt?: Date | null | undefined;
  id?: string | undefined;
  toolCallId: string;
  name: string;
  arguments: FunctionCallEntryArguments;
};

/** @internal */
export const FunctionCallEntryObject$inboundSchema: z.ZodNativeEnum<
  typeof FunctionCallEntryObject
> = z.nativeEnum(FunctionCallEntryObject);

/** @internal */
export const FunctionCallEntryObject$outboundSchema: z.ZodNativeEnum<
  typeof FunctionCallEntryObject
> = FunctionCallEntryObject$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionCallEntryObject$ {
  /** @deprecated use `FunctionCallEntryObject$inboundSchema` instead. */
  export const inboundSchema = FunctionCallEntryObject$inboundSchema;
  /** @deprecated use `FunctionCallEntryObject$outboundSchema` instead. */
  export const outboundSchema = FunctionCallEntryObject$outboundSchema;
}

/** @internal */
export const FunctionCallEntryType$inboundSchema: z.ZodNativeEnum<
  typeof FunctionCallEntryType
> = z.nativeEnum(FunctionCallEntryType);

/** @internal */
export const FunctionCallEntryType$outboundSchema: z.ZodNativeEnum<
  typeof FunctionCallEntryType
> = FunctionCallEntryType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionCallEntryType$ {
  /** @deprecated use `FunctionCallEntryType$inboundSchema` instead. */
  export const inboundSchema = FunctionCallEntryType$inboundSchema;
  /** @deprecated use `FunctionCallEntryType$outboundSchema` instead. */
  export const outboundSchema = FunctionCallEntryType$outboundSchema;
}

/** @internal */
export const FunctionCallEntry$inboundSchema: z.ZodType<
  FunctionCallEntry,
  z.ZodTypeDef,
  unknown
> = z.object({
  object: FunctionCallEntryObject$inboundSchema.default("entry"),
  type: FunctionCallEntryType$inboundSchema.default("function.call"),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  completed_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  id: z.string().optional(),
  tool_call_id: z.string(),
  name: z.string(),
  arguments: FunctionCallEntryArguments$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "completed_at": "completedAt",
    "tool_call_id": "toolCallId",
  });
});

/** @internal */
export type FunctionCallEntry$Outbound = {
  object: string;
  type: string;
  created_at?: string | undefined;
  completed_at?: string | null | undefined;
  id?: string | undefined;
  tool_call_id: string;
  name: string;
  arguments: FunctionCallEntryArguments$Outbound;
};

/** @internal */
export const FunctionCallEntry$outboundSchema: z.ZodType<
  FunctionCallEntry$Outbound,
  z.ZodTypeDef,
  FunctionCallEntry
> = z.object({
  object: FunctionCallEntryObject$outboundSchema.default("entry"),
  type: FunctionCallEntryType$outboundSchema.default("function.call"),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  id: z.string().optional(),
  toolCallId: z.string(),
  name: z.string(),
  arguments: FunctionCallEntryArguments$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    completedAt: "completed_at",
    toolCallId: "tool_call_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionCallEntry$ {
  /** @deprecated use `FunctionCallEntry$inboundSchema` instead. */
  export const inboundSchema = FunctionCallEntry$inboundSchema;
  /** @deprecated use `FunctionCallEntry$outboundSchema` instead. */
  export const outboundSchema = FunctionCallEntry$outboundSchema;
  /** @deprecated use `FunctionCallEntry$Outbound` instead. */
  export type Outbound = FunctionCallEntry$Outbound;
}

export function functionCallEntryToJSON(
  functionCallEntry: FunctionCallEntry,
): string {
  return JSON.stringify(
    FunctionCallEntry$outboundSchema.parse(functionCallEntry),
  );
}

export function functionCallEntryFromJSON(
  jsonString: string,
): SafeParseResult<FunctionCallEntry, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FunctionCallEntry$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FunctionCallEntry' from JSON`,
  );
}
