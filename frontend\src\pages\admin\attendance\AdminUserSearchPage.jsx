import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../../../layouts/AdminLayout';
import { 
    Search, 
    User, 
    Calendar,
    Phone,
    Mail,
    ChevronRight,
    Users,
    BookOpen
} from 'lucide-react';
import { getAllUsersAPI } from '../../../services/userApi';

const AdminUserSearchPage = () => {
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Load users
    useEffect(() => {
        const loadUsers = async () => {
            try {
                setLoading(true);
                const response = await getAllUsersAPI({ limit: 1000 });
                const userData = response.data.data || [];
                
                // Filter only students (assuming role-based filtering)
                const students = userData.filter(user => 
                    user.role === 'HS1' || user.userType === 'HS1'
                );
                
                setUsers(students);
                setFilteredUsers(students);
            } catch (error) {
                setError('Có lỗi xảy ra khi tải danh sách học viên');
                console.error('Error loading users:', error);
            } finally {
                setLoading(false);
            }
        };

        loadUsers();
    }, []);

    // Handle search
    useEffect(() => {
        if (!searchTerm.trim()) {
            setFilteredUsers(users);
        } else {
            const filtered = users.filter(user => {
                const fullName = `${user.firstName || ''} ${user.lastName || ''}`.toLowerCase();
                const phone = user.phone || '';
                const email = user.email || '';
                const username = user.username || '';
                
                return (
                    fullName.includes(searchTerm.toLowerCase()) ||
                    phone.includes(searchTerm) ||
                    email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    username.toLowerCase().includes(searchTerm.toLowerCase())
                );
            });
            setFilteredUsers(filtered);
        }
    }, [searchTerm, users]);

    // Handle user selection
    const handleUserSelect = (userId) => {
        navigate(`/admin/attendance/user/${userId}`);
    };

    // Format user display name
    const getUserDisplayName = (user) => {
        if (user.firstName && user.lastName) {
            return `${user.firstName} ${user.lastName}`;
        }
        return user.username || user.email || `User ${user.id}`;
    };

    if (loading) {
        return (
            <AdminLayout>
                <div className="min-h-screen bg-gray-50 p-4">
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-500"></div>
                    </div>
                </div>
            </AdminLayout>
        );
    }

    return (
        <AdminLayout>
            <div className="min-h-screen bg-gray-50">
                {/* Mobile Header */}
                <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
                    <div className="p-4">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="p-2 bg-sky-100 rounded-full">
                                <Users className="w-6 h-6 text-sky-600" />
                            </div>
                            <div>
                                <h1 className="text-lg font-bold text-gray-800">
                                    Chọn học viên
                                </h1>
                                <p className="text-sm text-gray-600">
                                    Tìm kiếm và chọn học viên để xem điểm danh
                                </p>
                            </div>
                        </div>

                        {/* Search Bar */}
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                                type="text"
                                placeholder="Tìm kiếm theo tên, số điện thoại, email..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                            />
                        </div>

                        {/* Results Count */}
                        <div className="mt-3 text-sm text-gray-600">
                            {filteredUsers.length} học viên được tìm thấy
                        </div>
                    </div>
                </div>

                {/* Content */}
                <div className="p-4">
                    {/* Error Message */}
                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                            <p className="text-sm">{error}</p>
                        </div>
                    )}

                    {/* User List */}
                    {filteredUsers.length === 0 ? (
                        <div className="bg-white rounded-lg p-8 text-center">
                            <User size={48} className="mx-auto text-gray-300 mb-4" />
                            <h3 className="text-lg font-medium text-gray-800 mb-2">
                                {searchTerm ? 'Không tìm thấy học viên' : 'Không có học viên nào'}
                            </h3>
                            <p className="text-gray-600 text-sm">
                                {searchTerm 
                                    ? 'Thử tìm kiếm với từ khóa khác' 
                                    : 'Chưa có học viên nào trong hệ thống'
                                }
                            </p>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {filteredUsers.map((user) => (
                                <div
                                    key={user.id}
                                    onClick={() => handleUserSelect(user.id)}
                                    className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 hover:shadow-md hover:border-sky-200 transition-all cursor-pointer active:scale-95"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3 flex-1 min-w-0">
                                            {/* Avatar */}
                                            <div className="w-12 h-12 bg-sky-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                <User className="w-6 h-6 text-sky-600" />
                                            </div>

                                            {/* User Info */}
                                            <div className="flex-1 min-w-0">
                                                <h3 className="font-medium text-gray-800 truncate">
                                                    {getUserDisplayName(user)}
                                                </h3>
                                                
                                                <div className="space-y-1 mt-1">
                                                    {user.phone && (
                                                        <div className="flex items-center gap-1 text-sm text-gray-600">
                                                            <Phone size={12} />
                                                            <span>{user.phone}</span>
                                                        </div>
                                                    )}
                                                    
                                                    {user.email && (
                                                        <div className="flex items-center gap-1 text-sm text-gray-600">
                                                            <Mail size={12} />
                                                            <span className="truncate">{user.email}</span>
                                                        </div>
                                                    )}
                                                    
                                                    <div className="flex items-center gap-1 text-xs text-gray-500">
                                                        <span>ID: {user.id}</span>
                                                        {user.username && (
                                                            <>
                                                                <span>•</span>
                                                                <span>@{user.username}</span>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Action Button */}
                                        <div className="flex items-center gap-2 flex-shrink-0">
                                            <div className="p-2 bg-sky-50 rounded-full">
                                                <Calendar className="w-4 h-4 text-sky-600" />
                                            </div>
                                            <ChevronRight className="w-5 h-5 text-gray-400" />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Quick Actions */}
                <div className="fixed bottom-4 right-4">
                    <button
                        onClick={() => navigate('/admin/attendance')}
                        className="bg-sky-600 hover:bg-sky-700 text-white p-3 rounded-full shadow-lg transition-colors"
                    >
                        <BookOpen size={24} />
                    </button>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminUserSearchPage;
