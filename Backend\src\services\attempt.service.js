import db from "../models/index.js";
import { Op, QueryTypes } from "sequelize";
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js";


const getDuration = (attempt, testDuration = null) => {
    const start = new Date(attempt.startTime);
    const end = new Date(attempt.endTime);

    let durationMs = end - start
    if (testDuration) {
        const maxDurationMs = testDuration * 60 * 1000;
        durationMs = Math.min(durationMs, maxDurationMs);
    }

    return {
        durationMs,
        duration: `${Math.floor(durationMs / 1000 / 60)} phút ${Math.floor((durationMs / 1000) % 60)} giây`,
    };
};

export const getAttemptsByUserId = async (id, { page, limit, sortOrder = "DESC" }) => {
    const offset = (page - 1) * limit;
    // Raw SQL để count tổng số bản ghi sau khi lọc
    const countResult = await db.sequelize.query(
        `
            SELECT COUNT(*) as total
            FROM \`studentExamAttempt\` AS s
            INNER JOIN \`exam\` AS e ON s.\`examId\` = e.\`id\`
            WHERE s.\`studentId\` = :studentId
            AND s.\`endTime\` IS NOT NULL
            AND s.\`score\` IS NOT NULL
            AND e.\`seeCorrectAnswer\` = true
            `,
        {
            replacements: { studentId: id },
            type: QueryTypes.SELECT,
        }
    );

    const count = parseInt(countResult[0].total, 10);

    // Truy vấn dữ liệu sau khi lọc, có phân trang
    const attempts = await db.StudentExamAttempt.findAll({
        where: {
            studentId: id,
            endTime: { [Op.ne]: null },
            score: { [Op.ne]: null },
        },
        include: [
            {
                model: db.Exam,
                as: "exam",
                attributes: ["name", "seeCorrectAnswer", "testDuration", "typeOfExam"],
                where: { seeCorrectAnswer: true }, // lọc ngay trong include
            },
        ],
        order: [["endTime", sortOrder]],
        offset,
        limit,
    });

    const attemptsWithDuration = attempts.map((attempt) => {
        const { durationMs, duration } = getDuration(attempt, attempt.exam.testDuration);

        return {
            ...attempt.toJSON(),
            durationMs,
            duration,
        };
    });

    return { data: attemptsWithDuration, total: count }
}