import { useState } from "react";
import { useSelector } from "react-redux";
import CommentItem from "./CommentItem";
import CommentInput from "./CommentInput";
import { setCurrentPage } from "src/features/comments/ExamCommentsSlice";
import { useDispatch } from "react-redux";
import LoadingData from "../loading/LoadingData";
import { MessageCircle } from "lucide-react";
import LoadingCommentItem from "./LoadingCommentItem"; // cập nhật đúng path của bạn

const CommentSection = ({ comments = [], onSubmit, onUpdate, onDelete, onReply }) => {
    const [content, setContent] = useState("");
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const { user } = useSelector((state) => state.auth);
    const { codes } = useSelector((state) => state.codes);
    const { pagination, loading } = useSelector((state) => state.comments);
    const { page, totalPages } = pagination;
    const dispatch = useDispatch();

    const handleEmojiClick = (emoji) => {
        setContent(prev => prev + emoji);
        setShowEmojiPicker(false);
    };

    const processedComments = comments.map((c) =>
        c.userId == user?.id ? { ...c, user: user } : c
    );

    const handleSend = () => {
        if (content.trim() === "") return;
        onSubmit?.(content);
        setContent("");
    };

    const handleReply = (content, parentCommentId) => {
        if (content.trim() === "") return;
        onReply?.(content, parentCommentId);
    };

    const handleLoadMore = () => {
        if (page < totalPages) {
            dispatch(setCurrentPage(page + 1));
        }
    };

    return (
        <div className="relative bg-white rounded-md border border-gray-200 p-4 flex flex-col gap-4">
            <div className="flex flex-col pr-2">
                <div className="flex flex-col gap-3 pr-2">
                    {processedComments.length > 0 || page < totalPages  ? (
                        processedComments.map((comment) => (
                            <CommentItem
                                key={comment.id}
                                comment={comment}
                                user={user}
                                codes={codes}
                                onUpdate={onUpdate}
                                onDelete={onDelete}
                                onReply={handleReply}
                            />
                        ))
                    ) : (
                        <p className="text-sm text-gray-500 italic">Chưa có bình luận nào.</p>
                    )}
                    {loading && (
                        <>
                            <LoadingCommentItem />
                            <LoadingCommentItem />
                            <LoadingCommentItem />
                        </>
                    )}
                </div>

                {page < totalPages && (
                    <div className="flex justify-center mt-2">
                        <button
                            onClick={handleLoadMore}
                            disabled={loading}
                            className="text-sm text-sky-600 hover:text-sky-800 px-4 py-1 border border-sky-300 rounded-md disabled:opacity-50"
                        >
                            {loading ? "Đang tải..." : "Xem thêm bình luận"}
                        </button>
                    </div>
                )}
            </div>


            <div className="flex items-center gap-2 pt-3 border-t">
                <div className="w-9 h-9 rounded-full bg-gray-300 flex items-center justify-center text-white font-bold text-sm">
                    {user?.avatarUrl ? (
                        <img src={user?.avatarUrl} alt="avatar" className="w-full h-full object-cover rounded-full" />
                    ) : (user?.firstName?.[0] ?? "") + (user?.lastName?.[0] ?? "")}
                </div>
                <CommentInput onSend={onSubmit} />
            </div>
        </div>
    );
};

export default CommentSection;
