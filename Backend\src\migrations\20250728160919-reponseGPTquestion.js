'use strict';

export default {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable('responseGPTQuestions', {
            id: {
                allowNull: false,
                autoIncrement: true,
                primaryKey: true,
                type: Sequelize.INTEGER
            },
            message: {
                type: Sequelize.TEXT,
                allowNull: false
            },
            questionId: {
                type: Sequelize.INTEGER,
                references: {
                    model: 'question',
                    key: 'id'
                },
                allowNull: true
            },
            response: {
                type: Sequelize.TEXT,
                allowNull: false
            },
            createdAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            }
        });
    },
    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('responseGPTQuestions');
    }
}
