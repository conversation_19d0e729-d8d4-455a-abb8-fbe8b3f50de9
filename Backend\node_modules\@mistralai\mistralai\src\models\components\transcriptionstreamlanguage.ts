/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const TranscriptionStreamLanguageType = {
  TranscriptionLanguage: "transcription.language",
} as const;
export type TranscriptionStreamLanguageType = ClosedEnum<
  typeof TranscriptionStreamLanguageType
>;

export type TranscriptionStreamLanguage = {
  type?: TranscriptionStreamLanguageType | undefined;
  audioLanguage: string;
  additionalProperties?: { [k: string]: any };
};

/** @internal */
export const TranscriptionStreamLanguageType$inboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamLanguageType
> = z.nativeEnum(TranscriptionStreamLanguageType);

/** @internal */
export const TranscriptionStreamLanguageType$outboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamLanguageType
> = TranscriptionStreamLanguageType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamLanguageType$ {
  /** @deprecated use `TranscriptionStreamLanguageType$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamLanguageType$inboundSchema;
  /** @deprecated use `TranscriptionStreamLanguageType$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamLanguageType$outboundSchema;
}

/** @internal */
export const TranscriptionStreamLanguage$inboundSchema: z.ZodType<
  TranscriptionStreamLanguage,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    type: TranscriptionStreamLanguageType$inboundSchema.default(
      "transcription.language",
    ),
    audio_language: z.string(),
  }).catchall(z.any()),
  "additionalProperties",
  true,
).transform((v) => {
  return remap$(v, {
    "audio_language": "audioLanguage",
  });
});

/** @internal */
export type TranscriptionStreamLanguage$Outbound = {
  type: string;
  audio_language: string;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const TranscriptionStreamLanguage$outboundSchema: z.ZodType<
  TranscriptionStreamLanguage$Outbound,
  z.ZodTypeDef,
  TranscriptionStreamLanguage
> = z.object({
  type: TranscriptionStreamLanguageType$outboundSchema.default(
    "transcription.language",
  ),
  audioLanguage: z.string(),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      audioLanguage: "audio_language",
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamLanguage$ {
  /** @deprecated use `TranscriptionStreamLanguage$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamLanguage$inboundSchema;
  /** @deprecated use `TranscriptionStreamLanguage$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamLanguage$outboundSchema;
  /** @deprecated use `TranscriptionStreamLanguage$Outbound` instead. */
  export type Outbound = TranscriptionStreamLanguage$Outbound;
}

export function transcriptionStreamLanguageToJSON(
  transcriptionStreamLanguage: TranscriptionStreamLanguage,
): string {
  return JSON.stringify(
    TranscriptionStreamLanguage$outboundSchema.parse(
      transcriptionStreamLanguage,
    ),
  );
}

export function transcriptionStreamLanguageFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionStreamLanguage, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionStreamLanguage$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionStreamLanguage' from JSON`,
  );
}
