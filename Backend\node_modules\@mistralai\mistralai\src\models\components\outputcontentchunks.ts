/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DocumentURLChunk,
  DocumentURLChunk$inboundSchema,
  DocumentURLChunk$Outbound,
  DocumentURLChunk$outboundSchema,
} from "./documenturlchunk.js";
import {
  ImageURLChunk,
  ImageURLChunk$inboundSchema,
  ImageURLChunk$Outbound,
  ImageURLChunk$outboundSchema,
} from "./imageurlchunk.js";
import {
  TextChunk,
  TextChunk$inboundSchema,
  TextChunk$Outbound,
  TextChunk$outboundSchema,
} from "./textchunk.js";
import {
  ToolFileChunk,
  ToolFileChunk$inboundSchema,
  ToolFileChunk$Outbound,
  ToolFileChunk$outboundSchema,
} from "./toolfilechunk.js";
import {
  ToolReferenceChunk,
  ToolReferenceChunk$inboundSchema,
  ToolReferenceChunk$Outbound,
  ToolReferenceChunk$outboundSchema,
} from "./toolreferencechunk.js";

export type OutputContentChunks =
  | TextChunk
  | ImageURLChunk
  | DocumentURLChunk
  | ToolFileChunk
  | ToolReferenceChunk;

/** @internal */
export const OutputContentChunks$inboundSchema: z.ZodType<
  OutputContentChunks,
  z.ZodTypeDef,
  unknown
> = z.union([
  TextChunk$inboundSchema,
  ImageURLChunk$inboundSchema,
  DocumentURLChunk$inboundSchema,
  ToolFileChunk$inboundSchema,
  ToolReferenceChunk$inboundSchema,
]);

/** @internal */
export type OutputContentChunks$Outbound =
  | TextChunk$Outbound
  | ImageURLChunk$Outbound
  | DocumentURLChunk$Outbound
  | ToolFileChunk$Outbound
  | ToolReferenceChunk$Outbound;

/** @internal */
export const OutputContentChunks$outboundSchema: z.ZodType<
  OutputContentChunks$Outbound,
  z.ZodTypeDef,
  OutputContentChunks
> = z.union([
  TextChunk$outboundSchema,
  ImageURLChunk$outboundSchema,
  DocumentURLChunk$outboundSchema,
  ToolFileChunk$outboundSchema,
  ToolReferenceChunk$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OutputContentChunks$ {
  /** @deprecated use `OutputContentChunks$inboundSchema` instead. */
  export const inboundSchema = OutputContentChunks$inboundSchema;
  /** @deprecated use `OutputContentChunks$outboundSchema` instead. */
  export const outboundSchema = OutputContentChunks$outboundSchema;
  /** @deprecated use `OutputContentChunks$Outbound` instead. */
  export type Outbound = OutputContentChunks$Outbound;
}

export function outputContentChunksToJSON(
  outputContentChunks: OutputContentChunks,
): string {
  return JSON.stringify(
    OutputContentChunks$outboundSchema.parse(outputContentChunks),
  );
}

export function outputContentChunksFromJSON(
  jsonString: string,
): SafeParseResult<OutputContentChunks, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OutputContentChunks$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OutputContentChunks' from JSON`,
  );
}
