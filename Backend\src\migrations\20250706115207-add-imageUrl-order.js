'use strict';

export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('statement1', 'order', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('statement1', 'imageUrl', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('statement1', 'order');
    await queryInterface.removeColumn('statement1', 'imageUrl');
  },
};
