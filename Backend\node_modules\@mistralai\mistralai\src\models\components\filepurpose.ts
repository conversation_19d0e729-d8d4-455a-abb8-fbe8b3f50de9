/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import {
  catchUnrecognizedEnum,
  OpenEnum,
  Unrecognized,
} from "../../types/enums.js";

export const FilePurpose = {
  FineTune: "fine-tune",
  Batch: "batch",
  Ocr: "ocr",
} as const;
export type FilePurpose = OpenEnum<typeof FilePurpose>;

/** @internal */
export const FilePurpose$inboundSchema: z.ZodType<
  FilePurpose,
  z.ZodTypeDef,
  unknown
> = z
  .union([
    z.nativeEnum(FilePurpose),
    z.string().transform(catchUnrecognizedEnum),
  ]);

/** @internal */
export const FilePurpose$outboundSchema: z.ZodType<
  FilePurpose,
  z.ZodTypeDef,
  FilePurpose
> = z.union([
  z.nativeEnum(FilePurpose),
  z.string().and(z.custom<Unrecognized<string>>()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilePurpose$ {
  /** @deprecated use `FilePurpose$inboundSchema` instead. */
  export const inboundSchema = FilePurpose$inboundSchema;
  /** @deprecated use `FilePurpose$outboundSchema` instead. */
  export const outboundSchema = FilePurpose$outboundSchema;
}
