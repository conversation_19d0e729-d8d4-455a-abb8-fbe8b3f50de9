import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedDay } from 'src/features/calendar/calendarSlice';

const NavigateTimeButton = ({ onClick, icon, label }) => {
    const dispatch = useDispatch();
    const { selectedDay } = useSelector((state) => state.calendar);

    return (
        <>
            <button
                onClick={() => {
                    const newDate = new Date(new Date(selectedDay).getTime() - 7 * 24 * 60 * 60 * 1000);
                    dispatch(setSelectedDay(newDate));
                }}
                className='p-1 rounded-full hover:bg-gray-100 transition'>
                <ChevronLeft className="w-4 h-4" />
            </button>

            <button
                onClick={() => {
                    const newDate = new Date(new Date(selectedDay).getTime() + 7 * 24 * 60 * 60 * 1000);
                    dispatch(setSelectedDay(newDate));
                }}
                className='p-1 rounded-full hover:bg-gray-100 transition'>
                <ChevronRight className="w-4 h-4" />
            </button>
        </>
    );
}

export default NavigateTimeButton;