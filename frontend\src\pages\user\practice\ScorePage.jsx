import UserLayout from "../../../layouts/UserLayout";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { use, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { fetchQuestionAndAnswersByAttempt } from "../../../features/scorePage/scorePageSlice";
import { reExamination } from 'src/features/scorePage/scorePageSlice';
import LatexRenderer from "../../../components/latex/RenderLatex";
import { useMemo } from "react";
import LoadingSpinner from "../../../components/loading/LoadingSpinner";
import PdfViewer from "../../../components/ViewPdf";
import ReportButton from "../../../components/button/ReportButton";
import { setErrorMessage } from "src/features/state/stateApiSlice";
import {
    CheckCircle,
    XCircle,
    AlertCircle,
    BookOpen,
    Check,
    X,
    Eye,
    EyeOff,
    ArrowLeft,
    BarChart3,
    Target,
    Hash,
} from "lucide-react";
import MarkdownPreviewWithMath from "src/components/latex/MarkDownPreview";
import YouTubePlayer from "src/components/YouTubePlayer";
import LoadingText from "src/components/loading/LoadingText";
import ExamOverviewHeader from "src/components/header/ExamOverviewHeader";
import { formatTime } from "src/utils/formatters";
import { fetchAttemptById } from "src/features/scorePage/scorePageSlice";
import { calculateDurationText } from "src/utils/formatters";
import { getTotalScoreExam } from "src/features/exam/examDetailSlice";
import { setQuestion } from "src/features/question/questionSlice";
import AIChatWidget from "src/components/ai/AiChatWidget";
import { fetchCodesByType } from "src/features/code/codeSlice";
import { getDescription } from "src/utils/codeUtils";
import ActionButton from "src/components/button/ActionButton";

const getQuestionStatus = (answer, question) => {
    if (!answer || answer.answerContent === null || answer.answerContent === undefined || answer.answerContent === "") {
        return "unanswered"; // Chưa trả lời - màu vàng
    }
    return answer.result ? "correct" : "incorrect";
};


// Component hiển thị nút câu hỏi với màu sắc theo trạng thái
const QuestionButton = ({ question, answer, index, onClick, isSelected }) => {
    const status = getQuestionStatus(answer, question);

    const getButtonStyle = () => {
        const baseStyle = "w-8 h-8 rounded-md font-medium text-xs transition-all duration-200 border ";

        if (isSelected) {
            switch (status) {
                case "correct":
                    return baseStyle + "bg-green-600 border-green-600 text-white";
                case "incorrect":
                    return baseStyle + "bg-red-600 border-red-600 text-white";
                case "unanswered":
                    return baseStyle + "bg-yellow-600 border-yellow-600 text-white";
                default:
                    return baseStyle + "bg-gray-600 border-gray-600 text-white";
            }
        } else {
            switch (status) {
                case "correct":
                    return baseStyle + "bg-green-50 border-green-200 text-green-700 hover:bg-green-100";
                case "incorrect":
                    return baseStyle + "bg-red-50 border-red-200 text-red-700 hover:bg-red-100";
                case "unanswered":
                    return baseStyle + "bg-yellow-50 border-yellow-200 text-yellow-700 hover:bg-yellow-100";
                default:
                    return baseStyle + "bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100";
            }
        }
    };

    return (
        <button
            onClick={() => onClick(question, answer, index)}
            className={getButtonStyle()}
        >
            {index + 1}
        </button>
    );
};

const AnswerIndicator = ({ label, selected, correctValue, isCorrect }) => {
    console.log("AnswerIndicator", label, selected, correctValue, isCorrect);
    return (
        <div className="flex items-center gap-2">
            <div
                className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all duration-200
          ${correctValue ? 'bg-green-600 border-green-600' : selected ? 'bg-red-600 border-red-600' : 'border-gray-300'}
        `}
            >
                <div className="w-2 h-2 bg-white rounded-full" />
            </div>

            <span
                className={`text-sm font-medium transition-colors duration-200
          ${correctValue ? 'text-green-600' : selected ? 'text-red-600' : 'text-gray-800'}
        `}
            >
                {label}
            </span>

            {selected ? (
                isCorrect ? (
                    <Check size={16} className="text-green-600" title="Bạn chọn đúng" />
                ) : (
                    <X size={16} className="text-red-600" title="Bạn chọn sai" />
                )
            ) : (
                <div className="w-4 h-4" />
            )}
        </div>
    );
};

// Component hiển thị chi tiết câu hỏi và câu trả lời
const QuestionDetail = ({ question, answer, index }) => {
    const [showSolution, setShowSolution] = useState(false);

    if (!question) return null;
    const status = getQuestionStatus(answer, question);

    const getStatusDisplay = () => {
        switch (status) {
            case "correct":
                return (
                    <div className="flex items-center gap-2 px-2 py-1 bg-green-100 border border-green-300 rounded-lg">
                        <CheckCircle className="text-green-600" size={14} />
                        <span className="text-sm text-green-800 font-semibold">Đúng</span>
                    </div>
                );
            case "incorrect":
                return (
                    <div className="flex items-center gap-2 px-2 py-1 bg-red-100 border border-red-300 rounded-lg">
                        <XCircle className="text-red-600" size={14} />
                        <span className="text-sm text-red-800 font-semibold">Sai</span>
                    </div>
                );
            case "unanswered":
                return (
                    <div className="flex items-center gap-2 px-2 py-1 bg-yellow-100 border border-yellow-300 rounded-lg">
                        <AlertCircle className="text-yellow-600" size={14} />
                        <span className=" text-sm text-yellow-800 font-semibold">Chưa trả lời</span>
                    </div>
                );
            default:
                return null;
        }
    };



    return (
        <div className="bg-white rounded-md border border-gray-200 shadow-sm">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-sky-50 to-blue-50">
                <div className="flex flex-row gap-2">
                    <h3 className="text-lg font-semibold text-gray-800">Câu {index + 1} <span className="text-sm text-gray-500">(ID: {question.id})</span></h3>
                    <ReportButton questionId={question.id} />
                </div>
                {getStatusDisplay()}
            </div>

            {/* Question Content */}
            <div className="p-4 space-y-4">
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">Nội dung câu hỏi:</h4>
                    <div className="text-sm text-gray-800 leading-relaxed">
                        <LatexRenderer text={question.content} className="text-gray-800" />
                    </div>
                    {question.imageUrl && (
                        <div className="flex flex-col items-center justify-center w-full mt-4">
                            <img
                                src={question.imageUrl}
                                alt="Question"
                                className="object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200"
                            />
                        </div>
                    )}
                </div>

                {/* Statements for TN questions */}
                {question.typeOfQuestion === "TN" && question.statements && (
                    <div className="space-y-3">
                        <h4 className="text-sm font-semibold text-gray-700">Các lựa chọn:</h4>
                        <div className="flex flex-col gap-3">
                            {[...question.statements]
                                .sort((a, b) => (a.order || 0) - (b.order || 0))
                                .map((statement, index) => {
                                    const isUserSelected = answer && answer.answerContent == statement.id;
                                    const isCorrect = statement.isCorrect;

                                    let statementStyle = "bg-gray-50 border-gray-200";
                                    let iconStyle = "bg-gray-400 text-white";

                                    if (isCorrect) {
                                        statementStyle = "bg-green-100 border-green-300";
                                        iconStyle = "bg-green-600 text-white";
                                    } else if (isUserSelected && !isCorrect) {
                                        statementStyle = "bg-red-100 border-red-300";
                                        iconStyle = "bg-red-600 text-white";
                                    } else if (isUserSelected) {
                                        statementStyle = "bg-sky-100 border-sky-300";
                                        iconStyle = "bg-sky-600 text-white";
                                    }

                                    return (
                                        <div
                                            key={statement.id || index}
                                            className={`p-3 rounded-lg border transition-all ${statementStyle}`}
                                        >
                                            <div className="flex items-start gap-3">
                                                <span className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${iconStyle}`}>
                                                    {isCorrect ? (
                                                        <Check size={12} />
                                                    ) : isUserSelected && !isCorrect ? (
                                                        <X size={12} />
                                                    ) : (
                                                        String.fromCharCode(65 + index)
                                                    )}
                                                </span>
                                                <div className="flex-1">
                                                    <div className="text-sm text-gray-800 leading-relaxed">
                                                        <LatexRenderer text={statement.content} className="text-gray-800" />
                                                    </div>
                                                    {statement.imageUrl && (
                                                        <div className="flex flex-col items-center justify-center w-full mt-2">
                                                            <img
                                                                src={statement.imageUrl}
                                                                alt={`Statement ${String.fromCharCode(65 + index)}`}
                                                                className="object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200"
                                                            />
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                        </div>
                    </div>
                )}

                {/* Statements for DS questions */}
                {question.typeOfQuestion === "DS" && question.statements && (
                    <div className="space-y-3">
                        <h4 className="text-sm font-semibold text-gray-700">Đánh giá từng phát biểu:</h4>
                        <div className="flex flex-col gap-3">
                            {[...question.statements]
                                .sort((a, b) => (a.order || 0) - (b.order || 0))
                                .map((statement, index) => {
                                    const userAnswers = answer ? JSON.parse(answer.answerContent || "[]") : [];
                                    const userAnswer = userAnswers.find((st) => statement.id === st.statementId)

                                    return (
                                        <div
                                            key={statement.id || index}
                                            className={`p-3 rounded-lg border transition-all `}
                                        >
                                            <div className="flex flex-col gap-3">
                                                <div className="flex items-start gap-3">
                                                    <span className="flex-shrink-0 w-6 h-6 rounded-full bg-sky-600 text-white flex items-center justify-center text-xs font-medium">
                                                        {String.fromCharCode(97 + index)}
                                                    </span>
                                                    <div className="flex-1">
                                                        <div className="text-sm text-gray-800 leading-relaxed">
                                                            <LatexRenderer text={statement.content} className="text-gray-800" />
                                                        </div>
                                                        {statement.imageUrl && (
                                                            <div className="flex flex-col items-center justify-center w-full mt-2">
                                                                <img
                                                                    src={statement.imageUrl}
                                                                    alt={`Statement ${String.fromCharCode(97 + index)}`}
                                                                    className="object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200"
                                                                />
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* True/False options display */}
                                                <div className="flex items-center gap-4 ml-9">
                                                    <AnswerIndicator
                                                        label="Đúng"
                                                        selected={userAnswer?.answer === true}
                                                        isCorrect={userAnswer?.answer === statement.isCorrect}
                                                        correctValue={statement.isCorrect === true}
                                                    />
                                                    <AnswerIndicator
                                                        label="Sai"
                                                        selected={userAnswer?.answer === false}
                                                        isCorrect={userAnswer?.answer === statement.isCorrect}
                                                        correctValue={statement.isCorrect === false}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                        </div>
                    </div>
                )}

                {/* User Answer for TLN questions only */}
                {question.typeOfQuestion === "TLN" && (
                    <>
                        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                            <h4 className="text-sm font-semibold text-blue-800 mb-2">Câu trả lời của bạn:</h4>
                            <div className="text-sm">
                                {answer && answer.answerContent ? (
                                    <LatexRenderer text={answer.answerContent} className="text-gray-800 font-medium" />
                                ) : (
                                    <span className="text-gray-500 italic">Chưa trả lời</span>
                                )}
                            </div>
                        </div>

                        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                            <h4 className="text-sm font-semibold text-green-800 mb-2">Đáp án đúng:</h4>
                            <div className="text-sm">
                                <LatexRenderer text={question.correctAnswer} className="text-green-800 font-medium" />
                            </div>
                        </div>
                    </>
                )}

                {/* Solution */}
                {question.solution && (
                    <div className="bg-sky-50 rounded-lg p-4 border border-sky-200">
                        <div className="flex items-center justify-between mb-3">
                            <h4 className="text-sm font-semibold text-sky-800">Lời giải:</h4>
                            <button
                                onClick={() => setShowSolution(!showSolution)}
                                className="flex items-center gap-2 px-3 py-1 text-xs bg-sky-100 hover:bg-sky-200 text-sky-800 rounded-lg transition-colors"
                            >
                                {showSolution ? <EyeOff size={14} /> : <Eye size={14} />}
                                {showSolution ? "Ẩn lời giải" : "Xem lời giải"}
                            </button>
                        </div>

                        {showSolution && (
                            <div className="space-y-3">
                                <div className="text-sm text-gray-800">
                                    <MarkdownPreviewWithMath content={question.solution} />
                                </div>
                                {question.solutionImageUrl && (
                                    <div className="flex justify-center">
                                        <img
                                            src={question.solutionImageUrl}
                                            alt="Lời giải"
                                            className="max-h-80 object-contain rounded-md border border-gray-200"
                                        />
                                    </div>
                                )}
                                {question.solutionUrl && (
                                    <div className="flex justify-center items-center">
                                        <YouTubePlayer url={question.solutionUrl} sizeClass="w-2/3 flex justify-center aspect-video" />
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

const QuestionListPanel = ({
    questionSections,
    answers,
    handleQuestionClick,
    selectedQuestion,
}) => {
    const legendItems = [
        { color: 'green', label: 'Đúng' },
        { color: 'red', label: 'Sai' },
        { color: 'yellow', label: 'Chưa trả lời' },
    ];
    return (
        <div className="bg-white rounded-md border border-gray-200 shadow-sm p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Danh sách câu hỏi</h3>

            {/* Legend */}
            <div className="flex flex-wrap gap-2 mb-4 text-xs">
                {legendItems.map((item) => (
                    <div className="flex items-center gap-1" key={item.label}>
                        <div className={`w-3 h-3 bg-${item.color}-100 border border-${item.color}-300 rounded`}></div>
                        <span className="text-gray-600">{item.label}</span>
                    </div>
                ))}
            </div>

            {/* Question Buttons */}
            <div className="flex flex-col gap-4">
                {questionSections.map((section, sectionIndex) => (
                    <div key={sectionIndex}>
                        <h4 className="text-sm font-semibold text-gray-700 mb-1">{section.title}</h4>
                        <div className="flex flex-wrap gap-2">
                            {section.questions?.map((question, index) => {
                                const answer = answers?.find((a) => a.questionId === question.id);
                                return (
                                    <QuestionButton
                                        key={question.id}
                                        question={question}
                                        answer={answer}
                                        index={index}
                                        onClick={handleQuestionClick}
                                        isSelected={selectedQuestion?.id === question.id}
                                    />
                                );
                            })}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

const InfoQuestion = ({ question }) => {
    const { codes } = useSelector((state) => state.codes);

    return (
        <div className="bg-white rounded-md border border-gray-200 shadow-sm p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Thông tin câu hỏi</h3>

            {question ? <div className="grid grid-cols-2 gap-y-2 text-sm text-gray-700">
                <div className="font-medium">ID:</div>
                <div>{question?.id}</div>

                <div className="font-medium">Độ khó:</div>
                <div>{getDescription("difficulty", question?.difficulty, codes)}</div>

                <div className="font-medium">Chương:</div>
                <div>{getDescription("chapter", question?.chapter, codes)}</div>

                <div className="font-medium">Lớp:</div>
                <div>{getDescription("grade", question?.class, codes)}</div>

                <div className="font-medium">Loại câu hỏi:</div>
                <div>
                    {question?.typeOfQuestion === "TN"
                        ? "Trắc nghiệm"
                        : question?.typeOfQuestion === "TLN"
                            ? "Tự luận ngắn"
                            : question?.typeOfQuestion === "DS"
                                ? "Đúng sai"
                                : "Không rõ"}
                </div>
            </div> : (
                <div className="text-sm text-gray-500">Chọn một câu hỏi để xem thông tin</div>
            )}
        </div>
    );
};

const BackButton = ({ onClick }) => (
    <button
        onClick={onClick}
        className="hover:underline w-fit flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
    >
        <ArrowLeft className="w-4 h-4" />
        <span>Quay lại</span>
    </button>
);

// Helper function to calculate statistics by grouping criteria
const calculateStatistics = (questions, answers, codes, groupBy = 'difficulty') => {
    const stats = {};

    questions.forEach((question, index) => {
        const answer = answers?.find((a) => a.questionId === question.id);
        const status = getQuestionStatus(answer, question);

        // Get group key based on groupBy parameter
        let groupKey;
        if (groupBy === 'difficulty') {
            groupKey = question.difficulty || 'Khác';
        } else if (groupBy === 'chapter') {
            const chapterCode = codes?.chapter?.find(c => c.code === question.chapter);
            groupKey = chapterCode?.description || 'Khác';
        }

        if (!stats[groupKey]) {
            stats[groupKey] = {
                total: 0,
                correct: 0,
                incorrect: 0,
                unanswered: 0,
                questions: []
            };
        }

        stats[groupKey].total++;
        stats[groupKey].questions.push({
            questionNumber: index + 1,
            status: status,
            question: question
        });

        if (status === 'correct') {
            stats[groupKey].correct++;
        } else if (status === 'incorrect') {
            stats[groupKey].incorrect++;
        } else {
            stats[groupKey].unanswered++;
        }
    });

    // Calculate accuracy percentage
    Object.keys(stats).forEach(key => {
        const answered = stats[key].correct + stats[key].incorrect;
        stats[key].accuracy = answered > 0 ? Math.round((stats[key].correct / answered) * 100) : 0;
    });

    return stats;
};

// Statistics Section Component with Table and Tabs
const StatisticsSection = ({ questions, answers, codes }) => {
    const [activeTab, setActiveTab] = useState('difficulty');

    const difficultyStats = calculateStatistics(questions, answers, codes, 'difficulty');
    const chapterStats = calculateStatistics(questions, answers, codes, 'chapter');

    const currentStats = activeTab === 'difficulty' ? difficultyStats : chapterStats;

    const renderTable = () => (
        <div className="overflow-x-auto rounded-md">
            <table className="w-full border-collapse">
                <thead>
                    <tr className="bg-gradient-to-r from-sky-50 to-blue-50">
                        <th className="border border-gray-200 px-4 py-3 text-left font-semibold text-gray-700">
                            {activeTab === 'difficulty' ? 'Độ khó' : 'Chương'}
                        </th>
                        <th className="border border-gray-200 px-4 py-3 text-center font-semibold text-gray-700">
                            Tổng số câu
                        </th>
                        <th className="border border-gray-200 px-4 py-3 text-center font-semibold text-green-700">
                            Đúng
                        </th>
                        <th className="border border-gray-200 px-4 py-3 text-center font-semibold text-red-700">
                            Sai
                        </th>
                        <th className="border border-gray-200 px-4 py-3 text-center font-semibold text-yellow-700">
                            Chưa trả lời
                        </th>
                        <th className="border border-gray-200 px-4 py-3 text-center font-semibold text-sky-700">
                            Độ chính xác
                        </th>
                        <th className="border border-gray-200 px-4 py-3 text-center font-semibold text-gray-700">
                            Danh sách câu hỏi
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {Object.entries(currentStats).map(([key, data], index) => (
                        <tr key={key} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="border border-gray-200 px-4 py-3 font-medium text-gray-800">
                                {key}
                            </td>
                            <td className="border border-gray-200 px-4 py-3 text-center text-gray-700">
                                {data.total}
                            </td>
                            <td className="border border-gray-200 px-4 py-3 text-center">
                                <span className="inline-flex items-center justify-center w-8 h-8 bg-green-100 text-green-700 rounded-full text-sm font-semibold">
                                    {data.correct}
                                </span>
                            </td>
                            <td className="border border-gray-200 px-4 py-3 text-center">
                                <span className="inline-flex items-center justify-center w-8 h-8 bg-red-100 text-red-700 rounded-full text-sm font-semibold">
                                    {data.incorrect}
                                </span>
                            </td>
                            <td className="border border-gray-200 px-4 py-3 text-center">
                                <span className="inline-flex items-center justify-center w-8 h-8 bg-yellow-100 text-yellow-700 rounded-full text-sm font-semibold">
                                    {data.unanswered}
                                </span>
                            </td>
                            <td className="border border-gray-200 px-4 py-3 text-center">
                                <span className="inline-flex items-center justify-center px-3 py-1 bg-sky-100 text-sky-700 rounded-full text-sm font-semibold">
                                    {data.accuracy}%
                                </span>
                            </td>
                            <td className="border border-gray-200 px-4 py-3">
                                <div className="flex flex-wrap gap-1 max-w-xs">
                                    {data.questions.map((q) => (
                                        <span
                                            key={q.questionNumber}
                                            className={`
                                                inline-flex items-center justify-center w-6 h-6 rounded text-xs font-medium
                                                ${q.status === 'correct' ? 'bg-green-100 text-green-700 border border-green-200' :
                                                    q.status === 'incorrect' ? 'bg-red-100 text-red-700 border border-red-200' :
                                                        'bg-yellow-100 text-yellow-700 border border-yellow-200'}
                                            `}
                                        >
                                            {q.questionNumber}
                                        </span>
                                    ))}
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );

    if (questions.length === 0) return null;

    return (
        <div className="mb-4">
            {/* Tab Navigation */}
            <div className="flex flex-col gap-2 bg-white rounded-md shadow-sm overflow-hidden">
                <div className="flex flex-row gap-2">
                    <ActionButton
                        onClick={() => setActiveTab('difficulty')}
                        title={"Thống kê theo độ khó"}
                        shortTitle="Độ khó"
                        isActive={activeTab === 'difficulty'}
                        icon={Target}
                    />
                    <ActionButton
                        onClick={() => setActiveTab('chapter')}
                        title={"Thống kê theo chương"}
                        shortTitle="Chương"
                        isActive={activeTab === 'chapter'}
                        icon={Hash}
                    />
                </div>

                {/* Table Content */}
                {renderTable()}
            </div>
        </div>
    );
};

const ScorePage = () => {
    const { attemptId } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { questions, answers, exam, attempt, loading, loadingReExamination } = useSelector((state) => state.scorePage);
    const [selectedQuestion, setSelectedQuestion] = useState(null);
    const [selectedAnswer, setSelectedAnswer] = useState(null);
    const [selectedIndex, setSelectedIndex] = useState(null);
    const { totalScore, loadingTotalScore } = useSelector((state) => state.examDetail);
    const { codes } = useSelector((state) => state.codes);

    const [questionsTN, setQuestionsTN] = useState([]);
    const [questionsDS, setQuestionsDS] = useState([]);
    const [questionsTLN, setQuestionsTLN] = useState([]);

    useEffect(() => {
        if (questions.length > 0) {
            setQuestionsTN(questions.filter((question) => question.typeOfQuestion === "TN"));
            setQuestionsDS(questions.filter((question) => question.typeOfQuestion === "DS"));
            setQuestionsTLN(questions.filter((question) => question.typeOfQuestion === "TLN"));
        }
    }, [questions]);

    // Hàm xử lý chấm lại bài
    const handleReExamination = () => {
        dispatch(reExamination(attemptId))
            .unwrap()
            .then(() => {
                dispatch(fetchQuestionAndAnswersByAttempt({ attemptId }));
                fetchAttemptById(attemptId);
            })
    };

    // Hàm xử lý khi click vào nút câu hỏi
    const handleQuestionClick = (question, answer, index) => {
        setSelectedQuestion(question);
        setSelectedAnswer(answer);
        setSelectedIndex(index);
        // Set question to Redux state for AI component
        dispatch(setQuestion(question));
    };

    useEffect(() => {
        dispatch(fetchAttemptById(attemptId));
        dispatch(fetchQuestionAndAnswersByAttempt({ attemptId }));
    }, [dispatch, attemptId]);

    useEffect(() => {
        if (exam?.id) {
            dispatch(getTotalScoreExam(exam.id));
        }
    }, [exam, dispatch]);

    useEffect(() => {
        dispatch(fetchCodesByType(["chapter", "grade", "difficulty", "question type"]));
    }, [dispatch]);

    useEffect(() => {
        if (exam && !exam.seeCorrectAnswer) {
            dispatch(setErrorMessage("Không thể xem kết quả bài thi này!"));
            navigate(`/practice/exam/${exam.id}`);
        }
    }, [exam, dispatch, navigate]);

    const [questionSections, setQuestionSections] = useState([]);

    useEffect(() => {
        const sections = [];

        if (questionsTN.length > 0) {
            sections.push({ title: 'Phần I - Trắc nghiệm', questions: questionsTN });
        }

        if (questionsDS.length > 0) {
            sections.push({ title: 'Phần II - Đúng sai', questions: questionsDS });
        }

        if (questionsTLN.length > 0) {
            sections.push({ title: 'Phần III - Trả lời ngắn', questions: questionsTLN });
        }

        setQuestionSections(sections);
    }, [questionsTN, questionsDS, questionsTLN]);


    return (
        <UserLayout>
            <div className="p-4 flex flex-col mb-9">
                <ExamOverviewHeader star={false} examStatus={false} title={"Điểm"} exam={exam} />

                {/* Statistics Cards */}
                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    {/* Box điểm số */}
                    <div className="flex-1 p-4 bg-sky-50 rounded-md border border-sky-100 shadow-sm">
                        <div className="flex justify-between items-center mb-4">
                            <h5 className="text-xs font-semibold text-sky-800 uppercase mb-1 tracking-wide">
                                Điểm của bạn
                            </h5>
                            <button
                                onClick={handleReExamination}
                                disabled={loadingReExamination}
                                className="px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white font-bold rounded-md transition-colors"
                            >
                                {loadingReExamination ? "Đang chấm lại..." : "Chấm lại bài"}
                            </button>
                        </div>
                        <LoadingText
                            loading={loading || loadingTotalScore} w="w-20" h="h-[30px]" >
                            <p className="text-4xl font-bold text-sky-600">
                                {attempt?.score ?? '--'} / {totalScore ?? 10}
                            </p>
                        </LoadingText>
                    </div>

                    {/* Box thời gian làm bài */}

                    <div className="flex-1 p-4 bg-indigo-50 rounded-md border border-indigo-100 shadow-sm">

                        <h5 className="text-xs font-semibold text-indigo-800 uppercase mb-1 tracking-wide">
                            Thời gian làm bài
                        </h5>
                        <LoadingText
                            loading={loading} w="w-20" h="h-[30px]" >
                            <p className="text-xl font-semibold text-indigo-700">
                                {calculateDurationText(attempt?.startTime, attempt?.endTime)}
                            </p>
                        </LoadingText>
                    </div>
                </div>

                {/* Statistics Section */}
                {/* <StatisticsSection
                    questions={questions}
                    answers={answers}
                    codes={codes}
                /> */}

                {/* Main Content */}
                <div className="flex flex-col lg:flex-row gap-4">
                    {/* Left Panel - Question Navigation */}
                    <div className="xl:w-1/5 lg:w-1/4 md:1/3 flex flex-col gap-4">
                        <QuestionListPanel
                            questionSections={questionSections}
                            answers={answers}
                            handleQuestionClick={handleQuestionClick}
                            selectedQuestion={selectedQuestion}
                        />
                        <InfoQuestion question={selectedQuestion} />
                    </div>

                    {/* Right Panel - Question Detail and AI */}
                    <div className="flex-1 space-y-6">
                        {selectedQuestion ? (
                            <QuestionDetail
                                question={selectedQuestion}
                                answer={selectedAnswer}
                                index={selectedIndex}
                            />
                        ) : (
                            <div className="bg-white rounded-md border border-gray-200 shadow-sm p-8 text-center">
                                <div className="text-gray-400 mb-4">
                                    <BookOpen size={48} className="mx-auto" />
                                </div>
                                <h3 className="text-lg font-semibold text-gray-600 mb-2">Chọn một câu hỏi để xem chi tiết</h3>
                                <p className="text-gray-500">Click vào các nút câu hỏi bên trái để xem nội dung và đáp án</p>
                            </div>
                        )}
                    </div>
                </div>
                {/* AI Chat Widget */}
                <AIChatWidget />
            </div>
        </UserLayout >
    );
};

export default ScorePage;