import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as questionApi from "../../services/questionApi";
// import { setCurrentPage, setTotalPages, setTotalItems } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
import { setExam } from "../exam/examSlice";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";

export const fetchQuestions = createAsyncThunk(
    "questions/fetchQuestions",
    async ({ search, page, pageSize, sortOrder }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getAllQuestionAPI, { search, page, pageSize, sortOrder }, (data) => {
            // dispatch(setCurrentPage(data.currentPage));
            // dispatch(setTotalPages(data.totalPages));
            // dispatch(setTotalItems(data.totalItems));
        }, true, false);
    }
);

export const fetchExamQuestionsWithoutPagination = createAsyncThunk(
    "questions/fetchExamQuestionsWithExam",
    async ({ id }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, { id, pageSize: 1000, sortOrder: 'DESC' }, (data) => {
        }, false, false);
    }
);

export const fetchExamQuestions = createAsyncThunk(
    "questions/fetchExamQuestions",
    async ({ id, search, page, pageSize, sortOrder }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, { id, search, page, pageSize, sortOrder }, (data) => {
            // dispatch(setCurrentPage(data.currentPage));
            // dispatch(setTotalPages(data.totalPages));
            // dispatch(setTotalItems(data.totalItems));
            // dispatch(setExam(data.exam));
        }, true, false);
    }
);

export const fetchPublicQuestionsByExamId = createAsyncThunk(
    "questions/fetchPublicQuestionsByExamId",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getPublicExamQuestionsAPI, { id }, (data) => {
        }, false, false);
    }
);

export const fetchQuestionById = createAsyncThunk(
    "questions/fetchQuestionById",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getQuestionByIdAPI, id, () => { }, false, false);
    }
);

export const fetchPublicQuestionById = createAsyncThunk(
    "questions/fetchPublicQuestionById",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getPublicQuestionByIdAPI, id, () => { }, false, false);
    }
);

export const postQuestion = createAsyncThunk(
    "questions/postQuestion",
    async ({ questionData, statementOptions, questionImage, solutionImage, statementImages, examId }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.postQuestionAPI, { questionData, statementOptions, questionImage, solutionImage, statementImages, examId }, (data) => {
        }, true, false);
    }

);

export const putQuestion = createAsyncThunk(
    "questions/putQuestion",
    async ({ questionId, questionData, statements }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.putQuestionAPI, { questionId, questionData, statements }, (data) => {
        }, true, false);
    }
);

export const putImageQuestion = createAsyncThunk(
    "questions/putImageQuestion",
    async ({ questionId, questionImage }, { dispatch }) => {
        const response = await apiHandler(dispatch, questionApi.putImageQuestionAPI, { questionId, questionImage }, (data) => {
        }, true, false);

        return response;
    }
);

export const putImageSolution = createAsyncThunk(
    "questions/putImageSolution",
    async ({ questionId, solutionImage }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.putImageSolutionAPI, { questionId, solutionImage }, (data) => {
        }, true, false);
    }
);

export const putStatementImage = createAsyncThunk(
    "questions/putStatementImage",
    async ({ statementId, statementImage }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.putStatementImageAPI, { statementId, statementImage }, (data) => {
        }, true, false);
    }
);

export const deleteQuestion = createAsyncThunk(
    "questions/deleteQuestion",
    async (questionId, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.deleteQuestionAPI, questionId, () => { }, true, false);
    }
);

export const findQuestions = createAsyncThunk(
    "questions/findQuestions",
    async (search, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.findQuestionsAPI, search, () => { }, false, false, false, false);
    }
);

const questionSlice = createSlice({
    name: "questions",
    initialState: {
        questions: [],
        firstSearch: true,
        question: null,
        questionsSearch: [],
        loadingSearch: false,
        pagination: { ...initialPaginationState },
        ...initialFilterState,
        questionsExam: [],
    },
    reducers: {
        resetDetailView: (state) => {
            state.question = null;
        },
        setDetailView: (state, action) => {
            state.isDetailView = true;
        },
        setQuestion: (state, action) => {
            state.question = action.payload;
        },
        setQuestions: (state, action) => {
            state.questions = action.payload;
        },
        ...paginationReducers,
        ...filterReducers,
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchQuestions.pending, (state) => {
                state.questions = [];
                state.loading = true;
            })
            .addCase(fetchQuestions.fulfilled, (state, action) => {
                if (action.payload) {
                    state.questions = action.payload.data;
                    state.pagination = action.payload.pagination;
                }
                state.loading = false;
            })
            .addCase(fetchQuestions.rejected, (state) => {
                state.questions = [];
                state.loading = false;
            })

            .addCase(fetchExamQuestions.pending, (state) => {
                state.questions = [];
                state.loading = true;
            })
            .addCase(fetchExamQuestions.fulfilled, (state, action) => {
                if (action.payload) {
                    state.questions = action.payload.data;
                    state.pagination = action.payload.pagination;
                }
                state.loading = false;
            })
            .addCase(fetchExamQuestions.rejected, (state) => {
                state.questions = [];
                state.loading = false;
            })

            .addCase(fetchExamQuestionsWithoutPagination.pending, (state) => {
                state.questionsExam = [];
            })
            .addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {
                if (action.payload) {
                    state.questionsExam = action.payload.data;
                }
            })
            .addCase(fetchExamQuestionsWithoutPagination.rejected, (state) => {
                state.questionsExam = [];
            })


            .addCase(fetchQuestionById.pending, (state) => {
                state.question = null;
            })
            .addCase(fetchQuestionById.fulfilled, (state, action) => {
                if (action.payload) {
                    state.question = action.payload.data;
                }
            })
            .addCase(fetchQuestionById.rejected, (state) => {
                state.question = null;
            })
            .addCase(fetchPublicQuestionById.pending, (state) => {
                state.question = null;
                state.loading = true;
            })
            .addCase(fetchPublicQuestionById.fulfilled, (state, action) => {
                if (action.payload) {
                    state.question = action.payload.data;
                }
                state.loading = false;
            })
            .addCase(fetchPublicQuestionById.rejected, (state) => {
                state.question = null;
                state.loading = false;
            })
            .addCase(fetchPublicQuestionsByExamId.pending, (state, action) => {
                state.questions = [];
            })
            .addCase(fetchPublicQuestionsByExamId.fulfilled, (state, action) => {
                if (action.payload) {
                    state.questions = action.payload.questions;
                }
            })
            .addCase(fetchPublicQuestionsByExamId.rejected, (state) => {
                state.questions = [];
            })
            .addCase(findQuestions.pending, (state) => {
                state.questionsSearch = [];
                if (state.firstSearch) {
                    state.firstSearch = false;
                }
                state.loadingSearch = true;
            })
            .addCase(findQuestions.fulfilled, (state, action) => {
                if (action.payload) {
                    state.questionsSearch = action.payload.data;
                }
                state.loadingSearch = false;
            })
            .addCase(findQuestions.rejected, (state) => {
                state.questionsSearch = [];
                state.loadingSearch = false;
            })
    },
});

export const {
    setQuestion,
    setQuestions,
    setClass,
    setCurrentPage,
    setLimit,
    setSortOrder,
    setLoading,
    setSearch
} = questionSlice.actions;
export default questionSlice.reducer;
