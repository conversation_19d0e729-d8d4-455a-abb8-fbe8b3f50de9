/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const GithubRepositoryInType = {
  Github: "github",
} as const;
export type GithubRepositoryInType = ClosedEnum<typeof GithubRepositoryInType>;

export type GithubRepositoryIn = {
  type?: GithubRepositoryInType | undefined;
  name: string;
  owner: string;
  ref?: string | null | undefined;
  weight?: number | undefined;
  token: string;
};

/** @internal */
export const GithubRepositoryInType$inboundSchema: z.ZodNativeEnum<
  typeof GithubRepositoryInType
> = z.nativeEnum(GithubRepositoryInType);

/** @internal */
export const GithubRepositoryInType$outboundSchema: z.ZodNativeEnum<
  typeof GithubRepositoryInType
> = GithubRepositoryInType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace GithubRepositoryInType$ {
  /** @deprecated use `GithubRepositoryInType$inboundSchema` instead. */
  export const inboundSchema = GithubRepositoryInType$inboundSchema;
  /** @deprecated use `GithubRepositoryInType$outboundSchema` instead. */
  export const outboundSchema = GithubRepositoryInType$outboundSchema;
}

/** @internal */
export const GithubRepositoryIn$inboundSchema: z.ZodType<
  GithubRepositoryIn,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: GithubRepositoryInType$inboundSchema.default("github"),
  name: z.string(),
  owner: z.string(),
  ref: z.nullable(z.string()).optional(),
  weight: z.number().default(1),
  token: z.string(),
});

/** @internal */
export type GithubRepositoryIn$Outbound = {
  type: string;
  name: string;
  owner: string;
  ref?: string | null | undefined;
  weight: number;
  token: string;
};

/** @internal */
export const GithubRepositoryIn$outboundSchema: z.ZodType<
  GithubRepositoryIn$Outbound,
  z.ZodTypeDef,
  GithubRepositoryIn
> = z.object({
  type: GithubRepositoryInType$outboundSchema.default("github"),
  name: z.string(),
  owner: z.string(),
  ref: z.nullable(z.string()).optional(),
  weight: z.number().default(1),
  token: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace GithubRepositoryIn$ {
  /** @deprecated use `GithubRepositoryIn$inboundSchema` instead. */
  export const inboundSchema = GithubRepositoryIn$inboundSchema;
  /** @deprecated use `GithubRepositoryIn$outboundSchema` instead. */
  export const outboundSchema = GithubRepositoryIn$outboundSchema;
  /** @deprecated use `GithubRepositoryIn$Outbound` instead. */
  export type Outbound = GithubRepositoryIn$Outbound;
}

export function githubRepositoryInToJSON(
  githubRepositoryIn: GithubRepositoryIn,
): string {
  return JSON.stringify(
    GithubRepositoryIn$outboundSchema.parse(githubRepositoryIn),
  );
}

export function githubRepositoryInFromJSON(
  jsonString: string,
): SafeParseResult<GithubRepositoryIn, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => GithubRepositoryIn$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'GithubRepositoryIn' from JSON`,
  );
}
