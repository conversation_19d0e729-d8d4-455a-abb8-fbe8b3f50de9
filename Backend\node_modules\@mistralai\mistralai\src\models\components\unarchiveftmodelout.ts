/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const UnarchiveFTModelOutObject = {
  Model: "model",
} as const;
export type UnarchiveFTModelOutObject = ClosedEnum<
  typeof UnarchiveFTModelOutObject
>;

export type UnarchiveFTModelOut = {
  id: string;
  object?: UnarchiveFTModelOutObject | undefined;
  archived?: boolean | undefined;
};

/** @internal */
export const UnarchiveFTModelOutObject$inboundSchema: z.ZodNativeEnum<
  typeof UnarchiveFTModelOutObject
> = z.nativeEnum(UnarchiveFTModelOutObject);

/** @internal */
export const UnarchiveFTModelOutObject$outboundSchema: z.ZodNativeEnum<
  typeof UnarchiveFTModelOutObject
> = UnarchiveFTModelOutObject$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UnarchiveFTModelOutObject$ {
  /** @deprecated use `UnarchiveFTModelOutObject$inboundSchema` instead. */
  export const inboundSchema = UnarchiveFTModelOutObject$inboundSchema;
  /** @deprecated use `UnarchiveFTModelOutObject$outboundSchema` instead. */
  export const outboundSchema = UnarchiveFTModelOutObject$outboundSchema;
}

/** @internal */
export const UnarchiveFTModelOut$inboundSchema: z.ZodType<
  UnarchiveFTModelOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  object: UnarchiveFTModelOutObject$inboundSchema.default("model"),
  archived: z.boolean().default(false),
});

/** @internal */
export type UnarchiveFTModelOut$Outbound = {
  id: string;
  object: string;
  archived: boolean;
};

/** @internal */
export const UnarchiveFTModelOut$outboundSchema: z.ZodType<
  UnarchiveFTModelOut$Outbound,
  z.ZodTypeDef,
  UnarchiveFTModelOut
> = z.object({
  id: z.string(),
  object: UnarchiveFTModelOutObject$outboundSchema.default("model"),
  archived: z.boolean().default(false),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UnarchiveFTModelOut$ {
  /** @deprecated use `UnarchiveFTModelOut$inboundSchema` instead. */
  export const inboundSchema = UnarchiveFTModelOut$inboundSchema;
  /** @deprecated use `UnarchiveFTModelOut$outboundSchema` instead. */
  export const outboundSchema = UnarchiveFTModelOut$outboundSchema;
  /** @deprecated use `UnarchiveFTModelOut$Outbound` instead. */
  export type Outbound = UnarchiveFTModelOut$Outbound;
}

export function unarchiveFTModelOutToJSON(
  unarchiveFTModelOut: UnarchiveFTModelOut,
): string {
  return JSON.stringify(
    UnarchiveFTModelOut$outboundSchema.parse(unarchiveFTModelOut),
  );
}

export function unarchiveFTModelOutFromJSON(
  jsonString: string,
): SafeParseResult<UnarchiveFTModelOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UnarchiveFTModelOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UnarchiveFTModelOut' from JSON`,
  );
}
