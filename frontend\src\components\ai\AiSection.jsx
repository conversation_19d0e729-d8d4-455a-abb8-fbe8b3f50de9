import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { askQuestionWithAI, resetAiResponse } from "src/features/ai/aiSlice";
import { Bo<PERSON>, ChevronUp, RefreshCcw } from "lucide-react";
import { BotMessageSquare } from "lucide-react";
import ArticleContent from "../article/ArticleContent";
import QuestionDropdown from "./QuestionDropdown";

// AI Question Section Component
const AIQuestionSection = () => {
    const { question } = useSelector((state) => state.questions);
    const { userQuestion, aiResponse, loading: aiLoading } = useSelector((state) => state.ai);
    const dispatch = useDispatch();
    const [selectedMessageId, setSelectedMessageId] = useState(null);

    const handleAskAI = (messageId) => {
        if (question?.id) {
            // Reset previous response if selecting different question
            if (selectedMessageId !== messageId) {
                dispatch(resetAiResponse());
            }
            setSelectedMessageId(messageId);
            dispatch(askQuestionWithAI({ questionId: question.id, messageId }));
        }
    };

    if (!question) return null;

    return (
        <div className=" bg-white rounded-md border border-gray-200 shadow-sm overflow-hidden">
            {/* Header */}
            <div
                className="flex items-center justify-between p-3 bg-gradient-to-r border-b border-gray-100 cursor-pointer from-sky-100 via-blue-100 to-indigo-100 transition-all duration-200"
            >
                <div className="flex items-center gap-2.5">
                    <div className="p-1 bg-white rounded-lg shadow-sm">
                        <Bot className="text-sky-600" size={14} />
                    </div>
                    <h3 className="text-sm font-semibold text-gray-800">Hỏi AI</h3>
                </div>
                <ChevronUp
                    className={` transition-all duration-200 rotate-180 text-sky-600`}
                    size={16}
                />
            </div>

            {/* Content */}
            <div className="p-4 space-y-4">
                {/* Question Options */}
                <div>
                    <h4 className="text-xs font-medium text-gray-600 mb-2.5 uppercase tracking-wide">Chọn câu hỏi:</h4>
                    <div className="grid grid-cols-1 gap-2">
                        <QuestionDropdown
                            userQuestion={userQuestion}
                            selectedMessageId={selectedMessageId}
                            aiLoading={aiLoading}
                            aiResponse={aiResponse}
                            handleAskAI={handleAskAI}
                        />
                    </div>
                </div>

                {/* AI Response */}
                {aiResponse && selectedMessageId && (
                    <div className=" p-2 bg-sky-50 rounded-xl border border-sky-100 shadow-sm">
                        <div className="flex items-center gap-2 mb-3">
                            <div className="p-1.5 bg-white rounded-lg shadow-sm">
                                <BotMessageSquare className="text-sky-600" size={14} />
                            </div>
                            <h5 className="text-xs font-semibold text-sky-800 uppercase tracking-wide">Phản hồi từ AI</h5>
                        </div>
                        <ArticleContent content={aiResponse} />
                    </div>
                )}

                {/* Loading State */}
                {aiLoading && (
                    <div className="flex items-center justify-center py-6">
                        <div className="flex items-center gap-2.5 px-4 py-2 bg-sky-50 rounded-full border border-sky-100">
                            <RefreshCcw size={14} className="text-sky-600 animate-spin" />
                            <span className="text-xs font-medium text-sky-700">AI đang phân tích...</span>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AIQuestionSection;
