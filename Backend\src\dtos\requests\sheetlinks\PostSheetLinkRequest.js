import Joi from "joi";

class PostSheetLinkRequest {
    constructor(data) {
        this.title = data.title;
        this.description = data.description;
        this.sheetUrl = data.sheetUrl;
        this.category = data.category;
        this.classId = data.classId;
        this.accessLevel = data.accessLevel;
    }

    static validate(data) {
        const schema = Joi.object({
            title: Joi.string().required().min(1).max(255).messages({
                'string.empty': 'Tiêu đề không được để trống',
                'string.min': 'Tiêu đề phải có ít nhất 1 ký tự',
                'string.max': 'Tiêu đề không được vượt quá 255 ký tự',
                'any.required': 'Tiêu đề là bắt buộc'
            }),
            description: Joi.string().optional().allow('').max(1000).messages({
                'string.max': '<PERSON><PERSON> tả không được vượt quá 1000 ký tự'
            }),
            sheetUrl: Joi.string().uri().required().messages({
                'string.uri': 'Link sheet phải là URL hợp lệ',
                'any.required': 'Link sheet là bắt buộc'
            }),
            category: Joi.string().optional().allow('').max(100).messages({
                'string.max': 'Danh mục không được vượt quá 100 ký tự'
            }),
            classId: Joi.number().integer().positive().optional().allow(null).messages({
                'number.integer': 'ID lớp học phải là số nguyên',
                'number.positive': 'ID lớp học phải là số dương'
            }),
            accessLevel: Joi.string().valid('PUBLIC', 'CLASS_ONLY', 'ADMIN_ONLY').optional().messages({
                'any.only': 'Mức độ truy cập phải là PUBLIC, CLASS_ONLY hoặc ADMIN_ONLY'
            })
        });

        return schema.validate(data);
    }
}

export default PostSheetLinkRequest;
