import { use, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchUsers } from "../../features/user/userSlice";
import { setSortOrder } from "../../features/user/userSlice";
import LoadingSpinner from "../loading/LoadingSpinner";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { TotalComponent } from "./TotalComponent";
import LoadingData from "../loading/LoadingData";

const UserList = () => {
    const dispatch = useDispatch();
    const { users, loading, pagination, graduationYearFilter, gradeFilter, classFilter, search } = useSelector((state) => state.users);
    const navigate = useNavigate();
    const { user } = useSelector((state) => state.auth);

    useEffect(() => {
        dispatch(fetchUsers({
            search,
            currentPage: pagination.page,
            limit: pagination.pageSize,
            sortOrder: pagination.sortOrder,
            graduationYear: graduationYearFilter,
            gradeFilter,
            classFilter
        }));
    }, [dispatch, search, pagination.page, pagination.pageSize, pagination.sortOrder, graduationYearFilter, gradeFilter, classFilter]);

    // useEffect(() => {
    //     console.log(users);
    // }, [users]);


    return (
        <LoadingData
            loading={loading}
            isNoData={users.length > 0 ? false : true}
            loadText="Đang tải danh sách học viên"
            noDataText="Không có học viên nào."
        >

            <div className="flex flex-col gap-4 min-h-0 text-sm">
                <TotalComponent
                    total={pagination.total}
                    page={pagination.page}
                    pageSize={pagination.pageSize}
                    setSortOrder={() => dispatch(setSortOrder())}
                />
                <div className="flex-grow h-[70vh] overflow-y-auto hide-scrollbar">
                    <table className="w-full border-collapse border border-[#E7E7ED]">
                        <thead className="bg-[#F6FAFD] sticky top-0 z-10">
                            <tr className="border border-[#E7E7ED]">
                                <th className="p-3 text-center">ID</th>
                                {user?.id === 1 && (
                                    <th className="p-3 text-center">Avatar</th>
                                )}
                                <th className="p-3 text-center">Họ và Tên</th>
                                <th className="p-3 text-center">Kiểu</th>
                                <th className="p-3 text-center">Số Điện Thoại</th>
                                <th className="p-3 text-center">Trường Học</th>
                                <th className="p-3 text-center">Lớp</th>
                                <th className="p-3 text-center">Năm tốt nghiệp</th>
                                <th className="p-3 text-center">Tài khoản</th>
                                <th className="p-3 text-center">Mật khẩu</th>
                                <th className="p-3 text-center">Lớp</th>
                                <th className="p-3 text-center">Ngày tham gia</th>
                            </tr>
                        </thead>
                        <tbody>
                            {users.map((student, index) => (
                                <tr
                                    onClick={() => navigate(`/admin/student-management/${student.id}`)}
                                    key={student.id} className="border border-[#E7E7ED] hover:bg-gray-50 cursor-pointer">
                                    <td className="p-3 text-center">{student.id}</td>
                                    {user?.id === 1 && (
                                        <td className="p-3 text-center">
                                            <img
                                                src={student.avatarUrl || "https://via.placeholder.com/50"}
                                                alt="Avatar"
                                                className="w-12 h-12 rounded-full object-cover"
                                            />
                                        </td>
                                    )}
                                    <td className="p-3 text-center">{student.lastName} {student.firstName}</td>
                                    <td className="p-3 text-center">{student.userType}</td>
                                    <td className="p-3 text-center">{student.phone || "Chưa có"}</td>
                                    <td className="p-3 text-center">{student.highSchool || "Chưa cập nhật"}</td>
                                    <td className="p-3 text-center">{student.class || "Chưa cập nhật"}</td>
                                    <td className="p-3 text-center">{student.graduationYear || "Chưa cập nhật"}</td>
                                    <td className="p-3 text-center">{student.username}</td>
                                    <td className="p-3 text-center">{student.password?.length > 10 ? student.password?.substring(0, 10) + "..." : student.password}</td>
                                    <td className="p-3 text-center">{student.classStatuses?.length > 0 ? (
                                        student.classStatuses?.map((cls, idx) => (
                                            <span key={idx} className="block">{cls.class.name}</span>
                                        ))
                                    ) : "Chưa cập nhật"}</td>
                                    <td className="p-3 text-center">
                                        {new Date(student.createdAt).toLocaleDateString()}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </LoadingData>
    );

};

export default UserList;
