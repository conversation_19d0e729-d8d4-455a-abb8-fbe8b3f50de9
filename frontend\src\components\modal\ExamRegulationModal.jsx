import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import {
    Clock,
    ScrollText,
    AlertTriangle,
    BookOpenCheck,
    X,
    Check,
} from 'lucide-react';

const ExamRegulationModal = ({ isOpen, onClose, onStartExam }) => {
    const [agreed, setAgreed] = useState(false);
    const { exam, darkMode } = useSelector((state) => state.doExam);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
            <div className={`w-[90%] max-w-2xl p-6 rounded-lg shadow-2xl max-h-[90vh] overflow-hidden flex flex-col
        ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-black'}
      `}>
                <h2 className="text-2xl font-bold mb-4 text-center flex items-center justify-center gap-2">
                    <ScrollText size={24} />
                    Quy chế thi môn Toán năm 2025
                </h2>

                <div className={`overflow-y-auto pr-2 hide-scrollbar space-y-4 p-2 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                    <p className="flex items-center gap-2">
                        <Clock size={18} className="text-sky-600" />
                        <strong>Thời gian làm bài:</strong> {exam?.testDuration ? exam?.testDuration + ' phút' : 'Vô thời hạn'}
                    </p>

                    <p className="flex items-center gap-2">
                        <ScrollText size={18} className="text-blue-500" />
                        <strong>Hình thức thi:</strong> Thi trắc nghiệm <span className="text-red-500 font-semibold">trực tuyến</span>
                    </p>

                    <div className={`p-4 rounded text-sm border flex gap-2 items-start
            ${darkMode
                            ? 'bg-yellow-900 text-yellow-200 border-yellow-600'
                            : 'bg-yellow-100 border-yellow-400 text-yellow-800'}
          `}>
                        <AlertTriangle className="min-w-[20px] mt-1" size={20} />
                        <div>
                            <strong>Lưu ý quan trọng:</strong><br />
                            - Khi bắt đầu thi, hệ thống sẽ <strong>bật chế độ toàn màn hình</strong>.<br />
                            - Mọi hành vi nghi vấn như chuyển tab, thoát toàn màn hình, sao chép nội dung đều sẽ bị <strong>ghi log</strong> và theo dõi.<br />
                            - Yêu cầu học sinh <span className="font-semibold text-red-500">tập trung trong toàn bộ thời gian làm bài</span>.
                        </div>
                    </div>

                    <p className="font-semibold flex items-center gap-2">
                        <BookOpenCheck size={18} className="text-green-600" />
                        Cấu trúc đề thi:
                    </p>

                    <ul className="list-disc ml-6 space-y-2 text-sm">
                        <li><strong>Phần I:</strong> 12 câu trắc nghiệm nhiều lựa chọn (mỗi câu 0.25 điểm)</li>
                        <li>
                            <strong>Phần II:</strong> 4 câu trắc nghiệm Đúng/Sai (mỗi câu có 4 ý):
                            <ul className="list-[circle] ml-5 mt-1 space-y-1">
                                <li>Chọn đúng 1 ý: 0.1 điểm</li>
                                <li>Chọn đúng 2 ý: 0.25 điểm</li>
                                <li>Chọn đúng 3 ý: 0.5 điểm</li>
                                <li>Chọn đúng cả 4 ý: 1 điểm</li>
                            </ul>
                        </li>
                        <li><strong>Phần III:</strong> 6 câu trả lời ngắn (mỗi câu 0.5 điểm)</li>
                    </ul>
                </div>

                {/* Đồng ý quy chế */}
                <div className="mt-6">
                    <label className="flex items-center space-x-2 text-sm">
                        <input
                            type="checkbox"
                            checked={agreed}
                            onChange={(e) => setAgreed(e.target.checked)}
                            className="w-4 h-4 accent-sky-600"
                        />
                        <span>Tôi đã đọc và đồng ý với quy chế thi</span>
                    </label>
                </div>

                {/* Nút hành động */}
                <div className="flex justify-between mt-6">
                    <button
                        onClick={onClose}
                        className={`px-4 py-2 text-sm rounded flex items-center gap-2 transition
              ${darkMode
                                ? 'bg-gray-600 hover:bg-gray-500 text-white'
                                : 'bg-gray-300 hover:bg-gray-400 text-black'}
            `}
                    >
                        <X size={16} />
                        Đóng
                    </button>

                    <button
                        disabled={!agreed}
                        onClick={onStartExam}
                        className={`px-4 py-2 rounded text-sm font-semibold flex items-center gap-2 transition
              ${agreed
                                ? 'bg-green-600 hover:bg-green-700 text-white'
                                : 'bg-gray-400 text-white cursor-not-allowed'}
            `}
                    >
                        <Check size={16} />
                        Làm bài
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ExamRegulationModal;
