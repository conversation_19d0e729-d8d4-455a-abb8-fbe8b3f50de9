import db from '../models/index.js';
import { Op } from 'sequelize';

/**
 * Create a new notification
 * @param {Object} notificationData - Notification data
 * @returns {Promise<Object>} Created notification
 */
export const createNotification = async (notificationData) => {
  try {
    const notification = await db.Notification.create(notificationData);
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

/**
 * Create notifications for multiple users
 * @param {Array<number>} userIds - Array of user IDs
 * @param {Object} notificationData - Notification data (without userId)
 * @returns {Promise<Array<Object>>} Created notifications
 */
export const createNotificationsForUsers = async (userIds, notificationData) => {
  try {
    const notifications = await Promise.all(
      userIds.map(userId =>
        db.Notification.create({
          ...notificationData,
          userId
        })
      )
    );
    return notifications;
  } catch (error) {
    console.error('Error creating notifications for users:', error);
    throw error;
  }
};

/**
 * Get notifications for a user
 * @param {number} userId - User ID
 * @param {Object} options - Query options
 * @returns {Promise<Object>} Notifications with pagination
 */
export const getUserNotifications = async (userId, options = {}) => {
  try {
    const {
      limit = 10,
      offset = 0,
      isRead = null,
      type = null,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = options;

    // Build where clause
    const whereClause = { userId };

    if (isRead !== null) {
      whereClause.isRead = isRead;
    }

    if (type) {
      whereClause.type = type;
    }

    // Query notifications
    const { rows: notifications, count: total } = await db.Notification.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [[sortBy, sortOrder]],
    });
    // Calculate current page for backward compatibility
    const page = Math.floor(offset / limit) + 1;

    return {
      notifications,
      pagination: {
        total,
        page,
        limit,
        offset,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('Error getting user notifications:', error);
    throw error;
  }
};

/**
 * Mark notifications as read
 * @param {number} userId - User ID
 * @param {Array<number>} notificationIds - Array of notification IDs to mark as read
 * @returns {Promise<number>} Number of updated notifications
 */
export const markNotificationsAsRead = async (userId, notificationIds) => {
  try {
    const whereClause = {
      userId,
      id: { [Op.in]: notificationIds }
    };

    const [updatedCount] = await db.Notification.update(
      { isRead: true },
      { where: whereClause }
    );

    return updatedCount;
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    throw error;
  }
};

/**
 * Mark all notifications as read for a user
 * @param {number} userId - User ID
 * @returns {Promise<number>} Number of updated notifications
 */
export const markAllNotificationsAsRead = async (userId) => {
  try {
    const [updatedCount] = await db.Notification.update(
      { isRead: true },
      { where: { userId, isRead: false } }
    );

    return updatedCount;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

/**
 * Delete notifications
 * @param {number} userId - User ID
 * @param {Array<number>} notificationIds - Array of notification IDs to delete
 * @returns {Promise<number>} Number of deleted notifications
 */
export const deleteNotifications = async (userId, notificationIds) => {
  try {
    const deletedCount = await db.Notification.destroy({
      where: {
        userId,
        id: { [Op.in]: notificationIds }
      }
    });

    return deletedCount;
  } catch (error) {
    console.error('Error deleting notifications:', error);
    throw error;
  }
};

/**
 * Delete a single notification
 * @param {number} userId - User ID
 * @param {number} notificationId - Notification ID to delete
 * @returns {Promise<number>} Number of deleted notifications (0 or 1)
 */
export const deleteNotification = async (userId, notificationId) => {
  try {
    const deletedCount = await db.Notification.destroy({
      where: {
        userId,
        id: notificationId
      }
    });

    return deletedCount;
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
};

/**
 * Get unread notification count for a user
 * @param {number} userId - User ID
 * @returns {Promise<number>} Unread notification count
 */
export const getUnreadNotificationCount = async (userId) => {
  try {
    const count = await db.Notification.count({
      where: {
        userId,
        isRead: false
      }
    });
    // console.log('Unread notification count:', count);

    return count;
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    throw error;
  }
};

/**
 * Delete old notifications (older than specified days)
 * @param {number} days - Number of days to keep notifications (default: 30)
 * @returns {Promise<number>} Number of deleted notifications
 */
export const deleteOldNotifications = async (days = 30) => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    console.log(`Deleting notifications older than ${cutoffDate.toISOString()}`);

    const deletedCount = await db.Notification.destroy({
      where: {
        createdAt: {
          [Op.lt]: cutoffDate
        }
      }
    });

    console.log(`Successfully deleted ${deletedCount} old notifications`);
    return deletedCount;
  } catch (error) {
    console.error('Error deleting old notifications:', error);
    throw error;
  }
};
