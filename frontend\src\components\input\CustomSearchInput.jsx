import React, { useEffect, useRef } from 'react';

const CustomSearchInput = ({
    placeholder = 'Tìm kiếm...',
    value,
    onChange,
    onEnter, // 👈 nhận hàm callback khi Enter
}) => {
    const inputRef = useRef(null);

    useEffect(() => {
        const handleCtrlF = (e) => {
            const isMac = navigator.platform.toUpperCase().includes('MAC');
            const isCtrlOrCmd = isMac ? e.metaKey : e.ctrlKey;

            if (isCtrlOrCmd && e.key.toLowerCase() === 'f') {
                e.preventDefault();
                const input = inputRef.current;
                if (input) {
                    input.focus();
                    input.select(); // 👈 Bôi đen toàn bộ nội dung input
                }
            }
        };

        window.addEventListener('keydown', handleCtrlF);
        return () => window.removeEventListener('keydown', handleCtrlF);
    }, []);

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && typeof onEnter === 'function') {
            onEnter();
        }
    };

    return (
        <div className="w-[300px] relative">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                className="absolute left-[1rem] top-1/2 transform -translate-y-1/2"
            >
                <path
                    d="M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z"
                    stroke="#131214"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </svg>
            <input
                ref={inputRef}
                type="text"
                placeholder={placeholder}
                value={value}
                onChange={onChange}
                onKeyDown={handleKeyDown} // 👈 xử lý Enter
                className="w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam"
            />
        </div>
    );
};

export default CustomSearchInput;
