import axios from 'axios';
import path from 'path';
import fs from 'fs';
import { uploadImage } from '../utils/imageUpload.js'; // hoặc đường dẫn thực tế của bạn
import { Buffer } from 'buffer';
import sharp from 'sharp';

/**
 * Lấy ảnh từ URL và chuyển thành base64 string để gửi GPT-4o
 * @param {string} imageUrl - Đường dẫn ảnh từ Firebase hoặc nơi khác
 * @returns {Promise<string>} - Chuỗi base64 có header: data:image/png;base64,...
 */
export const getImageBase64FromUrl = async (imageUrl) => {
    try {
        const response = await axios.get(imageUrl, {
            responseType: 'arraybuffer',
            timeout: 10000, // tránh treo
        });

        const contentType = response.headers['content-type'];

        // Kiểm tra MIME type có phải là ảnh không
        if (!contentType || !contentType.startsWith('image/')) {
            console.warn('❌ MIME type không hợp lệ:', contentType, imageUrl);
            throw new Error('Invalid MIME type: Only image types are supported.');
        }

        const base64 = Buffer.from(response.data, 'binary').toString('base64');
        return `data:${contentType};base64,${base64}`;
    } catch (error) {
        console.error('❌ Lỗi khi tải ảnh:', imageUrl, error.message);
        throw new Error('Không thể tải ảnh từ URL');
    }
};



/**
 * Convert image buffer to base64 JPEG string
 * @param {Object} file - req.file object from multer
 * @returns {Promise<{ base64: string, type: string }>}
 */
export const encodeImage = async (file) => {
    try {
        let buffer = file.buffer;
        let outputBuffer = buffer;
        let outputType = file.mimetype;

        // Nếu là PNG → chuyển sang JPEG
        if (file.mimetype === 'image/png') {
            outputBuffer = await sharp(buffer)
                .jpeg() // có thể giảm chất lượng nếu muốn
                .toBuffer();
            outputType = 'image/jpeg';
        }

        const base64 = outputBuffer.toString('base64');

        return {
            base64,
            type: outputType, // quan trọng để truyền đúng MIME vào performOcr
        };
    } catch (error) {
        console.error('Lỗi encodeImage:', error.message);
        return null;
    }
};




export const base64ToImage = async (base64) => {
    const matches = base64.match(/^data:(image\/\w+);base64,(.+)$/);
    const base64Data = matches ? matches[2] : base64;
    const buffer = Buffer.from(base64Data, 'base64');

    return buffer;
}


export async function uploadBase64Image(base64, folder) {
    const matches = base64.match(/^data:(image\/\w+);base64,(.+)$/);
    const base64Data = matches ? matches[2] : base64;
    const buffer = Buffer.from(base64Data, 'base64');

    const imageUrl = await uploadImage(buffer, folder);
    if (!imageUrl) {
        throw new Error('Upload failed');
    }

    return imageUrl;
}

/**
 * Upload nhiều ảnh base64 lên Firebase
 * @param {string[]} images - Mảng base64
 * @param {string} folder - Tên folder lưu ảnh
 * @returns {Promise<string[]>} - Mảng URL đã upload
 */
export async function uploadBase64Images(images, folder) {
    if (!Array.isArray(images) || images.length === 0) {
        throw new Error("Missing images array");
    }

    if (!folder) {
        throw new Error("Missing folder");
    }

    const uploadedUrls = [];

    for (let i = 0; i < images.length; i++) {
        const base64 = images[i];
        const imageUrl = await uploadBase64Image(base64, folder); // Sử dụng hàm uploadBase64Image đã định nghĩa ở trên
        if (!imageUrl) {
            throw new Error(`Upload failed for image ${i + 1}`);
        }

        uploadedUrls.push(imageUrl);
    }

    return uploadedUrls;
}