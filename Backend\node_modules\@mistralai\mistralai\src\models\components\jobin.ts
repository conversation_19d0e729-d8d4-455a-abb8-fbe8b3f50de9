/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ClassifierTargetIn,
  ClassifierTargetIn$inboundSchema,
  ClassifierTargetIn$Outbound,
  ClassifierTargetIn$outboundSchema,
} from "./classifiertargetin.js";
import {
  ClassifierTrainingParametersIn,
  ClassifierTrainingParametersIn$inboundSchema,
  ClassifierTrainingParametersIn$Outbound,
  ClassifierTrainingParametersIn$outboundSchema,
} from "./classifiertrainingparametersin.js";
import {
  CompletionTrainingParametersIn,
  CompletionTrainingParametersIn$inboundSchema,
  CompletionTrainingParametersIn$Outbound,
  CompletionTrainingParametersIn$outboundSchema,
} from "./completiontrainingparametersin.js";
import {
  FineTuneableModelType,
  FineTuneableModelType$inboundSchema,
  FineTuneableModelType$outboundSchema,
} from "./finetuneablemodeltype.js";
import {
  GithubRepositoryIn,
  GithubRepositoryIn$inboundSchema,
  GithubRepositoryIn$Outbound,
  GithubRepositoryIn$outboundSchema,
} from "./githubrepositoryin.js";
import {
  TrainingFile,
  TrainingFile$inboundSchema,
  TrainingFile$Outbound,
  TrainingFile$outboundSchema,
} from "./trainingfile.js";
import {
  WandbIntegration,
  WandbIntegration$inboundSchema,
  WandbIntegration$Outbound,
  WandbIntegration$outboundSchema,
} from "./wandbintegration.js";

export type JobInIntegrations = WandbIntegration;

export type Hyperparameters =
  | ClassifierTrainingParametersIn
  | CompletionTrainingParametersIn;

export type JobInRepositories = GithubRepositoryIn;

export type JobIn = {
  /**
   * The name of the model to fine-tune.
   */
  model: string;
  trainingFiles?: Array<TrainingFile> | undefined;
  /**
   * A list containing the IDs of uploaded files that contain validation data. If you provide these files, the data is used to generate validation metrics periodically during fine-tuning. These metrics can be viewed in `checkpoints` when getting the status of a running fine-tuning job. The same data should not be present in both train and validation files.
   */
  validationFiles?: Array<string> | null | undefined;
  /**
   * A string that will be added to your fine-tuning model name. For example, a suffix of "my-great-model" would produce a model name like `ft:open-mistral-7b:my-great-model:xxx...`
   */
  suffix?: string | null | undefined;
  /**
   * A list of integrations to enable for your fine-tuning job.
   */
  integrations?: Array<WandbIntegration> | null | undefined;
  /**
   * This field will be required in a future release.
   */
  autoStart?: boolean | undefined;
  invalidSampleSkipPercentage?: number | undefined;
  jobType?: FineTuneableModelType | null | undefined;
  hyperparameters:
    | ClassifierTrainingParametersIn
    | CompletionTrainingParametersIn;
  repositories?: Array<GithubRepositoryIn> | null | undefined;
  classifierTargets?: Array<ClassifierTargetIn> | null | undefined;
};

/** @internal */
export const JobInIntegrations$inboundSchema: z.ZodType<
  JobInIntegrations,
  z.ZodTypeDef,
  unknown
> = WandbIntegration$inboundSchema;

/** @internal */
export type JobInIntegrations$Outbound = WandbIntegration$Outbound;

/** @internal */
export const JobInIntegrations$outboundSchema: z.ZodType<
  JobInIntegrations$Outbound,
  z.ZodTypeDef,
  JobInIntegrations
> = WandbIntegration$outboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobInIntegrations$ {
  /** @deprecated use `JobInIntegrations$inboundSchema` instead. */
  export const inboundSchema = JobInIntegrations$inboundSchema;
  /** @deprecated use `JobInIntegrations$outboundSchema` instead. */
  export const outboundSchema = JobInIntegrations$outboundSchema;
  /** @deprecated use `JobInIntegrations$Outbound` instead. */
  export type Outbound = JobInIntegrations$Outbound;
}

export function jobInIntegrationsToJSON(
  jobInIntegrations: JobInIntegrations,
): string {
  return JSON.stringify(
    JobInIntegrations$outboundSchema.parse(jobInIntegrations),
  );
}

export function jobInIntegrationsFromJSON(
  jsonString: string,
): SafeParseResult<JobInIntegrations, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => JobInIntegrations$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'JobInIntegrations' from JSON`,
  );
}

/** @internal */
export const Hyperparameters$inboundSchema: z.ZodType<
  Hyperparameters,
  z.ZodTypeDef,
  unknown
> = z.union([
  ClassifierTrainingParametersIn$inboundSchema,
  CompletionTrainingParametersIn$inboundSchema,
]);

/** @internal */
export type Hyperparameters$Outbound =
  | ClassifierTrainingParametersIn$Outbound
  | CompletionTrainingParametersIn$Outbound;

/** @internal */
export const Hyperparameters$outboundSchema: z.ZodType<
  Hyperparameters$Outbound,
  z.ZodTypeDef,
  Hyperparameters
> = z.union([
  ClassifierTrainingParametersIn$outboundSchema,
  CompletionTrainingParametersIn$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Hyperparameters$ {
  /** @deprecated use `Hyperparameters$inboundSchema` instead. */
  export const inboundSchema = Hyperparameters$inboundSchema;
  /** @deprecated use `Hyperparameters$outboundSchema` instead. */
  export const outboundSchema = Hyperparameters$outboundSchema;
  /** @deprecated use `Hyperparameters$Outbound` instead. */
  export type Outbound = Hyperparameters$Outbound;
}

export function hyperparametersToJSON(
  hyperparameters: Hyperparameters,
): string {
  return JSON.stringify(Hyperparameters$outboundSchema.parse(hyperparameters));
}

export function hyperparametersFromJSON(
  jsonString: string,
): SafeParseResult<Hyperparameters, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Hyperparameters$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Hyperparameters' from JSON`,
  );
}

/** @internal */
export const JobInRepositories$inboundSchema: z.ZodType<
  JobInRepositories,
  z.ZodTypeDef,
  unknown
> = GithubRepositoryIn$inboundSchema;

/** @internal */
export type JobInRepositories$Outbound = GithubRepositoryIn$Outbound;

/** @internal */
export const JobInRepositories$outboundSchema: z.ZodType<
  JobInRepositories$Outbound,
  z.ZodTypeDef,
  JobInRepositories
> = GithubRepositoryIn$outboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobInRepositories$ {
  /** @deprecated use `JobInRepositories$inboundSchema` instead. */
  export const inboundSchema = JobInRepositories$inboundSchema;
  /** @deprecated use `JobInRepositories$outboundSchema` instead. */
  export const outboundSchema = JobInRepositories$outboundSchema;
  /** @deprecated use `JobInRepositories$Outbound` instead. */
  export type Outbound = JobInRepositories$Outbound;
}

export function jobInRepositoriesToJSON(
  jobInRepositories: JobInRepositories,
): string {
  return JSON.stringify(
    JobInRepositories$outboundSchema.parse(jobInRepositories),
  );
}

export function jobInRepositoriesFromJSON(
  jsonString: string,
): SafeParseResult<JobInRepositories, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => JobInRepositories$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'JobInRepositories' from JSON`,
  );
}

/** @internal */
export const JobIn$inboundSchema: z.ZodType<JobIn, z.ZodTypeDef, unknown> = z
  .object({
    model: z.string(),
    training_files: z.array(TrainingFile$inboundSchema).optional(),
    validation_files: z.nullable(z.array(z.string())).optional(),
    suffix: z.nullable(z.string()).optional(),
    integrations: z.nullable(z.array(WandbIntegration$inboundSchema))
      .optional(),
    auto_start: z.boolean().optional(),
    invalid_sample_skip_percentage: z.number().default(0),
    job_type: z.nullable(FineTuneableModelType$inboundSchema).optional(),
    hyperparameters: z.union([
      ClassifierTrainingParametersIn$inboundSchema,
      CompletionTrainingParametersIn$inboundSchema,
    ]),
    repositories: z.nullable(z.array(GithubRepositoryIn$inboundSchema))
      .optional(),
    classifier_targets: z.nullable(z.array(ClassifierTargetIn$inboundSchema))
      .optional(),
  }).transform((v) => {
    return remap$(v, {
      "training_files": "trainingFiles",
      "validation_files": "validationFiles",
      "auto_start": "autoStart",
      "invalid_sample_skip_percentage": "invalidSampleSkipPercentage",
      "job_type": "jobType",
      "classifier_targets": "classifierTargets",
    });
  });

/** @internal */
export type JobIn$Outbound = {
  model: string;
  training_files?: Array<TrainingFile$Outbound> | undefined;
  validation_files?: Array<string> | null | undefined;
  suffix?: string | null | undefined;
  integrations?: Array<WandbIntegration$Outbound> | null | undefined;
  auto_start?: boolean | undefined;
  invalid_sample_skip_percentage: number;
  job_type?: string | null | undefined;
  hyperparameters:
    | ClassifierTrainingParametersIn$Outbound
    | CompletionTrainingParametersIn$Outbound;
  repositories?: Array<GithubRepositoryIn$Outbound> | null | undefined;
  classifier_targets?: Array<ClassifierTargetIn$Outbound> | null | undefined;
};

/** @internal */
export const JobIn$outboundSchema: z.ZodType<
  JobIn$Outbound,
  z.ZodTypeDef,
  JobIn
> = z.object({
  model: z.string(),
  trainingFiles: z.array(TrainingFile$outboundSchema).optional(),
  validationFiles: z.nullable(z.array(z.string())).optional(),
  suffix: z.nullable(z.string()).optional(),
  integrations: z.nullable(z.array(WandbIntegration$outboundSchema)).optional(),
  autoStart: z.boolean().optional(),
  invalidSampleSkipPercentage: z.number().default(0),
  jobType: z.nullable(FineTuneableModelType$outboundSchema).optional(),
  hyperparameters: z.union([
    ClassifierTrainingParametersIn$outboundSchema,
    CompletionTrainingParametersIn$outboundSchema,
  ]),
  repositories: z.nullable(z.array(GithubRepositoryIn$outboundSchema))
    .optional(),
  classifierTargets: z.nullable(z.array(ClassifierTargetIn$outboundSchema))
    .optional(),
}).transform((v) => {
  return remap$(v, {
    trainingFiles: "training_files",
    validationFiles: "validation_files",
    autoStart: "auto_start",
    invalidSampleSkipPercentage: "invalid_sample_skip_percentage",
    jobType: "job_type",
    classifierTargets: "classifier_targets",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobIn$ {
  /** @deprecated use `JobIn$inboundSchema` instead. */
  export const inboundSchema = JobIn$inboundSchema;
  /** @deprecated use `JobIn$outboundSchema` instead. */
  export const outboundSchema = JobIn$outboundSchema;
  /** @deprecated use `JobIn$Outbound` instead. */
  export type Outbound = JobIn$Outbound;
}

export function jobInToJSON(jobIn: JobIn): string {
  return JSON.stringify(JobIn$outboundSchema.parse(jobIn));
}

export function jobInFromJSON(
  jsonString: string,
): SafeParseResult<JobIn, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => JobIn$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'JobIn' from JSON`,
  );
}
