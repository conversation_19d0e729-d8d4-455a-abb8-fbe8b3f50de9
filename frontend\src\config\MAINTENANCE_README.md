# Hệ thống <PERSON><PERSON><PERSON> trì (Maintenance System)

Hệ thống bảo trì cho phép bạn dễ dàng bật/tắt chế độ bảo trì cho toàn bộ website.

## 🚀 Cách sử dụng

### 1. Tắt chế độ bảo trì (FORCE DISABLE)

#### Cách 1: Force disable trong config file (Recommended)
```javascript
// Trong file src/config/maintenance.js
export const MAINTENANCE_CONFIG = {
    MAINTENANCE_MODE: false,
    FORCE_DISABLE: true,    // Đặt thành true để FORCE tắt hoàn toàn
    // ... các config khác
};
```
**Lưu ý**: `FORCE_DISABLE: true` sẽ override tất cả các cách bật khác và tự động clear localStorage.

### 2. Bật chế độ bảo trì thủ công

#### Cách 1: Thay đổi config file
```javascript
// Trong file src/config/maintenance.js
export const MAINTENANCE_CONFIG = {
    MAINTENANCE_MODE: true, // Đổi thành true để bật
    FORCE_DISABLE: false,   // Phải đặt thành false
    // ... các config khác
};
```

#### Cách 2: Sử dụng Developer Console (Development mode)
```javascript
// Mở Developer Console (F12) và chạy:
window.maintenanceUtils.enable()     // Bật chế độ bảo trì
window.maintenanceUtils.disable()    // Tắt chế độ bảo trì
window.maintenanceUtils.toggle()     // Chuyển đổi chế độ
window.maintenanceUtils.isActive()   // Kiểm tra trạng thái
```

#### Cách 3: Sử dụng localStorage
```javascript
// Bật chế độ bảo trì
localStorage.setItem('maintenanceMode', 'true');
window.location.reload();

// Tắt chế độ bảo trì
localStorage.removeItem('maintenanceMode');
window.location.reload();
```

### 2. Tự động bật khi có lỗi

Hệ thống sẽ tự động bật chế độ bảo trì khi:
- Gặp lỗi CORS từ backend
- Lỗi network/connection
- Backend không phản hồi

## 🔧 Cấu hình

### File config chính: `src/config/maintenance.js`

```javascript
export const MAINTENANCE_CONFIG = {
    // Toggle chính - đổi thành true để bật
    MAINTENANCE_MODE: false,
    
    // Tùy chỉnh nội dung
    TITLE: "Hệ thống đang bảo trì",
    DESCRIPTION: "Chúng tôi đang thực hiện bảo trì...",
    ESTIMATED_TIME: "15-30 phút",
    CONTACT_EMAIL: "<EMAIL>",
    
    // Tự động phát hiện từ API
    AUTO_DETECT_FROM_API: true,
    
    // Routes được bỏ qua (cho admin)
    BYPASS_ROUTES: [
        '/admin/maintenance',
        '/api/health'
    ]
};
```

### Tùy chỉnh giao diện

Chỉnh sửa file `src/pages/MaintenancePage.jsx` để thay đổi:
- Màu sắc
- Logo
- Nội dung
- Thời gian dự kiến
- Thông tin liên hệ

## 📋 Các tính năng

### ✅ Đã có
- [x] Bật/tắt chế độ bảo trì dễ dàng
- [x] Tự động phát hiện lỗi CORS/network
- [x] Bypass routes cho admin
- [x] Responsive design
- [x] Utilities cho development
- [x] Lưu trạng thái trong localStorage
- [x] Reload tự động khi thay đổi

### 🎯 Ưu điểm
- **Dễ sử dụng**: Chỉ cần đổi 1 biến thành true
- **Tự động**: Phát hiện lỗi và bật tự động
- **Linh hoạt**: Nhiều cách để bật/tắt
- **An toàn**: Có bypass routes cho admin
- **Responsive**: Hoạt động tốt trên mobile

## 🛠️ Development

### Testing
```javascript
// Test bật chế độ bảo trì
window.maintenanceUtils.enable();

// Test tắt chế độ bảo trì  
window.maintenanceUtils.disable();

// Kiểm tra trạng thái
console.log(window.maintenanceUtils.isActive());
```

### Debugging
- Mở Developer Console để xem logs
- Kiểm tra localStorage: `maintenanceMode`
- Xem network requests trong Network tab

## 📝 Lưu ý

1. **Production**: Chỉ đổi `MAINTENANCE_MODE: true` trong config
2. **Development**: Có thể dùng console utilities
3. **Emergency**: Dùng localStorage để bật nhanh
4. **Admin access**: Thêm routes vào `BYPASS_ROUTES` nếu cần

## 🚨 Emergency Procedures

### Bật nhanh chế độ bảo trì:
```javascript
localStorage.setItem('maintenanceMode', 'true');
location.reload();
```

### Tắt nhanh chế độ bảo trì:
```javascript
localStorage.removeItem('maintenanceMode');
location.reload();
```

## 📞 Hỗ trợ

Nếu có vấn đề với hệ thống bảo trì, liên hệ team development.
