/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BaseModelCard,
  BaseModelCard$inboundSchema,
  BaseModelCard$Outbound,
  BaseModelCard$outboundSchema,
} from "./basemodelcard.js";
import {
  FTModelCard,
  FTModelCard$inboundSchema,
  FTModelCard$Outbound,
  FTModelCard$outboundSchema,
} from "./ftmodelcard.js";

export type Data =
  | (BaseModelCard & { type: "base" })
  | (FTModelCard & { type: "fine-tuned" });

export type ModelList = {
  object?: string | undefined;
  data?:
    | Array<
      | (BaseModelCard & { type: "base" })
      | (FTModelCard & { type: "fine-tuned" })
    >
    | undefined;
};

/** @internal */
export const Data$inboundSchema: z.ZodType<Data, z.ZodTypeDef, unknown> = z
  .union([
    BaseModelCard$inboundSchema.and(
      z.object({ type: z.literal("base") }).transform((v) => ({
        type: v.type,
      })),
    ),
    FTModelCard$inboundSchema.and(
      z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
        type: v.type,
      })),
    ),
  ]);

/** @internal */
export type Data$Outbound =
  | (BaseModelCard$Outbound & { type: "base" })
  | (FTModelCard$Outbound & { type: "fine-tuned" });

/** @internal */
export const Data$outboundSchema: z.ZodType<Data$Outbound, z.ZodTypeDef, Data> =
  z.union([
    BaseModelCard$outboundSchema.and(
      z.object({ type: z.literal("base") }).transform((v) => ({
        type: v.type,
      })),
    ),
    FTModelCard$outboundSchema.and(
      z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
        type: v.type,
      })),
    ),
  ]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Data$ {
  /** @deprecated use `Data$inboundSchema` instead. */
  export const inboundSchema = Data$inboundSchema;
  /** @deprecated use `Data$outboundSchema` instead. */
  export const outboundSchema = Data$outboundSchema;
  /** @deprecated use `Data$Outbound` instead. */
  export type Outbound = Data$Outbound;
}

export function dataToJSON(data: Data): string {
  return JSON.stringify(Data$outboundSchema.parse(data));
}

export function dataFromJSON(
  jsonString: string,
): SafeParseResult<Data, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Data$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Data' from JSON`,
  );
}

/** @internal */
export const ModelList$inboundSchema: z.ZodType<
  ModelList,
  z.ZodTypeDef,
  unknown
> = z.object({
  object: z.string().default("list"),
  data: z.array(
    z.union([
      BaseModelCard$inboundSchema.and(
        z.object({ type: z.literal("base") }).transform((v) => ({
          type: v.type,
        })),
      ),
      FTModelCard$inboundSchema.and(
        z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
          type: v.type,
        })),
      ),
    ]),
  ).optional(),
});

/** @internal */
export type ModelList$Outbound = {
  object: string;
  data?:
    | Array<
      | (BaseModelCard$Outbound & { type: "base" })
      | (FTModelCard$Outbound & { type: "fine-tuned" })
    >
    | undefined;
};

/** @internal */
export const ModelList$outboundSchema: z.ZodType<
  ModelList$Outbound,
  z.ZodTypeDef,
  ModelList
> = z.object({
  object: z.string().default("list"),
  data: z.array(
    z.union([
      BaseModelCard$outboundSchema.and(
        z.object({ type: z.literal("base") }).transform((v) => ({
          type: v.type,
        })),
      ),
      FTModelCard$outboundSchema.and(
        z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
          type: v.type,
        })),
      ),
    ]),
  ).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ModelList$ {
  /** @deprecated use `ModelList$inboundSchema` instead. */
  export const inboundSchema = ModelList$inboundSchema;
  /** @deprecated use `ModelList$outboundSchema` instead. */
  export const outboundSchema = ModelList$outboundSchema;
  /** @deprecated use `ModelList$Outbound` instead. */
  export type Outbound = ModelList$Outbound;
}

export function modelListToJSON(modelList: ModelList): string {
  return JSON.stringify(ModelList$outboundSchema.parse(modelList));
}

export function modelListFromJSON(
  jsonString: string,
): SafeParseResult<ModelList, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ModelList$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ModelList' from JSON`,
  );
}
