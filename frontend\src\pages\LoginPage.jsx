import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { login } from '../features/auth/authSlice';
import { useNavigate, Link } from 'react-router-dom';
import AuthLayout from '../layouts/AuthLayout';
import Input from '../components/input/InputForAuthPage';
import Button from '../components/button/ButtonForAuthPage';
import GoogleLoginButton from '../components/button/GoogleLoginButton';
import LoadingSpinner from '../components/loading/LoadingSpinner';
import { AuthCheckbox } from '../components/checkBox/AuthCheckbox';
import { BeeMathLogo } from '../components/logo/BeeMathLogo';
import BannerLogin from 'src/assets/images/bannerlogin.png'

export default function LoginPage() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { loading } = useSelector((state) => state.auth);
    const [formData, setFormData] = useState({
        username: localStorage.getItem("savedUsername") || "",
        password: "",
    });

    const { user } = useSelector((state) => state.auth);
    useEffect(() => {
        if (user) {
            if (user.userType !== "HS1") {
                navigate('/admin');
                return;
            } else {
                if (localStorage.getItem('rememberMe') === "true") {
                    localStorage.setItem("savedUsername", formData.username);
                } else {
                    localStorage.removeItem("savedUsername");
                }
                let redirectPath = localStorage.getItem("redirect_after_login") || "/overview";
                redirectPath = redirectPath === "/" ? "/overview" : redirectPath;
                localStorage.removeItem("redirect_after_login");
                navigate(redirectPath); // Nếu dùng React Router
                return;
            }
        }
    }, [user, navigate]);

    const handleChange = (e) =>
        setFormData({ ...formData, [e.target.name]: e.target.value });

    const handleSubmit = async (e) => {
        e.preventDefault();
        const resultAction = await dispatch(login(formData))

        // if (login.fulfilled.match(resultAction)) {

        // }
    };

    return (
        <AuthLayout>
            <div className=" flex flex-col md:flex-row items-stretch justify-center h-screen w-full">
                <div className="relative w-full md:w-1/3 flex items-center justify-end  px-4 sm:px-6">

                    {/* 🔵 Vòng tròn mờ đằng sau */}
                    <div className="absolute inset-0 flex items-center justify-center z-0">
                        <div className="w-[500px] h-[500px] bg-blue-600/10 rounded-full blur-[175px]" />
                    </div>
                    {/* 🧾 Form (nổi trên vòng tròn) */}
                    <form
                        onSubmit={handleSubmit}
                        className="relative z-10 flex flex-col items-center gap-4 w-full max-w-sm"
                    >

                        {/* Tiêu đề */}
                        <div className='flex w-full items-center justify-start gap-2 lg:mb-16 mb-8'>
                            <div className="flex flex-col justify-start text-sky-600 text-3xl font-bold font-['Inter'] capitalize tracking-tight">
                                Đăng nhập
                                <span className="justify-start text-gray-500 text-sm font-normal font-['Inter'] leading-normal">
                                    Vui lòng điền thông tin để truy cập tài khoản của bạn.
                                </span>
                            </div>
                            {/* <BeeMathLogo className="w-[2rem] h-[2rem] lg:hidden block" /> */}
                        </div>

                        {/* Input Username */}

                        <div className="w-full">
                            <div className="justify-start text-slate-700 text-sm font-medium font-['Inter'] leading-tight">Tên đăng nhập</div>
                            <Input
                                type="text"
                                name="username"
                                placeholder="Tên đăng nhập"
                                title="Tên đăng nhập"
                                value={formData.username}
                                onChange={handleChange}
                                className="h-10 px-4 rounded-md sm:rounded-lg text-sm"
                                required
                            />
                        </div>

                        {/* Input Password */}
                        <div className="w-full">
                            <div className="justify-start text-slate-700 text-sm font-medium font-['Inter'] leading-tight">Mật khẩu</div>

                            <Input
                                type="password"
                                name="password"
                                placeholder="Mật khẩu"
                                title="Mật khẩu"
                                value={formData.password}
                                onChange={handleChange}
                                className="h-10 px-4 rounded-md sm:rounded-lg text-sm"
                                required
                            />
                        </div>

                        {/* Ghi nhớ & hỗ trợ */}
                        <div className="flex w-full items-center justify-between text-sm text-gray-700">
                            <div className="flex items-center gap-2">
                                <AuthCheckbox />
                                <span>Ghi nhớ tôi</span>
                            </div>
                            <span className="justify-start text-rose-600 text-sm font-medium font-['Inter'] leading-tight cursor-pointer hover:underline">Cần giúp đỡ?</span>
                        </div>

                        {/* Nút Đăng nhập */}
                        <Button
                            type="submit"
                            disabled={loading}
                            className="w-full py-1 text-white text-sm font-semibold bg-sky-600 hover:bg-sky-700 rounded-md shadow-md hover:shadow-lg transition"
                        >

                            {loading ? <LoadingSpinner size="1.5rem" color="text-white" /> : "Đăng nhập"}
                        </Button>

                        {/* Liên kết */}
                        {/* Liên kết */}
                        <div className="text-start text-xs text-gray-600 space-y-1">
                            <p className=" font-medium">
                                Nếu bạn chưa có tài khoản hoặc quên mật khẩu, vui lòng liên hệ với giáo viên!
                            </p>
                        </div>
                    </form>
                </div>

                <div className=" justify-end w-full md:w-2/3 hidden md:flex p-12">
                    <img
                        src={BannerLogin}
                        alt="Banner Login"
                        className="w-full h-full object-contain max-w-[1024px]"
                    />
                </div>
            </div >

        </AuthLayout >

    );
}
