/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type EmbeddingResponseData = {
  object?: string | undefined;
  embedding?: Array<number> | undefined;
  index?: number | undefined;
};

/** @internal */
export const EmbeddingResponseData$inboundSchema: z.ZodType<
  EmbeddingResponseData,
  z.ZodTypeDef,
  unknown
> = z.object({
  object: z.string().optional(),
  embedding: z.array(z.number()).optional(),
  index: z.number().int().optional(),
});

/** @internal */
export type EmbeddingResponseData$Outbound = {
  object?: string | undefined;
  embedding?: Array<number> | undefined;
  index?: number | undefined;
};

/** @internal */
export const EmbeddingResponseData$outboundSchema: z.ZodType<
  EmbeddingResponseData$Outbound,
  z.ZodTypeDef,
  EmbeddingResponseData
> = z.object({
  object: z.string().optional(),
  embedding: z.array(z.number()).optional(),
  index: z.number().int().optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace EmbeddingResponseData$ {
  /** @deprecated use `EmbeddingResponseData$inboundSchema` instead. */
  export const inboundSchema = EmbeddingResponseData$inboundSchema;
  /** @deprecated use `EmbeddingResponseData$outboundSchema` instead. */
  export const outboundSchema = EmbeddingResponseData$outboundSchema;
  /** @deprecated use `EmbeddingResponseData$Outbound` instead. */
  export type Outbound = EmbeddingResponseData$Outbound;
}

export function embeddingResponseDataToJSON(
  embeddingResponseData: EmbeddingResponseData,
): string {
  return JSON.stringify(
    EmbeddingResponseData$outboundSchema.parse(embeddingResponseData),
  );
}

export function embeddingResponseDataFromJSON(
  jsonString: string,
): SafeParseResult<EmbeddingResponseData, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => EmbeddingResponseData$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'EmbeddingResponseData' from JSON`,
  );
}
