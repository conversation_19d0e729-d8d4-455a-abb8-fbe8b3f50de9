import UserLayout from "../../../layouts/UserLayout"
import { useDispatch, useSelector } from "react-redux"
import { useEffect, useState, useRef } from "react"
import { fetchClassesByUser } from "../../../features/class/classSlice"
import InputSearch from "../../../components/input/InputSearch"
import ClassImage from "../../../components/image/ClassImage"
import { setSearch, setCurrentPage, setLimit } from "../../../features/class/classSlice"
import Pagination from "../../../components/Pagination"
import { useNavigate } from "react-router-dom"
import {
    GraduationCap,
    Calendar,
    Users,
    Filter,
    Plus,
    Loader,
    ChevronDown,
    UserCheck,
    Timer,
    Search
} from "lucide-react"
import LoadingData from "src/components/loading/LoadingData"
import { setOpenJoinClassModal } from "../../../features/class/classSlice"
import SortBar from "src/components/filter/SortBar"

const formatTime = (timeStr) => timeStr?.substring(0, 5); // Lấy "HH:mm"

const ClassCard = ({ cls, onClick }) => {
    return (
        <div
            onClick={onClick}
            className="flex flex-col md:flex-row justify-center items-start md:items-center p-3 lg:p-4 gap-3 lg:gap-4 bg-white border border-gray-200 rounded-md hover:shadow-md hover:border-cyan-300 cursor-pointer transition-all duration-200 group"
        >
            {/* Class Content */}
            <div className="flex-1 flex flex-col w-full md:w-auto">
                {/* Class Name and Code */}
                <div className="flex flex-row w-full justify-between">
                    <h3 className="text-zinc-900 font-semibold font-bevietnam text-sm truncate group-hover:text-cyan-700 transition-colors">
                        {cls.name}
                    </h3>
                    <div className={`p-1 border border-gray-200 rounded-full text-[0.6rem] text-gray-500 ${cls.studentClassStatus === 'JS' ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>
                        {cls.studentClassStatus === 'JS' ? 'Đang học' : 'Chờ duyệt'}
                    </div>
                </div>
                <div className="flex flex-col gap-1 mt-4 text-xs text-gray-600">
                    <div className="flex items-center gap-2">
                        <span className="px-2 py-[1px] rounded-full bg-cyan-100 text-cyan-700 font-medium text-[10px]">
                            Buổi 1
                        </span>
                        <span>{cls.dayOfWeek1} {formatTime(cls.startTime1)} - {formatTime(cls.endTime1)}</span>
                    </div>
                    {cls.dayOfWeek2 && (
                        <div className="flex items-center gap-2">
                            <span className="px-2 py-[1px] rounded-full bg-indigo-100 text-indigo-700 font-medium text-[10px]">
                                Buổi 2
                            </span>
                            <span>{cls.dayOfWeek2} {formatTime(cls.startTime2)} - {formatTime(cls.endTime2)}</span>
                        </div>
                    )}
                </div>
                <div className="flex flex-row gap-2 mt-4 flex-wrap">
                    <p className="text-xs text-gray-500 ">Mã lớp: {cls.class_code}</p>
                    <p className="text-xs text-gray-500 ">Khối: {cls.grade}</p>
                    <p className="text-xs text-gray-500 ">Năm học: {cls.academicYear}</p>
                </div>
            </div>
        </div>
    )
}

const ClassUserPage = () => {
    const navigate = useNavigate()
    const { classes, search, pagination, loading } = useSelector(state => state.classes)
    const { page: currentPage, pageSize: limit } = pagination;

    const dispatch = useDispatch()
    const [status, setStatus] = useState('JS');
    const [sortOption, setSortOption] = useState('default');
    const [showSortDropdown, setShowSortDropdown] = useState(false);
    const [choice, setChoice] = useState(0);
    const sortDropdownRef = useRef();

    useEffect(() => {
        dispatch(fetchClassesByUser())
        if (limit !== 4) dispatch(setLimit(4))
    }, [dispatch])

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (sortDropdownRef.current && !sortDropdownRef.current.contains(event.target)) {
                setShowSortDropdown(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Sync choice with status
    useEffect(() => {
        if (status === 'JS') {
            setChoice(0);
        } else if (status === 'WS') {
            setChoice(1);
        }
    }, [status]);

    const filteredClasses = classes.filter(cls =>
        (cls.name?.toLowerCase().includes(search.toLowerCase()) ||
            cls.class_code?.toLowerCase().includes(search.toLowerCase())) &&
        cls.studentClassStatus === status
    )

    const sortedClasses = [...filteredClasses].sort((a, b) => {
        switch (sortOption) {
            case 'az':
                return a.name.localeCompare(b.name);
            case 'za':
                return b.name.localeCompare(a.name);
            case 'newest':
                return new Date(b.createdAt) - new Date(a.createdAt);
            case 'oldest':
                return new Date(a.createdAt) - new Date(b.createdAt);
            default:
                return 0;
        }
    });

    const startIndex = (currentPage - 1) * limit;
    const paginatedClasses = sortedClasses.slice(startIndex, startIndex + limit);

    return (
        <UserLayout>
            <div className="flex w-full container">

                {/* Main Content */}
                <div className="flex-1 transition-all duration-300">
                    {/* Filter and Search Section */}
                    <div className="bg-white rounded-lg mb-4 lg:mb-6">
                        <div className="flex flex-col gap-3 lg:gap-4">
                            {/* Bottom Row: Search and Actions */}
                            <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
                                <button
                                    onClick={() => setStatus('JS')}
                                    className={`px-2 py-[7px] rounded-md text-xs font-medium transition-colors whitespace-nowrap ${status === 'JS'
                                        ? 'bg-sky-100 text-sky-700 border border-sky-300'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    <UserCheck className="w-4 h-4 inline mr-1 lg:mr-2" />
                                    <span className="hidden sm:inline">Lớp của bạn</span>
                                    <span className="sm:hidden">Của bạn</span>
                                </button>
                                <button
                                    onClick={() => setStatus('WS')}
                                    className={`px-2 py-[7px] rounded-md text-xs font-medium transition-colors whitespace-nowrap ${status === 'WS'
                                        ? 'bg-yellow-100 text-yellow-700 border border-yellow-300'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    <Timer className="w-4 h-4 inline mr-1 lg:mr-2" />
                                    <span className="hidden sm:inline">Lớp đang chờ</span>
                                    <span className="sm:hidden">Đang chờ</span>
                                </button>
                                {/* Search */}
                                <div className="flex-1">
                                    <div className="flex items-center cursor-pointer border border-gray-300 rounded-md px-2 py-[7px] text-gray-600 focus-within:ring-2 focus-within:ring-sky-500">
                                        <Search size={16} />
                                        <input
                                            id="questionId"
                                            type="text"
                                            value={search}
                                            onChange={(e) => {
                                                if (e.target.value !== search) {
                                                    dispatch(setSearch(e.target.value))
                                                }
                                                if (currentPage !== 1) {
                                                    dispatch(setCurrentPage(1))
                                                }
                                            }}
                                            placeholder="Tìm kiếm lớp..."
                                            className="flex-1 pl-2 text-xs outline-none bg-transparent"
                                        />
                                    </div>
                                </div>

                                {/* Sort and Join Class */}
                                <div className="flex gap-2">
                                    <SortBar
                                        selected={sortOption}
                                        onChange={setSortOption}
                                        sortOptions={[
                                            { label: "Mới nhất", value: "newest" },
                                            { label: "Cũ nhất", value: "oldest" },
                                            { label: "A-Z", value: "az" },
                                            { label: "Z-A", value: "za" },
                                        ]}
                                    />

                                    <button
                                        onClick={() => dispatch(setOpenJoinClassModal(true))}
                                        className="px-2 py-[7px] bg-sky-600 text-white rounded-md text-xs hover:bg-sky-700 transition-colors flex items-center gap-1 lg:gap-2"
                                    >
                                        <Plus className="w-4 h-4" />
                                        <span className="hidden sm:inline">Tham gia lớp</span>
                                        <span className="sm:hidden">Tham gia</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr className="my-4 border-gray-200" />

                    {/* Classes Section */}
                    <div className="mb-6 lg:mb-8">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 lg:mb-6 gap-3">
                            <h2 className=" font-bold text-gray-800 flex items-center gap-2">
                                {status === 'JS' ? (
                                    <>
                                        <UserCheck size={20} className="text-sky-600 " />
                                        Lớp của bạn
                                    </>
                                ) : (
                                    <>
                                        <Timer size={20} className="text-yellow-600 " />
                                        Lớp đang chờ
                                    </>
                                )}
                                {paginatedClasses.length > 0 && (
                                    <span
                                        className={`ml-2 text-white text-xs font-bold w-5 h-5 flex items-center justify-center rounded-full 
    ${status === 'JS' ? 'bg-sky-500' : 'bg-yellow-500'}`}
                                    >
                                        {paginatedClasses.length}
                                    </span>
                                )}
                            </h2>
                        </div>
                        <LoadingData
                            loading={loading}
                            isNoData={classes.length > 0 ? false : true}
                            loadText="Đang tải danh sách lớp học"
                            noDataText="Không có lớp học nào."
                            IconNoData={GraduationCap}
                        >
                            {paginatedClasses && paginatedClasses.length > 0 ? (
                                <div className="">
                                    <div className="space-y-3 lg:space-y-4">
                                        {paginatedClasses.map((cls, index) => (
                                            <ClassCard
                                                key={cls._id}
                                                cls={cls}
                                                onClick={() => navigate(`/class/${cls.class_code}`)}
                                            />
                                        ))}
                                    </div>
                                    <hr className="my-4 border-gray-200" />
                                    {/* Pagination */}
                                    <Pagination
                                        currentPage={currentPage}
                                        limit={limit}
                                        totalItems={sortedClasses.length}
                                        onPageChange={(p) => dispatch(setCurrentPage(p))}
                                    />
                                </div>
                            ) : (
                                <div className="p-6 lg:p-8 text-center text-gray-500">
                                    {status === 'JS' ? (
                                        <>
                                            <GraduationCap size={32} className="mx-auto mb-4 text-gray-300 lg:w-10 lg:h-10" />
                                            <p className="text-sm lg:text-base mb-4">Bạn chưa tham gia lớp học nào.</p>
                                            <button
                                                onClick={() => dispatch(setOpenJoinClassModal(true))}
                                                className="px-4 py-2 bg-sky-600 text-white rounded-md text-sm hover:bg-sky-700 transition-colors"
                                            >
                                                Tham gia lớp ngay
                                            </button>
                                        </>
                                    ) : (
                                        <>
                                            <Timer size={32} className="mx-auto mb-4 text-gray-300 lg:w-10 lg:h-10" />
                                            <p className="text-sm lg:text-base">Không có lớp nào đang chờ duyệt.</p>
                                        </>
                                    )}
                                </div>
                            )}
                        </LoadingData>

                    </div>
                </div>


            </div>
        </UserLayout>
    )
}

export default ClassUserPage