/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JobsApiRoutesFineTuningUpdateFineTunedModelRequest = {
  /**
   * The ID of the model to update.
   */
  modelId: string;
  updateFTModelIn: components.UpdateFTModelIn;
};

/**
 * OK
 */
export type JobsApiRoutesFineTuningUpdateFineTunedModelResponse =
  | (components.CompletionFTModelOut & { modelType: "completion" })
  | (components.ClassifierFTModelOut & { modelType: "classifier" });

/** @internal */
export const JobsApiRoutesFineTuningUpdateFineTunedModelRequest$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest,
    z.ZodTypeDef,
    unknown
  > = z.object({
    model_id: z.string(),
    UpdateFTModelIn: components.UpdateFTModelIn$inboundSchema,
  }).transform((v) => {
    return remap$(v, {
      "model_id": "modelId",
      "UpdateFTModelIn": "updateFTModelIn",
    });
  });

/** @internal */
export type JobsApiRoutesFineTuningUpdateFineTunedModelRequest$Outbound = {
  model_id: string;
  UpdateFTModelIn: components.UpdateFTModelIn$Outbound;
};

/** @internal */
export const JobsApiRoutesFineTuningUpdateFineTunedModelRequest$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest
  > = z.object({
    modelId: z.string(),
    updateFTModelIn: components.UpdateFTModelIn$outboundSchema,
  }).transform((v) => {
    return remap$(v, {
      modelId: "model_id",
      updateFTModelIn: "UpdateFTModelIn",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningUpdateFineTunedModelRequest$ {
  /** @deprecated use `JobsApiRoutesFineTuningUpdateFineTunedModelRequest$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningUpdateFineTunedModelRequest$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningUpdateFineTunedModelRequest$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest$Outbound;
}

export function jobsApiRoutesFineTuningUpdateFineTunedModelRequestToJSON(
  jobsApiRoutesFineTuningUpdateFineTunedModelRequest:
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningUpdateFineTunedModelRequest$outboundSchema.parse(
      jobsApiRoutesFineTuningUpdateFineTunedModelRequest,
    ),
  );
}

export function jobsApiRoutesFineTuningUpdateFineTunedModelRequestFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningUpdateFineTunedModelRequest,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningUpdateFineTunedModelRequest$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningUpdateFineTunedModelRequest' from JSON`,
  );
}

/** @internal */
export const JobsApiRoutesFineTuningUpdateFineTunedModelResponse$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse,
    z.ZodTypeDef,
    unknown
  > = z.union([
    components.CompletionFTModelOut$inboundSchema.and(
      z.object({ model_type: z.literal("completion") }).transform((v) => ({
        modelType: v.model_type,
      })),
    ),
    components.ClassifierFTModelOut$inboundSchema.and(
      z.object({ model_type: z.literal("classifier") }).transform((v) => ({
        modelType: v.model_type,
      })),
    ),
  ]);

/** @internal */
export type JobsApiRoutesFineTuningUpdateFineTunedModelResponse$Outbound =
  | (components.CompletionFTModelOut$Outbound & { model_type: "completion" })
  | (components.ClassifierFTModelOut$Outbound & { model_type: "classifier" });

/** @internal */
export const JobsApiRoutesFineTuningUpdateFineTunedModelResponse$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse
  > = z.union([
    components.CompletionFTModelOut$outboundSchema.and(
      z.object({ modelType: z.literal("completion") }).transform((v) => ({
        model_type: v.modelType,
      })),
    ),
    components.ClassifierFTModelOut$outboundSchema.and(
      z.object({ modelType: z.literal("classifier") }).transform((v) => ({
        model_type: v.modelType,
      })),
    ),
  ]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningUpdateFineTunedModelResponse$ {
  /** @deprecated use `JobsApiRoutesFineTuningUpdateFineTunedModelResponse$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningUpdateFineTunedModelResponse$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningUpdateFineTunedModelResponse$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse$Outbound;
}

export function jobsApiRoutesFineTuningUpdateFineTunedModelResponseToJSON(
  jobsApiRoutesFineTuningUpdateFineTunedModelResponse:
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningUpdateFineTunedModelResponse$outboundSchema.parse(
      jobsApiRoutesFineTuningUpdateFineTunedModelResponse,
    ),
  );
}

export function jobsApiRoutesFineTuningUpdateFineTunedModelResponseFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningUpdateFineTunedModelResponse,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningUpdateFineTunedModelResponse$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningUpdateFineTunedModelResponse' from JSON`,
  );
}
