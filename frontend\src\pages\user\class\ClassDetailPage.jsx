import UserLayout from "../../../layouts/UserLayout"
import { useDispatch, useSelector } from "react-redux"
import { fetchLessonLearningItemInClass } from "../../../features/class/classSlice"
import { useEffect, useState } from "react"
import { useParams } from "react-router-dom"
import SlideShow from "../../../components/image/SlideShow"
import QrCode from "../../../components/QrCode"
import { fetchCodesByType } from "../../../features/code/codeSlice"
import { useNavigate } from "react-router-dom"
import { joinClass } from "../../../features/class/classSlice"
import LoadingSpinner from "../../../components/loading/LoadingSpinner"
import LearningItemIcon from "../../../components/image/LearningItemIcon"
import {
    Calendar,
    Users,
    BookOpen,
    Clock,
    ChevronDown,
    ChevronUp,
    QrCode as QrCodeIcon,
    ArrowRight,
    Home,
    AlertTriangle,
    GraduationCap,
    BookOpenCheck
} from "lucide-react"
import NotFoundLayout from "src/layouts/404NotFound"
import ClassBanner from "../../../components/banner/ClassBanner"

const InfoRow = ({ icon, label, value }) => (
    <div className="flex items-center gap-2 text-sm">
        <span className="text-gray-600">{icon}</span>
        <span className="w-28 font-medium text-gray-900">{label}:</span>
        <span className="text-gray-700">{value}</span>
    </div>
);

const ScheduleInFo = ({ classDetail, codes }) => {
    const schedules = [];

    if (classDetail?.dayOfWeek1 && classDetail?.startTime1 && classDetail?.endTime1) {
        schedules.push(
            `${codes['dow']?.find((c) => c.code === classDetail?.dayOfWeek1)?.description} ${classDetail?.startTime1.slice(0, 5)} - ${classDetail?.endTime1.slice(0, 5)}`
        );
    }

    if (classDetail?.dayOfWeek2 && classDetail?.startTime2 && classDetail?.endTime2) {
        schedules.push(
            `${codes['dow']?.find((c) => c.code === classDetail?.dayOfWeek2)?.description} ${classDetail?.startTime2.slice(0, 5)} - ${classDetail?.endTime2.slice(0, 5)}`
        );
    }

    return (
        <div className="flex items-center gap-2 text-sm">
            <Clock size={14} className="text-gray-600" />
            <span className="w-28 font-medium text-gray-900">Lịch học:</span>
            <div className="flex flex-col gap-1">
                {schedules.map((item, index) => (
                    <p key={index} className="font-medium text-gray-700 text-xs">
                        {item}
                    </p>
                ))}
            </div>
        </div>
    );
};

const ClassDetailPage = () => {
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const { classCode } = useParams()
    const { classDetail } = useSelector(state => state.classes)
    const { codes } = useSelector(state => state.codes)
    const { loading } = useSelector(state => state.classes)
    const [loadingJoin, setLoadingJoin] = useState(false)
    const currentUrl = window.location.href;
    const images = classDetail?.slide?.slideImages?.map(img => img.imageUrl) || []
    const [openLessons, setOpenLessons] = useState([]);

    const toggleLesson = (index) => {
        setOpenLessons((prev) =>
            prev.includes(index)
                ? prev.filter((i) => i !== index) // nếu đang mở thì đóng lại
                : [...prev, index]               // nếu đang đóng thì mở ra
        );
    };

    const handleClicked = async () => {
        if (classDetail?.userStatus == "JS") {
            navigate(`/class/${classCode}/learning`)
        } else if (classDetail?.userStatus == "WS") {
            return
        } else {
            setLoadingJoin(true)
            await dispatch(joinClass(classCode))
            setLoadingJoin(false)
        }
    }

    useEffect(() => {
        if (classCode) dispatch(fetchLessonLearningItemInClass(classCode))
    }, [dispatch, classCode])

    useEffect(() => {
        dispatch(fetchCodesByType(['dow', 'duration']))
    }, [dispatch])

    // Xác định trạng thái nút tham gia
    const getJoinButtonStyles = () => {
        if (classDetail?.userStatus === 'JS') {
            return {
                bg: 'bg-green-600 hover:bg-green-700',
                text: 'Vào học ngay',
                icon: <ArrowRight size={16} className="ml-2" />
            };
        } else if (classDetail?.userStatus === 'WS') {
            return {
                bg: 'bg-amber-500 hover:bg-amber-600',
                text: 'Chờ phê duyệt',
                icon: <Clock size={16} className="ml-2" />
            };
        } else {
            return {
                bg: 'bg-sky-600 hover:bg-sky-700',
                text: 'Tham gia lớp học',
                icon: <Users size={16} className="ml-2" />
            };
        }
    };

    const buttonStyle = getJoinButtonStyles();

    return (
        <UserLayout>
            <NotFoundLayout
                isLoading={loading}
                textLoading="Đang tải thông tin lớp học..."
                isFound={classDetail ? true : false}
                message="Không tìm thấy lớp học với mã này"
                backLink={`/class`}
                backText="Quay lại danh sách lớp"
                className="flex flex-col container justify-center items-start gap-4 p-4 bg-white"
            >
                {/* Banner */}
                <div className="w-full">
                    {images && images.length > 0 ? (
                        <SlideShow
                            images={images}
                            interval={5000}
                            text={classDetail?.name}
                            h="h-[20rem]"
                        />
                    ) : (
                        <ClassBanner
                            className={classDetail?.name}
                            title={classDetail?.name || "Lớp học"}
                            subtitle="Chào mừng bạn đến với lớp học trực tuyến"
                            studentCount={classDetail?.joinedStudentCount || 0}
                            lessonCount={classDetail?.lessons?.length || 0}
                        />
                    )}
                </div>

                <div className="w-full h-full flex flex-col-reverse lg:flex-row justify-center items-start border border-gray-200">
                    {/* Main Content */}
                    <div className="flex-1 w-full inline-flex flex-col justify-start items-start border-r border-gray-200">
                        {/* Description Card */}
                        <div className="w-full p-4">
                            <h2 className="font-semibold text-gray-800 mb-2 flex items-center">
                                <BookOpen size={16} className="mr-2 text-sky-600" />
                                Mô tả
                            </h2>
                            <p className="text-sm text-gray-700 leading-relaxed">
                                {classDetail?.description || "Không có mô tả cho lớp học này."}
                            </p>
                        </div>
                        <hr className="my-3 w-full bg-gray-200" />

                        {/* Lessons Card */}
                        <div className="w-full p-4">
                            <h2 className="font-semibold text-gray-800 mb-2 flex items-center">
                                <Calendar size={16} className="mr-2 text-sky-600" />
                                Nội dung
                            </h2>
                            <p className="text-xs text-gray-700 leading-relaxed mb-4 italic">Đây chỉ là phần xem trước vui lòng "vào học ngay" để xem chi tiết</p>

                            {classDetail?.lessons?.length > 0 ? (
                                <div className="">
                                    {classDetail?.lessons?.map((lesson, index) => (
                                        <div className="relative w-full ml-2">
                                            <div className="absolute -left-2 top-2 bg-white py-1">
                                                <BookOpenCheck size={14} className="text-cyan-600" />
                                            </div>
                                            <div key={index} className="border-l border-gray-200 overflow-hidden">
                                                {/* Lesson Header */}
                                                <div
                                                    onClick={() => toggleLesson(index)}
                                                    className={`cursor-pointer py-2 px-4 flex justify-between items-center transition-colors duration-300 bg-white text-gray-800 hover:bg-gray-100`}
                                                >
                                                    <div className="font-medium flex items-center">
                                                        <span className="mr-2 text-sm">{lesson.name}</span>
                                                        {lesson.learningItems?.length > 0 && (
                                                            <span className="whitespace-nowrap bg-white text-sky-700 text-xs px-2 py-1 rounded-full">
                                                                {lesson.learningItems.length} mục
                                                            </span>
                                                        )}
                                                    </div>

                                                    {openLessons.includes(index) ? (
                                                        <ChevronUp size={20} />
                                                    ) : (
                                                        <ChevronDown size={20} />
                                                    )}
                                                </div>

                                                {/* Lesson Content */}
                                                <div
                                                    className={`transition-all duration-300 ease-in-out overflow-hidden ${openLessons.includes(index)
                                                        ? "max-h-96 opacity-100"
                                                        : "max-h-0 opacity-0"
                                                        }`}
                                                >
                                                    {lesson.learningItems?.map((learningItem, i) => (
                                                        <div
                                                            key={i}
                                                            className="pl-8 pr-4 py-2 flex items-center hover:bg-gray-50 space-x-2"
                                                        >
                                                            <LearningItemIcon type={learningItem.typeOfLearningItem} />
                                                            <span className="text-xs text-gray-800">{learningItem.name}</span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center py-8 text-gray-500">
                                    <AlertTriangle size={40} className="mb-2 text-amber-500" />
                                    <p>Không có buổi học nào</p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="w-full lg:w-1/3 flex flex-col lg:sticky lg:top-24">
                        {/* Class Info Card */}
                        <div className="flex flex-col bg-white p-4 gap-4">
                            <h2 className=" font-semibold text-gray-800 flex items-center">
                                <BookOpen size={16} className="mr-2 text-sky-600" />
                                Thông tin lớp học
                            </h2>

                            <div className="space-y-4 flex flex-col">
                                {/* Class Code */}
                                <InfoRow
                                    icon={<QrCodeIcon size={14} className="text-gray-600" />}
                                    label="Mã lớp"
                                    value={classDetail?.class_code}
                                />

                                {/* Grade */}
                                <InfoRow
                                    icon={<GraduationCap size={14} className="text-gray-600" />}
                                    label="Khối"
                                    value={classDetail?.grade}
                                />

                                {/* Student Count */}
                                <InfoRow
                                    icon={<Users size={14} className="text-gray-600" />}
                                    label="Số học sinh"
                                    value={classDetail?.joinedStudentCount}
                                />

                                {/* Student Count */}
                                <InfoRow
                                    icon={<Calendar size={14} className="text-gray-600" />}
                                    label="Số buổi học"
                                    value={classDetail?.lessons?.length}
                                />

                            </div>

                            {/* Schedule */}
                            <ScheduleInFo classDetail={classDetail} codes={codes} />

                        </div>
                        <hr className="my-3 w-full bg-gray-200" />

                        {/* QR Code Card */}
                        <div className="bg-white  p-4 flex flex-col items-center gap-3">
                            <div className=" text-gray-800 flex items-center w-full justify-center">
                                <QrCodeIcon size={16} className="mr-2 text-sky-600" />
                                <p className="font-semibold">
                                    Mã QR lớp học
                                </p>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-200 mb-3">
                                <QrCode url={currentUrl} size={150} />
                            </div>
                            <p className="text-xs text-gray-500 text-center italic">
                                Quét mã QR để truy cập nhanh vào trang lớp học
                            </p>
                        </div>

                        <hr className="my-3 w-full bg-gray-200" />

                        {/* Action Buttons */}
                        <div className="space-y-3 p-4 text-sm">
                            <button
                                onClick={handleClicked}
                                className={`w-full py-3 px-4 rounded ${buttonStyle.bg} text-white font-medium transition-colors duration-200 flex items-center justify-center`}
                                disabled={classDetail?.userStatus === 'WS'}
                            >
                                {loadingJoin ? (
                                    <LoadingSpinner size="1.25rem" color="text-white" minHeight="min-h-0" />
                                ) : (
                                    <>
                                        {buttonStyle.text}
                                        {buttonStyle.icon}
                                    </>
                                )}
                            </button>

                            <button
                                onClick={() => navigate('/class')}
                                className="w-full py-3 px-4 rounded bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center"
                            >
                                <Home size={18} className="mr-2" />
                                Quay lại danh sách lớp
                            </button>
                        </div>
                    </div>
                </div>
            </NotFoundLayout>
        </UserLayout>
    )
}

export default ClassDetailPage
