import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import PostUserRequest from '../dtos/requests/user/PostUserRequest.js'
import PutUserRequest from '../dtos/requests/user/PutUserRequest.js'
import LoginUserRequest from '../dtos/requests/user/LoginUserRequest.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'
import { handleMulterError } from '../middlewares/handelMulter.js'
import * as UserController from '../controllers/UserController.js'
import upload from '../middlewares/uploadExcel.js';

const router = express.Router()

// Route đăng kí người dùng
router.post('/v1/user/register',
    requireRoles(Roles.AllExceptMarketingStudent),
    validate(PostUserRequest),
    async<PERSON>and<PERSON>(UserController.registerUser)
)

router.post('/v1/admin/register',
    requireRoles(Roles.JustAdmin),
    asyncHandler(UserController.createUser)
)

router.post('/v1/admin/bulk-register',
    requireRoles(Roles.JustAdmin),
    upload.single('file'),
    UserController.bulkRegister
)

// Route đăng nhập người dùng
router.post('/v1/user/login',
    validate(LoginUserRequest),
    asyncHandler(UserController.login)
)


router.get('/v1/user/check-login',
    UserController.checkLogin
)

router.get('/v1/user/me',
    UserController.getUserMe
)

router.post('/v1/user/logout',
    requireRoles(Roles.All),
    UserController.logout
)
router.get('/v1/admin/user/search',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(UserController.findUsers)
)

// Route lấy tất cả người dùng
router.get('/v1/admin/user',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(UserController.getAllUsers)
)

router.get('/v1/admin/staff',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(UserController.getAllStaff)
)

// Route lấy tất cả người dùng theo class
router.get('/v1/admin/user/class/:classId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(UserController.getUsersByClass)
)

// Route lấy thông tin người dùng theo ID
router.get('/v1/admin/user/:id',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(UserController.getUserById)
)

// Route cập nhật thông tin người dùng theo ID
router.put('/v1/admin/user/:id',
    // validate(PutUserRequest),
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(UserController.putUser)
)

// Route cập nhật thông tin người dùng của chính mình
router.put('/v1/user',
    validate(PutUserRequest),
    requireRoles(Roles.All),
    asyncHandler(UserController.updateUserInfo)
)

// Route cập nhật mật khẩu người dùng
router.put('/v1/user/password',
    requireRoles(Roles.All),
    asyncHandler(UserController.changePassword)
)

// Route cập nhật loại người dùng
router.put('/v1/admin/user/:id/user-type',
    requireRoles(Roles.JustAdmin),
    asyncHandler(UserController.changeUserType)
)

// Route cập nhật avatar người dùng
router.put('/v1/user/avatar',
    requireRoles(Roles.All),
    uploadGoogleImageMiddleware.single('avatar'),
    handleMulterError,
    asyncHandler(UserController.updateAvatar)
)

// Route xóa tất cả người dùng không hoạt động
router.delete('/v1/admin/inactive/user',
    requireRoles(Roles.JustAdmin),
    asyncHandler(UserController.deleteAllInactiveUsers)
)

// Route xóa người dùng (chỉ admin và teacher)
router.delete('/v1/admin/user/:id',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(UserController.deleteUser)
)

router.put('/v1/admin/graduation-year/user',
    // requireRoles(Roles.JustAdmin),
    asyncHandler(UserController.addGraduationYearToUsers)
)


export default router
