import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
/**
 * The type of entity, used to share a library.
 */
export declare const EntityType: {
    readonly User: "User";
    readonly Workspace: "Workspace";
    readonly Org: "Org";
};
/**
 * The type of entity, used to share a library.
 */
export type EntityType = OpenEnum<typeof EntityType>;
/** @internal */
export declare const EntityType$inboundSchema: z.ZodType<EntityType, z.ZodTypeDef, unknown>;
/** @internal */
export declare const EntityType$outboundSchema: z.ZodType<EntityType, z.ZodTypeDef, EntityType>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace EntityType$ {
    /** @deprecated use `EntityType$inboundSchema` instead. */
    const inboundSchema: z.ZodType<EntityType, z.ZodTypeDef, unknown>;
    /** @deprecated use `EntityType$outboundSchema` instead. */
    const outboundSchema: z.ZodType<EntityType, z.ZodTypeDef, EntityType>;
}
//# sourceMappingURL=entitytype.d.ts.map