import Joi from "joi"

class PostClassRequest {
    constructor(data) {
        this.name = data.name
        this.grade = data.grade
        this.description = data.description
        this.academicYear = data.academicYear
        this.status = data.status
        this.slideId = data.slideId
        this.dayOfWeek1 = data.dayOfWeek1
        this.dayOfWeek2 = data.dayOfWeek2
        this.startTime1 = data.startTime1
        this.startTime2 = data.startTime2
        this.endTime1 = data.endTime1
        this.endTime2 = data.endTime2
        this.public = data.public
    }

    static validate(data) {
        // console.log(data)
        const schema = Joi.object({
            name: Joi.string().required(),
            grade: Joi.string().length(2).optional(),
            description: Joi.string().optional().allow(''),
            academicYear: Joi.string().required(),
            status: Joi.string().required(),
            slideId: Joi.number().optional(),
            dayOfWeek1: Joi.string().optional().allow(null),
            dayOfWeek2: Joi.string().optional().allow(null),
            startTime1: Joi.string().optional().allow(null),
            startTime2: Joi.string().optional().allow(null),
            endTime1: Joi.string().optional().allow(null),
            endTime2: Joi.string().optional().allow(null),
            public: Joi.boolean().optional(),
        })

        return schema.validate(data)
    }
}

export default PostClassRequest
