/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FunctionCall,
  FunctionCall$inboundSchema,
  FunctionCall$Outbound,
  FunctionCall$outboundSchema,
} from "./functioncall.js";
import {
  ToolTypes,
  ToolTypes$inboundSchema,
  ToolTypes$outboundSchema,
} from "./tooltypes.js";

export type Metadata = {};

export type ToolCall = {
  id?: string | undefined;
  type?: ToolTypes | undefined;
  function: FunctionCall;
  index?: number | undefined;
  metadata?: Metadata | null | undefined;
};

/** @internal */
export const Metadata$inboundSchema: z.ZodType<
  Metadata,
  z.ZodTypeDef,
  unknown
> = z.object({});

/** @internal */
export type Metadata$Outbound = {};

/** @internal */
export const Metadata$outboundSchema: z.ZodType<
  Metadata$Outbound,
  z.ZodTypeDef,
  Metadata
> = z.object({});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Metadata$ {
  /** @deprecated use `Metadata$inboundSchema` instead. */
  export const inboundSchema = Metadata$inboundSchema;
  /** @deprecated use `Metadata$outboundSchema` instead. */
  export const outboundSchema = Metadata$outboundSchema;
  /** @deprecated use `Metadata$Outbound` instead. */
  export type Outbound = Metadata$Outbound;
}

export function metadataToJSON(metadata: Metadata): string {
  return JSON.stringify(Metadata$outboundSchema.parse(metadata));
}

export function metadataFromJSON(
  jsonString: string,
): SafeParseResult<Metadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Metadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Metadata' from JSON`,
  );
}

/** @internal */
export const ToolCall$inboundSchema: z.ZodType<
  ToolCall,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string().default("null"),
  type: ToolTypes$inboundSchema.optional(),
  function: FunctionCall$inboundSchema,
  index: z.number().int().default(0),
  metadata: z.nullable(z.lazy(() => Metadata$inboundSchema)).optional(),
});

/** @internal */
export type ToolCall$Outbound = {
  id: string;
  type?: string | undefined;
  function: FunctionCall$Outbound;
  index: number;
  metadata?: Metadata$Outbound | null | undefined;
};

/** @internal */
export const ToolCall$outboundSchema: z.ZodType<
  ToolCall$Outbound,
  z.ZodTypeDef,
  ToolCall
> = z.object({
  id: z.string().default("null"),
  type: ToolTypes$outboundSchema.optional(),
  function: FunctionCall$outboundSchema,
  index: z.number().int().default(0),
  metadata: z.nullable(z.lazy(() => Metadata$outboundSchema)).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolCall$ {
  /** @deprecated use `ToolCall$inboundSchema` instead. */
  export const inboundSchema = ToolCall$inboundSchema;
  /** @deprecated use `ToolCall$outboundSchema` instead. */
  export const outboundSchema = ToolCall$outboundSchema;
  /** @deprecated use `ToolCall$Outbound` instead. */
  export type Outbound = ToolCall$Outbound;
}

export function toolCallToJSON(toolCall: ToolCall): string {
  return JSON.stringify(ToolCall$outboundSchema.parse(toolCall));
}

export function toolCallFromJSON(
  jsonString: string,
): SafeParseResult<ToolCall, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolCall$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolCall' from JSON`,
  );
}
