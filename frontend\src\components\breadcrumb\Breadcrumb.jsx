import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Home, BookMarked, FileText, BarChart2, History, Eye } from 'lucide-react';

/**
 * Breadcrumb component for navigation
 * @param {Object} props - Component props
 * @param {Array} props.items - Array of breadcrumb items
 * @param {string} props.items[].label - Label for the breadcrumb item
 * @param {string} props.items[].path - Path for the breadcrumb item
 * @param {string} props.items[].icon - Icon name for the breadcrumb item (home, practice, exam, ranking, history, preview)
 * @param {boolean} props.items[].active - Whether the breadcrumb item is active
 */
const Breadcrumb = ({ items = [] }) => {
    const navigate = useNavigate();

    // Function to render the appropriate icon
    const renderIcon = (iconName, className = "hover:text-sky-600") => {
        const iconSize = 16;
        switch (iconName) {
            case 'home':
                return <Home size={iconSize} className={className} />;
            case 'practice':
                return <BookMarked size={iconSize} className={className} />;
            case 'exam':
                return <FileText size={iconSize} className={className} />;
            case 'ranking':
                return <BarChart2 size={iconSize} className={className} />;
            case 'history':
                return <History size={iconSize} className={className} />;
            case 'preview':
                return <Eye size={iconSize} className={className} />;
            case 'score':
                return <BarChart2 size={iconSize} className={className} />;
            default:
                return null;
        }
    };

    return (
        <div className="flex flex-wrap items-center gap-2 text-sm font-semibold text-zinc-900 font-bevietnam lg:mb-4 mb-2 bg-white p-3 rounded-lg shadow-sm">
            {items.map((item, index) => (
                <React.Fragment key={index}>
                    {index > 0 && (
                        <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 15 16" fill="none">
                            <path d="M2.36002 0.940682C2.17719 0.84421 1.97253 0.796607 1.76588 0.802488C1.55924 0.808369 1.35762 0.867534 1.18057 0.974249C1.00352 1.08096 0.857033 1.23161 0.75532 1.41158C0.653607 1.59156 0.600113 1.79476 0.600024 2.00148V13.9975C0.60011 14.2054 0.654199 14.4097 0.756991 14.5903C0.859783 14.771 1.00775 14.9219 1.1864 15.0282C1.36505 15.1345 1.56824 15.1926 1.77608 15.1967C1.98392 15.2009 2.18928 15.151 2.37202 15.0519L13.572 8.97188C14.4152 8.51428 14.408 7.30388 13.5616 6.85588L2.36002 0.940682Z" fill="black" />
                        </svg>
                    )}
                    {item.active ? (
                        <span className="text-sky-600 font-medium flex items-center gap-1">
                            {item.icon && renderIcon(item.icon)}
                            <span>{item.label}</span>
                        </span>
                    ) : (
                        <span
                            onClick={() => navigate(item.path)}
                            className="cursor-pointer hover:text-sky-600 transition-colors flex items-center gap-1"
                        >
                            {item.icon && renderIcon(item.icon)}
                            <span>{item.label}</span>
                        </span>
                    )}
                </React.Fragment>
            ))}
        </div>
    );
};

export default Breadcrumb;