import { useState, useEffect } from "react";
import { Send } from "lucide-react";
import { useSelector } from "react-redux";
import EmojiPicker from "./EmojiPicker"; // giả sử bạn có component này

const CommentInput = ({ onSend, onReply, parentCommentId, value }) => {
    const [content, setContent] = useState("");

    const handleSend = () => {
        if (content.trim() === "") return;
        if (parentCommentId) {
            onReply?.(content, parentCommentId);
        } else {
            onSend?.(content);
        }
        setContent("");
    };
    const handleEmojiClick = (emoji) => {
        setContent(prev => prev + emoji);
    };

    useEffect(() => {
        setContent(value);
    }, [value]);


    return (
        <div className="flex-1 bg-gray-100 px-3 py-2 rounded-full flex items-center gap-2">
            <input
                type="text"
                placeholder="<PERSON>i<PERSON><PERSON> b<PERSON>nh luận..."
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSend()}
                className="flex-1 bg-transparent text-sm outline-none"
            />
            <EmojiPicker onSelect={handleEmojiClick} />
            <button onClick={handleSend} className="text-sky-600 hover:text-sky-800 transition" title="Gửi">
                <Send size={16} />
            </button>
        </div>
    )

}

export default CommentInput;
