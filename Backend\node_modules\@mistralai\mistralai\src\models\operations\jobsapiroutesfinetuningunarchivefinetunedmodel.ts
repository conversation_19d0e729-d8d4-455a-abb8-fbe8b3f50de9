/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest = {
  /**
   * The ID of the model to unarchive.
   */
  modelId: string;
};

/** @internal */
export const JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest,
    z.ZodTypeDef,
    unknown
  > = z.object({
    model_id: z.string(),
  }).transform((v) => {
    return remap$(v, {
      "model_id": "modelId",
    });
  });

/** @internal */
export type JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound = {
  model_id: string;
};

/** @internal */
export const JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest
  > = z.object({
    modelId: z.string(),
  }).transform((v) => {
    return remap$(v, {
      modelId: "model_id",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$ {
  /** @deprecated use `JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$Outbound;
}

export function jobsApiRoutesFineTuningUnarchiveFineTunedModelRequestToJSON(
  jobsApiRoutesFineTuningUnarchiveFineTunedModelRequest:
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$outboundSchema.parse(
      jobsApiRoutesFineTuningUnarchiveFineTunedModelRequest,
    ),
  );
}

export function jobsApiRoutesFineTuningUnarchiveFineTunedModelRequestFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest' from JSON`,
  );
}
