import authenticateToken from '../helpers/tokenHelper.js'
import UserType from '../constants/UserType.js'

/**
 * Middleware kiểm tra quyền truy cập dựa trên vai trò hoặc userId cụ thể
 * @param {string[]} roles - danh sách role được phép (ví dụ: ['ADMIN', 'TEACHER'])
 * @param {number[]} allowedUserIds - danh sách userId được phép
 */
export const requireRoles = (roles = [], allowedUserIds = []) => [
    authenticateToken,
    (req, res, next) => {
        const { userType, id } = req.user;

        const hasRole = roles.includes(userType);
        const isAllowedUser = allowedUserIds.includes(id);

        if (!hasRole && !isAllowedUser && roles.length > 0 && userType !== UserType.ADMIN) {
            return res.status(403).json({ message: '<PERSON><PERSON>n không có quyền truy cập.' });
        }

        next();
    },
];
