import React, { useState, useRef } from 'react';
import { Star } from 'lucide-react';

const StarRating = ({ 
    initialRating = 0, 
    onChange = () => {}, 
    size = 20, 
    disabled = false,
    showValue = true,
    className = ""
}) => {
    const [rating, setRating] = useState(initialRating);
    const [hoverRating, setHoverRating] = useState(0);
    const [isHovering, setIsHovering] = useState(false);
    const starRefs = useRef([]);

    const handleMouseEnter = (starIndex, event) => {
        if (disabled) return;
        
        const star = starRefs.current[starIndex];
        const rect = star.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const starWidth = rect.width;
        const isLeftHalf = x < starWidth / 2;
        
        const newHoverRating = isLeftHalf ? starIndex + 0.5 : starIndex + 1;
        setHoverRating(newHoverRating);
        setIsHovering(true);
    };

    const handleMouseMove = (starIndex, event) => {
        if (disabled) return;
        
        const star = starRefs.current[starIndex];
        const rect = star.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const starWidth = rect.width;
        const isLeftHalf = x < starWidth / 2;
        
        const newHoverRating = isLeftHalf ? starIndex + 0.5 : starIndex + 1;
        setHoverRating(newHoverRating);
    };

    const handleMouseLeave = () => {
        if (disabled) return;
        setIsHovering(false);
        setHoverRating(0);
    };

    const handleClick = (starIndex, event) => {
        if (disabled) return;
        
        const star = starRefs.current[starIndex];
        const rect = star.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const starWidth = rect.width;
        const isLeftHalf = x < starWidth / 2;
        
        const newRating = isLeftHalf ? starIndex + 0.5 : starIndex + 1;
        setRating(newRating);
        onChange(newRating);
    };

    const getStarFillPercentage = (starIndex) => {
        const currentRating = isHovering ? hoverRating : rating;
        const starValue = starIndex + 1;
        
        if (currentRating >= starValue) {
            return 100; // Full star
        } else if (currentRating >= starValue - 0.5) {
            return 50; // Half star
        } else {
            return 0; // Empty star
        }
    };

    const getStarColor = (starIndex) => {
        const currentRating = isHovering ? hoverRating : rating;
        const starValue = starIndex + 1;
        
        if (currentRating >= starValue - 0.5) {
            return isHovering ? 'text-yellow-400' : 'text-yellow-500';
        } else {
            return 'text-gray-300';
        }
    };

    return (
        <div className={`flex items-center gap-1 ${className}`}>
            <div className="flex items-center gap-0.5">
                {[0, 1, 2, 3, 4].map((starIndex) => {
                    const fillPercentage = getStarFillPercentage(starIndex);
                    const starColor = getStarColor(starIndex);
                    
                    return (
                        <div
                            key={starIndex}
                            ref={el => starRefs.current[starIndex] = el}
                            className={`relative cursor-pointer transition-all duration-150 ${
                                disabled ? 'cursor-not-allowed opacity-50' : 'hover:scale-110'
                            }`}
                            onMouseEnter={(e) => handleMouseEnter(starIndex, e)}
                            onMouseMove={(e) => handleMouseMove(starIndex, e)}
                            onMouseLeave={handleMouseLeave}
                            onClick={(e) => handleClick(starIndex, e)}
                            style={{ width: size, height: size }}
                        >
                            {/* Background star (empty) */}
                            <Star
                                size={size}
                                className="absolute inset-0 text-gray-300"
                                fill="currentColor"
                            />
                            
                            {/* Foreground star (filled) */}
                            <div
                                className="absolute inset-0 overflow-hidden"
                                style={{ width: `${fillPercentage}%` }}
                            >
                                <Star
                                    size={size}
                                    className={`${starColor} transition-colors duration-150`}
                                    fill="currentColor"
                                />
                            </div>
                        </div>
                    );
                })}
            </div>
            
            {showValue && (
                <span className="text-sm text-gray-600 ml-2 min-w-[3rem]">
                    {isHovering ? hoverRating.toFixed(1) : rating.toFixed(1)} / 5.0
                </span>
            )}
        </div>
    );
};

export default StarRating;
