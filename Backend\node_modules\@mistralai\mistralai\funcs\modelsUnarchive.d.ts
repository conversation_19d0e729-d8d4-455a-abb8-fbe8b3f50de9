import { MistralCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import { SDKError } from "../models/errors/sdkerror.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
/**
 * Unarchive Fine Tuned Model
 *
 * @remarks
 * Un-archive a fine-tuned model.
 */
export declare function modelsUnarchive(client: MistralCore, request: operations.JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest, options?: RequestOptions): APIPromise<Result<components.UnarchiveFTModelOut, SDKError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>>;
//# sourceMappingURL=modelsUnarchive.d.ts.map