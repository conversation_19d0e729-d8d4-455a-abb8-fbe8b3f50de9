import { MAINTENANCE_CONFIG } from '../config/maintenance';

/**
 * Utility functions for managing maintenance mode
 */

// Enable maintenance mode
export const enableMaintenanceMode = () => {
    localStorage.setItem('maintenanceMode', 'true');
    console.log('🚧 Maintenance mode enabled');
};

// Disable maintenance mode
export const disableMaintenanceMode = () => {
    localStorage.removeItem('maintenanceMode');
    console.log('✅ Maintenance mode disabled');
};

// Check if maintenance mode is currently active
export const isMaintenanceModeActive = () => {
    // If FORCE_DISABLE is true, always return false (maintenance disabled)
    if (MAINTENANCE_CONFIG.FORCE_DISABLE) {
        // Clear localStorage if it exists
        if (localStorage.getItem('maintenanceMode')) {
            localStorage.removeItem('maintenanceMode');
            console.log('🧹 Cleared localStorage maintenance flag due to FORCE_DISABLE');
        }
        return false;
    }

    // Check localStorage first (for dynamic enabling)
    const storageValue = localStorage.getItem('maintenanceMode');

    console.log('🔍 Maintenance check:', {
        localStorage: storageValue,
        config: MAINTENANCE_CONFIG.MAINTENANCE_MODE,
        forceDisable: MAINTENANCE_CONFIG.FORCE_DISABLE,
        result: storageValue === 'true' || MAINTENANCE_CONFIG.MAINTENANCE_MODE
    });

    if (storageValue === 'true') {
        return true;
    }

    // Check config file
    return MAINTENANCE_CONFIG.MAINTENANCE_MODE;
};

// Toggle maintenance mode
export const toggleMaintenanceMode = () => {
    if (isMaintenanceModeActive()) {
        disableMaintenanceMode();
    } else {
        enableMaintenanceMode();
    }
    
    // Reload page to apply changes
    setTimeout(() => {
        window.location.reload();
    }, 500);
};

// Enable maintenance mode and reload page
export const enableMaintenanceModeAndReload = () => {
    enableMaintenanceMode();
    setTimeout(() => {
        window.location.reload();
    }, 1000);
};

// Disable maintenance mode and reload page
export const disableMaintenanceModeAndReload = () => {
    disableMaintenanceMode();
    setTimeout(() => {
        window.location.reload();
    }, 500);
};

// Check if current route should bypass maintenance
export const shouldBypassMaintenance = (pathname) => {
    return MAINTENANCE_CONFIG.shouldBypassMaintenance(pathname);
};

// Force clear all maintenance flags (for debugging)
export const forceClearMaintenanceMode = () => {
    // Clear localStorage
    localStorage.removeItem('maintenanceMode');

    // Reset config (this won't persist after reload)
    MAINTENANCE_CONFIG.MAINTENANCE_MODE = false;

    console.log('🧹 Force cleared all maintenance flags');
    console.log('Current status:', isMaintenanceModeActive());

    // Reload page to apply changes
    setTimeout(() => {
        window.location.reload();
    }, 500);
};

// For development/testing purposes - add to window object
if (process.env.NODE_ENV === 'development') {
    window.maintenanceUtils = {
        enable: enableMaintenanceMode,
        disable: disableMaintenanceMode,
        toggle: toggleMaintenanceMode,
        isActive: isMaintenanceModeActive,
        enableAndReload: enableMaintenanceModeAndReload,
        disableAndReload: disableMaintenanceModeAndReload,
        forceClear: forceClearMaintenanceMode
    };

    console.log('🔧 Maintenance utilities available at window.maintenanceUtils');
    console.log('Usage:');
    console.log('  window.maintenanceUtils.enable() - Enable maintenance mode');
    console.log('  window.maintenanceUtils.disable() - Disable maintenance mode');
    console.log('  window.maintenanceUtils.toggle() - Toggle maintenance mode');
    console.log('  window.maintenanceUtils.isActive() - Check if active');
    console.log('  window.maintenanceUtils.forceClear() - Force clear all flags');
}
