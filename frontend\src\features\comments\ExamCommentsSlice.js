import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import * as examCommentsApi from "../../services/examCommentsApi";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";


export const fetchCommentsByExamId = createAsyncThunk(
    "comments/fetchCommentsByExamId",
    async ({ examId, page = 1 }, { dispatch }) => {
        return await apiHandler(dispatch, examCommentsApi.getCommentsByExamIdAPI, { examId, page }, () => {
        }, false, false);
    }
);

export const fetchRepliesByCommentId = createAsyncThunk(
    "comments/fetchRepliesByCommentId",
    async ({ commentId, page = 1 }, { dispatch }) => {
        return await apiHandler(dispatch, examCommentsApi.getRepliesByCommentIdAPI, { commentId, page }, () => {
        }, false, false);
    }
);

export const postComment = createAsyncThunk(
    "comments/postComment",
    async ({ examId, content, parentCommentId = null }, { dispatch }) => {
        return await apiHandler(dispatch, examCommentsApi.postCommentAPI, { examId, content, parentCommentId }, () => {
        }, false, false);
    }
);

export const putComment = createAsyncThunk(
    "comments/putComment",
    async ({ commentId, content }, { dispatch }) => {
        return await apiHandler(dispatch, examCommentsApi.putCommentAPI, { commentId, content }, () => {
        }, false, false);
    }
);

export const deleteComment = createAsyncThunk(
    "comments/deleteComment",
    async (commentId, { dispatch }) => {
        return await apiHandler(dispatch, examCommentsApi.deleteCommentAPI, commentId, () => {
        }, false, false);
    }
);

const examCommentsSlice = createSlice({
    name: "comments",
    initialState: {
        comments: [],
        replies: {},
        loading: false,
        loadingAdd: false,
        loadingEdit: false,
        loadingDelete: false,
        pagination: { ...initialPaginationState },
        ...initialFilterState,

    },
    reducers: {
        ...paginationReducers,
        ...filterReducers,
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchCommentsByExamId.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchCommentsByExamId.fulfilled, (state, action) => {
                if (action.payload) {

                    state.pagination = action.payload.pagination;
                    if (state.pagination.page == 1) {
                        state.comments = action.payload.data;
                    } else {
                        state.comments = [...state.comments, ...action.payload.data];
                    }
                }
                state.loading = false;
            })
            .addCase(fetchCommentsByExamId.rejected, (state) => {
                state.loading = false;
            })
            .addCase(fetchRepliesByCommentId.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchRepliesByCommentId.fulfilled, (state, action) => {
                if (action.payload) {
                    const comments = action.payload.data;
                    const commentId = action.meta.arg.commentId;
                    state.replies[commentId] = comments;
                }
                state.loading = false;
            })
            .addCase(fetchRepliesByCommentId.rejected, (state) => {
                state.loading = false;
            })
            .addCase(postComment.pending, (state) => {
                state.loadingAdd = true;
            })
            .addCase(postComment.fulfilled, (state, action) => {
                if (action.payload) {
                    const comment = action.payload.data;
                    if (comment.commentId) {
                        state.comments = state.comments.map(c => {
                            if (c.id === comment.commentId) {
                                c.replyCount++;
                            }
                            return c;
                        });
                    } else {
                        state.comments.unshift(comment);
                    }
                }
                state.loadingAdd = false;
            })
            .addCase(postComment.rejected, (state) => {
                state.loadingAdd = false;
            })
            .addCase(putComment.pending, (state) => {
                state.loadingEdit = true;
            })
            .addCase(putComment.fulfilled, (state, action) => {
                if (action.payload) {
                    const comment = action.payload.data;
                    if (comment.commentId && Array.isArray(state.replies[comment.commentId])) {
                        state.replies[comment.commentId] = state.replies[comment.commentId].map(c => {
                            if (c.id === comment.id) {
                                return {
                                    ...c,
                                    ...comment,
                                };
                            }
                            return c;
                        });
                    } else if (!comment.commentId) {
                        const index = state.comments.findIndex(c => c.id === action.payload.data.id);
                        if (index !== -1) {
                            state.comments[index] = {
                                ...state.comments[index],
                                ...comment,
                            };
                        }
                    }
                }
                state.loadingEdit = false;
            })
            .addCase(putComment.rejected, (state) => {
                state.loadingEdit = false;
            })
            .addCase(deleteComment.pending, (state) => {
                state.loadingDelete = true;
            })
            .addCase(deleteComment.fulfilled, (state, action) => {
                if (action.payload) {
                    const commentId = action.payload.data;

                    // Xóa khỏi danh sách comment gốc
                    state.comments = state.comments.filter(c => c.id != commentId);

                    // Xóa reply có id === commentId ở tất cả các danh sách replies
                    Object.keys(state.replies).forEach(parentId => {
                        state.replies[parentId] = state.replies[parentId].filter(reply => reply.id != commentId);
                        state.comments = state.comments.map(c => {
                            if (c.id == parentId) {
                                c.replyCount--;
                            }
                            return c;
                        });
                    });

                    // Nếu commentId là cha (tức nó có mảng replies riêng), xóa luôn
                    if (state.replies[commentId]) {
                        delete state.replies[commentId];
                    }
                }

                state.loadingDelete = false;
            })
            .addCase(deleteComment.rejected, (state) => {
                state.loadingDelete = false;
            })
    },
});

export const { setCurrentPage, setLoading } = examCommentsSlice.actions;
export default examCommentsSlice.reducer;