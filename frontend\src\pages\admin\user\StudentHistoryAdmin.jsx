import UserAdminLayout from "src/layouts/UserAdminLayout";
import { TableAdmin, TdAdmin, ThAdmin, TheadAdmin } from "src/components/table/TableAdmin";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { fetchAttemptsByUserId } from "src/features/attempt/attemptSlice";
import { TotalComponent } from "src/components/table/TotalComponent";
import { setSortOrder, setCurrentPage } from "src/features/filter/filterSlice";
import LoadingData from "src/components/loading/LoadingData";
import Pagination from "src/components/Pagination";

const Table = () => {
    const { currentPage, totalItems, limit, sortOrder } = useSelector((state) => state.filter);
    const { userAttempts, loading } = useSelector((state) => state.attempts);
    const dispatch = useDispatch();

    return (
        <LoadingData
            loading={loading}
            loadText="Đang tải dữ liệu làm bài"
            noDataText="Không có dữ liệu làm bài."
            isNoData={userAttempts.length > 0 ? false : true}>
            <TotalComponent
                total={totalItems}
                page={currentPage}
                pageSize={limit}
                setSortOrder={() => dispatch(setSortOrder())}
            />
            <TableAdmin>
                <TheadAdmin>
                    <ThAdmin>Tên đề</ThAdmin>
                    <ThAdmin>Loại đề</ThAdmin>
                    <ThAdmin>Thời lượng</ThAdmin>
                    <ThAdmin>Điểm</ThAdmin>
                    <ThAdmin>Bắt đầu</ThAdmin>
                    <ThAdmin>Kết thúc</ThAdmin>
                    <ThAdmin>Thời gian làm</ThAdmin>
                </TheadAdmin>
                <tbody>
                    {userAttempts.map((attempt, index) => (
                        <tr key={index} className="hover:bg-blue-50 transition">
                            <TdAdmin>{attempt.exam.name}</TdAdmin>
                            <TdAdmin>{attempt.exam.typeOfExam}</TdAdmin>
                            <TdAdmin>{attempt.exam.testDuration ? attempt.exam.testDuration : 'Không có'}</TdAdmin>
                            <TdAdmin>{attempt.score}</TdAdmin>
                            <TdAdmin>{new Date(attempt.startTime).toLocaleString("vi-VN", { timeZone: "Asia/Ho_Chi_Minh" })}</TdAdmin>
                            <TdAdmin>{new Date(attempt.endTime).toLocaleString("vi-VN", { timeZone: "Asia/Ho_Chi_Minh" })}</TdAdmin>
                            <TdAdmin>{attempt.duration}</TdAdmin>
                        </tr>
                    ))}
                </tbody>
            </TableAdmin>
            <Pagination
                totalItems={totalItems}
                currentPage={currentPage}
                limit={limit}
                onPageChange={(page) => dispatch(setCurrentPage(page))}
            />
        </LoadingData>
    )
}

export const StudentHistoryAdmin = () => {
    const dispatch = useDispatch();
    const { userId } = useParams();
    const { userAttempts } = useSelector((state) => state.attempts);
    const { currentPage, totalItems, limit, sortOrder } = useSelector((state) => state.filter);

    useEffect(() => {
        if (userId) {
            dispatch(fetchAttemptsByUserId({ userId, params: { page: currentPage, limit, sortOrder } }));
        }
    }, [userId, dispatch, currentPage, limit, sortOrder]);


    return (
        <UserAdminLayout>
            <div className="flex-1 overflow-hidden p-6 text-sm">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                    <div className="flex-shrink-0 p-6 border-b border-gray-200 flex items-center justify-between mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Lịch sử làm đề</h3>
                    </div>

                    <div className="flex-1 min-h-0 p-6 gap-4 flex flex-col">
                        <Table />

                    </div>
                </div>
            </div>
        </UserAdminLayout>
    );
};

export default StudentHistoryAdmin;
