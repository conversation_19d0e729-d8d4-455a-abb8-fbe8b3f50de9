
import { useDispatch, useSelector } from "react-redux";
import NetworkSpeedTest from "../NetworkSpeedTest";
import TimeDisplay from "../sidebar/TimeDisplay";
import { formatTime } from "../../utils/formatters";
import QuestionCounter from "./QuestionCounter";
import ProgressBar from "./ProgressBar";
import SubmitButton from "./SubmitButton";
import { setView } from "src/features/doExam/doExamSlice";

const QuestionsButtonSection = ({ title, questions, questionRefs }) => {
    const { darkMode, saveQuestions, errorQuestions } = useSelector((state) => state.doExam);
    const dispatch = useDispatch();

    const getButtonStyle = (questionId) => {
        const idStr = String(questionId);

        if (errorQuestions.map(String).includes(idStr)) {
            return darkMode
                ? "bg-red-600 text-white"
                : "bg-red-500 text-white";
        } else if (saveQuestions.map(String).includes(idStr)) {
            return darkMode
                ? "bg-green-600 text-white"
                : "bg-green-500 text-white";
        } else {
            return darkMode
                ? "bg-gray-700 hover:bg-gray-600 text-white"
                : "bg-sky-100 hover:bg-sky-300 text-black";
        }
    };


    const scrollToQuestion = (questionId) => {
        const element = questionRefs?.current?.[questionId];
        if (element) {
            const topOffset = 96;
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition = window.pageYOffset + elementPosition - topOffset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth',
            });

            // 👉 Thêm class highlight hiệu ứng đẹp hơn (sky + bg + shadow)
            const highlightClasses = [
                'border-2',
                'border-sky-500',
                'bg-sky-100/40',
                'rounded-lg',
                'shadow-lg',
                'transition-all',
                'duration-700',
            ];
            const highlightClassesDark = [
                'border-sky-400',
                'bg-sky-900/40',
            ];

            element.classList.add(...highlightClasses);
            if (darkMode) {
                element.classList.add(...highlightClassesDark);
                element.classList.remove('border-sky-500', 'bg-sky-100/40');
            }

            // ❌ Gỡ highlight sau 1s
            setTimeout(() => {
                element.classList.remove(...highlightClasses, ...highlightClassesDark);
            }, 1000);
        }
    };




    return (
        <div className="flex flex-col gap-2">
            <div className="font-bold">{title}</div>
            <div className="flex flex-wrap gap-2">
                {questions.map((question, index) => (
                    <button
                        key={index}
                        onClick={() => {
                            dispatch(setView(question.typeOfQuestion));

                            // Đợi 100ms để UI render xong rồi mới scroll
                            setTimeout(() => {
                                scrollToQuestion(question.id);
                            }, 100);
                        }}

                        className={`w-7 h-7 rounded text-xs font-bold flex items-center justify-center transition-colors ${getButtonStyle(question.id)}`}
                    >
                        {index + 1}
                    </button>
                ))}
            </div>
        </div>
    );
};

const ExamSideBar = ({ questionRefs }) => {
    const { darkMode, loadingLoadExam, exam, remainingTime, questionTN, questionDS, questionTLN, saveQuestions, errorQuestions, selectedQuestion, questions, handleAutoSubmit, loadingSubmit } = useSelector((state) => state.doExam);
    return (
        <>
            <div className="flex flex-col gap-2">
                <div className="flex items-center justify-between w-full">
                    <p className="font-semibold">Tốc độ mạng: </p>
                    <NetworkSpeedTest />
                </div>
                <div className="flex items-center justify-between w-full">
                    <p className="font-semibold">Thời gian còn lại: </p>
                    <TimeDisplay
                        isLoading={loadingLoadExam}
                        exam={exam}
                        remainingTime={remainingTime}
                        isDarkMode={darkMode}
                    />
                </div>
                <ProgressBar
                    completed={saveQuestions.length}
                    total={questions.length}
                    isDarkMode={darkMode}
                />
            </div>
            <hr className="sm:my-4 my-2" />
            <QuestionsButtonSection title="Phần I - Trắc nghiệm" questions={questionTN} questionRefs={questionRefs} />
            <hr className="sm:my-4 my-2" />

            <QuestionsButtonSection title="Phần II - Đúng sai" questions={questionDS} questionRefs={questionRefs} />
            <hr className="sm:my-4 my-2" />

            <QuestionsButtonSection title="Phần III - Trả lời ngắn" questions={questionTLN} questionRefs={questionRefs} />
            <hr className="sm:my-4 my-2" />

            <div className="flex flex-col gap-2">
                <QuestionCounter
                    count={saveQuestions.length}
                    label="Số câu đã làm"
                    bgColor="bg-green-600"
                    bgColorLight="bg-green-500"
                    isDarkMode={darkMode}
                />

                <QuestionCounter
                    count={selectedQuestion !== null ? 1 : 0}
                    label="Số câu đang làm"
                    bgColor="bg-yellow-600"
                    bgColorLight="bg-yellow-400"
                    textColorLight="text-black"
                    isDarkMode={darkMode}
                />

                <QuestionCounter
                    count={questions.length - saveQuestions.length}
                    label="Số câu chưa làm"
                    bgColor="bg-gray-700"
                    bgColorLight="bg-gray-300"
                    textColorLight="text-black"
                    isDarkMode={darkMode}
                />

                <QuestionCounter
                    count={errorQuestions.length}
                    label="Số câu chưa lưu"
                    bgColor="bg-red-600"
                    bgColorLight="bg-red-400"
                    isDarkMode={darkMode}
                />
                <hr className="sm:my-4 my-2" />

                <SubmitButton
                    handleSubmit={handleAutoSubmit}
                    isLoading={loadingSubmit || loadingLoadExam}
                />
            </div>
        </>
    )
}

export default ExamSideBar;