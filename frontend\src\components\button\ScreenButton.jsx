import { useState, useEffect } from "react";
import { Maximize, Minimize  } from "lucide-react";
import ButtonHeader from "../header/ButtonHeader";

const FullScreen = ({ hidden = false, isDarkMode, className = "fullscreen-btn bg-transparent border-none cursor-pointer text-white text-2xl transition-all duration-300 transform hover:scale-110 p-2 rounded-md" }) => {
    const [isFullscreen, setIsFullscreen] = useState(false);

    const enterFullscreen = () => {
        const elem = document.documentElement; // Toàn bộ phần tử HTML

        if (elem.requestFullscreen) {
            elem.requestFullscreen();
        } else if (elem.mozRequestFullScreen) { // Firefox
            elem.mozRequestFullScreen();
        } else if (elem.webkitRequestFullscreen) { // Chrome, Safari và Opera
            elem.webkitRequestFullscreen();
        } else if (elem.msRequestFullscreen) { // IE/Edge
            elem.msRequestFullscreen();
        }
    };

    const exitFullscreen = () => {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.mozCancelFullScreen) { // Firefox
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) { // Chrome, Safari và Opera
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { // IE/Edge
            document.msExitFullscreen();
        }
    };

    // Kiểm tra nếu đang ở chế độ full screen và thay đổi trạng thái
    useEffect(() => {
        const onFullscreenChange = () => {
            if (document.fullscreenElement) {
                setIsFullscreen(true);
            } else {
                setIsFullscreen(false);
            }
        };

        document.addEventListener("fullscreenchange", onFullscreenChange);
        document.addEventListener("webkitfullscreenchange", onFullscreenChange);
        document.addEventListener("mozfullscreenchange", onFullscreenChange);
        document.addEventListener("MSFullscreenChange", onFullscreenChange);

        return () => {
            document.removeEventListener("fullscreenchange", onFullscreenChange);
            document.removeEventListener("webkitfullscreenchange", onFullscreenChange);
            document.removeEventListener("mozfullscreenchange", onFullscreenChange);
            document.removeEventListener("MSFullscreenChange", onFullscreenChange);
        };
    }, []);

    const toggleFullscreen = () => {
        if (isFullscreen) {
            exitFullscreen();
        } else {
            enterFullscreen();
        }
    };

    return (
        <ButtonHeader
            darkMode={isDarkMode}
            onClick={toggleFullscreen}
            Icon={isFullscreen ? Minimize : Maximize}
            hidden={hidden}
            title={isFullscreen ? "Thu nhỏ" : "Toàn màn hình"}
        />
    );
}

export default FullScreen;