import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'
import uploadPDF from '../middlewares/pdfGoogleUpload.js'
import * as ArticleController from '../controllers/ArticleController.js'

const router = express.Router()

// lấy tất cả bài viết ai cũng có thể xem
router.get('/v1/user/article',
    requireRoles(Roles.JustStudent),
    asyncHandler(ArticleController.getArticle)
)

// lấy tất cả bài viết mới nhất
// router.get('/v1/user/article/newest',
//     asyncHandler(ArticleController.getNewestArticle)
// )

// đếm số lượng bài viết theo loại
router.get('/v1/user/article/type/count',
    requireRoles(Roles.JustStudent),
    asyncHandler(ArticleController.countArticleByType)
)

// lấy bài viết theo id
router.get('/v1/user/article/:id',
    requireRoles(Roles.JustStudent),
    asyncHandler(ArticleController.getArticleById)
)

// lấy bài viết theo loại
router.put('/v1/admin/article/:id',
    requireRoles(Roles.JustClassManagement),
    asyncHandler(ArticleController.putArticle)
)

// tạo mới bài viết
router.post('/v1/admin/article',
    requireRoles(Roles.JustClassManagement),
    asyncHandler(ArticleController.postArticle)
)

// xóa bài viết
router.delete('/v1/admin/article/:id',
    requireRoles(Roles.JustClassManagement),
    asyncHandler(ArticleController.deleteArticle)
)

export default router
