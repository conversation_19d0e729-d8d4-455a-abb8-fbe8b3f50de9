/**
 * Simple cache manager for API responses
 */

// Cache storage
const cache = new Map();

// Default TTL (Time To Live) in milliseconds
const DEFAULT_TTL = 60 * 1000; // 1 minute

/**
 * Get a value from cache
 * @param {string} key - Cache key
 * @returns {any|null} - Cached value or null if not found or expired
 */
export const getCachedValue = (key) => {
  if (!cache.has(key)) return null;
  
  const { value, expiry } = cache.get(key);
  const now = Date.now();
  
  // Check if cache entry has expired
  if (now > expiry) {
    cache.delete(key);
    return null;
  }
  
  return value;
};

/**
 * Set a value in cache
 * @param {string} key - Cache key
 * @param {any} value - Value to cache
 * @param {number} ttl - Time to live in milliseconds (default: 1 minute)
 */
export const setCachedValue = (key, value, ttl = DEFAULT_TTL) => {
  const expiry = Date.now() + ttl;
  cache.set(key, { value, expiry });
};

/**
 * Clear a specific cache entry
 * @param {string} key - Cache key to clear
 */
export const clearCacheEntry = (key) => {
  cache.delete(key);
};

/**
 * Clear all cache entries
 */
export const clearAllCache = () => {
  cache.clear();
};

/**
 * Clear all cache entries with a specific prefix
 * @param {string} prefix - Prefix to match
 */
export const clearCacheWithPrefix = (prefix) => {
  for (const key of cache.keys()) {
    if (key.startsWith(prefix)) {
      cache.delete(key);
    }
  }
};

/**
 * Get a cached API response or fetch it if not cached
 * @param {string} key - Cache key
 * @param {Function} fetchFunction - Function to fetch data if not cached
 * @param {number} ttl - Time to live in milliseconds (default: 1 minute)
 * @returns {Promise<any>} - Cached or fetched data
 */
export const getCachedOrFetch = async (key, fetchFunction, ttl = DEFAULT_TTL) => {
  const cachedValue = getCachedValue(key);
  
  if (cachedValue !== null) {
    return cachedValue;
  }
  
  const fetchedValue = await fetchFunction();
  setCachedValue(key, fetchedValue, ttl);
  
  return fetchedValue;
};
