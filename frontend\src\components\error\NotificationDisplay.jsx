import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { clearSuccessMessage, clearErrorsMessage } from "../../features/state/stateApiSlice";
import { X } from "lucide-react";

const NotificationDisplay = ({ customClassName = "" }) => {
    const successMessage = useSelector((state) => state.states.successMessage);
    const errorsMessage = useSelector((state) => state.states.errorsMessage);
    const dispatch = useDispatch();
    const [visibleMessages, setVisibleMessages] = useState([]);
    const [headerHeight, setHeaderHeight] = useState(0);

    // Lấy chiều cao header
    useEffect(() => {
        const header = document.querySelector('header');
        if (header) {
            const updateHeight = () => setHeaderHeight(header.offsetHeight + 16);
            updateHeight();
            window.addEventListener('resize', updateHeight);
            return () => window.removeEventListener('resize', updateHeight);
        }
    }, []);

    useEffect(() => {
        const addMessage = (message, type) => {
            const id = `${type}-${Date.now()}-${Math.random().toString(36).slice(2, 6)}`;
            const newMessage = { id, message, type, fadeOut: false };
            setVisibleMessages(prev => [...prev, newMessage]);

            // Trigger fade-out
            setTimeout(() => {
                setVisibleMessages(prev =>
                    prev.map(msg => msg.id === id ? { ...msg, fadeOut: true } : msg)
                );
            }, 2500);

            // Remove completely
            setTimeout(() => {
                setVisibleMessages(prev => prev.filter(msg => msg.id !== id));
                if (type === "success") dispatch(clearSuccessMessage());
                if (type === "error") dispatch(clearErrorsMessage());
            }, 3000);
        };

        if (successMessage) addMessage(successMessage, "success");
        if (errorsMessage) addMessage(errorsMessage, "error");
    }, [successMessage, errorsMessage, dispatch]);

    if (visibleMessages.length === 0) return null;

    return (
        <>
            <div className="fixed inset-0 pointer-events-none z-[9998]" />
            <div
                className={`
                    fixed
                    z-[9999]
                    pointer-events-auto
                    left-4 right-4 
                    top-24
                    sm:left-auto sm:right-6
                    w-[90%] sm:w-[350px]
                    flex flex-col gap-3
                    ${customClassName}
                `}
            >
                {visibleMessages.map((msg, index) => (
                    <div
                        key={msg.id}
                        className={`flex items-center justify-between pl-4 pr-2 py-2 rounded-md shadow-lg transition-all duration-500
                            ${msg.type === "success"
                                ? "bg-[#F0FDF4]  text-[#14532D] "
                                : "bg-[#FEF2F2] text-[#D32A07]"}
                            ${msg.fadeOut ? "opacity-0 translate-x-8" : "opacity-100 translate-x-0"}
                            backdrop-blur-sm`}
                        style={{ transitionDelay: `${index * 100}ms` }}
                    >
                        <div className="flex items-center gap-2 pr-2">
                            {msg.type === "success" ? (
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16" fill="none">
                                    <path d="M8 1C9.85652 1 11.637 1.7375 12.9497 3.05025C14.2625 4.36301 15 6.14348 15 8C15 9.85652 14.2625 11.637 12.9497 12.9497C11.637 14.2625 9.85652 15 8 15C6.14348 15 4.36301 14.2625 3.05025 12.9497C1.7375 11.637 1 9.85652 1 8C1 6.14348 1.7375 4.36301 3.05025 3.05025C4.36301 1.7375 6.14348 1 8 1ZM7.128 9.381L5.573 7.825C5.51725 7.76925 5.45107 7.72503 5.37824 7.69486C5.3054 7.66469 5.22734 7.64917 5.1485 7.64917C5.06966 7.64917 4.9916 7.66469 4.91876 7.69486C4.84593 7.72503 4.77975 7.76925 4.724 7.825C4.61142 7.93758 4.54817 8.09028 4.54817 8.2495C4.54817 8.40872 4.61142 8.56142 4.724 8.674L6.704 10.654C6.75959 10.71 6.82572 10.7545 6.89857 10.7848C6.97143 10.8152 7.04957 10.8308 7.1285 10.8308C7.20743 10.8308 7.28557 10.8152 7.35843 10.7848C7.43128 10.7545 7.49741 10.71 7.553 10.654L11.653 6.553C11.7095 6.49748 11.7544 6.43133 11.7852 6.35836C11.816 6.28539 11.8321 6.20705 11.8324 6.12785C11.8328 6.04864 11.8175 5.97015 11.7874 5.8969C11.7573 5.82365 11.7129 5.75708 11.657 5.70104C11.601 5.645 11.5345 5.6006 11.4613 5.5704C11.388 5.54019 11.3096 5.52478 11.2304 5.52506C11.1512 5.52533 11.0728 5.54129 10.9998 5.572C10.9268 5.60271 10.8606 5.64757 10.805 5.704L7.128 9.381Z" fill="#05DF72" />
                                </svg>

                            ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 17C12.2833 17 12.521 16.904 12.713 16.712C12.905 16.52 13.0007 16.2827 13 16C12.9993 15.7173 12.9033 15.48 12.712 15.288C12.5207 15.096 12.2833 15 12 15C11.7167 15 11.4793 15.096 11.288 15.288C11.0967 15.48 11.0007 15.7173 11 16C10.9993 16.2827 11.0953 16.5203 11.288 16.713C11.4807 16.9057 11.718 17.0013 12 17ZM12 13C12.2833 13 12.521 12.904 12.713 12.712C12.905 12.52 13.0007 12.2827 13 12V8C13 7.71667 12.904 7.47933 12.712 7.288C12.52 7.09667 12.2827 7.00067 12 7C11.7173 6.99933 11.48 7.09533 11.288 7.288C11.096 7.48067 11 7.718 11 8V12C11 12.2833 11.096 12.521 11.288 12.713C11.48 12.905 11.7173 13.0007 12 13ZM12 22C10.6167 22 9.31667 21.7373 8.1 21.212C6.88334 20.6867 5.825 19.9743 4.925 19.075C4.025 18.1757 3.31267 17.1173 2.788 15.9C2.26333 14.6827 2.00067 13.3827 2 12C1.99933 10.6173 2.262 9.31733 2.788 8.1C3.314 6.88267 4.02633 5.82433 4.925 4.925C5.82367 4.02567 6.882 3.31333 8.1 2.788C9.318 2.26267 10.618 2 12 2C13.382 2 14.682 2.26267 15.9 2.788C17.118 3.31333 18.1763 4.02567 19.075 4.925C19.9737 5.82433 20.6863 6.88267 21.213 8.1C21.7397 9.31733 22.002 10.6173 22 12C21.998 13.3827 21.7353 14.6827 21.212 15.9C20.6887 17.1173 19.9763 18.1757 19.075 19.075C18.1737 19.9743 17.1153 20.687 15.9 21.213C14.6847 21.739 13.3847 22.0013 12 22Z" fill="#FF6467" />
                                </svg>
                            )}
                            <span className="text-sm font-medium">{msg.message}</span>
                        </div>
                        <button
                            onClick={() =>
                                setVisibleMessages(prev => prev.filter(m => m.id !== msg.id))
                            }
                            className={`${msg.type === "success" ? "text-[#14532D] hover:bg-[#d6fce1]" : "text-[#D32A07] hover:bg-[#FEE2E2]"} transition p-2 rounded-md`}
                        >
                            <X className="w-4 h-4" />
                        </button>
                    </div>
                ))}
            </div>
        </>
    );
};

export default NotificationDisplay;
