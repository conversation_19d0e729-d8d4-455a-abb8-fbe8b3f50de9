import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type JobsApiRoutesFineTuningStartFineTuningJobRequest = {
    jobId: string;
};
/**
 * OK
 */
export type JobsApiRoutesFineTuningStartFineTuningJobResponse = (components.ClassifierDetailedJobOut & {
    jobType: "classifier";
}) | (components.CompletionDetailedJobOut & {
    jobType: "completion";
});
/** @internal */
export declare const JobsApiRoutesFineTuningStartFineTuningJobRequest$inboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound = {
    job_id: string;
};
/** @internal */
export declare const JobsApiRoutesFineTuningStartFineTuningJobRequest$outboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningStartFineTuningJobRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace JobsApiRoutesFineTuningStartFineTuningJobRequest$ {
    /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningStartFineTuningJobRequest>;
    /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound` instead. */
    type Outbound = JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound;
}
export declare function jobsApiRoutesFineTuningStartFineTuningJobRequestToJSON(jobsApiRoutesFineTuningStartFineTuningJobRequest: JobsApiRoutesFineTuningStartFineTuningJobRequest): string;
export declare function jobsApiRoutesFineTuningStartFineTuningJobRequestFromJSON(jsonString: string): SafeParseResult<JobsApiRoutesFineTuningStartFineTuningJobRequest, SDKValidationError>;
/** @internal */
export declare const JobsApiRoutesFineTuningStartFineTuningJobResponse$inboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobResponse, z.ZodTypeDef, unknown>;
/** @internal */
export type JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound = (components.ClassifierDetailedJobOut$Outbound & {
    job_type: "classifier";
}) | (components.CompletionDetailedJobOut$Outbound & {
    job_type: "completion";
});
/** @internal */
export declare const JobsApiRoutesFineTuningStartFineTuningJobResponse$outboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningStartFineTuningJobResponse>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace JobsApiRoutesFineTuningStartFineTuningJobResponse$ {
    /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobResponse$inboundSchema` instead. */
    const inboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobResponse, z.ZodTypeDef, unknown>;
    /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobResponse$outboundSchema` instead. */
    const outboundSchema: z.ZodType<JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningStartFineTuningJobResponse>;
    /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound` instead. */
    type Outbound = JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound;
}
export declare function jobsApiRoutesFineTuningStartFineTuningJobResponseToJSON(jobsApiRoutesFineTuningStartFineTuningJobResponse: JobsApiRoutesFineTuningStartFineTuningJobResponse): string;
export declare function jobsApiRoutesFineTuningStartFineTuningJobResponseFromJSON(jsonString: string): SafeParseResult<JobsApiRoutesFineTuningStartFineTuningJobResponse, SDKValidationError>;
//# sourceMappingURL=jobsapiroutesfinetuningstartfinetuningjob.d.ts.map