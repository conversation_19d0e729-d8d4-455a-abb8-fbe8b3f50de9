import { useState, useEffect, use } from "react";
import UserAdminLayout from "src/layouts/UserAdminLayout";
import { TableAdmin, TdAdmin, ThAdmin, TheadAdmin } from "src/components/table/TableAdmin";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchUserTuitionPaymentsAdmin } from "src/features/tuition/tuitionSlice";
import { setSortOrder, setCurrentPage } from "src/features/tuition/tuitionSlice";
import LoadingData from "src/components/loading/LoadingData";
import { TotalComponent } from "src/components/table/TotalComponent";
import Pagination from "src/components/Pagination";

const Table = () => {
    const { tuitionPayments, loading, pagination } = useSelector((state) => state.tuition);
    const dispatch = useDispatch();
    const { page, total, pageSize } = pagination;

    return (
        <LoadingData
            loading={loading}
            loadText="Đang tải dữ liệu học phí"
            noDataText="Không có dữ liệu học phí."
            isNoData={tuitionPayments.length === 0}
        >
            <TotalComponent
                total={total}
                page={page}
                pageSize={pageSize}
                setSortOrder={() => dispatch(setSortOrder())}
            />
            <TableAdmin>
                <TheadAdmin>
                    <ThAdmin>Id</ThAdmin>
                    <ThAdmin>Tháng</ThAdmin>
                    <ThAdmin>Trạng thái</ThAdmin>
                    <ThAdmin>Thời gian đóng</ThAdmin>
                    <ThAdmin>Hạn đóng</ThAdmin>
                    <ThAdmin>Ghi chú</ThAdmin>
                </TheadAdmin>
                <tbody>
                    {tuitionPayments.map((payment, index) => (
                        <tr key={payment.id || index} className="hover:bg-blue-50 transition border-b">
                            <TdAdmin>{payment.id}</TdAdmin>
                            <TdAdmin>{payment.monthFormatted}</TdAdmin>
                            <TdAdmin>
                                {payment.isPaid ? (
                                    <span className="text-green-600 font-medium">Đã đóng</span>
                                ) : payment.isOverdue ? (
                                    <span className="text-red-600 font-medium">Quá hạn</span>
                                ) : (
                                    <span className="text-yellow-600 font-medium">Chưa đóng</span>
                                )}
                            </TdAdmin>
                            <TdAdmin>{payment.paymentDateFormatted ? payment.paymentDateFormatted : '—'}</TdAdmin>
                            <TdAdmin>{payment.dueDateFormatted ? payment.dueDateFormatted : '—'}</TdAdmin>
                            <TdAdmin>{payment.note || '—'}</TdAdmin>
                        </tr>
                    ))}
                </tbody>
            </TableAdmin>
            <Pagination
                totalItems={total}
                currentPage={page}
                limit={pageSize}
                onPageChange={(page) => dispatch(setCurrentPage(page))}
            />
        </LoadingData>
    );
};


export const StudentTuitionAdmin = () => {
    const dispatch = useDispatch();
    const { userId } = useParams();

    const { tuitionPayments, loading, pagination } = useSelector((state) => state.tuition);

    useEffect(() => {
        dispatch(fetchUserTuitionPaymentsAdmin(userId));
    }, [dispatch, userId]);

    return (
        <UserAdminLayout>
            <div className="flex-1 overflow-hidden p-6 text-sm">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                    <div className="flex-shrink-0 p-6 border-b border-gray-200 flex items-center justify-between mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Học phí học sinh</h3>
                    </div>

                    <div className="flex-1 min-h-0 p-6 gap-4 flex flex-col">
                        <Table />
                    </div>
                </div>
            </div>
        </UserAdminLayout>
    );
};

export default StudentTuitionAdmin;
