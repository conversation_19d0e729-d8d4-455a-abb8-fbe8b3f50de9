import { Op } from "sequelize"
import db from "../models/index.js"
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"

const Question = db.Question
const Statement = db.Statement
const Exam = db.Exam


export const getQuestionsWithFilter = async ({ sortOrder = 'DESC', search = '', page = 1, limit = 10 }) => {
    const offset = (page - 1) * limit

    let whereClause = {}
    if (search.trim() !== '') {
        whereClause = {
            [Op.or]: [
                { content: { [Op.like]: `%${search}%` } },
                { typeOfQuestion: { [Op.like]: `%${search}%` } },
                { chapter: { [Op.like]: `%${search}%` } },
                { difficulty: { [Op.like]: `%${search}%` } },
                { class: { [Op.like]: `%${search}%` } },
                { id: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } },
            ],
        }
    }

    const [questionList, total] = await Promise.all([
        Question.findAll({
            where: whereClause,
            offset,
            limit,
            include: [
                {
                    model: Statement,
                    as: 'statements',
                    attributes: ['content', 'order', 'isCorrect', 'imageUrl'],
                },
            ],
            order: [['createdAt', sortOrder]],
        }),
        Question.count({ where: whereClause }),
    ])

    return new ResponseDataPagination(questionList, {
        total,
        page,
        pageSize: limit,
        totalPages: Math.ceil(total / limit),
        sortOrder,
    })
}