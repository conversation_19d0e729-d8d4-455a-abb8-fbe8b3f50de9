import db from '../models/index.js';
const { Class } = db;
import { Op, where } from 'sequelize';
import StudentClassStatus from '../constants/StudentClassStatus.js';

export const getAllClasses = async ({ search = '', page = 1, limit = 10, sortOrder = 'DESC' }) => {
    const offset = (page - 1) * limit;
    const whereClause = {
        ...(search.trim() && {
            [Op.or]: [
                { name: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } },
                { status: { [Op.like]: `%${search}%` } },
            ],
        }),
    };

    const { rows: classes, count: total } = await Class.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [
            ['academicYear', 'DESC'], // Sắp xếp theo năm học mới nhất trước
            ['grade', 'DESC'], // Sắp xếp theo lớp học (ví dụ: 10, 11, 12)
            ['name', 'ASC'], // hoặc 'DESC' tùy bạn muốn
            ['createdAt', sortOrder], // 'ASC' hoặc 'DESC'
        ],
    });

    return {
        classes,
        total,
        pageSize: limit,
        page,
        totalPages: Math.ceil(total / limit),
        sortOrder,
    };
};

/**
 * Lấy thông tin lớp học theo ID
 * @param {number} id - ID của lớp học
 * @param {Object} options - Các tùy chọn bổ sung (transaction, ...)
 * @returns {Promise<Object>} Thông tin lớp học
 */
export const getClassById = async (id, options = {}) => {
    const classItem = await db.Class.findByPk(id, options);
    return classItem;
};

export const getClassByClassCode = async (classCode) => {
    const classItem = await db.Class.findOne({
        where: { class_code: classCode },
    });

    return classItem;
};

export const getClassDetailById = async (id) => {
    const classItem = await db.Class.findOne({
        where: { id },
        include: [
            {
                model: db.Slide,
                as: 'slide',
                include: [
                    {
                        model: db.SlideImage,
                        as: 'slideImages',
                    }
                ]
            }
        ]
    });

    const studentCount = await getStudentCount(id);

    return {
        ...classItem.toJSON(),
        studentCount,
    };
};

export const getClassDetailByClassCode = async (classCode) => {
    const classItem = await db.Class.findOne({
        where: { class_code: classCode },
        include: [
            {
                model: db.Slide,
                as: 'slide',
                include: [
                    {
                        model: db.SlideImage,
                        as: 'slideImages',
                    }
                ]
            }
        ]
    });

    return classItem
};

export const getClassesByUserId = async (userId) => {
    const classes = await db.StudentClassStatus.findAll({
        where: { studentId: userId },
        include: [
            {
                model: db.Class,
                as: 'class',
            },
        ],
    });

    return classes;
};

export const getOverviewClasses = async (userId) => {
    const classes = await db.StudentClassStatus.findAll({
        where: { studentId: userId, status: StudentClassStatus.JOINED },
        include: [
            {
                model: db.Class,
                as: 'class',
            },
        ],
    });

    return classes;
};

export const getAllStudentInClasses = async (classId) => {
    const studentClassStatuses = await db.StudentClassStatus.findAll({
        where: {
            classId,
            status: StudentClassStatus.JOINED
        },
        include: [
            {
                model: db.User,
                as: 'student',
                where: {
                    userType: 'HS1'
                },
                attributes: ['id'] // Chỉ lấy id để tối ưu performance
            }
        ],
        attributes: ['studentId']
    });

    // Trả về mảng userId
    return studentClassStatuses.map(s => s.studentId);
};

export const getDetailLessonLearningItemByClassId = async (classCode) => {
    const foundClass = await db.Class.findOne({
        where: { class_code: classCode, public: true },
        include: [
            {
                model: db.Lesson,
                as: 'lessons',
                attributes: ['name', 'description', 'id', 'day'], // Chỉ lấy name & description của Lesson
                include: [
                    {
                        model: db.LearningItem,
                        as: 'learningItems',
                        attributes: ['name', 'id', 'typeOfLearningItem'] // Chỉ lấy name & description của LearningItem
                    }
                ],
                order: [
                    ['day', 'ASC']
                ]
            },
            {
                model: db.Slide,
                as: 'slide',
                include: [
                    {
                        model: db.SlideImage,
                        as: 'slideImages'
                    }
                ]
            }
        ]
    });

    return foundClass;
};

export const getFullLessonLearningItemByClassCode = async (classCode, userId) => {
    const foundClass = await db.Class.findOne({
        where: { class_code: classCode },
        include: [
            {
                model: db.Lesson,
                as: 'lessons',
                include: [
                    {
                        model: db.LearningItem,
                        as: 'learningItems',
                        include: [
                            {
                                model: db.StudentStudyStatus,
                                as: 'studyStatuses',
                                where: { studentId: userId },
                                attributes: ['isDone', 'studyTime'],
                                required: false
                            }
                        ]
                    }
                ]
            }
        ]
    });
    return foundClass;
};

export const getFullLessonByClassID = async (classId) => {
    const foundClass = await db.Class.findOne({
        where: { id: classId },
        include: [
            {
                model: db.Lesson,
                as: 'lessons',
                include: [
                    {
                        model: db.LearningItem,
                        as: 'learningItems'
                    }
                ]
            }
        ],
        order: [
            [{ model: db.Lesson, as: 'lessons' }, 'createdAt', 'DESC'],
            [{ model: db.Lesson, as: 'lessons' }, { model: db.LearningItem, as: 'learningItems' }, 'createdAt', 'DESC']
        ]
    });

    return foundClass;
};

export const postClass = async (data) => {
    try {
        // Bước 1: Tạo mã lớp trước (chưa có ID nên tạm random)
        const randomCode = Math.random().toString(36).substring(2, 7).toUpperCase(); // VD: "KX1P2"

        // Tạo bản ghi tạm với class_code rỗng hoặc code tạm
        const newClass = await Class.create({ ...data, class_code: "TEMP" });

        // Bước 2: Gán lại mã lớp có thêm ID để đảm bảo duy nhất
        const classCode = `${randomCode}${newClass.id}`;

        // Bước 3: Cập nhật lại
        newClass.class_code = classCode;
        await newClass.save();

        return newClass;
    } catch (error) {
        console.error("Tạo lớp thất bại:", error);
        throw error;
    }
};

export const putClass = async (id, data) => {
    try {
        const [updated] = await Class.update(data, { where: { id } });

        if (!updated) {
            throw new Error("Cập nhật lớp học không thành công!");
        }

        const updatedClass = await Class.findByPk(id);

        return updatedClass;
    } catch (error) {
        console.error("Cập nhật lớp thất bại:", error);
        throw error;
    }
};

/**
 * Xóa lớp học theo ID
 * @param {number} id - ID của lớp học cần xóa
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<boolean>} Kết quả xóa
 */
export const deleteClass = async (id, transaction = null) => {
    try {
        const options = { where: { id } };

        // Thêm transaction vào options nếu có
        if (transaction) {
            options.transaction = transaction;
        }

        const deleted = await Class.destroy(options);

        if (!deleted) {
            throw new Error("Xóa lớp học không thành công!");
        }

        return true;
    } catch (error) {
        console.error("Xóa lớp thất bại:", error);
        throw error;
    }
};

export const getStudentCount = async (classId, status = StudentClassStatus.JOINED) => {
    const studentCount = await db.StudentClassStatus.count({
        where: {
            classId,
            status,
        },
    });
    return studentCount;
};

export const getStatusByUserIdAndClassId = async (userId, classId) => {
    const statusData = await db.StudentClassStatus.findOne({
        where: { studentId: userId, classId }
    });

    const status = statusData?.status || StudentClassStatus.NOT_JOINED;

    return status;
};

export const createStudentInClass = async (studentId, classId, status, transaction = null) => {
    try {
        const options = {};
        if (transaction) {
            options.transaction = transaction;
        }

        // Add await keyword here to properly wait for the creation to complete
        const insert = await db.StudentClassStatus.create({ studentId, classId, status }, options);

        if (!insert) {
            throw new Error("Học viên đã tồn tại trong lớp học!");
        }

        return status;
    }
    catch (error) {
        console.error("Thêm học viên vào lớp học thất bại:", error);
        throw error;
    }
};

export const updateStudentStatusInClass = async (studentId, classId, status, transaction = null) => {
    try {
        const options = { where: { studentId, classId } };

        if (transaction) {
            options.transaction = transaction;
        }

        const updated = await db.StudentClassStatus.update({ status }, options);

        if (!updated[0]) {
            throw new Error("Cập nhật trạng thái học viên trong lớp học không thành công!");
        }

        return true;
    } catch (error) {
        console.error("Cập nhật trạng thái học viên trong lớp học thất bại:", error);
        throw error;
    }
};



export const deleteAllStudentInClass = async (classId, transaction = null) => {
    try {
        const options = { where: { classId } };

        if (transaction) {
            options.transaction = transaction;
        }

        const deleted = await db.StudentClassStatus.destroy(options);
        console.log("deleted", deleted)
        if (!deleted && deleted !== 0) {
            throw new Error("Xóa học viên khỏi lớp học không thành công!");
        }

        return true;
    } catch (error) {
        console.error("Xóa học viên khỏi lớp học thất bại:", error);
        throw error;
    }
};

export const deleteStudenInClass = async (studentId, classId, transaction = null) => {
    try {
        const options = { where: { studentId, classId } };

        if (transaction) {
            options.transaction = transaction;
        }

        const deleted = await db.StudentClassStatus.destroy(options);

        if (!deleted) {
            throw new Error("Xóa học viên khỏi lớp học không thành công!");
        }

        return true;
    } catch (error) {
        console.error("Xóa học viên khỏi lớp học thất bại:", error);
        throw error;
    }
};

