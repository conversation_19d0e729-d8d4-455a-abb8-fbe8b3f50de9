import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchAchievementDataForHomepage } from '../../features/achievement/achievementSlice';
import { Award, Trophy, Star, Target, TrendingUp, Users, Medal, Crown } from 'lucide-react';
import SlideShow from '../image/SlideShow';
import LoadingSpinner from '../loading/LoadingSpinner';

const AchievementSection = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { homepageData, homepageLoading } = useSelector(state => state.achievements);
    const [activeTab, setActiveTab] = useState(null);
    const [tabs, setTabs] = useState([]);

    // Fetch achievement data when component mounts
    useEffect(() => {
        dispatch(fetchAchievementDataForHomepage());
    }, [dispatch]);

    // Set up tabs based on fetched data
    useEffect(() => {
        if (homepageData && homepageData.length > 0) {
            // Create tabs from categories
            const newTabs = homepageData.map(category => ({
                id: category.id,
                label: category.label
            }));

            setTabs(newTabs);

            // Set the first tab as active if none is selected
            if (!activeTab) {
                setActiveTab(homepageData[0].id);
            }
        }
    }, [homepageData, activeTab]);

    // Get the active category data
    const getActiveCategoryData = () => {
        if (!activeTab || !homepageData) return null;
        return homepageData.find(category => category.id === activeTab);
    };

    const activeCategory = getActiveCategoryData();

    // Get images and captions for the active category
    const getActiveImagesData = () => {
        if (!activeCategory || !activeCategory.images) return { images: [], captions: [] };

        const images = activeCategory.images.map(image => image.image_url);

        // Process captions to convert escaped newlines to actual newlines
        const captions = activeCategory.images.map(image =>
            image.caption ? image.caption.replace(/\\n/g, '\n') : ""
        );

        return { images, captions };
    };

    if (homepageLoading) {
        return (
            <section className="w-full px-4 py-16 bg-gradient-to-br from-amber-50 via-white to-orange-50">
                <div className="max-w-screen-xl mx-auto text-center">
                    <div className="inline-block px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-4">
                        Thành tích học sinh
                    </div>
                    <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4">
                        Đang tải dữ liệu thành tích...
                    </h2>
                    <div className="flex justify-center">
                        <LoadingSpinner
                            size="4rem"
                            showText={false}
                        />
                    </div>
                </div>
            </section>
        );
    }

    if (!homepageData || homepageData.length === 0) {
        return (
            <section className="w-full px-4 py-16 bg-gradient-to-br from-amber-50 via-white to-orange-50">
                <div className="max-w-screen-xl mx-auto text-center">
                    <div className="inline-block px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-4">
                        Thành tích học sinh
                    </div>
                    <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4 flex items-center justify-center gap-3">
                        <Award className="w-8 h-8 text-amber-600" />
                        Thành tích xuất sắc
                    </h2>
                    <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                        Chưa có dữ liệu thành tích. Vui lòng quay lại sau.
                    </p>
                </div>
            </section>
        );
    }

    return (
        <section className="w-full px-4 py-16 bg-gradient-to-br from-amber-50 via-white to-orange-50">
            <div className="max-w-screen-xl mx-auto">
                {/* Header */}
                <motion.div
                    className="text-center mb-12"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                >
                    <div className="inline-block px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-4">
                        Thành tích học sinh
                    </div>
                    <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4 flex items-center justify-center gap-3">
                        <Award className="w-8 h-8 text-amber-600" />
                        Thành tích xuất sắc
                    </h2>
                    <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                        Tự hào về những thành tích xuất sắc của học sinh lớp Toán thầy Bee qua các kỳ thi quan trọng.
                    </p>
                </motion.div>

                {/* Tab navigation */}
                <motion.div
                    className="flex flex-wrap justify-center gap-3 mb-12"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                >
                    {tabs.map((tab, index) => (
                        <motion.button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg ${
                                activeTab === tab.id
                                    ? "bg-gradient-to-r from-amber-500 to-orange-500 text-white scale-105"
                                    : "bg-white text-amber-700 hover:bg-amber-50 border border-amber-200"
                            }`}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                        >
                            {tab.label}
                        </motion.button>
                    ))}
                </motion.div>

                {/* Main Content */}
                {activeCategory && (
                    <motion.div
                        className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.4 }}
                    >
                        {/* Left side - Slideshow */}
                        <motion.div
                            className="order-2 lg:order-1"
                            initial={{ opacity: 0, x: -50 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.6 }}
                        >
                            <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-amber-100">
                                {/* Decorative gradient overlay */}
                                <div className="absolute inset-0 bg-gradient-to-br from-amber-50/20 to-orange-50/20 z-10 pointer-events-none"></div>

                                {/* Decorative icon */}
                                <div className="absolute top-6 left-6 w-16 h-16 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center z-20 shadow-lg">
                                    <Trophy className="w-8 h-8 text-white" />
                                </div>

                                {/* Image counter */}
                                <div className="absolute top-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
                                    {getActiveImagesData().images.length > 0 ? `1 / ${getActiveImagesData().images.length}` : '0 / 0'}
                                </div>

                                {/* Slideshow */}
                                <div className="w-full aspect-square">
                                    <SlideShow
                                        key={activeTab} // Force re-render when tab changes
                                        interval={4000}
                                        images={getActiveImagesData().images}
                                        captions={getActiveImagesData().captions}
                                        h="h-full"
                                        objectFit='object-contain'
                                    />
                                </div>
                            </div>
                        </motion.div>

                        {/* Right side - Achievement content */}
                        <motion.div
                            className="order-1 lg:order-2"
                            initial={{ opacity: 0, x: 50 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.8 }}
                        >
                            <div className="space-y-8">
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 1.0 }}
                                >
                                    <h3 className="text-3xl font-bold text-amber-600 mb-4">{activeCategory.title}</h3>
                                    <p className="text-gray-700 text-lg leading-relaxed whitespace-pre-line">
                                        {activeCategory.description ? activeCategory.description.replace(/\\n/g, '\n') : ""}
                                    </p>
                                </motion.div>

                                {/* Stats Grid */}
                                <motion.div
                                    className="grid grid-cols-2 gap-4"
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 1.2 }}
                                >
                                    {activeCategory.stats && activeCategory.stats.map((stat, index) => (
                                        <motion.div
                                            key={index}
                                            className="bg-white p-6 rounded-xl shadow-lg border border-amber-100 hover:shadow-xl transition-all duration-300"
                                            initial={{ opacity: 0, scale: 0.9 }}
                                            whileInView={{ opacity: 1, scale: 1 }}
                                            transition={{ duration: 0.4, delay: 1.4 + index * 0.1 }}
                                            whileHover={{ scale: 1.05 }}
                                        >
                                            <div className="text-3xl font-bold text-amber-500 mb-2">{stat.value}</div>
                                            <div className="text-gray-600 text-sm font-medium">{stat.label}</div>
                                        </motion.div>
                                    ))}
                                </motion.div>

                                {/* Call to Action */}
                                <motion.div
                                    className="pt-4"
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 1.6 }}
                                >
                                    <motion.button
                                        onClick={() => navigate('/achievements')}
                                        className="px-8 py-3 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-full hover:from-amber-600 hover:to-orange-600 transition-all duration-300 flex items-center gap-3 mx-auto lg:mx-0 shadow-lg hover:shadow-xl font-semibold"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        <Medal size={20} />
                                        Xem tất cả thành tích
                                    </motion.button>
                                </motion.div>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </div>
        </section>
    );
};

export default AchievementSection;
