/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { embeddingsCreate } from "../funcs/embeddingsCreate.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import { unwrapAsync } from "../types/fp.js";

export class Embeddings extends ClientSDK {
  /**
   * Embeddings
   *
   * @remarks
   * Embeddings
   */
  async create(
    request: components.EmbeddingRequest,
    options?: RequestOptions,
  ): Promise<components.EmbeddingResponse> {
    return unwrapAsync(embeddingsCreate(
      this,
      request,
      options,
    ));
  }
}
