import ButtonFunctionBarAdmin from "../button/ButtonFunctionBarAdmin";
import { useSelector, useDispatch } from "react-redux";
import { useState, useEffect, useRef } from "react";
import Pagination from "../Pagination";
import FilterBar from "./FilterBar";
import { FileSpreadsheet } from "lucide-react";
import { setIsAddView } from "src/features/filter/filterSlice";

const StudentManagementBar = ({
    pagination = true,
    isSearch = true,
    showExportExcel = false,
    showFilter = false,
    limit = 10,
    currentPage = 1,
    totalPages = 1,
    totalItems = 0,
    setLimit = () => {},
    setCurrentPage = () => {},
    setSearch = () => {},
    handleExportToExcel = () => {},
}) => {
    const dispatch = useDispatch();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [isDropdownOpenPage, setIsDropdownOpenPage] = useState(false);
    const dropdownRef = useRef(null);
    const [inputValue, setInputValue] = useState("");

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsDropdownOpen(false);
                setIsDropdownOpenPage(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const options = [5, 10, 15, 20, 30];
    const optionsPage = Array.from({ length: totalPages }, (_, i) => i + 1);

    const handleSelectLimit = (newLimit) => {
        setLimit(newLimit);
        setIsDropdownOpen(false);
    };

    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
        setIsDropdownOpenPage(false);
    };

    useEffect(() => {
        const delayDebounceFn = setTimeout(() => {
            setSearch(inputValue);
        }, 1000);

        return () => clearTimeout(delayDebounceFn);
    }, [inputValue, setSearch]);

    const iconAdd = (
        <div data-svg-wrapper className="relative">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 4L12 20M20 12L4 12" stroke="#202325" strokeWidth="1.5" strokeLinecap="round" />
            </svg>
        </div>
    );

    return (
        <div className="w-full space-y-3 pb-4">
            {/* Main Action Bar */}
            <div className="flex flex-col lg:flex-row lg:items-center gap-3">
                {/* Left Side: Search */}
                <div className="flex items-center gap-3 min-w-0">
                    {isSearch && (
                        <div className="relative flex-1 max-w-sm">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                            >
                                <path
                                    d="M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z"
                                    stroke="currentColor"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                            <input
                                type="text"
                                placeholder="Tìm kiếm học sinh..."
                                value={inputValue}
                                onChange={(e) => setInputValue(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                            />
                        </div>
                    )}
                </div>

                {/* Right Side: Action Buttons */}
                <div className="flex items-center gap-2 flex-shrink-0">
                    {showFilter && <FilterBar />}

                    {showExportExcel && (
                        <button
                            onClick={handleExportToExcel}
                            className="flex items-center gap-1.5 px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors whitespace-nowrap"
                            title="Xuất Excel"
                        >
                            <FileSpreadsheet className="w-4 h-4" />
                            <span className="hidden sm:inline">Excel</span>
                        </button>
                    )}

                    <ButtonFunctionBarAdmin
                        icon={iconAdd}
                        text={'Thêm học sinh'}
                        onClick={() => dispatch(setIsAddView(true))}
                    />
                </div>
            </div>

            {/* Pagination Controls */}
            {pagination && (
                <div className="flex flex-col justify-end sm:flex-row sm:items-center gap-3 pt-2 border-t border-gray-100">
                    {/* Left: Items per page */}
                    <div className="flex items-center gap-2 text-sm text-gray-700">
                        <label htmlFor="limitSelect" className="font-medium whitespace-nowrap">
                            Hiển thị:
                        </label>
                        <div className="relative">
                            <select
                                id="limitSelect"
                                value={limit}
                                onChange={(e) => handleSelectLimit(Number(e.target.value))}
                                className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500"
                            >
                                {options.map((option) => (
                                    <option key={option} value={option}>
                                        {option}
                                    </option>
                                ))}
                            </select>
                            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>
                        <span className="text-gray-500 whitespace-nowrap">/ trang</span>
                    </div>

                    {/* Right: Pagination */}
                    <div className="flex items-center justify-end">
                        <Pagination
                            currentPage={currentPage}
                            totalItems={totalItems}
                            limit={limit}
                            onPageChange={handlePageChange}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default StudentManagementBar;
