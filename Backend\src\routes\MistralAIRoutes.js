import express from 'express';
import asyncHandler from '../middlewares/asyncHandler.js';
import * as MistralAIController from '../controllers/MistralAIController.js';
import uploadPDF from '../middlewares/pdfGoogleUpload.js';
import { requireRoles } from '../middlewares/jwtMiddleware.js';
import Roles from '../constants/Roles.js';
const router = express.Router();
import uploadImage from '../middlewares/imageGoogleUpload.js';

router.post('/v1/AI/ocr/pdf',
    requireRoles(Roles.AllExceptMarketingStudent),
    uploadPDF.single('pdf'),
    asyncHandler(MistralAIController.ocrPdfWithMistral)
)

router.post('/v1/AI/handle/exam/pdf',
    requireRoles(Roles.AllExceptMarketingStudent),
    uploadPDF.single('pdf'),
    asyncHandler(MistralAIController.handleExamPdfWithAI)
)

router.post('/v1/AI/ocr/image',
    requireRoles(Roles.AllExceptMarketingStudent),
    uploadImage.single('image'),
    asyncHandler(MistralAIController.ocrImageWithMistral)
)

export default router;