import LoadingSpinner from "../loading/LoadingSpinner";

const ButtonForUserPage = ({
    text,
    onClick,
    className = '',
    loading = false,
    colorSpinner = 'text-white'
}) => {
    return (
        <button
            onClick={onClick}
            className={className}
            disabled={loading}
        >
            {loading ? (
                <LoadingSpinner color={colorSpinner} minHeight="min-h-0" />
            ) : (
                <span>{text}</span>
            )}
        </button>
    );
}

export default ButtonForUserPage;