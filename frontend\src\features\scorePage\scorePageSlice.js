import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import { getAttemptByIdApi } from "../../services/attemptApi";
import { getQuestionAndAnswersByAttemptAPI } from "../../services/answerApi";
import { reExaminationAPI } from "../../services/examApi";

export const fetchAttemptById = createAsyncThunk(
    "scorePage/fetchAttemptById",
    async (attemptId, { dispatch }) => {
        return await apiHandler(dispatch, getAttemptByIdApi, attemptId, () => { }, false, false);
    }
);

export const fetchQuestionAndAnswersByAttempt = createAsyncThunk(
    "scorePage/fetchQuestionAndAnswersByAttempt",
    async ({ attemptId }, { dispatch }) => {
        return await apiHandler(dispatch, getQuestionAndAnswersByAttemptAPI, { attemptId }, () => {
        }, false, false);
    }
);

export const reExamination = createAsyncThunk(
    "scorePage/reExamination",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, reExaminationAPI, id, () => { }, true, false, false, false);
    }
);


const scorePageSlice = createSlice({
    name: "scorePage",
    initialState: {
        attempt: null,
        loading: false,
        questions: [],
        answers: [],
        loadingQuestion: false,
        exam: null,
        score: null,
        loadingReExamination: false,
    },
    reducers: {
        setAttempt: (state, action) => {
            state.attempt = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAttemptById.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchAttemptById.fulfilled, (state, action) => {
                if (action.payload) {
                    state.attempt = action.payload.data;
                }
                state.loading = false;
            })
            .addCase(fetchAttemptById.rejected, (state) => {
                state.loading = false;
            })
            .addCase(fetchQuestionAndAnswersByAttempt.pending, (state) => {
                state.questions = [];
                state.answers = [];
                state.exam = null;
                state.loadingQuestion = true;
            })
            .addCase(fetchQuestionAndAnswersByAttempt.fulfilled, (state, action) => {
                if (action.payload) {
                    const { questions, answers, score, exam } = action.payload.data;
                    state.questions = questions;
                    state.answers = answers;
                    state.score = score;
                    state.exam = exam;
                }
                state.loadingQuestion = false;
            })
            .addCase(fetchQuestionAndAnswersByAttempt.rejected, (state) => {
                state.questions = [];
                state.answers = [];
                state.exam = null;
                state.loadingQuestion = false;
            })
            .addCase(reExamination.pending, (state) => {
                state.loadingReExamination = true;
            })
            .addCase(reExamination.fulfilled, (state) => {
                state.loadingReExamination = false;
            })
            .addCase(reExamination.rejected, (state) => {
                state.loadingReExamination = false;
            })
    },
});

export const { setAttempt } = scorePageSlice.actions;
export default scorePageSlice.reducer;