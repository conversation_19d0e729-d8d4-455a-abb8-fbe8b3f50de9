/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsGetStatusV1Request = {
  libraryId: string;
  documentId: string;
};

/** @internal */
export const LibrariesDocumentsGetStatusV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsGetStatusV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  document_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "document_id": "documentId",
  });
});

/** @internal */
export type LibrariesDocumentsGetStatusV1Request$Outbound = {
  library_id: string;
  document_id: string;
};

/** @internal */
export const LibrariesDocumentsGetStatusV1Request$outboundSchema: z.ZodType<
  LibrariesDocumentsGetStatusV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsGetStatusV1Request
> = z.object({
  libraryId: z.string(),
  documentId: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    documentId: "document_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsGetStatusV1Request$ {
  /** @deprecated use `LibrariesDocumentsGetStatusV1Request$inboundSchema` instead. */
  export const inboundSchema =
    LibrariesDocumentsGetStatusV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsGetStatusV1Request$outboundSchema` instead. */
  export const outboundSchema =
    LibrariesDocumentsGetStatusV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsGetStatusV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsGetStatusV1Request$Outbound;
}

export function librariesDocumentsGetStatusV1RequestToJSON(
  librariesDocumentsGetStatusV1Request: LibrariesDocumentsGetStatusV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsGetStatusV1Request$outboundSchema.parse(
      librariesDocumentsGetStatusV1Request,
    ),
  );
}

export function librariesDocumentsGetStatusV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesDocumentsGetStatusV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      LibrariesDocumentsGetStatusV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesDocumentsGetStatusV1Request' from JSON`,
  );
}
