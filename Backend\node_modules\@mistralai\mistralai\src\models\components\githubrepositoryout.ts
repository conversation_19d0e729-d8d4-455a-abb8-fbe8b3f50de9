/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const GithubRepositoryOutType = {
  Github: "github",
} as const;
export type GithubRepositoryOutType = ClosedEnum<
  typeof GithubRepositoryOutType
>;

export type GithubRepositoryOut = {
  type?: GithubRepositoryOutType | undefined;
  name: string;
  owner: string;
  ref?: string | null | undefined;
  weight?: number | undefined;
  commitId: string;
};

/** @internal */
export const GithubRepositoryOutType$inboundSchema: z.ZodNativeEnum<
  typeof GithubRepositoryOutType
> = z.nativeEnum(GithubRepositoryOutType);

/** @internal */
export const GithubRepositoryOutType$outboundSchema: z.ZodNativeEnum<
  typeof GithubRepositoryOutType
> = GithubRepositoryOutType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace GithubRepositoryOutType$ {
  /** @deprecated use `GithubRepositoryOutType$inboundSchema` instead. */
  export const inboundSchema = GithubRepositoryOutType$inboundSchema;
  /** @deprecated use `GithubRepositoryOutType$outboundSchema` instead. */
  export const outboundSchema = GithubRepositoryOutType$outboundSchema;
}

/** @internal */
export const GithubRepositoryOut$inboundSchema: z.ZodType<
  GithubRepositoryOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: GithubRepositoryOutType$inboundSchema.default("github"),
  name: z.string(),
  owner: z.string(),
  ref: z.nullable(z.string()).optional(),
  weight: z.number().default(1),
  commit_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "commit_id": "commitId",
  });
});

/** @internal */
export type GithubRepositoryOut$Outbound = {
  type: string;
  name: string;
  owner: string;
  ref?: string | null | undefined;
  weight: number;
  commit_id: string;
};

/** @internal */
export const GithubRepositoryOut$outboundSchema: z.ZodType<
  GithubRepositoryOut$Outbound,
  z.ZodTypeDef,
  GithubRepositoryOut
> = z.object({
  type: GithubRepositoryOutType$outboundSchema.default("github"),
  name: z.string(),
  owner: z.string(),
  ref: z.nullable(z.string()).optional(),
  weight: z.number().default(1),
  commitId: z.string(),
}).transform((v) => {
  return remap$(v, {
    commitId: "commit_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace GithubRepositoryOut$ {
  /** @deprecated use `GithubRepositoryOut$inboundSchema` instead. */
  export const inboundSchema = GithubRepositoryOut$inboundSchema;
  /** @deprecated use `GithubRepositoryOut$outboundSchema` instead. */
  export const outboundSchema = GithubRepositoryOut$outboundSchema;
  /** @deprecated use `GithubRepositoryOut$Outbound` instead. */
  export type Outbound = GithubRepositoryOut$Outbound;
}

export function githubRepositoryOutToJSON(
  githubRepositoryOut: GithubRepositoryOut,
): string {
  return JSON.stringify(
    GithubRepositoryOut$outboundSchema.parse(githubRepositoryOut),
  );
}

export function githubRepositoryOutFromJSON(
  jsonString: string,
): SafeParseResult<GithubRepositoryOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => GithubRepositoryOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'GithubRepositoryOut' from JSON`,
  );
}
