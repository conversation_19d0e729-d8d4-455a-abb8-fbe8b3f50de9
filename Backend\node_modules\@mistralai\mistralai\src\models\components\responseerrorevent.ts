/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const ResponseErrorEventType = {
  ConversationResponseError: "conversation.response.error",
} as const;
export type ResponseErrorEventType = ClosedEnum<typeof ResponseErrorEventType>;

export type ResponseErrorEvent = {
  type?: ResponseErrorEventType | undefined;
  createdAt?: Date | undefined;
  message: string;
  code: number;
};

/** @internal */
export const ResponseErrorEventType$inboundSchema: z.ZodNativeEnum<
  typeof ResponseErrorEventType
> = z.nativeEnum(ResponseErrorEventType);

/** @internal */
export const ResponseErrorEventType$outboundSchema: z.ZodNativeEnum<
  typeof ResponseErrorEventType
> = ResponseErrorEventType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseErrorEventType$ {
  /** @deprecated use `ResponseErrorEventType$inboundSchema` instead. */
  export const inboundSchema = ResponseErrorEventType$inboundSchema;
  /** @deprecated use `ResponseErrorEventType$outboundSchema` instead. */
  export const outboundSchema = ResponseErrorEventType$outboundSchema;
}

/** @internal */
export const ResponseErrorEvent$inboundSchema: z.ZodType<
  ResponseErrorEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ResponseErrorEventType$inboundSchema.default(
    "conversation.response.error",
  ),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  message: z.string(),
  code: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
  });
});

/** @internal */
export type ResponseErrorEvent$Outbound = {
  type: string;
  created_at?: string | undefined;
  message: string;
  code: number;
};

/** @internal */
export const ResponseErrorEvent$outboundSchema: z.ZodType<
  ResponseErrorEvent$Outbound,
  z.ZodTypeDef,
  ResponseErrorEvent
> = z.object({
  type: ResponseErrorEventType$outboundSchema.default(
    "conversation.response.error",
  ),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  message: z.string(),
  code: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseErrorEvent$ {
  /** @deprecated use `ResponseErrorEvent$inboundSchema` instead. */
  export const inboundSchema = ResponseErrorEvent$inboundSchema;
  /** @deprecated use `ResponseErrorEvent$outboundSchema` instead. */
  export const outboundSchema = ResponseErrorEvent$outboundSchema;
  /** @deprecated use `ResponseErrorEvent$Outbound` instead. */
  export type Outbound = ResponseErrorEvent$Outbound;
}

export function responseErrorEventToJSON(
  responseErrorEvent: ResponseErrorEvent,
): string {
  return JSON.stringify(
    ResponseErrorEvent$outboundSchema.parse(responseErrorEvent),
  );
}

export function responseErrorEventFromJSON(
  jsonString: string,
): SafeParseResult<ResponseErrorEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ResponseErrorEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ResponseErrorEvent' from JSON`,
  );
}
