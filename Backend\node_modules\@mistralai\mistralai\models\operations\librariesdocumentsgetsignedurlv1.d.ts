import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDocumentsGetSignedUrlV1Request = {
    libraryId: string;
    documentId: string;
};
/** @internal */
export declare const LibrariesDocumentsGetSignedUrlV1Request$inboundSchema: z.ZodType<LibrariesDocumentsGetSignedUrlV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDocumentsGetSignedUrlV1Request$Outbound = {
    library_id: string;
    document_id: string;
};
/** @internal */
export declare const LibrariesDocumentsGetSignedUrlV1Request$outboundSchema: z.ZodType<LibrariesDocumentsGetSignedUrlV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetSignedUrlV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDocumentsGetSignedUrlV1Request$ {
    /** @deprecated use `LibrariesDocumentsGetSignedUrlV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDocumentsGetSignedUrlV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDocumentsGetSignedUrlV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDocumentsGetSignedUrlV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetSignedUrlV1Request>;
    /** @deprecated use `LibrariesDocumentsGetSignedUrlV1Request$Outbound` instead. */
    type Outbound = LibrariesDocumentsGetSignedUrlV1Request$Outbound;
}
export declare function librariesDocumentsGetSignedUrlV1RequestToJSON(librariesDocumentsGetSignedUrlV1Request: LibrariesDocumentsGetSignedUrlV1Request): string;
export declare function librariesDocumentsGetSignedUrlV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDocumentsGetSignedUrlV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdocumentsgetsignedurlv1.d.ts.map