{"ast": null, "code": "var _jsxFileName = \"D:\\\\ToanThayBee\\\\frontend\\\\src\\\\components\\\\input\\\\TextArea.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport { Info } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TextArea = _ref => {\n  _s();\n  let {\n    value,\n    onChange,\n    placeholder,\n    label,\n    Icon = null,\n    hint = \"\",\n    buttonFilterText = false,\n    debounceDelay = 500 // 👈 cho phép tùy chỉnh thời gian debounce\n  } = _ref;\n  const textAreaRef = useRef(null);\n  const debounceRef = useRef(null);\n  const [internalValue, setInternalValue] = useState(value || \"\");\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  // Cập nhật chiều cao khi nhập\n  useEffect(() => {\n    if (textAreaRef.current) {\n      textAreaRef.current.style.height = \"auto\";\n      textAreaRef.current.style.height = textAreaRef.current.scrollHeight + \"px\";\n    }\n  }, [internalValue]);\n\n  // Nếu value ngoài thay đổi → cập nhật nội bộ\n  useEffect(() => {\n    setInternalValue(value || \"\");\n  }, [value]);\n  const handleChange = e => {\n    const newValue = e.target.value;\n    setInternalValue(newValue);\n\n    // debounce: chỉ gọi onChange sau 500ms không gõ nữa\n    if (debounceRef.current) {\n      clearTimeout(debounceRef.current);\n    }\n    debounceRef.current = setTimeout(() => {\n      onChange({\n        target: {\n          value: newValue\n        }\n      });\n    }, debounceDelay);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2 relative w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-xs font-medium text-gray-700 flex items-center gap-1 relative\",\n        children: [Icon && /*#__PURE__*/_jsxDEV(Icon, {\n          className: \"w-3 h-3 inline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 30\n        }, this), label, hint && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex items-center\",\n          onMouseEnter: () => setShowTooltip(true),\n          onMouseLeave: () => setShowTooltip(false),\n          children: [/*#__PURE__*/_jsxDEV(Info, {\n            className: \"w-3 h-3 text-gray-400 cursor-pointer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 29\n          }, this), showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-6 left-0 z-50 w-64 p-2 text-xs text-white bg-gray-800 rounded shadow-md\",\n            children: hint\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 17\n      }, this), buttonFilterText && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: buttonFilterText.onClick,\n        className: \"text-xs font-medium text-white hover:underline px-2 py-1 bg-blue-500 rounded\",\n        children: buttonFilterText.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n      ref: textAreaRef,\n      value: internalValue,\n      onChange: handleChange,\n      className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none overflow-hidden\",\n      placeholder: placeholder,\n      rows: 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 9\n  }, this);\n};\n_s(TextArea, \"fyuWwh0lVtLto9HPZYncpx7MnVg=\");\n_c = TextArea;\nexport default TextArea;\nvar _c;\n$RefreshReg$(_c, \"TextArea\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "Info", "jsxDEV", "_jsxDEV", "TextArea", "_ref", "_s", "value", "onChange", "placeholder", "label", "Icon", "hint", "buttonFilterText", "deboun<PERSON><PERSON><PERSON><PERSON>", "textAreaRef", "debounceRef", "internalValue", "setInternalValue", "showTooltip", "setShowTooltip", "current", "style", "height", "scrollHeight", "handleChange", "e", "newValue", "target", "clearTimeout", "setTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMouseEnter", "onMouseLeave", "onClick", "text", "ref", "rows", "_c", "$RefreshReg$"], "sources": ["D:/ToanThayBee/frontend/src/components/input/TextArea.jsx"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport { Info } from \"lucide-react\";\r\n\r\nconst TextArea = ({\r\n    value,\r\n    onChange,\r\n    placeholder,\r\n    label,\r\n    Icon = null,\r\n    hint = \"\",\r\n    buttonFilterText = false,\r\n    debounceDelay = 500 // 👈 cho phép tùy chỉnh thời gian debounce\r\n}) => {\r\n    const textAreaRef = useRef(null);\r\n    const debounceRef = useRef(null);\r\n    const [internalValue, setInternalValue] = useState(value || \"\");\r\n    const [showTooltip, setShowTooltip] = useState(false);\r\n\r\n    // Cập nhật chiều cao khi nhập\r\n    useEffect(() => {\r\n        if (textAreaRef.current) {\r\n            textAreaRef.current.style.height = \"auto\";\r\n            textAreaRef.current.style.height = textAreaRef.current.scrollHeight + \"px\";\r\n        }\r\n    }, [internalValue]);\r\n\r\n    // Nếu value ngoài thay đổi → cập nhật nội bộ\r\n    useEffect(() => {\r\n        setInternalValue(value || \"\");\r\n    }, [value]);\r\n\r\n    const handleChange = (e) => {\r\n        const newValue = e.target.value;\r\n        setInternalValue(newValue);\r\n\r\n        // debounce: chỉ gọi onChange sau 500ms không gõ nữa\r\n        if (debounceRef.current) {\r\n            clearTimeout(debounceRef.current);\r\n        }\r\n\r\n        debounceRef.current = setTimeout(() => {\r\n            onChange({ target: { value: newValue } });\r\n        }, debounceDelay);\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 relative w-full\">\r\n            <div className=\"flex flex-row justify-between items-center\">\r\n                <label className=\"text-xs font-medium text-gray-700 flex items-center gap-1 relative\">\r\n                    {Icon && <Icon className=\"w-3 h-3 inline\" />}\r\n                    {label}\r\n                    {hint && (\r\n                        <div\r\n                            className=\"relative flex items-center\"\r\n                            onMouseEnter={() => setShowTooltip(true)}\r\n                            onMouseLeave={() => setShowTooltip(false)}\r\n                        >\r\n                            <Info className=\"w-3 h-3 text-gray-400 cursor-pointer\" />\r\n                            {showTooltip && (\r\n                                <div className=\"absolute top-6 left-0 z-50 w-64 p-2 text-xs text-white bg-gray-800 rounded shadow-md\">\r\n                                    {hint}\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    )}\r\n                </label>\r\n                {buttonFilterText && (\r\n                    <button\r\n                        onClick={buttonFilterText.onClick}\r\n                        className=\"text-xs font-medium text-white hover:underline px-2 py-1 bg-blue-500 rounded\"\r\n                    >\r\n                        {buttonFilterText.text}\r\n                    </button>\r\n                )}\r\n            </div>\r\n            <textarea\r\n                ref={textAreaRef}\r\n                value={internalValue}\r\n                onChange={handleChange}\r\n                className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none overflow-hidden\"\r\n                placeholder={placeholder}\r\n                rows={1}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default TextArea;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,QAAQ,GAAGC,IAAA,IASX;EAAAC,EAAA;EAAA,IATY;IACdC,KAAK;IACLC,QAAQ;IACRC,WAAW;IACXC,KAAK;IACLC,IAAI,GAAG,IAAI;IACXC,IAAI,GAAG,EAAE;IACTC,gBAAgB,GAAG,KAAK;IACxBC,aAAa,GAAG,GAAG,CAAC;EACxB,CAAC,GAAAT,IAAA;EACG,MAAMU,WAAW,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMiB,WAAW,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAACO,KAAK,IAAI,EAAE,CAAC;EAC/D,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAF,SAAS,CAAC,MAAM;IACZ,IAAIiB,WAAW,CAACM,OAAO,EAAE;MACrBN,WAAW,CAACM,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;MACzCR,WAAW,CAACM,OAAO,CAACC,KAAK,CAACC,MAAM,GAAGR,WAAW,CAACM,OAAO,CAACG,YAAY,GAAG,IAAI;IAC9E;EACJ,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;;EAEnB;EACAnB,SAAS,CAAC,MAAM;IACZoB,gBAAgB,CAACX,KAAK,IAAI,EAAE,CAAC;EACjC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAMC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAACrB,KAAK;IAC/BW,gBAAgB,CAACS,QAAQ,CAAC;;IAE1B;IACA,IAAIX,WAAW,CAACK,OAAO,EAAE;MACrBQ,YAAY,CAACb,WAAW,CAACK,OAAO,CAAC;IACrC;IAEAL,WAAW,CAACK,OAAO,GAAGS,UAAU,CAAC,MAAM;MACnCtB,QAAQ,CAAC;QAAEoB,MAAM,EAAE;UAAErB,KAAK,EAAEoB;QAAS;MAAE,CAAC,CAAC;IAC7C,CAAC,EAAEb,aAAa,CAAC;EACrB,CAAC;EAED,oBACIX,OAAA;IAAK4B,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAChD7B,OAAA;MAAK4B,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACvD7B,OAAA;QAAO4B,SAAS,EAAC,oEAAoE;QAAAC,QAAA,GAChFrB,IAAI,iBAAIR,OAAA,CAACQ,IAAI;UAACoB,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC3C1B,KAAK,EACLE,IAAI,iBACDT,OAAA;UACI4B,SAAS,EAAC,4BAA4B;UACtCM,YAAY,EAAEA,CAAA,KAAMjB,cAAc,CAAC,IAAI,CAAE;UACzCkB,YAAY,EAAEA,CAAA,KAAMlB,cAAc,CAAC,KAAK,CAAE;UAAAY,QAAA,gBAE1C7B,OAAA,CAACF,IAAI;YAAC8B,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxDjB,WAAW,iBACRhB,OAAA;YAAK4B,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAChGpB;UAAI;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACPvB,gBAAgB,iBACbV,OAAA;QACIoC,OAAO,EAAE1B,gBAAgB,CAAC0B,OAAQ;QAClCR,SAAS,EAAC,8EAA8E;QAAAC,QAAA,EAEvFnB,gBAAgB,CAAC2B;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACNjC,OAAA;MACIsC,GAAG,EAAE1B,WAAY;MACjBR,KAAK,EAAEU,aAAc;MACrBT,QAAQ,EAAEiB,YAAa;MACvBM,SAAS,EAAC,8IAA8I;MACxJtB,WAAW,EAAEA,WAAY;MACzBiC,IAAI,EAAE;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC9B,EAAA,CAlFIF,QAAQ;AAAAuC,EAAA,GAARvC,QAAQ;AAoFd,eAAeA,QAAQ;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}