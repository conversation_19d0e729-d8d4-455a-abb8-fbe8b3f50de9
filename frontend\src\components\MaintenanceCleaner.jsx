import { useEffect } from 'react';
import { MAINTENANCE_CONFIG } from '../config/maintenance';

/**
 * Component to automatically clear maintenance mode when FORCE_DISABLE is true
 * This runs once when the app starts
 */
const MaintenanceCleaner = () => {
    useEffect(() => {
        // If FORCE_DISABLE is true, clear any existing maintenance flags
        if (MAINTENANCE_CONFIG.FORCE_DISABLE) {
            const maintenanceFlag = localStorage.getItem('maintenanceMode');
            
            if (maintenanceFlag === 'true') {
                localStorage.removeItem('maintenanceMode');
                console.log('🧹 MaintenanceCleaner: Cleared maintenance flag due to FORCE_DISABLE');
            }
        }
    }, []);

    // This component doesn't render anything
    return null;
};

export default MaintenanceCleaner;
