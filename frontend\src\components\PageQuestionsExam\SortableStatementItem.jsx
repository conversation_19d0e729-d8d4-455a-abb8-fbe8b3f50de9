import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import LatexRenderer from "src/components/latex/RenderLatex";

const SortableStatementItem = ({ statement, index, prefix, isCorrect, questionType }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: `${statement.id || index}` });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    // Xác định màu sắc dựa trên loại câu hỏi và tính đúng/sai
    const getTextColor = () => {
        if (questionType === "TN") {
            return isCorrect ? "text-green-600" : "text-gray-800";
        } else if (questionType === "DS") {
            return isCorrect ? "text-green-600" : "text-red-600";
        }
        return "text-gray-800";
    };

    return (
        <div ref={setNodeRef} style={style} className="relative">
            {/* Drag Handle */}
            <div
                {...attributes}
                {...listeners}
                className="absolute left-0 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors"
                title="Kéo để sắp xếp lại thứ tự mệnh đề"
            >
                <GripVertical size={12} className="text-gray-400" />
            </div>

            {/* Statement Content with left padding for drag handle */}
            <div className={`pl-6 flex flex-row gap-1 ${getTextColor()} text-xs`}>
                <span className="font-semibold">{prefix} </span>
                <LatexRenderer text={statement.content} />
            </div>

            {statement.imageUrl && (
                <div className="mt-2 pl-6">
                    <img
                        src={statement.imageUrl}
                        alt="statement"
                        className="max-h-32 object-contain rounded-md border border-gray-200"
                    />
                </div>
            )}
        </div>
    );
};

export default SortableStatementItem;
