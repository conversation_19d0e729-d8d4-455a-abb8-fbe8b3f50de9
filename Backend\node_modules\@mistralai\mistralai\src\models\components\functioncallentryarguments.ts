/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FunctionCallEntryArguments = { [k: string]: any } | string;

/** @internal */
export const FunctionCallEntryArguments$inboundSchema: z.ZodType<
  FunctionCallEntryArguments,
  z.ZodTypeDef,
  unknown
> = z.union([z.record(z.any()), z.string()]);

/** @internal */
export type FunctionCallEntryArguments$Outbound = { [k: string]: any } | string;

/** @internal */
export const FunctionCallEntryArguments$outboundSchema: z.ZodType<
  FunctionCallEntryArguments$Outbound,
  z.ZodTypeDef,
  FunctionCallEntryArguments
> = z.union([z.record(z.any()), z.string()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionCallEntryArguments$ {
  /** @deprecated use `FunctionCallEntryArguments$inboundSchema` instead. */
  export const inboundSchema = FunctionCallEntryArguments$inboundSchema;
  /** @deprecated use `FunctionCallEntryArguments$outboundSchema` instead. */
  export const outboundSchema = FunctionCallEntryArguments$outboundSchema;
  /** @deprecated use `FunctionCallEntryArguments$Outbound` instead. */
  export type Outbound = FunctionCallEntryArguments$Outbound;
}

export function functionCallEntryArgumentsToJSON(
  functionCallEntryArguments: FunctionCallEntryArguments,
): string {
  return JSON.stringify(
    FunctionCallEntryArguments$outboundSchema.parse(functionCallEntryArguments),
  );
}

export function functionCallEntryArgumentsFromJSON(
  jsonString: string,
): SafeParseResult<FunctionCallEntryArguments, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FunctionCallEntryArguments$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FunctionCallEntryArguments' from JSON`,
  );
}
