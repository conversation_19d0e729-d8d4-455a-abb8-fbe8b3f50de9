/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type DocumentOut = {
  id: string;
  libraryId: string;
  hash: string;
  mimeType: string;
  extension: string;
  size: number;
  name: string;
  summary?: string | null | undefined;
  createdAt: Date;
  lastProcessedAt?: Date | null | undefined;
  numberOfPages?: number | null | undefined;
  processingStatus: string;
  uploadedById: string;
  uploadedByType: string;
  tokensProcessingMainContent?: number | null | undefined;
  tokensProcessingSummary?: number | null | undefined;
  tokensProcessingTotal: number;
};

/** @internal */
export const DocumentOut$inboundSchema: z.ZodType<
  DocumentOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  library_id: z.string(),
  hash: z.string(),
  mime_type: z.string(),
  extension: z.string(),
  size: z.number().int(),
  name: z.string(),
  summary: z.nullable(z.string()).optional(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  last_processed_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  number_of_pages: z.nullable(z.number().int()).optional(),
  processing_status: z.string(),
  uploaded_by_id: z.string(),
  uploaded_by_type: z.string(),
  tokens_processing_main_content: z.nullable(z.number().int()).optional(),
  tokens_processing_summary: z.nullable(z.number().int()).optional(),
  tokens_processing_total: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "mime_type": "mimeType",
    "created_at": "createdAt",
    "last_processed_at": "lastProcessedAt",
    "number_of_pages": "numberOfPages",
    "processing_status": "processingStatus",
    "uploaded_by_id": "uploadedById",
    "uploaded_by_type": "uploadedByType",
    "tokens_processing_main_content": "tokensProcessingMainContent",
    "tokens_processing_summary": "tokensProcessingSummary",
    "tokens_processing_total": "tokensProcessingTotal",
  });
});

/** @internal */
export type DocumentOut$Outbound = {
  id: string;
  library_id: string;
  hash: string;
  mime_type: string;
  extension: string;
  size: number;
  name: string;
  summary?: string | null | undefined;
  created_at: string;
  last_processed_at?: string | null | undefined;
  number_of_pages?: number | null | undefined;
  processing_status: string;
  uploaded_by_id: string;
  uploaded_by_type: string;
  tokens_processing_main_content?: number | null | undefined;
  tokens_processing_summary?: number | null | undefined;
  tokens_processing_total: number;
};

/** @internal */
export const DocumentOut$outboundSchema: z.ZodType<
  DocumentOut$Outbound,
  z.ZodTypeDef,
  DocumentOut
> = z.object({
  id: z.string(),
  libraryId: z.string(),
  hash: z.string(),
  mimeType: z.string(),
  extension: z.string(),
  size: z.number().int(),
  name: z.string(),
  summary: z.nullable(z.string()).optional(),
  createdAt: z.date().transform(v => v.toISOString()),
  lastProcessedAt: z.nullable(z.date().transform(v => v.toISOString()))
    .optional(),
  numberOfPages: z.nullable(z.number().int()).optional(),
  processingStatus: z.string(),
  uploadedById: z.string(),
  uploadedByType: z.string(),
  tokensProcessingMainContent: z.nullable(z.number().int()).optional(),
  tokensProcessingSummary: z.nullable(z.number().int()).optional(),
  tokensProcessingTotal: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    mimeType: "mime_type",
    createdAt: "created_at",
    lastProcessedAt: "last_processed_at",
    numberOfPages: "number_of_pages",
    processingStatus: "processing_status",
    uploadedById: "uploaded_by_id",
    uploadedByType: "uploaded_by_type",
    tokensProcessingMainContent: "tokens_processing_main_content",
    tokensProcessingSummary: "tokens_processing_summary",
    tokensProcessingTotal: "tokens_processing_total",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DocumentOut$ {
  /** @deprecated use `DocumentOut$inboundSchema` instead. */
  export const inboundSchema = DocumentOut$inboundSchema;
  /** @deprecated use `DocumentOut$outboundSchema` instead. */
  export const outboundSchema = DocumentOut$outboundSchema;
  /** @deprecated use `DocumentOut$Outbound` instead. */
  export type Outbound = DocumentOut$Outbound;
}

export function documentOutToJSON(documentOut: DocumentOut): string {
  return JSON.stringify(DocumentOut$outboundSchema.parse(documentOut));
}

export function documentOutFromJSON(
  jsonString: string,
): SafeParseResult<DocumentOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DocumentOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DocumentOut' from JSON`,
  );
}
