import { useSelector } from "react-redux";

const QuestionSectionTitle = ({ title, short }) => {
    return (
        <div className="flex flex-col items-center">
            <span className="block md:hidden font-medium">{short}</span>   {/* mobile: TN, DS, TLN */}
            <span className="hidden md:block text-xl w-full text-center font-bold">{title}</span>                {/* desktop: Phần I - Trắc nghiệ<PERSON> */}
        </div>
    );
};

export default QuestionSectionTitle;