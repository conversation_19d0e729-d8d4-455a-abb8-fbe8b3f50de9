import db from "../models/index.js"
import UserType from "../constants/UserType.js"
import { CommentService } from '../services/comments.service.js';

export const getComments = async (req, res) => {
    const { examId } = req.params;
    const page = parseInt(req.query.page, 10) || 1;
    const comments = await CommentService.getByExamId(examId, page);
    res.status(200).json({
        message: "Lấy danh sách bình luận thành công",
        ...comments,
    });
};

export const getReplies = async (req, res) => {
    const { commentId } = req.params;
    const page = parseInt(req.query.page, 10) || 1;
    const replies = await CommentService.getRepliesByCommentId(commentId, page);
    res.status(200).json({
        message: "<PERSON><PERSON><PERSON> danh sách phản hồi thành công",
        ...replies,
    });
};

export const postComment = async (req, res) => {
    const data = {
        userId: req.user.id,
        examId: req.body.examId,
        content: req.body.content,
        commentId: req.body.parentCommentId || null,
    };
    const comment = await CommentService.create(data);
    const output = {
        ...comment.toJSON(),
        replyCount: 0,
    }
    res.status(201).json({
        message: "Thêm bình luận thành công",
        data: output,
    });
};

export const putComment = async (req, res) => {
    const { commentId } = req.params;
    const { content } = req.body;
    const { id } = req.user;
    const updated = await CommentService.update(commentId, content, id);
    const output = {
        ...updated.toJSON(),
    }
    res.status(200).json({
        message: "Cập nhật bình luận thành công",
        data: output,
    });
};

export const deleteComment = async (req, res) => {
    const { commentId } = req.params;
    const { id } = req.user;

    await CommentService.remove(commentId, id);

    res.status(200).json({
        message: "Xóa bình luận thành công",
        data: commentId,
    });
};

