import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDocumentsGetStatusV1Request = {
    libraryId: string;
    documentId: string;
};
/** @internal */
export declare const LibrariesDocumentsGetStatusV1Request$inboundSchema: z.ZodType<LibrariesDocumentsGetStatusV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDocumentsGetStatusV1Request$Outbound = {
    library_id: string;
    document_id: string;
};
/** @internal */
export declare const LibrariesDocumentsGetStatusV1Request$outboundSchema: z.ZodType<LibrariesDocumentsGetStatusV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetStatusV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDocumentsGetStatusV1Request$ {
    /** @deprecated use `LibrariesDocumentsGetStatusV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDocumentsGetStatusV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDocumentsGetStatusV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDocumentsGetStatusV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetStatusV1Request>;
    /** @deprecated use `LibrariesDocumentsGetStatusV1Request$Outbound` instead. */
    type Outbound = LibrariesDocumentsGetStatusV1Request$Outbound;
}
export declare function librariesDocumentsGetStatusV1RequestToJSON(librariesDocumentsGetStatusV1Request: LibrariesDocumentsGetStatusV1Request): string;
export declare function librariesDocumentsGetStatusV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDocumentsGetStatusV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdocumentsgetstatusv1.d.ts.map