'use strict';
/** @type {import('sequelize-cli').Migration} */
export default {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addIndex('class', ['class_code'], {
      name: 'idx_class_code',
      unique: true,
    });

    await queryInterface.addIndex('class', ['grade'], {
      name: 'idx_class_grade',
    });

    await queryInterface.addIndex('class', ['academicYear'], {
      name: 'idx_class_academicYear',
    });

    await queryInterface.addIndex('class', ['status'], {
      name: 'idx_class_status',
    });

    // await queryInterface.addIndex('class', ['grade', 'academicYear'], {
    //   name: 'idx_class_grade_academicYear',
    // });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('class', 'idx_class_code');
    await queryInterface.removeIndex('class', 'idx_class_grade');
    await queryInterface.removeIndex('class', 'idx_class_academicYear');
    await queryInterface.removeIndex('class', 'idx_class_status');
    // await queryInterface.removeIndex('class', 'idx_class_grade_academicYear');
  }
};
