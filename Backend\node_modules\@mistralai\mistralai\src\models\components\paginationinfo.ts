/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type PaginationInfo = {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasMore: boolean;
};

/** @internal */
export const PaginationInfo$inboundSchema: z.ZodType<
  PaginationInfo,
  z.ZodTypeDef,
  unknown
> = z.object({
  total_items: z.number().int(),
  total_pages: z.number().int(),
  current_page: z.number().int(),
  page_size: z.number().int(),
  has_more: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    "total_items": "totalItems",
    "total_pages": "totalPages",
    "current_page": "currentPage",
    "page_size": "pageSize",
    "has_more": "hasMore",
  });
});

/** @internal */
export type PaginationInfo$Outbound = {
  total_items: number;
  total_pages: number;
  current_page: number;
  page_size: number;
  has_more: boolean;
};

/** @internal */
export const PaginationInfo$outboundSchema: z.ZodType<
  PaginationInfo$Outbound,
  z.ZodTypeDef,
  PaginationInfo
> = z.object({
  totalItems: z.number().int(),
  totalPages: z.number().int(),
  currentPage: z.number().int(),
  pageSize: z.number().int(),
  hasMore: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    totalItems: "total_items",
    totalPages: "total_pages",
    currentPage: "current_page",
    pageSize: "page_size",
    hasMore: "has_more",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace PaginationInfo$ {
  /** @deprecated use `PaginationInfo$inboundSchema` instead. */
  export const inboundSchema = PaginationInfo$inboundSchema;
  /** @deprecated use `PaginationInfo$outboundSchema` instead. */
  export const outboundSchema = PaginationInfo$outboundSchema;
  /** @deprecated use `PaginationInfo$Outbound` instead. */
  export type Outbound = PaginationInfo$Outbound;
}

export function paginationInfoToJSON(paginationInfo: PaginationInfo): string {
  return JSON.stringify(PaginationInfo$outboundSchema.parse(paginationInfo));
}

export function paginationInfoFromJSON(
  jsonString: string,
): SafeParseResult<PaginationInfo, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => PaginationInfo$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'PaginationInfo' from JSON`,
  );
}
