import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as doExamApi from "../../services/doExamApi";
import { apiHandler } from "../../utils/apiHandler";
import { getExamPublic } from "../../services/examApi";
import { getPublicExamQuestionsAPI } from "../../services/questionApi";
import { getAnswersByAttemptAPI } from "../../services/answerApi";

export const fetchPublicExamById = createAsyncThunk(
    "doExam/fetchPublicExamById",
    async (examId, { dispatch }) => {
        return await apiHandler(dispatch, getExamPublic, examId, null, false, false, false, false);
    }
);

export const fetchAnswersByAttempt = createAsyncThunk(
    "answers/fetchAnswersByAttempt",
    async (attemptId, { dispatch }) => {
        return await apiHandler(dispatch, getAnswersByAttemptAPI, { attemptId }, () => { }, false, false);
    }
);


export const fetchPublicQuestionsByExamId = createAsyncThunk(
    "doExam/fetchPublicQuestionsByExamId",
    async (examId, { dispatch }) => {
        return await apiHandler(dispatch, getPublicExamQuestionsAPI, { id: examId }, null, false, false, false, false);
    }
);

export const joinExam = createAsyncThunk(
    "doExam/joinExam",
    async (examId, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.joinExamApi, examId, null, false, false, false, false);
    }
);

export const submitAnswer = createAsyncThunk(
    "doExam/submitAnswer",
    async ({ questionId, answerContent, type }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.submitAnswerApi, { questionId, answerContent, type }, null, false, false, false, false);
    }
);

export const calculateScore = createAsyncThunk(
    "doExam/calculateScore",
    async ({ attemptId, answers }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.calculateScoreApi, { attemptId, answers }, null, false, false, false, false);
    }
);

export const summitExam = createAsyncThunk(
    "doExam/summitExam",
    async (attemptId, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.summitExamAPI, { attemptId }, () => { }, true, false);
    }
);

export const getRemainingTime = createAsyncThunk(
    "doExam/getRemainingTime",
    async ({ examId, attemptId }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.getRemainingTimeApi, { examId, attemptId }, null, false, false, false, false);
    }
);

export const logUserActivity = createAsyncThunk(
    "doExam/logUserActivity",
    async ({ examId, attemptId, activityType, details }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.logUserActivityApi, { examId, attemptId, activityType, details }, null, false, false, false, false);
    }
);

export const submitAnswerWithAttempt = createAsyncThunk(
    "doExam/submitAnswerWithAttempt",
    async ({ questionId, answerContent, type, attemptId }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.submitAnswerWithAttemptApi, { questionId, answerContent, type, attemptId }, null, false, false, false, false);
    }
);

export const leaveExam = createAsyncThunk(
    "doExam/leaveExam",
    async ({ examId, attemptId }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.leaveExamApi, { examId, attemptId }, null, false, false, false, false);
    }
);

const initialState = {
    exam: null,
    view: "TN",
    questionsView: 'all',
    showSidebar: true,
    loadingExam: false,
    questions: [],
    questionTN: [],
    questionDS: [],
    questionTLN: [],
    loadingQuestions: false,

    showModalSubmit: false,
    isTimeUp: false,
    attemptId: null,
    startTime: null,
    loadingJoin: false,
    loadingSubmitAnswer: false,
    loadingCalculate: false,
    loadingSubmit: false,
    loadingTime: false,
    loadingActivity: false,
    loadingLeave: false,
    isSubmit: false,
    startTime: null,
    saveQuestions: [], // Changed from Set to Array
    errorQuestions: [], // Changed from Set to Array
    remainingTime: null,
    darkMode: false,
    fontSize: 14,
    imageSize: 10,
    singleQuestionMode: false,
    showMarkedOnly: false,
    showGuide: false,
    prefixStatements: ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'],
    prefixStatementsDS: ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'],

    answers: [],
    answersTN: [],
    answersDS: {},
    answersTLN: [],
    loadingAnswers: false,

}

const doExamSlice = createSlice({
    name: "doExam",
    initialState,
    reducers: {
        setAttemptId: (state, action) => {
            state.attemptId = action.payload;
        },
        setStartTime: (state, action) => {
            state.startTime = action.payload;
        },
        setSaveQuestions: (state, action) => {
            // Ensure unique values when setting
            state.saveQuestions = Array.isArray(action.payload)
                ? [...new Set(action.payload)]
                : [];

            // console.log("setSaveQuestions", state.saveQuestions)
        },
        setAnswers: (state, action) => {
            const newAnswer = action.payload;
            const { questionId, typeOfQuestion } = newAnswer;

            // Cập nhật hoặc thêm mới vào answers
            const index = state.answers.findIndex((a) => a.questionId === questionId);
            if (index !== -1) {
                state.answers[index] = newAnswer;
            } else {
                state.answers.push(newAnswer);
            }

            // Phân loại lại chỉ khi cần (một lần duy nhất)
            state.answersTN = [];
            state.answersTLN = [];
            state.answersDS = {};

            for (const answer of state.answers) {
                switch (answer.typeOfQuestion) {
                    case "TN":
                        state.answersTN.push(answer);
                        break;
                    case "TLN":
                        state.answersTLN.push(answer);
                        break;
                    case "DS":
                        state.answersDS[answer.questionId] = answer.answerContent;
                        break;
                }
            }
        },
        setShowSidebar: (state, action) => {
            state.showSidebar = action.payload;
        },
        toggleSidebar: (state) => {
            state.showSidebar = !state.showSidebar;
        },
        setView: (state, action) => {
            state.view = action.payload;
        },
        toggleDarkMode: (state) => {
            state.darkMode = !state.darkMode;
        },
        toggleGuide: (state) => {
            state.showGuide = !state.showGuide;
        },
        setShowGuide: (state, action) => {
            state.showGuide = action.payload;
        },
        setFontSize: (state, action) => {
            state.fontSize = action.payload;
        },
        setImageSize: (state, action) => {
            state.imageSize = action.payload;
        },
        setSingleQuestionMode: (state, action) => {
            state.singleQuestionMode = action.payload;
        },
        setShowMarkedOnly: (state, action) => {
            state.showMarkedOnly = action.payload;
        },
        setShowModalSubmit: (state, action) => {
            state.showModalSubmit = action.payload;
        },
        setErrorQuestions: (state, action) => {
            // Ensure unique values when setting
            // console.log(action.payload)
            state.errorQuestions = Array.isArray(action.payload)
                ? [...new Set(action.payload)]
                : [];

            // console.log("setErrorQuestions", state.errorQuestions)
        },
        setRemainingTime: (state, action) => {
            state.remainingTime = action.payload;
        },
        setIsTimeUp: (state, action) => {
            state.isTimeUp = action.payload;
        },
        resetQuestionStates: (state) => {
            // Reset both saveQuestions and errorQuestions when submitting exam
            state.saveQuestions = [];
            state.errorQuestions = [];
            // console.log("🔄 Reset: Cleared saveQuestions and errorQuestions on exam submit");
        },
        reset: () => initialState,
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAnswersByAttempt.pending, (state) => {
                state.answers = [];
                state.answersTN = [];
                state.answersDS = {};
                state.answersTLN = [];
                state.loadingAnswers = true;
            })
            .addCase(fetchAnswersByAttempt.fulfilled, (state, action) => {
                if (action.payload) {
                    state.answers = action.payload.data;
                    state.answersTN = action.payload.data.filter((answer) => answer.typeOfQuestion === "TN");
                    state.answersDS = action.payload.data
                        .filter((answer) => answer.typeOfQuestion === "DS")
                        .reduce((acc, answer) => {
                            let content = answer.answerContent;
                            if (typeof content === "string" && content.length > 0) {
                                try {
                                    content = JSON.parse(content);
                                } catch (e) {
                                    console.warn("Không parse được answerContent:", content);
                                    content = null;
                                }
                            }
                            acc[answer.questionId] = content;
                            return acc;
                        }, {});
                    state.answersTLN = action.payload.data.filter((answer) => answer.typeOfQuestion === "TLN");
                    for (const answer of state.answersTN) {
                        if (!state.saveQuestions.includes(answer.questionId) && answer.answerContent) {
                            state.saveQuestions.push(answer.questionId);
                        }
                    }
                    for (const answer of state.answersTLN) {
                        if (!state.saveQuestions.includes(answer.questionId) && answer.answerContent.trim()) {
                            state.saveQuestions.push(answer.questionId);
                        }
                    }
                    for (const [questionId, answerContent] of Object.entries(state.answersDS)) {
                        // console.log("answerContent", answerContent)
                        if (
                            !state.saveQuestions.includes(questionId) &&
                            Array.isArray(answerContent) &&
                            answerContent.length == 4
                        ) {
                            state.saveQuestions.push(questionId);
                        }
                    }
                }
                state.loadingAnswers = false;
            })
            .addCase(fetchAnswersByAttempt.rejected, (state) => {
                state.answers = [];
                state.answersTN = [];
                state.answersDS = {};
                state.answersTLN = [];
                state.loadingAnswers = false;
            })
            .addCase(fetchPublicExamById.pending, (state) => {
                state.loadingExam = true;
                state.exam = null;
            })
            .addCase(fetchPublicExamById.fulfilled, (state, action) => {
                state.exam = action.payload.data;
                state.loadingExam = false;
            })
            .addCase(fetchPublicExamById.rejected, (state) => {
                state.loadingExam = false;
                state.exam = null;
            })
            .addCase(fetchPublicQuestionsByExamId.pending, (state) => {
                state.loadingQuestions = true;
                state.questions = [];
                state.questionTN = [];
                state.questionDS = [];
                state.questionTLN = [];
            })
            .addCase(fetchPublicQuestionsByExamId.fulfilled, (state, action) => {
                state.questions = action.payload.questions;
                state.questionTN = action.payload.questions.filter((question) => question.typeOfQuestion === "TN");
                state.questionDS = action.payload.questions.filter((question) => question.typeOfQuestion === "DS");
                state.questionTLN = action.payload.questions.filter((question) => question.typeOfQuestion === "TLN");
                state.loadingQuestions = false;
            })
            .addCase(fetchPublicQuestionsByExamId.rejected, (state) => {
                state.loadingQuestions = false;
                state.questions = [];
                state.questionTN = [];
                state.questionDS = [];
                state.questionTLN = [];
            })
            .addCase(joinExam.pending, (state) => {
                state.loadingJoin = true;
                state.saveQuestions = [];
                state.errorQuestions = [];
                state.attemptId = null;
                state.startTime = null;
            })
            .addCase(joinExam.fulfilled, (state, action) => {
                state.attemptId = action.payload.attemptId;
                state.startTime = action.payload.startTime;
                state.loadingJoin = false;
            })
            .addCase(joinExam.rejected, (state) => {
                state.loadingJoin = false;
            })
            .addCase(submitAnswer.pending, (state) => {
                state.loadingSubmitAnswer = true;
            })
            .addCase(submitAnswer.fulfilled, (state, action) => {
                state.loadingSubmitAnswer = false;
            })
            .addCase(submitAnswer.rejected, (state) => {
                state.loadingSubmitAnswer = false;
            })
            .addCase(calculateScore.pending, (state) => {
                state.loadingCalculate = true;
            })
            .addCase(calculateScore.fulfilled, (state, action) => {
                // Handle score calculation result
                state.loadingCalculate = false;
            })
            .addCase(calculateScore.rejected, (state) => {
                state.loadingCalculate = false;
            })
            .addCase(summitExam.pending, (state) => {
                state.loadingSubmit = true;
            })
            .addCase(summitExam.fulfilled, (state, action) => {
                state.loadingSubmit = false;
                state.isSubmit = true;
                // Reset question states when exam is successfully submitted
                state.saveQuestions = [];
                state.errorQuestions = [];
                // console.log("🔄 Exam submitted: Reset saveQuestions and errorQuestions");
            })
            .addCase(summitExam.rejected, (state) => {
                state.loadingSubmit = false;
                state.isSubmit = false;
            })
            // getRemainingTime
            .addCase(getRemainingTime.pending, (state) => {
                state.loadingTime = true;
                state.isTimeUp = false;
                state.remainingTime = null;
            })
            .addCase(getRemainingTime.fulfilled, (state, action) => {
                state.loadingTime = false;
                if (action.payload?.data?.remainingTime !== undefined) {
                    state.remainingTime = action.payload.data.remainingTime;
                    state.isTimeUp = action.payload.data.isTimeUp;
                }
            })
            .addCase(getRemainingTime.rejected, (state) => {
                state.loadingTime = false;
                state.isTimeUp = false;
                state.remainingTime = null;
            })
            // logUserActivity
            .addCase(logUserActivity.pending, (state) => {
                state.loadingActivity = true;
            })
            .addCase(logUserActivity.fulfilled, (state) => {
                state.loadingActivity = false;
            })
            .addCase(logUserActivity.rejected, (state) => {
                state.loadingActivity = false;
            })
            // submitAnswerWithAttempt
            .addCase(submitAnswerWithAttempt.pending, (state) => {
                state.loadingSubmitAnswer = true;
            })
            .addCase(submitAnswerWithAttempt.fulfilled, (state, action) => {
                state.loadingSubmitAnswer = false;
                // Add questionId to saveQuestions if successful
                if (action.payload?.data?.questionId) {
                    const questionId = action.payload.data.questionId;

                    // Add to saveQuestions if not already there
                    if (!state.saveQuestions.includes(questionId)) {
                        state.saveQuestions.push(questionId);
                        // console.log(`✅ API Success: Added questionId ${questionId} to saveQuestions`);
                    }

                    // Remove from errorQuestions if it was there
                    const wasInError = state.errorQuestions.includes(questionId);
                    state.errorQuestions = state.errorQuestions.filter(id => id !== questionId);
                    // if (wasInError) {
                    //     console.log(`✅ API Success: Removed questionId ${questionId} from errorQuestions`);
                    // }
                }
            })
            .addCase(submitAnswerWithAttempt.rejected, (state, action) => {
                state.loadingSubmitAnswer = false;
                // Add questionId to errorQuestions if failed
                if (action.meta?.arg?.questionId) {
                    const questionId = action.meta.arg.questionId;

                    // Add to errorQuestions if not already there
                    if (!state.errorQuestions.includes(questionId)) {
                        state.errorQuestions.push(questionId);
                        console.log(`❌ API Failed: Added questionId ${questionId} to errorQuestions`);
                    }

                    // Remove from saveQuestions if it was there
                    const wasInSave = state.saveQuestions.includes(questionId);
                    state.saveQuestions = state.saveQuestions.filter(id => id !== questionId);
                    if (wasInSave) {
                        console.log(`❌ API Failed: Removed questionId ${questionId} from saveQuestions`);
                    }
                }
            })
            // leaveExam
            .addCase(leaveExam.pending, (state) => {
                state.loadingLeave = true;
            })
            .addCase(leaveExam.fulfilled, (state) => {
                state.loadingLeave = false;
            })
            .addCase(leaveExam.rejected, (state) => {
                state.loadingLeave = false;
            })
    },
});

export const {
    setAttemptId,
    setStartTime,
    setSaveQuestions,
    setErrorQuestions,
    setRemainingTime,
    resetQuestionStates,
    toggleDarkMode,
    setFontSize,
    setImageSize,
    setSingleQuestionMode,
    setShowMarkedOnly,
    toggleGuide,
    setShowGuide,
    reset,
    setView,
    setSelectedQuestion,
    setShowModalSubmit,
    setAnswers,
    setIsTimeUp,
    setShowSidebar,
    toggleSidebar,
} = doExamSlice.actions;

export default doExamSlice.reducer;
