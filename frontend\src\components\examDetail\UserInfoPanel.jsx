import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { setView } from "../../features/exam/examDetailSlice";
import { User, Trophy, Clock, RotateCcw, Eye } from "lucide-react";

const UserInfoPanel = ({ examId }) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { exam } = useSelector((state) => state.examDetail);
    const { userRank, bestAttempt, userAttemptCount } = useSelector((state) => state.attempts);
    const { user } = useSelector((state) => state.auth);

    if (!user || !bestAttempt || !userRank) {
        return null;
    }

    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            {/* Header */}
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <div className="flex items-center gap-2">
                    <User size={16} className="text-sky-600" />
                    <p className="font-Inter text-sm font-semibold">Thông tin của bạn</p>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                    <Trophy size={16} className="flex-shrink-0" />
                    <p className="text-xs">#{userRank}</p>
                </div>
            </div>

            {/* Content */}
            <div className="p-4">
                <div className="flex flex-col gap-3">
                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                        <div className="flex items-center gap-2">
                            <Trophy size={14} className="text-green-600" />
                            <span className="text-sm text-gray-600">Điểm cao nhất:</span>
                        </div>
                        <span className="font-semibold text-green-600">{bestAttempt.score}</span>
                    </div>

                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                        <div className="flex items-center gap-2">
                            <Clock size={14} className="text-blue-600" />
                            <span className="text-sm text-gray-600">Thời gian:</span>
                        </div>
                        <span className="font-semibold">{bestAttempt.duration}</span>
                    </div>

                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                        <div className="flex items-center gap-2">
                            <Trophy size={14} className="text-sky-600" />
                            <span className="text-sm text-gray-600">Xếp hạng:</span>
                        </div>
                        <span className="font-semibold text-sky-600">#{userRank}</span>
                    </div>

                    <div className="flex items-center justify-between py-2 border-b border-gray-100">
                        <div className="flex items-center gap-2">
                            <RotateCcw size={14} className="text-purple-600" />
                            <span className="text-sm text-gray-600">Số lần làm:</span>
                        </div>
                        <span className="font-semibold">{userAttemptCount || 1}</span>
                    </div>


                </div>
            </div>
        </div>
    );
};

export default UserInfoPanel;
