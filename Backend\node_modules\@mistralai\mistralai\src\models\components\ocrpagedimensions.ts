/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type OCRPageDimensions = {
  /**
   * Dots per inch of the page-image
   */
  dpi: number;
  /**
   * Height of the image in pixels
   */
  height: number;
  /**
   * Width of the image in pixels
   */
  width: number;
};

/** @internal */
export const OCRPageDimensions$inboundSchema: z.ZodType<
  OCRPageDimensions,
  z.ZodTypeDef,
  unknown
> = z.object({
  dpi: z.number().int(),
  height: z.number().int(),
  width: z.number().int(),
});

/** @internal */
export type OCRPageDimensions$Outbound = {
  dpi: number;
  height: number;
  width: number;
};

/** @internal */
export const OCRPageDimensions$outboundSchema: z.ZodType<
  OCRPageDimensions$Outbound,
  z.ZodTypeDef,
  OCRPageDimensions
> = z.object({
  dpi: z.number().int(),
  height: z.number().int(),
  width: z.number().int(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OCRPageDimensions$ {
  /** @deprecated use `OCRPageDimensions$inboundSchema` instead. */
  export const inboundSchema = OCRPageDimensions$inboundSchema;
  /** @deprecated use `OCRPageDimensions$outboundSchema` instead. */
  export const outboundSchema = OCRPageDimensions$outboundSchema;
  /** @deprecated use `OCRPageDimensions$Outbound` instead. */
  export type Outbound = OCRPageDimensions$Outbound;
}

export function ocrPageDimensionsToJSON(
  ocrPageDimensions: OCRPageDimensions,
): string {
  return JSON.stringify(
    OCRPageDimensions$outboundSchema.parse(ocrPageDimensions),
  );
}

export function ocrPageDimensionsFromJSON(
  jsonString: string,
): SafeParseResult<OCRPageDimensions, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OCRPageDimensions$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OCRPageDimensions' from JSON`,
  );
}
