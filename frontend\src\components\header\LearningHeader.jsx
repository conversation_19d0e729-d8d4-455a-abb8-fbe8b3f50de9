import FullScreen from "../button/ScreenButton";
import { Moon, Sun, HelpCircle, Home } from "lucide-react"
import { BookO<PERSON>, Menu, X } from "lucide-react"
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import ButtonHeader from "./ButtonHeader";

const HeaderLearning = ({ isDarkMode, setIsSidebarOpen, isSidebarOpen, toggleDarkMode, toggleGuide, activeItem }) => {
    const { classDetail } = useSelector((state) => state.classes);
    const navigate = useNavigate();

    return (
        <div className={`shadow-sm border-b flex-shrink-0 z-30 transition-colors duration-300 ${isDarkMode
            ? 'bg-gray-800 border-gray-700'
            : 'bg-white border-gray-200'
            }`}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <div className="flex items-center gap-4">
                        {/* Mobile sidebar toggle */}
                        <button
                            className={`lg:hidden p-2 rounded-md transition-colors duration-200 ${isDarkMode
                                ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                                }`}
                            onClick={() => setIsSidebarOpen(prev => !prev)}
                        >
                            {isSidebarOpen ? <X size={20} /> : <Menu size={20} />}
                        </button>


                        <div className="flex items-center gap-3">
                            <BookOpen size={20} className="text-sky-600" />
                            <div>
                                <h1 className={`lg:text-lg font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>
                                    {classDetail?.name || 'Đang tải...'}
                                </h1>
                                <p className={`text-sm sm:block hidden transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'
                                    }`}>Học tập trực tuyến</p>
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center gap-3">
                        {/* Guide Button */}

                        {/* Dark Mode Toggle */}
                        <ButtonHeader
                            darkMode={isDarkMode}
                            onClick={toggleDarkMode}
                            Icon={isDarkMode ? Sun : Moon}
                            title={isDarkMode ? "Chế độ sáng" : "Chế độ tối"}
                        />

                        <FullScreen isDarkMode={isDarkMode} />

                        <ButtonHeader
                            darkMode={isDarkMode}
                            onClick={toggleGuide}
                            Icon={HelpCircle}
                            title="Hướng dẫn"
                        />

                        <ButtonHeader
                            darkMode={isDarkMode}
                            onClick={() => navigate('/class')}
                            Icon={Home}
                            title="Về trang Lớp học"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HeaderLearning;