interface Marker {
    duration: number;
    name: string;
    time: number;
}

/**
 * Represents the different types of events that can be dispatched.
 */
type EventType = 'complete' | 'frame' | 'load' | 'loadError' | 'renderError' | 'loop' | 'pause' | 'play' | 'stop' | 'destroy' | 'freeze' | 'unfreeze' | 'render' | 'ready';
/**
 * Maps an event type string to its respective event interface.
 */
type EventByType<T> = T extends 'complete' ? CompleteEvent : T extends 'frame' ? FrameEvent : T extends 'load' ? LoadEvent : T extends 'loadError' ? LoadErrorEvent : T extends 'renderError' ? RenderErrorEvent : T extends 'loop' ? LoopEvent : T extends 'pause' ? PauseEvent : T extends 'play' ? PlayEvent : T extends 'stop' ? StopEvent : T extends 'destroy' ? DestroyEvent : T extends 'freeze' ? FreezeEvent : T extends 'unfreeze' ? UnfreezeEvent : T extends 'render' ? RenderEvent : T extends 'ready' ? ReadyEvent : never;
/**
 * Base interface for all events.
 */
interface BaseEvent {
    type: EventType;
}
interface RenderEvent extends BaseEvent {
    currentFrame: number;
    type: 'render';
}
interface FreezeEvent extends BaseEvent {
    type: 'freeze';
}
interface UnfreezeEvent extends BaseEvent {
    type: 'unfreeze';
}
interface DestroyEvent extends BaseEvent {
    type: 'destroy';
}
/**
 * Event fired when a loop action occurs.
 */
interface LoopEvent extends BaseEvent {
    loopCount: number;
    type: 'loop';
}
/**
 * Event fired during frame changes.
 */
interface FrameEvent extends BaseEvent {
    currentFrame: number;
    type: 'frame';
}
/**
 * Event fired when a load action occurs.
 */
interface LoadEvent extends BaseEvent {
    type: 'load';
}
/**
 * Event fired when a loading error occurs.
 */
interface LoadErrorEvent extends BaseEvent {
    error: Error;
    type: 'loadError';
}
/**
 * Event fired when a loading error occurs.
 */
interface RenderErrorEvent extends BaseEvent {
    error: Error;
    type: 'renderError';
}
/**
 * Event fired when a completion action occurs.
 */
interface CompleteEvent extends BaseEvent {
    type: 'complete';
}
/**
 * Event fired when a pause action occurs.
 */
interface PauseEvent extends BaseEvent {
    type: 'pause';
}
/**
 * Event fired when a play action occurs.
 */
interface PlayEvent extends BaseEvent {
    type: 'play';
}
/**
 * Event fired when a stop action occurs.
 */
interface StopEvent extends BaseEvent {
    type: 'stop';
}
/**
 * Event fired when a WASM module is initialized and ready.
 */
interface ReadyEvent extends BaseEvent {
    type: 'ready';
}
/**
 * Type representing all possible event types.
 */
type Event = LoopEvent | FrameEvent | LoadEvent | LoadErrorEvent | RenderErrorEvent | CompleteEvent | PauseEvent | PlayEvent | StopEvent | DestroyEvent | FreezeEvent | UnfreezeEvent | RenderEvent | ReadyEvent;
interface EventListener<T extends EventType> {
    (event: EventByType<T>): void;
}
/**
 * Manages registration and dispatching of event listeners.
 */
declare class EventManager {
    private readonly _eventListeners;
    addEventListener<T extends EventType>(type: T, listener: EventListener<T>): void;
    removeEventListener<T extends EventType>(type: T, listener?: EventListener<T>): void;
    dispatch<T extends EventType>(event: EventByType<T>): void;
    removeAllEventListeners(): void;
}

interface RenderConfig {
    autoResize?: boolean;
    devicePixelRatio?: number;
    freezeOnOffscreen?: boolean;
}
type Mode = 'forward' | 'reverse' | 'bounce' | 'reverse-bounce';
type Data = string | ArrayBuffer | Record<string, unknown>;
type Fit = 'contain' | 'cover' | 'fill' | 'none' | 'fit-width' | 'fit-height';
interface Layout {
    align?: [number, number];
    fit?: Fit;
}
interface Config {
    animationId?: string;
    autoplay?: boolean;
    backgroundColor?: string;
    canvas: HTMLCanvasElement | OffscreenCanvas;
    data?: Data;
    layout?: Layout;
    loop?: boolean;
    marker?: string;
    mode?: Mode;
    renderConfig?: RenderConfig;
    segment?: [number, number];
    speed?: number;
    src?: string;
    themeId?: string;
    useFrameInterpolation?: boolean;
}
interface Manifest {
    animations: Array<{
        background?: string;
        id: string;
        initialTheme?: string;
        themes?: string[];
    }>;
    generator?: string;
    stateMachines?: Array<{
        id: string;
    }>;
    themes?: Array<{
        id: string;
    }>;
    version?: string;
}

interface RenderSurface {
    height: number;
    width: number;
}
declare class DotLottie {
    private readonly _canvas;
    private _context;
    private readonly _eventManager;
    private _animationFrameId;
    private readonly _frameManager;
    private _dotLottieCore;
    private static _wasmModule;
    private _renderConfig;
    private _isFrozen;
    private _backgroundColor;
    private readonly _pointerUpMethod;
    private readonly _pointerDownMethod;
    private readonly _pointerMoveMethod;
    private readonly _pointerEnterMethod;
    private readonly _pointerExitMethod;
    constructor(config: Omit<Config, 'canvas'> & {
        canvas: HTMLCanvasElement | OffscreenCanvas | RenderSurface;
    });
    private _dispatchError;
    private _fetchData;
    private _loadFromData;
    private _loadFromSrc;
    get buffer(): Uint8Array | null;
    get activeAnimationId(): string | undefined;
    get activeThemeId(): string | undefined;
    get layout(): Layout | undefined;
    get marker(): string | undefined;
    get manifest(): Manifest | null;
    get renderConfig(): RenderConfig;
    get segment(): [number, number] | undefined;
    get loop(): boolean;
    get mode(): Mode;
    get isFrozen(): boolean;
    get backgroundColor(): string;
    get autoplay(): boolean;
    get useFrameInterpolation(): boolean;
    get speed(): number;
    get isReady(): boolean;
    get isLoaded(): boolean;
    get isPlaying(): boolean;
    get isPaused(): boolean;
    get isStopped(): boolean;
    get currentFrame(): number;
    get loopCount(): number;
    get totalFrames(): number;
    get duration(): number;
    get segmentDuration(): number;
    get canvas(): HTMLCanvasElement | OffscreenCanvas | RenderSurface;
    load(config: Omit<Config, 'canvas'>): void;
    private _render;
    private _draw;
    play(): void;
    pause(): void;
    stop(): void;
    setFrame(frame: number): void;
    setSpeed(speed: number): void;
    setBackgroundColor(color: string): void;
    setLoop(loop: boolean): void;
    setUseFrameInterpolation(useFrameInterpolation: boolean): void;
    addEventListener<T extends EventType>(type: T, listener: EventListener<T>): void;
    removeEventListener<T extends EventType>(type: T, listener?: EventListener<T>): void;
    destroy(): void;
    freeze(): void;
    unfreeze(): void;
    resize(): void;
    setSegment(startFrame: number, endFrame: number): void;
    setMode(mode: Mode): void;
    setRenderConfig(config: RenderConfig): void;
    loadAnimation(animationId: string): void;
    setMarker(marker: string): void;
    markers(): Marker[];
    setTheme(themeId: string): boolean;
    resetTheme(): boolean;
    setThemeData(themeData: string): boolean;
    setSlots(slots: string): void;
    setLayout(layout: Layout): void;
    setViewport(x: number, y: number, width: number, height: number): boolean;
    static setWasmUrl(url: string): void;
    loadStateMachine(stateMachineId: string): boolean;
    startStateMachine(): boolean;
    stopStateMachine(): boolean;
    private _getPointerPosition;
    private _onPointerUp;
    private _onPointerDown;
    private _onPointerMove;
    private _onPointerEnter;
    private _onPointerLeave;
    postPointerUpEvent(x: number, y: number): number | undefined;
    postPointerDownEvent(x: number, y: number): number | undefined;
    postPointerMoveEvent(x: number, y: number): number | undefined;
    postPointerEnterEvent(x: number, y: number): number | undefined;
    postPointerExitEvent(x: number, y: number): number | undefined;
    getStateMachineListeners(): string[];
    private _setupStateMachineListeners;
    private _cleanupStateMachineListeners;
    loadStateMachineData(stateMachineData: string): boolean;
    animationSize(): {
        height: number;
        width: number;
    };
    setStateMachineBooleanContext(name: string, value: boolean): boolean;
    setStateMachineNumericContext(name: string, value: number): boolean;
    setStateMachineStringContext(name: string, value: string): boolean;
    /**
     * Get the Oriented Bounding Box (OBB) points of a layer by its name
     * @param layerName - The name of the layer
     * @returns An array of 8 numbers representing 4 points (x,y) of the OBB in clockwise order starting from top-left
     *          [x0, y0, x1, y1, x2, y2, x3, y3]
     *
     * @example
     * ```typescript
     * // Draw a polygon around the layer 'Layer 1'
     * dotLottie.addEventListener('render', () => {
     *   const obbPoints = dotLottie.getLayerBoundingBox('Layer 1');
     *
     *   if (obbPoints) {
     *     context.beginPath();
     *     context.moveTo(obbPoints[0], obbPoints[1]); // First point
     *     context.lineTo(obbPoints[2], obbPoints[3]); // Second point
     *     context.lineTo(obbPoints[4], obbPoints[5]); // Third point
     *     context.lineTo(obbPoints[6], obbPoints[7]); // Fourth point
     *     context.closePath();
     *     context.stroke();
     *   }
     * });
     * ```
     */
    getLayerBoundingBox(layerName: string): number[] | undefined;
    /**
     * @experimental
     * Start a tween animation between two frame values with custom easing
     * @param frame - Starting frame value
     * @param duration - Duration of the tween in seconds
     * @returns true if tween was started successfully
     */
    tween(frame: number, duration: number): boolean;
    /**
     * @experimental
     * Start a tween animation to a specific marker
     * @param marker - The marker name to tween to
     * @returns true if tween was started successfully
     */
    tweenToMarker(marker: string, duration: number): boolean;
    static transformThemeToLottieSlots(theme: string, slots: string): string;
}

interface DotLottieInstanceState {
    activeAnimationId: string | undefined;
    activeThemeId: string | undefined;
    autoplay: boolean;
    backgroundColor: string;
    currentFrame: number;
    duration: number;
    isFrozen: boolean;
    isLoaded: boolean;
    isPaused: boolean;
    isPlaying: boolean;
    isReady: boolean;
    isStopped: boolean;
    layout: Layout | undefined;
    loop: boolean;
    loopCount: number;
    manifest: Manifest | null;
    marker: string | undefined;
    markers: Marker[];
    mode: Mode;
    renderConfig: RenderConfig;
    segment: [number, number] | undefined;
    segmentDuration: number;
    speed: number;
    totalFrames: number;
    useFrameInterpolation: boolean;
}
declare class DotLottieWorker {
    private static readonly _workerManager;
    private readonly _eventManager;
    private readonly _id;
    private readonly _worker;
    private readonly _canvas;
    private _dotLottieInstanceState;
    private static _wasmUrl;
    private _created;
    private readonly _pointerUpMethod;
    private readonly _pointerDownMethod;
    private readonly _pointerMoveMethod;
    private readonly _pointerEnterMethod;
    private readonly _pointerExitMethod;
    constructor(config: Config & {
        workerId?: string;
    });
    private _handleWorkerEvent;
    private _create;
    get loopCount(): number;
    get isLoaded(): boolean;
    get isPlaying(): boolean;
    get isPaused(): boolean;
    get isStopped(): boolean;
    get currentFrame(): number;
    get isFrozen(): boolean;
    get segmentDuration(): number;
    get totalFrames(): number;
    get segment(): [number, number] | undefined;
    get speed(): number;
    get duration(): number;
    get isReady(): boolean;
    get mode(): Mode;
    get canvas(): HTMLCanvasElement | OffscreenCanvas | null;
    get autoplay(): boolean;
    get backgroundColor(): string;
    get loop(): boolean;
    get useFrameInterpolation(): boolean;
    get renderConfig(): RenderConfig;
    get manifest(): Manifest | null;
    get activeAnimationId(): string | undefined;
    get marker(): string | undefined;
    get activeThemeId(): string | undefined;
    get layout(): Layout | undefined;
    play(): Promise<void>;
    pause(): Promise<void>;
    stop(): Promise<void>;
    setSpeed(speed: number): Promise<void>;
    setMode(mode: Mode): Promise<void>;
    setFrame(frame: number): Promise<void>;
    setSegment(start: number, end: number): Promise<void>;
    setRenderConfig(renderConfig: RenderConfig): Promise<void>;
    setUseFrameInterpolation(useFrameInterpolation: boolean): Promise<void>;
    setTheme(themeId: string): Promise<boolean>;
    load(config: Omit<Config, 'canvas'>): Promise<void>;
    setLoop(loop: boolean): Promise<void>;
    resize(): Promise<void>;
    destroy(): Promise<void>;
    freeze(): Promise<void>;
    unfreeze(): Promise<void>;
    setBackgroundColor(backgroundColor: string): Promise<void>;
    loadAnimation(animationId: string): Promise<void>;
    setLayout(layout: Layout): Promise<void>;
    private _updateDotLottieInstanceState;
    markers(): Marker[];
    setMarker(marker: string): Promise<void>;
    setThemeData(themeData: string): Promise<boolean>;
    setViewport(x: number, y: number, width: number, height: number): Promise<boolean>;
    animationSize(): Promise<{
        height: number;
        width: number;
    }>;
    /**
     * @experimental
     * Start a tween animation between two frame values with custom easing
     * @param frame - Starting frame value
     * @param duration - Duration of the tween in seconds
     * @returns true if tween was started successfully
     */
    tween(frame: number, duration: number): Promise<boolean>;
    /**
     * @experimental
     * Start a tween animation to a specific marker
     * @param marker - The marker name to tween to
     * @param duration - Duration of the tween in seconds
     * @returns true if tween was started successfully
     */
    tweenToMarker(marker: string, duration: number): Promise<boolean>;
    private _sendMessage;
    addEventListener<T extends EventType>(type: T, listener: EventListener<T>): void;
    removeEventListener<T extends EventType>(type: T, listener?: EventListener<T>): void;
    static setWasmUrl(url: string): void;
    loadStateMachine(stateMachineId: string): Promise<boolean>;
    loadStateMachineData(stateMachineData: string): Promise<boolean>;
    startStateMachine(): Promise<boolean>;
    stopStateMachine(): Promise<boolean>;
    getStateMachineListeners(): Promise<string[]>;
    private _getPointerPosition;
    private _onPointerUp;
    private _onPointerDown;
    private _onPointerMove;
    private _onPointerEnter;
    private _onPointerLeave;
    private _setupStateMachineListeners;
    private _cleanupStateMachineListeners;
}

export { type BaseEvent, type CompleteEvent, type Config, type Data, type DestroyEvent, DotLottie, type DotLottieInstanceState, DotLottieWorker, type Event, type EventListener, EventManager, type EventType, type Fit, type FrameEvent, type FreezeEvent, type Layout, type LoadErrorEvent, type LoadEvent, type LoopEvent, type Manifest, type Mode, type PauseEvent, type PlayEvent, type ReadyEvent, type RenderConfig, type RenderErrorEvent, type RenderEvent, type StopEvent, type UnfreezeEvent };
