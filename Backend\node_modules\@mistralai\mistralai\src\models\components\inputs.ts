/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  AssistantMessage,
  AssistantMessage$inboundSchema,
  AssistantMessage$Outbound,
  AssistantMessage$outboundSchema,
} from "./assistantmessage.js";
import {
  InstructRequest,
  InstructRequest$inboundSchema,
  InstructRequest$Outbound,
  InstructRequest$outboundSchema,
} from "./instructrequest.js";
import {
  SystemMessage,
  SystemMessage$inboundSchema,
  SystemMessage$Outbound,
  SystemMessage$outboundSchema,
} from "./systemmessage.js";
import {
  ToolMessage,
  ToolMessage$inboundSchema,
  ToolMessage$Outbound,
  ToolMessage$outboundSchema,
} from "./toolmessage.js";
import {
  UserMessage,
  UserMessage$inboundSchema,
  UserMessage$Outbound,
  UserMessage$outboundSchema,
} from "./usermessage.js";

export type InstructRequestInputsMessages =
  | (SystemMessage & { role: "system" })
  | (UserMessage & { role: "user" })
  | (AssistantMessage & { role: "assistant" })
  | (ToolMessage & { role: "tool" });

export type InstructRequestInputs = {
  messages: Array<
    | (SystemMessage & { role: "system" })
    | (UserMessage & { role: "user" })
    | (AssistantMessage & { role: "assistant" })
    | (ToolMessage & { role: "tool" })
  >;
};

/**
 * Chat to classify
 */
export type Inputs = InstructRequestInputs | Array<InstructRequest>;

/** @internal */
export const InstructRequestInputsMessages$inboundSchema: z.ZodType<
  InstructRequestInputsMessages,
  z.ZodTypeDef,
  unknown
> = z.union([
  SystemMessage$inboundSchema.and(
    z.object({ role: z.literal("system") }).transform((v) => ({
      role: v.role,
    })),
  ),
  UserMessage$inboundSchema.and(
    z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role })),
  ),
  AssistantMessage$inboundSchema.and(
    z.object({ role: z.literal("assistant") }).transform((v) => ({
      role: v.role,
    })),
  ),
  ToolMessage$inboundSchema.and(
    z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role })),
  ),
]);

/** @internal */
export type InstructRequestInputsMessages$Outbound =
  | (SystemMessage$Outbound & { role: "system" })
  | (UserMessage$Outbound & { role: "user" })
  | (AssistantMessage$Outbound & { role: "assistant" })
  | (ToolMessage$Outbound & { role: "tool" });

/** @internal */
export const InstructRequestInputsMessages$outboundSchema: z.ZodType<
  InstructRequestInputsMessages$Outbound,
  z.ZodTypeDef,
  InstructRequestInputsMessages
> = z.union([
  SystemMessage$outboundSchema.and(
    z.object({ role: z.literal("system") }).transform((v) => ({
      role: v.role,
    })),
  ),
  UserMessage$outboundSchema.and(
    z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role })),
  ),
  AssistantMessage$outboundSchema.and(
    z.object({ role: z.literal("assistant") }).transform((v) => ({
      role: v.role,
    })),
  ),
  ToolMessage$outboundSchema.and(
    z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace InstructRequestInputsMessages$ {
  /** @deprecated use `InstructRequestInputsMessages$inboundSchema` instead. */
  export const inboundSchema = InstructRequestInputsMessages$inboundSchema;
  /** @deprecated use `InstructRequestInputsMessages$outboundSchema` instead. */
  export const outboundSchema = InstructRequestInputsMessages$outboundSchema;
  /** @deprecated use `InstructRequestInputsMessages$Outbound` instead. */
  export type Outbound = InstructRequestInputsMessages$Outbound;
}

export function instructRequestInputsMessagesToJSON(
  instructRequestInputsMessages: InstructRequestInputsMessages,
): string {
  return JSON.stringify(
    InstructRequestInputsMessages$outboundSchema.parse(
      instructRequestInputsMessages,
    ),
  );
}

export function instructRequestInputsMessagesFromJSON(
  jsonString: string,
): SafeParseResult<InstructRequestInputsMessages, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => InstructRequestInputsMessages$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'InstructRequestInputsMessages' from JSON`,
  );
}

/** @internal */
export const InstructRequestInputs$inboundSchema: z.ZodType<
  InstructRequestInputs,
  z.ZodTypeDef,
  unknown
> = z.object({
  messages: z.array(
    z.union([
      SystemMessage$inboundSchema.and(
        z.object({ role: z.literal("system") }).transform((v) => ({
          role: v.role,
        })),
      ),
      UserMessage$inboundSchema.and(
        z.object({ role: z.literal("user") }).transform((v) => ({
          role: v.role,
        })),
      ),
      AssistantMessage$inboundSchema.and(
        z.object({ role: z.literal("assistant") }).transform((v) => ({
          role: v.role,
        })),
      ),
      ToolMessage$inboundSchema.and(
        z.object({ role: z.literal("tool") }).transform((v) => ({
          role: v.role,
        })),
      ),
    ]),
  ),
});

/** @internal */
export type InstructRequestInputs$Outbound = {
  messages: Array<
    | (SystemMessage$Outbound & { role: "system" })
    | (UserMessage$Outbound & { role: "user" })
    | (AssistantMessage$Outbound & { role: "assistant" })
    | (ToolMessage$Outbound & { role: "tool" })
  >;
};

/** @internal */
export const InstructRequestInputs$outboundSchema: z.ZodType<
  InstructRequestInputs$Outbound,
  z.ZodTypeDef,
  InstructRequestInputs
> = z.object({
  messages: z.array(
    z.union([
      SystemMessage$outboundSchema.and(
        z.object({ role: z.literal("system") }).transform((v) => ({
          role: v.role,
        })),
      ),
      UserMessage$outboundSchema.and(
        z.object({ role: z.literal("user") }).transform((v) => ({
          role: v.role,
        })),
      ),
      AssistantMessage$outboundSchema.and(
        z.object({ role: z.literal("assistant") }).transform((v) => ({
          role: v.role,
        })),
      ),
      ToolMessage$outboundSchema.and(
        z.object({ role: z.literal("tool") }).transform((v) => ({
          role: v.role,
        })),
      ),
    ]),
  ),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace InstructRequestInputs$ {
  /** @deprecated use `InstructRequestInputs$inboundSchema` instead. */
  export const inboundSchema = InstructRequestInputs$inboundSchema;
  /** @deprecated use `InstructRequestInputs$outboundSchema` instead. */
  export const outboundSchema = InstructRequestInputs$outboundSchema;
  /** @deprecated use `InstructRequestInputs$Outbound` instead. */
  export type Outbound = InstructRequestInputs$Outbound;
}

export function instructRequestInputsToJSON(
  instructRequestInputs: InstructRequestInputs,
): string {
  return JSON.stringify(
    InstructRequestInputs$outboundSchema.parse(instructRequestInputs),
  );
}

export function instructRequestInputsFromJSON(
  jsonString: string,
): SafeParseResult<InstructRequestInputs, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => InstructRequestInputs$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'InstructRequestInputs' from JSON`,
  );
}

/** @internal */
export const Inputs$inboundSchema: z.ZodType<Inputs, z.ZodTypeDef, unknown> = z
  .union([
    z.lazy(() => InstructRequestInputs$inboundSchema),
    z.array(InstructRequest$inboundSchema),
  ]);

/** @internal */
export type Inputs$Outbound =
  | InstructRequestInputs$Outbound
  | Array<InstructRequest$Outbound>;

/** @internal */
export const Inputs$outboundSchema: z.ZodType<
  Inputs$Outbound,
  z.ZodTypeDef,
  Inputs
> = z.union([
  z.lazy(() => InstructRequestInputs$outboundSchema),
  z.array(InstructRequest$outboundSchema),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Inputs$ {
  /** @deprecated use `Inputs$inboundSchema` instead. */
  export const inboundSchema = Inputs$inboundSchema;
  /** @deprecated use `Inputs$outboundSchema` instead. */
  export const outboundSchema = Inputs$outboundSchema;
  /** @deprecated use `Inputs$Outbound` instead. */
  export type Outbound = Inputs$Outbound;
}

export function inputsToJSON(inputs: Inputs): string {
  return JSON.stringify(Inputs$outboundSchema.parse(inputs));
}

export function inputsFromJSON(
  jsonString: string,
): SafeParseResult<Inputs, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Inputs$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Inputs' from JSON`,
  );
}
