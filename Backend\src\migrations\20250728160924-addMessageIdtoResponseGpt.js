'use strict';

export default {
    async up(queryInterface, Sequelize) {
        await queryInterface.addColumn('responseGPTQuestions', 'messageId', {
            type: Sequelize.INTEGER,
            allowNull: false,
        });
        await queryInterface.removeColumn('responseGPTQuestions', 'message');
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.removeColumn('responseGPTQuestions', 'messageId');
        await queryInterface.addColumn('responseGPTQuestions', 'message', {
            type: Sequelize.TEXT,
            allowNull: false,
        });
    },
};
