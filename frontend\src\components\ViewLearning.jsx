import YouTubePlayer from "./YouTubePlayer";
import { useDispatch, useSelector } from "react-redux";
import { fetchPublicExamById } from "../features/exam/examSlice";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PdfViewer from "./ViewPdf";
import {
    Play,
    FileText,
    PenTool,
    Calendar,
    Clock,
    CheckCircle,
    AlertCircle,
    BookOpen,
    Target,
    BarChart3,
    ExternalLink,
    Download
} from "lucide-react";



const ViewLearning = ({ activeItem, isDarkMode = false, }) => {
    const { exam } = useSelector((state) => state.exams);
    const { codes } = useSelector((state) => state.codes);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    let countDone = 0
    if (
        activeItem?.type === 'lesson' &&
        Array.isArray(activeItem?.item?.learningItems) &&
        activeItem.item.learningItems.length > 0
    ) {
        activeItem?.item?.learningItems?.forEach((item) => {
            if (item?.studyStatuses?.length === 0 || !item?.studyStatuses) return
            countDone = item?.studyStatuses[0]?.isDone ? countDone + 1 : countDone
        })
    }


    useEffect(() => {
        if (activeItem?.type === 'learningItem' && activeItem?.item?.typeOfLearningItem === 'BTVN') {
            dispatch(fetchPublicExamById(activeItem?.item?.url));
        }
    }, [dispatch, activeItem]);

    const handleClicked = () => {
        if (exam?.isDone) {
            // Navigate to the exam detail page with history view
            navigate(`/practice/exam/${exam?.id}?view=history`);
        } else {
            navigate(`/practice/exam/${exam?.id}`);
        }
    }

    // Helper function to get filename from URL
    const getFileNameFromUrl = (url) => {
        if (!url) return 'file';
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            const filename = pathname.split('/').pop();
            return filename || 'file';
        } catch {
            return 'file';
        }
    }

    // Helper function to download file
    const handleDownload = (url, filename) => {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    return (
        <div className="w-full h-full flex justify-center items-start">
            <div className="w-full max-w-4xl">
                {/* Guide Content */}
                {activeItem?.type === 'guide' && (
                    <div className={`rounded-md shadow-sm border transition-colors duration-300 ${isDarkMode
                        ? 'bg-gray-800 border-gray-700'
                        : 'bg-white border-gray-200'
                        }`}>
                        {/* Header */}
                        <div className={`p-6 border-b transition-colors duration-200 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'
                            }`}>
                            <div className="flex items-start gap-4">
                                <div className={`lg:w-12 lg:h-12 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 transition-colors duration-200 ${isDarkMode ? 'bg-blue-900/30' : 'bg-blue-100'
                                    }`}>
                                    <AlertCircle className="text-blue-600 lg:w-8 lg:h-8 w-6 h-6" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h1 className={`text-lg lg:text-2xl font-bold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                        }`}>
                                        Hướng dẫn sử dụng hệ thống học tập
                                    </h1>
                                    <p className={`text-xs transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                        }`}>
                                        Tìm hiểu cách sử dụng các tính năng của hệ thống học tập trực tuyến
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="p-6 space-y-8">
                            {/* Getting Started */}
                            <div className="space-y-4">
                                <h2 className={`lg:text-xl text-base font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>🚀 Bắt đầu học tập</h2>

                                <div className="grid md:grid-cols-2 gap-4">
                                    <div className={`p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                        }`}>
                                        <h3 className={`lg:text-base text-sm font-semibold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                            }`}>1. Chọn bài học</h3>
                                        <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                            }`}>
                                            Từ menu bên trái, click vào tên bài học để xem nội dung chi tiết và tiến độ học tập.
                                        </p>
                                    </div>

                                    <div className={`p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                        }`}>
                                        <h3 className={`lg:text-base font-semibold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                            }`}>2. Học từng mục</h3>
                                        <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                            }`}>
                                            Click vào từng mục học tập (video, tài liệu, bài tập) để bắt đầu học.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Learning Items Guide */}
                            <div className="space-y-4">
                                <h2 className={`lg:text-xl text-base font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>📚 Các loại nội dung học tập</h2>

                                <div className="space-y-4">
                                    <div className={`p-4 border-l-4 border-red-500 transition-colors duration-200 ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'
                                        }`}>
                                        <div className="flex items-center gap-3 mb-2">
                                            <Play size={20} className="text-red-500" />
                                            <h3 className={`lg:text-base text-sm font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                }`}>Video bài giảng</h3>
                                        </div>
                                        <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                            }`}>
                                            • Xem video trực tiếp trên trang<br />
                                            • Click "Mở trong tab mới" để xem ở cửa sổ riêng<br />
                                            • Đánh dấu "Đã học" sau khi hoàn thành
                                        </p>
                                    </div>

                                    <div className={`p-4 border-l-4 border-blue-500 transition-colors duration-200 ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'
                                        }`}>
                                        <div className="flex items-center gap-3 mb-2">
                                            <FileText size={20} className="text-blue-500" />
                                            <h3 className={`lg:text-base text-sm font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                }`}>Tài liệu PDF</h3>
                                        </div>
                                        <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                            }`}>
                                            • Đọc trực tiếp trên trang với trình xem PDF<br />
                                            • Click "Tải xuống" để lưu về máy<br />
                                            • Click "Mở trong tab mới" để xem ở cửa sổ riêng
                                        </p>
                                    </div>

                                    <div className={`p-4 border-l-4 border-green-500 transition-colors duration-200 ${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'
                                        }`}>
                                        <div className="flex items-center gap-3 mb-2">
                                            <PenTool size={20} className="text-green-500" />
                                            <h3 className={`lg:text-base text-sm font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                }`}>Bài tập về nhà</h3>
                                        </div>
                                        <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                            }`}>
                                            • Xem thông tin bài tập (ngày đăng, hạn nộp)<br />
                                            • Click "Bắt đầu làm bài" để làm bài tập<br />
                                            • Xem kết quả sau khi hoàn thành
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Progress Tracking */}
                            <div className="space-y-4">
                                <h2 className={`lg:text-xl text-base font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>📊 Theo dõi tiến độ</h2>

                                <div className={`p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                    }`}>
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-3">
                                            <div className="w-3 h-3 bg-green-500 rounded-full" />
                                            <span className={`text-xs lg:text-sm  font-medium transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                }`}>Đã học:</span>
                                            <span className={`text-xs lg:text-sm  transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                                }`}>Mục học tập đã hoàn thành</span>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <div className="w-3 h-3 bg-yellow-400 rounded-full" />
                                            <span className={`text-xs lg:text-sm  font-medium transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                }`}>Chưa học:</span>
                                            <span className={`text-xs lg:text-sm  transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                                }`}>Mục học tập chưa hoàn thành</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Navigation Tips */}
                            <div className="space-y-4">
                                <h2 className={`lg:text-xl text-base font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>🧭 Mẹo điều hướng</h2>

                                <div className="grid md:grid-cols-2 gap-4">
                                    <div className={`lg:text-base text-sm p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                        }`}>
                                        <h3 className={`lg:text-base text-sm font-semibold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                            }`}>Thanh điều hướng dưới</h3>
                                        <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                            }`}>
                                            Sử dụng nút "Trước" và "Tiếp" để chuyển giữa các mục học tập một cách nhanh chóng.
                                        </p>
                                    </div>

                                    <div className={`p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                        }`}>
                                        <h3 className={`lg:text-base text-sm font-semibold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                            }`}>Chế độ tối/sáng</h3>
                                        <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                            }`}>
                                            Click icon mặt trăng/mặt trời ở góc trên để chuyển đổi chế độ hiển thị.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Support */}
                            <div className={`p-6 rounded-lg border-2 border-dashed transition-colors duration-200 ${isDarkMode
                                ? 'border-gray-600 bg-gray-700/50'
                                : 'border-gray-300 bg-gray-50'
                                }`}>
                                <div className="text-center">
                                    <h3 className={`lg:text-lg text-base font-semibold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                        }`}>💡 Cần hỗ trợ?</h3>
                                    <p className={`text-xs lg:text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                        }`}>
                                        Nếu bạn gặp khó khăn trong quá trình sử dụng, hãy liên hệ với giáo viên hoặc quản trị viên để được hỗ trợ.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {activeItem?.item === null && activeItem?.type !== 'guide' && (
                    <div className={`rounded-xl shadow-sm border p-8 transition-colors duration-300 ${isDarkMode
                        ? 'bg-gray-800 border-gray-700'
                        : 'bg-white border-gray-200'
                        }`}>
                        <div className="text-center mb-8">
                            <div className={`lg:w-16 lg:h-16 w-14 h-14 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors duration-200 ${isDarkMode ? 'bg-sky-900/30' : 'bg-sky-100'
                                }`}>
                                <BookOpen className="text-sky-600 lg:w-8 lg:h-8 w-6 h-6" />
                            </div>
                            <h2 className={`lg:text-xl text-base font-bold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                }`}>Chào mừng đến với lớp học trực tuyến</h2>
                            <p className={`lg:text-base text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                }`}>Chọn một bài học từ menu bên trái để bắt đầu hành trình học tập của bạn</p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-6 mb-8">
                            <div className={`text-center p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'
                                }`}>
                                <Play size={24} className="text-red-500 mx-auto mb-2" />
                                <h3 className={`lg:text-base text-sm font-semibold mb-1 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>Video bài giảng</h3>
                                <p className={`lg:text-sm text-xs transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                    }`}>Học qua video trực quan và dễ hiểu</p>
                            </div>
                            <div className={`text-center p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'
                                }`}>
                                <FileText size={24} className="text-blue-500 mx-auto mb-2" />
                                <h3 className={`lg:text-base text-sm font-semibold mb-1 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>Tài liệu học tập</h3>
                                <p className={`lg:text-sm text-xs transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                    }`}>Đọc và nghiên cứu tài liệu PDF</p>
                            </div>
                            <div className={`text-center p-4 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'
                                }`}>
                                <PenTool size={24} className="text-green-500 mx-auto mb-2" />
                                <h3 className={`lg:text-base text-sm font-semibold mb-1 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>Bài tập thực hành</h3>
                                <p className={`lg:text-sm text-xs transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                    }`}>Làm bài tập để củng cố kiến thức</p>
                            </div>
                        </div>

                        <div className={`rounded-lg p-6 transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                            }`}>
                            <h3 className={`lg:text-base text-sm font-semibold mb-4 flex items-center transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                }`}>
                                <AlertCircle size={20} className="text-amber-500 mr-2" />
                                Hướng dẫn sử dụng
                            </h3>
                            <div className={`space-y-3 text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                }`}>
                                <div className="flex items-start gap-3">
                                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                                    <div className="lg:text-sm text-xs">
                                        <span className="font-medium">Đã học:</span> Mục học tập đã hoàn thành
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                                    <div className="lg:text-sm text-xs">
                                        <span className="font-medium">Chưa học:</span> Mục học tập chưa hoàn thành
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <CheckCircle size={16} className="text-green-500 flex-shrink-0" />
                                    <div className="lg:text-sm text-xs">
                                        Sau khi hoàn thành mỗi mục, nhấn <span className="font-medium text-green-600">"Đánh dấu đã học"</span> để lưu tiến độ
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {activeItem?.type === 'learningItem' && (
                    <div className={`rounded-xl shadow-sm border transition-colors duration-300 ${isDarkMode
                        ? 'bg-gray-800 border-gray-700'
                        : 'bg-white border-gray-200'
                        }`}>
                        {/* Header */}
                        <div className={`p-6 border-b transition-colors duration-200 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'
                            }`}>
                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0">
                                    {activeItem?.item?.typeOfLearningItem === 'VID' && (
                                        <div className={`lg:w-12 lg:h-12 h-10 w-10 rounded-lg flex items-center justify-center transition-colors duration-200 ${isDarkMode ? 'bg-red-900/30' : 'bg-red-100'
                                            }`}>
                                            <Play className="text-red-600 lg:w-6 lg:h-6 w-5 h-5" />
                                        </div>
                                    )}
                                    {activeItem?.item?.typeOfLearningItem === 'DOC' && (
                                        <div className={`lg:w-12 lg:h-12 h-10 w-10 rounded-lg flex items-center justify-center transition-colors duration-200 ${isDarkMode ? 'bg-blue-900/30' : 'bg-blue-100'
                                            }`}>
                                            <FileText size={24} className="text-blue-600 lg:w-6 lg:h-6 w-5 h-5" />
                                        </div>
                                    )}
                                    {activeItem?.item?.typeOfLearningItem === 'BTVN' && (
                                        <div className={`lg:w-12 lg:h-12 h-10 w-10 rounded-lg flex items-center justify-center transition-colors duration-200 ${isDarkMode ? 'bg-green-900/30' : 'bg-green-100'
                                            }`}>
                                            <PenTool size={24} className="text-green-600 lg:w-6 lg:h-6 w-5 h-5" />
                                        </div>
                                    )}
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h1 className={`lg:text-2xl text-xl font-bold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                        }`}>
                                        {activeItem?.item?.name}
                                    </h1>
                                    <div className={`flex items-center gap-2 text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                        }`}>
                                        <span className={`text-xs px-2 py-1 rounded-full transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
                                            }`}>
                                            {activeItem?.item?.typeOfLearningItem === 'VID' && 'Video bài giảng'}
                                            {activeItem?.item?.typeOfLearningItem === 'DOC' && 'Tài liệu học tập'}
                                            {activeItem?.item?.typeOfLearningItem === 'BTVN' && 'Bài tập về nhà'}
                                        </span>
                                        {activeItem?.item?.studyStatuses?.[0]?.isDone && (
                                            <span className="flex items-center gap-1 text-green-600">
                                                <CheckCircle size={16} />
                                                Đã hoàn thành
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="p-6 space-y-6">
                            {/* URL Display and Download Section */}
                            {activeItem?.item?.url && (
                                <div className={`rounded-lg p-4 border transition-colors duration-200 ${isDarkMode
                                    ? 'bg-gray-700 border-gray-600'
                                    : 'bg-gray-50 border-gray-200'
                                    }`}>
                                    <div className="flex items-center justify-between gap-4">
                                        <div className="flex-1 min-w-0">
                                            <h4 className={`text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                }`}>
                                                {activeItem?.item?.typeOfLearningItem === 'VID' && 'Link video bài giảng'}
                                                {activeItem?.item?.typeOfLearningItem === 'DOC' && 'Tài liệu PDF'}
                                                {activeItem?.item?.typeOfLearningItem === 'BTVN' && 'Link bài tập'}
                                            </h4>
                                            <div className="flex items-center gap-2 flex-wrap">
                                                <button
                                                    onClick={() => {
                                                        if (activeItem?.item?.typeOfLearningItem === 'BTVN') {
                                                            window.open("/practice/exam/" + activeItem?.item?.url, '_blank')
                                                        } else {
                                                            window.open(activeItem?.item?.url, '_blank')
                                                        }
                                                    }}
                                                    className="text-xs flex items-center gap-2 px-3 py-2 lg:text-sm rounded-md bg-sky-600 hover:bg-sky-700 text-white transition-colors duration-200"
                                                >
                                                    <ExternalLink className="lg:w-4 lg:h-4 w-3 h-3 " />
                                                    Mở trong tab mới
                                                </button>

                                                {/* Download button for PDF files */}
                                                {activeItem?.item?.typeOfLearningItem === 'DOC' && (
                                                    <button
                                                        onClick={() => handleDownload(
                                                            activeItem?.item?.url,
                                                            getFileNameFromUrl(activeItem?.item?.url)
                                                        )}
                                                        className="text-xs flex items-center gap-2 px-3 py-2 lg:text-sm rounded-md bg-green-600 hover:bg-green-700 text-white transition-colors duration-200"
                                                    >
                                                        <Download className="lg:w-4 lg:h-4 w-3 h-3 " />
                                                        Tải xuống
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                            {activeItem?.item?.description && (
                                <div className={`rounded-lg p-4 border transition-colors duration-200 ${isDarkMode
                                    ? 'bg-gray-700 border-gray-600'
                                    : 'bg-gray-50 border-gray-200'
                                    }`}>
                                    <div className="flex items-center justify-between gap-4">
                                        <div className="flex-1 min-w-0">
                                            <h4 className={`text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                }`}>
                                                Mô tả:
                                            </h4>
                                            <div className="flex items-center gap-2 flex-wrap">
                                                <p className={`lg:text-sm text-xs transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                                    }`}>
                                                    {activeItem?.item?.description}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Video Content */}
                            {(activeItem?.item?.typeOfLearningItem === 'VID' && activeItem?.item?.url) && (
                                <div className="space-y-4">
                                    <YouTubePlayer url={activeItem?.item?.url} />
                                </div>
                            )}

                            {/* Homework Content */}
                            {(activeItem?.item?.typeOfLearningItem === 'BTVN' && activeItem?.item?.url) && (
                                <div className="space-y-6">

                                    <div className={`flex items-center gap-2 p-3 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                        }`}>
                                        <FileText size={16} className={`transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                            }`} />
                                        <p className={`text-sm font-medium transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                            }`}>Link file đề: </p>
                                        {exam?.fileUrl ? (
                                            <a
                                                href={exam?.fileUrl}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className={`text-sm transition-colors underline duration-200 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'
                                                    }`}>
                                                Xem file đính kèm
                                            </a>
                                        ) : (
                                            <p className={`text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                }`}>
                                                Chưa có file đính kèm
                                            </p>
                                        )}
                                    </div>

                                    <div className="grid md:grid-cols-2 gap-4">
                                        <div className="space-y-4">
                                            <div className={`flex items-center gap-2 p-3 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                                }`}>
                                                <Calendar size={16} className={`transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                    }`} />
                                                <div>
                                                    <p className={`text-sm font-medium transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                        }`}>Ngày đăng</p>
                                                    <p className={`text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                        }`}>
                                                        {new Date(activeItem?.item?.createdAt).toLocaleDateString('vi-VN')}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className={`flex items-center gap-2 p-3 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                                }`}>
                                                <Clock size={16} className={`transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                    }`} />
                                                <div>
                                                    <p className={`text-sm font-medium transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                        }`}>Hạn nộp</p>
                                                    <p className={`text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                        }`}>
                                                        {activeItem?.item?.deadline
                                                            ? new Date(activeItem?.item?.deadline).toLocaleDateString('vi-VN')
                                                            : "Không giới hạn"
                                                        }
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="space-y-4">
                                            <div className={`flex items-center gap-2 p-3 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                                }`}>
                                                <Target size={16} className={`transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                    }`} />
                                                <div>
                                                    <p className={`text-sm font-medium transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                        }`}>Trạng thái</p>
                                                    <p className={`text-sm font-medium ${exam?.isDone ? 'text-green-600' : 'text-amber-600'
                                                        }`}>
                                                        {exam?.isDone ? "Đã hoàn thành" : "Chưa hoàn thành"}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className={`flex items-center gap-2 p-3 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                                }`}>
                                                <BarChart3 size={16} className={`transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                    }`} />
                                                <div>
                                                    <p className={`text-sm font-medium transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                                        }`}>Tỷ lệ đạt</p>
                                                    <p className={`text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                                        }`}>
                                                        {exam?.passRate ? `${exam?.passRate}%` : "Không có"}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex justify-center">
                                        <button
                                            onClick={handleClicked}
                                            className={`lg:text-base text-sm px-4 py-2 rounded-md font-medium transition-colors ${exam?.isDone
                                                ? 'bg-green-600 hover:bg-green-700 text-white'
                                                : 'bg-sky-600 hover:bg-sky-700 text-white'
                                                }`}
                                        >
                                            {exam?.isDone ? 'Xem kết quả' : 'Bắt đầu làm bài'}
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Document Content */}
                            {activeItem?.item?.typeOfLearningItem === 'DOC' && activeItem?.item?.url && (
                                <div className="space-y-4">
                                    <PdfViewer
                                        url={activeItem?.item?.url}
                                        height={'800px'}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                )}
                {activeItem?.type === 'lesson' && (
                    <div className={`rounded-xl shadow-sm border transition-colors duration-300 ${isDarkMode
                        ? 'bg-gray-800 border-gray-700'
                        : 'bg-white border-gray-200'
                        }`}>
                        {/* Header */}
                        <div className={`p-6 border-b transition-colors duration-200 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'
                            }`}>
                            <div className="flex items-start gap-4">
                                <div className={`lg:w-12 lg:h-12 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 transition-colors duration-200 ${isDarkMode ? 'bg-sky-900/30' : 'bg-sky-100'
                                    }`}>
                                    <BookOpen className="text-sky-600 lg:w-6 lg:h-6 w-5 h-5" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h1 className={`lg:text-2xl text-xl font-bold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                        }`}>
                                        {activeItem?.item?.name}
                                    </h1>
                                    <div className={`flex flex-wrap items-center gap-4 text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                        }`}>
                                        <div className="flex items-center gap-1 lg:text-base text-sm">
                                            <Calendar size={16} />
                                            <span>
                                                {activeItem?.item?.day
                                                    ? new Date(activeItem?.item?.day).toLocaleDateString('vi-VN')
                                                    : 'Chưa có ngày học'
                                                }
                                            </span>
                                        </div>
                                        {activeItem?.item?.chapter && (
                                            <div className="flex items-center gap-1 ">
                                                <BookOpen size={16} className="flex-shrink-0" />
                                                <span className="text-sky-600 font-medium lg:text-base text-sm">
                                                    {codes['chapter']?.find((item) => item.code === activeItem?.item?.chapter)?.description || activeItem?.item?.chapter}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="p-6 space-y-6">
                            {/* Description */}
                            {activeItem?.item?.description && (
                                <div className={`rounded-lg p-4 transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                                    }`}>
                                    <h3 className={`font-semibold mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                        }`}>Mô tả bài học</h3>
                                    <p className={`lg:text-sm text-xs whitespace-pre-line leading-relaxed transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                        }`}>
                                        {activeItem?.item?.description}
                                    </p>
                                </div>
                            )}

                            {/* Learning Progress */}
                            <div className="grid md:grid-cols-2 gap-4">
                                <div className={`rounded-lg p-4 transition-colors duration-200 ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'
                                    }`}>
                                    <div className="flex items-center gap-2 mb-2">
                                        <Target className="text-blue-600 lg:w-6 lg:h-6 w-5 h-5" />
                                        <h3 className={`font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                            }`}>Tổng số mục học</h3>
                                    </div>
                                    <p className="lg:text-2xl text-xl font-bold text-blue-600">
                                        {activeItem?.item?.learningItemCount || 0}
                                    </p>
                                    <p className={`lg:text-sm text-xs mt-1 transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                        }`}>mục học tập</p>
                                </div>

                                <div className={`rounded-lg p-4 transition-colors duration-200 ${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'
                                    }`}>
                                    <div className="flex items-center gap-2 mb-2">
                                        <CheckCircle className="text-green-600 lg:w-6 lg:h-6 w-5 h-5" />
                                        <h3 className={`font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                            }`}>Tiến độ hoàn thành</h3>
                                    </div>
                                    <p className="lg:text-2xl text-xl font-bold text-green-600">
                                        {Math.round(
                                            activeItem?.item?.learningItemCount > 0
                                                ? (countDone / activeItem?.item?.learningItemCount) * 100
                                                : 0
                                        )}%
                                    </p>
                                    <p className={`lg:text-sm text-xs mt-1 transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                                        }`}>
                                        {countDone} / {activeItem?.item?.learningItemCount} đã hoàn thành
                                    </p>
                                </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="space-y-2">
                                <div className={`lg:text-sm text-xs flex justify-between font-medium transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'
                                    }`}>
                                    <span>Tiến độ học tập</span>
                                    <span>{countDone} / {activeItem?.item?.learningItemCount} mục</span>
                                </div>
                                {activeItem?.item?.learningItemCount > 0 && (
                                    <div className={`w-full rounded-full lg:h-3 h-2 transition-colors duration-200 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                                        }`}>
                                        <div
                                            className="bg-gradient-to-r from-green-500 to-green-600 lg:h-3 h-2 rounded-full transition-all duration-500 ease-out"
                                            style={{
                                                width: `${(countDone / activeItem?.item?.learningItemCount * 100).toFixed(0)}%`
                                            }}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default ViewLearning;