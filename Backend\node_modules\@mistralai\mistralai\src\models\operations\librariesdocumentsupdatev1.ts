/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsUpdateV1Request = {
  libraryId: string;
  documentId: string;
  documentUpdateIn: components.DocumentUpdateIn;
};

/** @internal */
export const LibrariesDocumentsUpdateV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsUpdateV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  document_id: z.string(),
  DocumentUpdateIn: components.DocumentUpdateIn$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "document_id": "documentId",
    "DocumentUpdateIn": "documentUpdateIn",
  });
});

/** @internal */
export type LibrariesDocumentsUpdateV1Request$Outbound = {
  library_id: string;
  document_id: string;
  DocumentUpdateIn: components.DocumentUpdateIn$Outbound;
};

/** @internal */
export const LibrariesDocumentsUpdateV1Request$outboundSchema: z.ZodType<
  LibrariesDocumentsUpdateV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsUpdateV1Request
> = z.object({
  libraryId: z.string(),
  documentId: z.string(),
  documentUpdateIn: components.DocumentUpdateIn$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    documentId: "document_id",
    documentUpdateIn: "DocumentUpdateIn",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsUpdateV1Request$ {
  /** @deprecated use `LibrariesDocumentsUpdateV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesDocumentsUpdateV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsUpdateV1Request$outboundSchema` instead. */
  export const outboundSchema =
    LibrariesDocumentsUpdateV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsUpdateV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsUpdateV1Request$Outbound;
}

export function librariesDocumentsUpdateV1RequestToJSON(
  librariesDocumentsUpdateV1Request: LibrariesDocumentsUpdateV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsUpdateV1Request$outboundSchema.parse(
      librariesDocumentsUpdateV1Request,
    ),
  );
}

export function librariesDocumentsUpdateV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesDocumentsUpdateV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesDocumentsUpdateV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesDocumentsUpdateV1Request' from JSON`,
  );
}
