/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JobsApiRoutesFineTuningStartFineTuningJobRequest = {
  jobId: string;
};

/**
 * OK
 */
export type JobsApiRoutesFineTuningStartFineTuningJobResponse =
  | (components.ClassifierDetailedJobOut & { jobType: "classifier" })
  | (components.CompletionDetailedJobOut & { jobType: "completion" });

/** @internal */
export const JobsApiRoutesFineTuningStartFineTuningJobRequest$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningStartFineTuningJobRequest,
    z.ZodTypeDef,
    unknown
  > = z.object({
    job_id: z.string(),
  }).transform((v) => {
    return remap$(v, {
      "job_id": "jobId",
    });
  });

/** @internal */
export type JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound = {
  job_id: string;
};

/** @internal */
export const JobsApiRoutesFineTuningStartFineTuningJobRequest$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningStartFineTuningJobRequest
  > = z.object({
    jobId: z.string(),
  }).transform((v) => {
    return remap$(v, {
      jobId: "job_id",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningStartFineTuningJobRequest$ {
  /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobRequest$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningStartFineTuningJobRequest$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobRequest$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningStartFineTuningJobRequest$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningStartFineTuningJobRequest$Outbound;
}

export function jobsApiRoutesFineTuningStartFineTuningJobRequestToJSON(
  jobsApiRoutesFineTuningStartFineTuningJobRequest:
    JobsApiRoutesFineTuningStartFineTuningJobRequest,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningStartFineTuningJobRequest$outboundSchema.parse(
      jobsApiRoutesFineTuningStartFineTuningJobRequest,
    ),
  );
}

export function jobsApiRoutesFineTuningStartFineTuningJobRequestFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningStartFineTuningJobRequest,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningStartFineTuningJobRequest$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningStartFineTuningJobRequest' from JSON`,
  );
}

/** @internal */
export const JobsApiRoutesFineTuningStartFineTuningJobResponse$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningStartFineTuningJobResponse,
    z.ZodTypeDef,
    unknown
  > = z.union([
    components.ClassifierDetailedJobOut$inboundSchema.and(
      z.object({ job_type: z.literal("classifier") }).transform((v) => ({
        jobType: v.job_type,
      })),
    ),
    components.CompletionDetailedJobOut$inboundSchema.and(
      z.object({ job_type: z.literal("completion") }).transform((v) => ({
        jobType: v.job_type,
      })),
    ),
  ]);

/** @internal */
export type JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound =
  | (components.ClassifierDetailedJobOut$Outbound & { job_type: "classifier" })
  | (components.CompletionDetailedJobOut$Outbound & { job_type: "completion" });

/** @internal */
export const JobsApiRoutesFineTuningStartFineTuningJobResponse$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningStartFineTuningJobResponse
  > = z.union([
    components.ClassifierDetailedJobOut$outboundSchema.and(
      z.object({ jobType: z.literal("classifier") }).transform((v) => ({
        job_type: v.jobType,
      })),
    ),
    components.CompletionDetailedJobOut$outboundSchema.and(
      z.object({ jobType: z.literal("completion") }).transform((v) => ({
        job_type: v.jobType,
      })),
    ),
  ]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningStartFineTuningJobResponse$ {
  /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobResponse$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningStartFineTuningJobResponse$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobResponse$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningStartFineTuningJobResponse$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningStartFineTuningJobResponse$Outbound;
}

export function jobsApiRoutesFineTuningStartFineTuningJobResponseToJSON(
  jobsApiRoutesFineTuningStartFineTuningJobResponse:
    JobsApiRoutesFineTuningStartFineTuningJobResponse,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningStartFineTuningJobResponse$outboundSchema.parse(
      jobsApiRoutesFineTuningStartFineTuningJobResponse,
    ),
  );
}

export function jobsApiRoutesFineTuningStartFineTuningJobResponseFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningStartFineTuningJobResponse,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningStartFineTuningJobResponse$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningStartFineTuningJobResponse' from JSON`,
  );
}
