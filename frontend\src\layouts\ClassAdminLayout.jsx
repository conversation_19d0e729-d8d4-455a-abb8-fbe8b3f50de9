import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import AdminSidebar from '../components/sidebar/AdminSidebar';
import { Home } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchClassById } from '../features/class/classSlice';


const ClassAdminLayout = ({ children }) => {
    const { classId } = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { classDetail } = useSelector(state => state.classes);
    const [tabs, setTabs] = useState([
        { name: '<PERSON> tiết', path: `/admin/class-management/${classId}`, active: true },
        { name: 'Danh sách học sinh', path: `/admin/class-management/${classId}/users`, active: false },
        { name: '<PERSON><PERSON> sách bu<PERSON>i học', path: `/admin/class-management/${classId}/lessons`, active: false },
        { name: '<PERSON><PERSON><PERSON><PERSON> danh', path: `/admin/class-management/${classId}/attendance`, active: false }
    ]);
    const { closeSidebar } = useSelector((state) => state.sidebar);

    const handleTabClick = (tab) => {
        setTabs(prevTabs => prevTabs.map(t => ({
            ...t,
            active: t.name === tab.name
        })));
        navigate(tab.path);
    };

    useEffect(() => {
        setTabs(prevTabs =>
            prevTabs.map(tab => ({
                ...tab,
                active: location.pathname === tab.path
            }))
        );
    }, [location.pathname]);

    useEffect(() => {
        if (classId && String(classDetail?.id) !== String(classId)) {
            dispatch(fetchClassById(classId));
        }
    }, [classId, dispatch, classDetail?.id]);

    return (
        <div className="flex min-h-screen bg-gray-50">
            <AdminSidebar />
            <div className={`flex-1 transition-all duration-300 ${closeSidebar ? 'ml-[104px]' : 'ml-64'}`}>
                <div className="flex flex-col min-h-screen">
                    {/* Header */}
                    <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                        <div className="flex items-center gap-4">
                            {/* Breadcrumb */}
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Home size={16} />
                                <span>Trang chủ</span>
                                <span>/</span>
                                <span>Quản lý lớp học</span>
                                <span>/</span>
                                <span className="font-semibold">{tabs.find(tab => tab.active)?.name}</span>
                            </div>
                        </div>
                        <h1 className="text-2xl font-bold text-gray-900 mt-2">Chi tiết lớp học - {classDetail?.name || classId}</h1>
                    </div>

                    {/* Navigation Tabs */}
                    <div className="bg-white border-b border-gray-200 px-6">
                        <div className="flex space-x-8">
                            {tabs.map((tab) => (
                                <button
                                    key={tab.name}
                                    onClick={() => handleTabClick(tab)}
                                    className={`py-3 px-4 text-sm font-medium ${tab.active ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                                >
                                    {tab.name}
                                </button>
                            ))}
                        </div>
                    </div>
                    {/* Main Content */}
                    {children}
                </div>
            </div>
        </div>

    );
};

export default ClassAdminLayout;