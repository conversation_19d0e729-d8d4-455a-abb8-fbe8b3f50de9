import { sendUserNotification, sendClassNotification, sendExamNotification } from '../utils/notificationUtils.js';
import * as notificationService from '../services/notification.service.js';

/**
 * Example of how to send a notification to a specific user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const sendNotificationToUserExample = async (req, res) => {
  try {
    const { userId, title, content, type, relatedId, relatedType, actionUrl } = req.body;
    
    // Validate required fields
    if (!userId || !title || !content) {
      return res.status(400).json({
        message: 'Missing required fields: userId, title, content'
      });
    }
    
    // Get the Socket.IO instance from the app
    const io = req.app.get('io');
    
    // Send the notification
    const notification = await sendUserNotification(
      io,
      userId,
      title,
      content,
      type || 'SYSTEM',
      relatedId,
      relatedType,
      actionUrl
    );
    
    return res.status(200).json({
      message: 'Notification sent successfully',
      data: notification
    });
  } catch (error) {
    console.error('Error sending notification:', error);
    return res.status(500).json({
      message: 'Failed to send notification',
      error: error.message
    });
  }
};

/**
 * Example of how to send a notification to all users in a class
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const sendNotificationToClassExample = async (req, res) => {
  try {
    const { classId, title, content, actionUrl } = req.body;
    
    // Validate required fields
    if (!classId || !title || !content) {
      return res.status(400).json({
        message: 'Missing required fields: classId, title, content'
      });
    }
    
    // Get the Socket.IO instance from the app
    const io = req.app.get('io');
    
    // Send the notification
    const notifications = await sendClassNotification(
      io,
      classId,
      title,
      content,
      'CLASS',
      actionUrl
    );
    
    return res.status(200).json({
      message: 'Class notification sent successfully',
      data: {
        count: notifications.length,
        notifications
      }
    });
  } catch (error) {
    console.error('Error sending class notification:', error);
    return res.status(500).json({
      message: 'Failed to send class notification',
      error: error.message
    });
  }
};

/**
 * Example of how to send a notification to all users taking an exam
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const sendNotificationToExamExample = async (req, res) => {
  try {
    const { examId, title, content, actionUrl } = req.body;
    
    // Validate required fields
    if (!examId || !title || !content) {
      return res.status(400).json({
        message: 'Missing required fields: examId, title, content'
      });
    }
    
    // Get the Socket.IO instance from the app
    const io = req.app.get('io');
    
    // Send the notification
    const notifications = await sendExamNotification(
      io,
      examId,
      title,
      content,
      actionUrl
    );
    
    return res.status(200).json({
      message: 'Exam notification sent successfully',
      data: {
        count: notifications.length,
        notifications
      }
    });
  } catch (error) {
    console.error('Error sending exam notification:', error);
    return res.status(500).json({
      message: 'Failed to send exam notification',
      error: error.message
    });
  }
};
