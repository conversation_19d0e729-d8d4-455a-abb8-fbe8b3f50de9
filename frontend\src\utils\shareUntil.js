/**
 * Hàm chia sẻ đường dẫn sử dụng Web Share API (nếu có) hoặc copy vào clipboard
 * @param {Object} params
 * @param {string} params.title - Tiêu đề chia sẻ
 * @param {string} params.text - Nội dung chia sẻ
 * @param {string} [params.url=window.location.href] - Đường dẫn chia sẻ (mặc định là URL hiện tại)
 */
export const shareContent = async ({ title, text, url = window.location.href }) => {
    const shareData = {
        title,
        text,
        url
    };

    try {
        if (navigator.share) {
            await navigator.share(shareData);
        } else {
            await navigator.clipboard.writeText(url);
            alert('Đã sao chép link chia sẻ vào clipboard!');
        }
    } catch (error) {
        console.error('Error sharing:', error);
        try {
            await navigator.clipboard.writeText(url);
            alert('Đ<PERSON> sao chép link chia sẻ vào clipboard!');
        } catch (clipboardError) {
            console.error('Clipboard error:', clipboardError);
            alert('<PERSON>h<PERSON><PERSON> thể chia sẻ. Vui lòng sao chép link từ thanh địa chỉ.');
        }
    }
};
