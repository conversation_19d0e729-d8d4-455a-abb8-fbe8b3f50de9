import db from "../models/index.js"
import { Op, or, where } from "sequelize";
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js";
import * as mistralService from '../services/mistralAI.service.js';
import { encodePdfToBase64 } from '../services/pdf.service.js';
import { uploadBase64Images } from "../services/image.service.js";

export const postExam1 = async (req, res) => {
    // console.log(req.body);
    const { data } = req.body;
    const file = req.file;

    const base64 = encodePdfToBase64(file.buffer);
    if (!base64) return res.status(500).json({ message: 'Không thể đọc file.' });

    const result = await mistralService.performOcr(base64);

    const mergedMarkdown = result.pages
        .map(page => page.markdown)
        .filter(Boolean)
        .join('\n\n---\n\n');

    const base64Images = result.pages
        .map(page => page.images.map(image => image.imageBase64))
        .flat();

    const imageUrls = await uploadBase64Images(base64Images, 'draftQuestionImage');

    const exam = await db.Exam1.create({
        name: data.name,
        typeOfExam: data.typeOfExam,
        class: data.class,
        year: data.year,
        public: data.public,
        markdownExam: mergedMarkdown,
        imageUrls: JSON.stringify(imageUrls), // lưu chuỗi JSON thay vì .toString()
    });
}

export const getAllExams1 = async (req, res) => {
    const search = req.query.search || ''
    const page = parseInt(req.query.page, 10) || 1
    const pageSize = parseInt(req.query.pageSize, 10) || 10
    const sortOrder = req.query.sortOrder || 'DESC'
    const offset = (page - 1) * pageSize

    const { count, rows } = await db.Exam1.findAndCountAll({
        where: {
            name: {
                [Op.like]: `%${search}%`
            }
        },
        order: [['createdAt', sortOrder]],
        limit: pageSize,
        offset: offset,
    });

    return res.status(200).json({
        message: "Lấy danh sách exam AI thành công",
        ... new ResponseDataPagination(rows, {
            page,
            pageSize,
            total: count,
            totalPages: Math.ceil(count / pageSize),
            sortOrder
        })
    });
}

const getAllQuestions1ByExamIdService = async (examId) => {
    const result = await db.Exam1.findOne({
        where: { id: examId },
        include: [
            {
                model: db.Question1,
                as: 'question1s',
                include: [
                    {
                        model: db.Statement1,
                        as: 'statement1s'
                    }
                ]
            }
        ]
    });

    return result;
}

export const getAllQuestions1ByExamId = async (req, res) => {
    const { examId } = req.params;

    const result = await getAllQuestions1ByExamIdService(examId);

    if (!result) {
        return res.status(404).json({ message: "Không tìm thấy bài thi" });
    }

    return res.status(200).json({
        message: "Lấy danh sách câu hỏi của bài thi thành công",
        data: result
    });
}


export const putExam1 = async (req, res) => {
    const { id } = req.params;
    const { exam, questions } = req.body;

    const transaction = await db.sequelize.transaction();
    try {
        const exam1 = await db.Exam1.findByPk(id, { transaction });

        if (!exam1) {
            await transaction.rollback();
            return res.status(404).json({ message: "Không tìm thấy bài thi" });
        }

        await exam1.update(exam, { transaction });

        for (const [index, item] of questions.entries()) {
            let question = await db.Question1.findByPk(item.id, { transaction });

            if (!question) {
                question = await db.Question1.create({
                    examId: exam1.id,
                    content: item.content || null,
                    difficulty: item.difficulty || null,
                    solution: item.solution || null,
                    chapter: item.chapter || null,
                    class: item.class || null,
                    order: item.order,
                    imageUrl: item.imageUrl || null,
                    solutionImageUrl: item.solutionImageUrl || null,
                    correctAnswer: item.correctAnswer || null,
                }, { transaction });
            } else {
                // so sánh rồi mới update
                if (question.content === item.content &&
                    question.difficulty === item.difficulty &&
                    question.solution === item.solution &&
                    question.chapter === item.chapter &&
                    question.class === item.class &&
                    question.order === item.order &&
                    question.imageUrl === item.imageUrl &&
                    question.solutionImageUrl === item.solutionImageUrl &&
                    question.correctAnswer === item.correctAnswer) {
                } else {
                    await question.update({
                        content: item.content || null,
                        difficulty: item.difficulty || null,
                        solution: item.solution || null,
                        chapter: item.chapter || null,
                        class: item.class || null,
                        order: item.order,
                        imageUrl: item.imageUrl || null,
                        solutionImageUrl: item.solutionImageUrl || null,
                        correctAnswer: item.correctAnswer || null,
                    }, { transaction });
                }
            }
            if (item.typeOfQuestion === "TN" || item.typeOfQuestion === "DS") {

                for (const statement of item.statement1s) {
                    let stmt = await db.Statement1.findByPk(statement.id, { transaction });

                    if (!stmt) {
                        stmt = await db.Statement1.create({
                            questionId: question.id,
                            content: statement.content || null,
                            isCorrect: statement.isCorrect || false,
                            order: statement.order,
                            imageUrl: statement.imageUrl || null,
                        }, { transaction });
                    } else {
                        await stmt.update({
                            content: statement.content || null,
                            isCorrect: statement.isCorrect || false,
                            order: statement.order,
                            imageUrl: statement.imageUrl || null,
                        }, { transaction });
                    }
                }
            }
        }

        await transaction.commit();
        return res.status(200).json({ message: "Cập nhật bài thi thành công" });

    }
    catch (error) {
        await transaction.rollback();
        console.error("Lỗi khi cập nhật bài thi:", error);
        return res.status(500).json({ message: "Đã xảy ra lỗi khi cập nhật bài thi" });
    }
}

export const commitExam1ToExam = async (req, res) => {
    const { id } = req.params;

    const transaction = await db.sequelize.transaction();
    try {
        const exam1 = await getAllQuestions1ByExamIdService(id);

        if (!exam1) {
            await transaction.rollback();
            return res.status(404).json({ message: "Không tìm thấy bài thi" });
        }

        const { question1s } = exam1;
        const examData = {
            name: exam1.name,
            typeOfExam: exam1.typeOfExam,
            class: exam1.class,
            year: exam1.year,
            public: exam1.public,
        }

        // console.log("examData", examData)

        const newExam = await db.Exam.create(examData, { transaction });

        const codes = await db.AllCode.findAll({
            where: {
                type: 'chapter',
            }
        });

        for (const question of question1s) {
            // console.log("question", question.class)
            const { statement1s, order } = question;
            const chapter = codes.find(code => code.code === question.chapter) ? question.chapter : null

            const questionData = {
                content: question.content,
                typeOfQuestion: question.typeOfQuestion,
                correctAnswer: question.typeOfQuestion === "TLN" ? question.correctAnswer.trim().replace(',', '.') : null,
                solution: question.solution,
                chapter: chapter,
                class: question.class,
                difficulty: question.difficulty,
                imageUrl: question.imageUrl,
                solutionImageUrl: question.solutionImageUrl,
            }
            const newQuestion = await db.Question.create(questionData, { transaction });
            await db.ExamQuestions.create(
                {
                    examId: newExam.id,
                    questionId: newQuestion.id,
                    order: order,
                },
                { transaction }
            );

            for (const statement of statement1s) {
                const statementData = {
                    content: statement.content,
                    isCorrect: statement.isCorrect,
                    questionId: newQuestion.id,
                    order: statement.order,
                    imageUrl: statement.imageUrl,
                }
                await db.Statement.create(statementData, { transaction });
            }
        }

        await exam1.destroy({ transaction });

        await transaction.commit();

        return res.status(200).json({ message: "Commit bài thi thành công" });

    }
    catch (error) {
        await transaction.rollback();
        console.error("Lỗi khi commit bài thi:", error);
        return res.status(500).json({ message: "Đã xảy ra lỗi khi commit bài thi" });
    }
}





