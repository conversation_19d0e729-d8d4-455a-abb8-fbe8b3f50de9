import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";
import { getAllPublicExamAPI } from "../../services/examApi";
import { saveExamForUserAPI } from "../../services/studentExamApi";

export const fetchPublicExams = createAsyncThunk(
    "practice/fetchPublicExams",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, getAllPublicExamAPI, data, (data) => {
        }, false, false);
    }
);

export const saveExamForUser = createAsyncThunk(
    "practice/saveExamForUser",
    async ({ examId }, { dispatch }) => {
        return await apiHandler(dispatch, saveExamForUserAPI, { examId }, () => { }, true, false, false, false);
    }
);

const practiceSlice = createSlice({
    name: "practice",
    initialState: {
        exams: [],
        loading: false,
        typeOfExam: '',
        grade: '12',
        chapter: '',
        sort: 'newest',
        isClassroomExam: null,
        view: 'all',
        year: '',
        pagination: { ...initialPaginationState },
        ...initialFilterState,
    },
    reducers: {
        ...paginationReducers,
        ...filterReducers,
        setSort: (state, action) => {
            state.sort = action.payload;
        },
        setView: (state, action) => {
            state.view = action.payload;
            state.pagination.page = 1;
        },
        setTypeOfExam: (state, action) => {
            state.typeOfExam = action.payload;
            state.pagination.page = 1;
        },
        setGrade: (state, action) => {
            state.grade = action.payload;
            state.pagination.page = 1;
        },
        setChapter: (state, action) => {
            state.chapter = action.payload;
            state.pagination.page = 1;
        },
        setYear: (state, action) => {
            state.year = action.payload;
            state.pagination.page = 1;
        },
        setIsClassroomExam: (state, action) => {
            state.isClassroomExam = action.payload;
            state.pagination.page = 1;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchPublicExams.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchPublicExams.fulfilled, (state, action) => {
                if (action.payload) {
                    state.exams = action.payload.data;
                    state.pagination = action.payload.pagination;
                }
                state.loading = false;
            })
            .addCase(fetchPublicExams.rejected, (state) => {
                state.loading = false;
            })
            .addCase(saveExamForUser.fulfilled, (state, action) => {
                if (action.payload) {
                    const { examId, isSave } = action.payload.data;
                    // Trường hợp 2: Khi exams chứa danh sách exam thông thường
                    if (state.exams && Array.isArray(state.exams)) {
                        state.exams = state.exams.map(
                            (exam) => {
                                if (exam.id === examId) {
                                    if (exam.statuses.length > 0) {
                                        exam.statuses[0].isSave = isSave;
                                    } else {
                                        exam.statuses.push({ isSave });
                                    }
                                }
                                return exam;
                            }
                        );
                    }
                }
            })
    },
});

export const { setCurrentPage, setLimit, setSortOrder, setLoading, setSearch, setSort, setView, setTypeOfExam, setGrade, setChapter, setYear, setIsClassroomExam } = practiceSlice.actions;
export default practiceSlice.reducer;