import UserType from './UserType.js'

const Roles = {
    JustAdmin: [UserType.ADMIN],
    JustMarketing: [UserType.MARKETING, UserType.TEACHER],
    JustTeacher: [UserType.TEACHER],
    JustStudent: [UserType.STUDENT, UserType.TEACHER],
    JustAssistant: [UserType.ASSISTANT, UserType.TEACHER],
    JustHumanResourceManagement: [UserType.HUMANRESOURCEMANAGEMENT, UserType.TEACHER],
    JustClassManagement: [UserType.CLASSMANAGEMENT, UserType.TEACHER],
    All: [
        UserType.ADMIN,
        UserType.MARKETING,
        UserType.TEACHER,
        UserType.STUDENT,
        UserType.ASSISTANT,
        UserType.HUMANRESOURCEMANAGEMENT,
        UserType.CLASSMANAGEMENT
    ],
    AllExceptStudent: [
        UserType.ADMIN,
        UserType.MARKETING,
        UserType.TEACHER,
        UserType.ASSISTANT,
        UserType.HUMANRESOURCEMANAGEMENT,
        UserType.CLASSMANAGEMENT
    ],
    AllExceptMarketingStudent: [
        UserType.ADMIN,
        UserType.TEACHER,
        UserType.ASSISTANT,
        UserType.HUMANRESOURCEMANAGEMENT,
        UserType.CLASSMANAGEMENT
    ]

}

export default Roles