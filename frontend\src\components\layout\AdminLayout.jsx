import React from "react";
import { useSelector } from "react-redux";
import AdminSidebar from "../sidebar/AdminSidebar";

const AdminLayout = ({ children, title, subtitle }) => {
    const { closeSidebar } = useSelector(state => state.sidebar);

    return (
        <div className="flex min-h-screen bg-gray-50">
            {/* Sidebar */}
            <AdminSidebar />
            
            {/* Main Content */}
            <div 
                className={`flex-1 transition-all duration-300 ${
                    closeSidebar ? 'ml-20' : 'ml-64'
                }`}
            >
                {/* Header */}
                {(title || subtitle) && (
                    <div className="bg-white border-b border-gray-200 px-6 py-4">
                        <div className="max-w-7xl mx-auto">
                            {title && (
                                <h1 className="text-2xl font-bold text-gray-900">
                                    {title}
                                </h1>
                            )}
                            {subtitle && (
                                <p className="text-gray-600 mt-1">
                                    {subtitle}
                                </p>
                            )}
                        </div>
                    </div>
                )}
                
                {/* Page Content */}
                <main className="flex-1">
                    {children}
                </main>
            </div>
        </div>
    );
};

export default AdminLayout;
