import multer from 'multer';

// <PERSON>h sách mime type hợp lệ
const allowedImageMimes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/heic',
    'image/heif'
];
const allowedPdfMime = 'application/pdf';

// Hàm kiểm tra file là image hoặc pdf
const mixedFileFilter = (req, file, callback) => {
    if (allowedImageMimes.includes(file.mimetype) || file.mimetype === allowedPdfMime) {
        callback(null, true);
    } else {
        callback(new Error('Only image and PDF files are allowed'), false);
    }
};

// C<PERSON>u hình multer
const upload = multer({
    storage: multer.memoryStorage(),
    fileFilter: mixedFileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
    },
});

// Gộp tất cả field vào middleware duy nhất
const uploadAll = upload.fields([
    { name: 'examImage', maxCount: 1 },
    { name: 'questionImages', maxCount: 20 },
    { name: 'statementImages', maxCount: 20 },
    { name: 'solutionImages', maxCount: 20 },
    { name: 'pdf', maxCount: 1 },
]);

export default uploadAll;
