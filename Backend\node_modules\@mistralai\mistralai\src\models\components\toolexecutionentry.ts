/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BuiltInConnectors,
  BuiltInConnectors$inboundSchema,
  BuiltInConnectors$outboundSchema,
} from "./builtinconnectors.js";

export const ToolExecutionEntryObject = {
  Entry: "entry",
} as const;
export type ToolExecutionEntryObject = ClosedEnum<
  typeof ToolExecutionEntryObject
>;

export const ToolExecutionEntryType = {
  ToolExecution: "tool.execution",
} as const;
export type ToolExecutionEntryType = ClosedEnum<typeof ToolExecutionEntryType>;

export type ToolExecutionEntry = {
  object?: ToolExecutionEntryObject | undefined;
  type?: ToolExecutionEntryType | undefined;
  createdAt?: Date | undefined;
  completedAt?: Date | null | undefined;
  id?: string | undefined;
  name: BuiltInConnectors;
  arguments: string;
  info?: { [k: string]: any } | undefined;
};

/** @internal */
export const ToolExecutionEntryObject$inboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionEntryObject
> = z.nativeEnum(ToolExecutionEntryObject);

/** @internal */
export const ToolExecutionEntryObject$outboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionEntryObject
> = ToolExecutionEntryObject$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionEntryObject$ {
  /** @deprecated use `ToolExecutionEntryObject$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionEntryObject$inboundSchema;
  /** @deprecated use `ToolExecutionEntryObject$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionEntryObject$outboundSchema;
}

/** @internal */
export const ToolExecutionEntryType$inboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionEntryType
> = z.nativeEnum(ToolExecutionEntryType);

/** @internal */
export const ToolExecutionEntryType$outboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionEntryType
> = ToolExecutionEntryType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionEntryType$ {
  /** @deprecated use `ToolExecutionEntryType$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionEntryType$inboundSchema;
  /** @deprecated use `ToolExecutionEntryType$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionEntryType$outboundSchema;
}

/** @internal */
export const ToolExecutionEntry$inboundSchema: z.ZodType<
  ToolExecutionEntry,
  z.ZodTypeDef,
  unknown
> = z.object({
  object: ToolExecutionEntryObject$inboundSchema.default("entry"),
  type: ToolExecutionEntryType$inboundSchema.default("tool.execution"),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  completed_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  id: z.string().optional(),
  name: BuiltInConnectors$inboundSchema,
  arguments: z.string(),
  info: z.record(z.any()).optional(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "completed_at": "completedAt",
  });
});

/** @internal */
export type ToolExecutionEntry$Outbound = {
  object: string;
  type: string;
  created_at?: string | undefined;
  completed_at?: string | null | undefined;
  id?: string | undefined;
  name: string;
  arguments: string;
  info?: { [k: string]: any } | undefined;
};

/** @internal */
export const ToolExecutionEntry$outboundSchema: z.ZodType<
  ToolExecutionEntry$Outbound,
  z.ZodTypeDef,
  ToolExecutionEntry
> = z.object({
  object: ToolExecutionEntryObject$outboundSchema.default("entry"),
  type: ToolExecutionEntryType$outboundSchema.default("tool.execution"),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  id: z.string().optional(),
  name: BuiltInConnectors$outboundSchema,
  arguments: z.string(),
  info: z.record(z.any()).optional(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    completedAt: "completed_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionEntry$ {
  /** @deprecated use `ToolExecutionEntry$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionEntry$inboundSchema;
  /** @deprecated use `ToolExecutionEntry$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionEntry$outboundSchema;
  /** @deprecated use `ToolExecutionEntry$Outbound` instead. */
  export type Outbound = ToolExecutionEntry$Outbound;
}

export function toolExecutionEntryToJSON(
  toolExecutionEntry: ToolExecutionEntry,
): string {
  return JSON.stringify(
    ToolExecutionEntry$outboundSchema.parse(toolExecutionEntry),
  );
}

export function toolExecutionEntryFromJSON(
  jsonString: string,
): SafeParseResult<ToolExecutionEntry, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolExecutionEntry$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolExecutionEntry' from JSON`,
  );
}
