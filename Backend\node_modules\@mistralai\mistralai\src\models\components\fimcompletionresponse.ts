/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ChatCompletionChoice,
  ChatCompletionChoice$inboundSchema,
  ChatCompletionChoice$Outbound,
  ChatCompletionChoice$outboundSchema,
} from "./chatcompletionchoice.js";
import {
  UsageInfo,
  UsageInfo$inboundSchema,
  UsageInfo$Outbound,
  UsageInfo$outboundSchema,
} from "./usageinfo.js";

export type FIMCompletionResponse = {
  id: string;
  object: string;
  model: string;
  usage: UsageInfo;
  created: number;
  choices: Array<ChatCompletionChoice>;
};

/** @internal */
export const FIMCompletionResponse$inboundSchema: z.ZodType<
  FIMCompletionResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  object: z.string(),
  model: z.string(),
  usage: UsageInfo$inboundSchema,
  created: z.number().int(),
  choices: z.array(ChatCompletionChoice$inboundSchema),
});

/** @internal */
export type FIMCompletionResponse$Outbound = {
  id: string;
  object: string;
  model: string;
  usage: UsageInfo$Outbound;
  created: number;
  choices: Array<ChatCompletionChoice$Outbound>;
};

/** @internal */
export const FIMCompletionResponse$outboundSchema: z.ZodType<
  FIMCompletionResponse$Outbound,
  z.ZodTypeDef,
  FIMCompletionResponse
> = z.object({
  id: z.string(),
  object: z.string(),
  model: z.string(),
  usage: UsageInfo$outboundSchema,
  created: z.number().int(),
  choices: z.array(ChatCompletionChoice$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FIMCompletionResponse$ {
  /** @deprecated use `FIMCompletionResponse$inboundSchema` instead. */
  export const inboundSchema = FIMCompletionResponse$inboundSchema;
  /** @deprecated use `FIMCompletionResponse$outboundSchema` instead. */
  export const outboundSchema = FIMCompletionResponse$outboundSchema;
  /** @deprecated use `FIMCompletionResponse$Outbound` instead. */
  export type Outbound = FIMCompletionResponse$Outbound;
}

export function fimCompletionResponseToJSON(
  fimCompletionResponse: FIMCompletionResponse,
): string {
  return JSON.stringify(
    FIMCompletionResponse$outboundSchema.parse(fimCompletionResponse),
  );
}

export function fimCompletionResponseFromJSON(
  jsonString: string,
): SafeParseResult<FIMCompletionResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FIMCompletionResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FIMCompletionResponse' from JSON`,
  );
}
