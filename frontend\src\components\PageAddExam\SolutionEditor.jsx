import React, { useState, useRef, useEffect } from "react";
import { ImagePlus, Upload, Sparkles } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { fixTextAndLatex } from "../../features/ai/aiSlice";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";
import TextArea from "../input/TextArea";

const SolutionEditor = ({ solution, onSolutionChange, preview = true }) => {
    const [isDraggingOver, setIsDraggingOver] = useState(false);
    const textareaRef = useRef(null);
    const dispatch = useDispatch();
    const { fixTextResult } = useSelector((state) => state.ai);
    const [loading, setLoading] = useState(false);
    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOver(false);

        const draggedImage = e.dataTransfer.getData("text/plain");
        if (!draggedImage || !onSolutionChange) return;

        // Lấy vị trí cursor từ textarea
        let insertPosition = 0;

        if (textareaRef.current) {
            // Lấy vị trí cursor hiện tại trong textarea
            const cursorPos = textareaRef.current.selectionStart;
            insertPosition = cursorPos !== undefined ? cursorPos : (solution ? solution.length : 0);
        } else {
            // Fallback: chèn vào cuối
            insertPosition = solution ? solution.length : 0;
        }

        // Tạo markdown image syntax
        const imageMarkdown = `\n![Ảnh](${draggedImage})\n`;

        // Chèn vào vị trí cursor
        const newSolution = solution
            ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition)
            : imageMarkdown;

        onSolutionChange(newSolution);

        // Đặt lại cursor sau khi chèn ảnh
        setTimeout(() => {
            if (textareaRef.current) {
                const newCursorPos = insertPosition + imageMarkdown.length;
                textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
                textareaRef.current.focus();
            }
        }, 0);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.dataTransfer.types.includes('text/plain')) {
            setIsDraggingOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!e.currentTarget.contains(e.relatedTarget)) {
            setIsDraggingOver(false);
            // setDragPosition(null);
        }
    };

    const handleTextareaChange = (e) => {
        onSolutionChange(e.target.value);
    };

    const handleAIFix = async () => {
        if (!solution || !solution.trim()) {
            alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');
            return;
        }

        try {
            setLoading(true);
            const result = await dispatch(fixTextAndLatex(solution)).unwrap();
            setLoading(false);
            if (result.data.hasChanges) {
                onSolutionChange(result.data.fixedText);
            } else {
                alert('Không tìm thấy lỗi nào cần sửa');
            }
        } catch (error) {
            console.error('Error fixing text:', error);
            alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');
        }
    };


    return (
        <div className="mt-2 space-y-3">
            <div className="flex items-center justify-between">
                <div className="text-sm font-semibold text-green-700">Lời giải:</div>
                <button
                    onClick={handleAIFix}
                    disabled={loading || !solution?.trim()}
                    className="flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="Sử dụng AI để sửa chính tả và ký hiệu LaTeX"
                >
                    <Sparkles className="w-3 h-3" />
                    {loading ? 'Đang sửa...' : 'AI Fix'}
                </button>
            </div>
            {/* Editor với drop zone */}
            <div
                className={`relative border rounded-lg transition-all duration-200 ${isDraggingOver
                    ? "border-2 border-dashed border-blue-400 bg-blue-50"
                    : "border-gray-300 hover:border-blue-300"
                    }`}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                {/* Textarea */}
                <TextArea
                    ref={textareaRef}
                    value={solution}
                    onChange={(e) => onSolutionChange(e.target.value)}
                    placeholder="Nhập lời giải (hỗ trợ Markdown và LaTeX)..."
                    debounceDelay={500}
                />

                {/* Hint */}
                {!solution && !isDraggingOver && (
                    <div className="absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none">
                        <ImagePlus className="w-4 h-4" />
                        <span>Kéo ảnh vào để chèn</span>
                    </div>
                )}
            </div>

            {/* Preview */}
            {solution && preview && (
                <div className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <div className="text-xs text-gray-500 mb-2">Xem trước:</div>
                    <MarkdownPreviewWithMath content={solution} />
                </div>
            )}
        </div>
    );
};

export default SolutionEditor;
