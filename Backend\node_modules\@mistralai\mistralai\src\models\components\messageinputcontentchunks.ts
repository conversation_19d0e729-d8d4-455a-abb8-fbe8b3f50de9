/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DocumentURLChunk,
  DocumentURLChunk$inboundSchema,
  DocumentURLChunk$Outbound,
  DocumentURLChunk$outboundSchema,
} from "./documenturlchunk.js";
import {
  ImageURLChunk,
  ImageURLChunk$inboundSchema,
  ImageURLChunk$Outbound,
  ImageURLChunk$outboundSchema,
} from "./imageurlchunk.js";
import {
  TextChunk,
  TextChunk$inboundSchema,
  TextChunk$Outbound,
  TextChunk$outboundSchema,
} from "./textchunk.js";
import {
  ToolFileChunk,
  ToolFileChunk$inboundSchema,
  ToolFileChunk$Outbound,
  ToolFileChunk$outboundSchema,
} from "./toolfilechunk.js";

export type MessageInputContentChunks =
  | TextChunk
  | ImageURLChunk
  | DocumentURLChunk
  | ToolFileChunk;

/** @internal */
export const MessageInputContentChunks$inboundSchema: z.ZodType<
  MessageInputContentChunks,
  z.ZodTypeDef,
  unknown
> = z.union([
  TextChunk$inboundSchema,
  ImageURLChunk$inboundSchema,
  DocumentURLChunk$inboundSchema,
  ToolFileChunk$inboundSchema,
]);

/** @internal */
export type MessageInputContentChunks$Outbound =
  | TextChunk$Outbound
  | ImageURLChunk$Outbound
  | DocumentURLChunk$Outbound
  | ToolFileChunk$Outbound;

/** @internal */
export const MessageInputContentChunks$outboundSchema: z.ZodType<
  MessageInputContentChunks$Outbound,
  z.ZodTypeDef,
  MessageInputContentChunks
> = z.union([
  TextChunk$outboundSchema,
  ImageURLChunk$outboundSchema,
  DocumentURLChunk$outboundSchema,
  ToolFileChunk$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageInputContentChunks$ {
  /** @deprecated use `MessageInputContentChunks$inboundSchema` instead. */
  export const inboundSchema = MessageInputContentChunks$inboundSchema;
  /** @deprecated use `MessageInputContentChunks$outboundSchema` instead. */
  export const outboundSchema = MessageInputContentChunks$outboundSchema;
  /** @deprecated use `MessageInputContentChunks$Outbound` instead. */
  export type Outbound = MessageInputContentChunks$Outbound;
}

export function messageInputContentChunksToJSON(
  messageInputContentChunks: MessageInputContentChunks,
): string {
  return JSON.stringify(
    MessageInputContentChunks$outboundSchema.parse(messageInputContentChunks),
  );
}

export function messageInputContentChunksFromJSON(
  jsonString: string,
): SafeParseResult<MessageInputContentChunks, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MessageInputContentChunks$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MessageInputContentChunks' from JSON`,
  );
}
