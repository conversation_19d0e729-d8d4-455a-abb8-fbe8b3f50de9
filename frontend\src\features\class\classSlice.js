import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as ClassApi from "../../services/classApi";
// import { setCurrentPage, setTotalItems, setTotalPages } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
// import { setLimit } from "../user/userSlice";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";

export const fetchClasses = createAsyncThunk(
    "classes/fetchClasses",
    async ({ search, page, pageSize, sortOrder }, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getAllClassesAPI, { search, page, pageSize, sortOrder }, (data) => {
            // dispatch(setCurrentPage(data.currentPage));
            // dispatch(setTotalPages(data.totalPages));
            // dispatch(setTotalItems(data.totalItems));
        }, true, false, true);
    }
);

export const fetchClassesPublic = createAsyncThunk(
    "classes/fetchClassesPublic",
    async (_, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getClassPublicAPI, null, () => { }, false, false, false, false);
    }
);

export const findClasses = createAsyncThunk(
    "classes/findClasses",
    async (search, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.findClassesAPI, search, () => { }, false, false, false, false);
    }
);

export const findClassByCode = createAsyncThunk(
    "classes/findClassByCode",
    async (classCode, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.findClassByCodeAPI, classCode, () => { }, false, false);
    }
);

export const fetchClassById = createAsyncThunk(
    "classes/fetchClassById",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getClassByIdAPI, id, () => { }, true, false);
    }
);

export const fetchClassesByUser = createAsyncThunk(
    "classes/fetchClassesByUser",
    async (_, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getClassByUserAPI, null, (data) => {
        }, false, false);
    }
);

export const fetchClassesByUserId = createAsyncThunk(
    "classes/fetchClassesByUserId",
    async (userId, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getClassesByUserIdAPI, userId, () => { }, false, false);
    }
);

export const fetchLessonLearningItemInClass = createAsyncThunk(
    "classes/fetchLessonLearningItemInClass",
    async (classCode, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getLessonLearningItemInClassAPI, classCode, () => { }, false, false);
    }
);

export const fetchClassesOverview = createAsyncThunk(
    "classes/fetchClassesOverview",
    async (_, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getClassOverviewAPI, null, () => { }, false, false);
    }
);

export const joinClass = createAsyncThunk(
    "classes/joinClass",
    async (classCode, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.joinClassAPI, classCode, () => { }, true, false);
    }
);

export const getDataForLearning = createAsyncThunk(
    "classes/getDataForLearning",
    async (classCode, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getDataForLearningAPI, classCode, () => { }, false, false);
    }
);

export const getFullLessonLearningItemByClassId = createAsyncThunk(
    "classes/getFullLessonLearningItemByClassId",
    async ({ classId }, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.getFullLessonLearningItemByClassIdAPI, { classId }, () => { }, true, false);
    }
);

export const putSlideImagesForClass = createAsyncThunk(
    "classes/putSlideImagesForClass",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.putSlideImagesForClassAPI, data, () => { }, true, false);
    }
);

export const postClass = createAsyncThunk(
    "classes/postClass",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.postClassAPI, data, () => { }, true, false);
    }
);

export const putClass = createAsyncThunk(
    "classes/putClass",
    async ({ data, id }, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.putClassAPI, { data, id }, () => { }, true, false);
    }
);

export const acceptStudentClass = createAsyncThunk(
    "classes/acceptStudentClass",
    async ({ classId, studentId }, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.acceptStudentClassAPI, { classId, studentId }, () => { }, true, false);
    }
);

export const addStudentToClass = createAsyncThunk(
    "classes/addStudentToClass",
    async ({ classId, studentId }, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.addStudentToClassAPI, { classId, studentId }, () => { }, true, false);
    }
);

export const kickStudentFromClass = createAsyncThunk(
    "classes/kickStudentFromClass",
    async ({ classId, studentId }, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.kickStudentFromClassAPI, { classId, studentId }, () => { }, true, false);
    }
);

export const uploadLearningItemPdf = createAsyncThunk(
    "classes/uploadLearningItemPdf",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.uploadLearningItemPdfAPI, data, () => { }, true, false);
    }
);

export const putLearningItem = createAsyncThunk(
    "classes/putLearningItem",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.putLearningItemAPI, data, () => { }, true, false);
    }
);

export const postLesson = createAsyncThunk(
    "classes/postLesson",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.postLessonAPI, data, () => { }, true, false);
    }
);

export const deleteLesson = createAsyncThunk(
    "classes/deleteLesson",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.deleteLessonAPI, data, () => { }, true, false);
    }
);

export const putLesson = createAsyncThunk(
    "classes/putLesson",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.putLessonAPI, data, () => { }, true, false);
    }
);

export const deleteLearningItem = createAsyncThunk(
    "classes/deleteLearningItem",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.deleteLearningItemAPI, data, () => { }, true, false);
    }
);

export const markLearningItem = createAsyncThunk(
    "classes/markLearningItem",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.markLearningItemAPI, data, () => { }, false, false);
    }
);

export const postLearningItem = createAsyncThunk(
    "classes/postLearningItem",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.postLearningItemAPI, data, () => { }, true, false);
    }
);

export const deleteClass = createAsyncThunk(
    "classes/deleteClass",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.deleteClassAPI, data, () => { }, true, false);
    }
);

export const fetchLessonByClassId = createAsyncThunk(
    "classes/fetchLessonByClassId",
    async (classId, { dispatch }) => {
        return await apiHandler(dispatch, ClassApi.fetchLessonByClassIdAPI, classId, () => { }, true, false);
    }
);

const classSlice = createSlice({
    name: "classes",
    initialState: {
        openJoinClassModal: false,
        classes: [],
        classesOverview: [],
        classesSearch: [],
        classDetail: null,
        learningItems: [],
        loadingClass: false,
        loadingLearningItem: false,
        lessons: [],
        loadingLessons: false,
        lessonClass: null,
        loadingPut: false,
        loadingAdd: false,
        loadingFind: false,
        isFound: false,
        pagination: { ...initialPaginationState },
        ...initialFilterState,
    },
    reducers: {
        setClass: (state, action) => {
            state.classDetail = action.payload;
        },
        setIsFound: (state, action) => {
            state.isFound = action.payload;
        },
        ...paginationReducers,
        ...filterReducers,
        setOpenJoinClassModal: (state, action) => {
            state.openJoinClassModal = action.payload;
        },
        resetClassDetail: (state) => {
            state.classDetail = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // Fetch classes
            .addCase(fetchClasses.pending, (state) => {
                state.classes = [];
                state.loading = true;
            })
            .addCase(fetchClasses.fulfilled, (state, action) => {
                // console.log("fetchClasses action.payload", action.payload.data);
                if (action.payload) {
                    state.classes = action.payload.data.data || [];
                    state.pagination = action.payload.data.pagination || { ...initialPaginationState };
                }
                state.loading = false;
            })
            .addCase(fetchClasses.rejected, (state) => {
                state.loading = false;
            })
            .addCase(fetchClassById.pending, (state) => {
                state.classDetail = null;
            })
            .addCase(fetchClassById.fulfilled, (state, action) => {
                state.classDetail = action.payload.data;
            })
            .addCase(fetchClassesByUser.pending, (state) => {
                state.classes = [];
                state.loading = true;
            })
            .addCase(fetchClassesByUser.fulfilled, (state, action) => {
                state.classes = action.payload.data;
                state.pagination.page = 1;
                state.pagination.pageSize = 4;
                state.loading = false;
            })
            .addCase(fetchClassesByUser.rejected, (state) => {
                state.loading = false;
            })
            .addCase(fetchClassesByUserId.pending, (state) => {
                state.classes = [];
            })
            .addCase(fetchClassesByUserId.fulfilled, (state, action) => {
                state.classes = action.payload.data;
            })
            .addCase(fetchClassesOverview.pending, (state) => {
                state.classesOverview = [];
                state.loadingClass = true;
            })
            .addCase(fetchClassesOverview.fulfilled, (state, action) => {
                state.classesOverview = action.payload.data;
                state.loadingClass = false;
            })
            .addCase(fetchClassesOverview.rejected, (state) => {
                state.loadingClass = false;
            })
            .addCase(fetchLessonLearningItemInClass.pending, (state) => {
                state.loading = true;
                state.classDetail = null;
            })
            .addCase(fetchLessonLearningItemInClass.fulfilled, (state, action) => {
                state.loading = false;
                state.classDetail = action.payload.data;
            })
            .addCase(fetchLessonLearningItemInClass.rejected, (state) => {
                state.loading = false;
                state.classDetail = null;
            })
            .addCase(joinClass.fulfilled, (state) => {
                if (state.classDetail) {
                    state.classDetail.userStatus = 'WS';
                }
            })
            .addCase(getDataForLearning.pending, (state) => {
                state.loadingClass = true;
                state.classDetail = null;
            })
            .addCase(getDataForLearning.fulfilled, (state, action) => {
                state.loadingClass = false;
                state.classDetail = action.payload.data;
            })
            .addCase(getDataForLearning.rejected, (state) => {
                state.loadingClass = false;
                state.classDetail = null;
            })
            .addCase(getFullLessonLearningItemByClassId.pending, (state) => {
                state.lessonClass = null;
            })
            .addCase(getFullLessonLearningItemByClassId.fulfilled, (state, action) => {
                state.lessonClass = action.payload.data;
            })
            .addCase(markLearningItem.fulfilled, (state, action) => {
                const { learningItemId, isDone, studyTime } = action.payload.data;

                for (const lesson of state.classDetail?.lessons || []) {
                    const learningItem = lesson.learningItems?.find(item => item.id === learningItemId);
                    if (learningItem) {
                        learningItem.studyStatuses[0].isDone = isDone;
                        learningItem.studyStatuses[0].studyTime = studyTime;
                        break;
                    }
                }
            })
            .addCase(uploadLearningItemPdf.pending, (state) => {
                state.loadingPut = true
            })
            .addCase(uploadLearningItemPdf.fulfilled, (state, action) => {
                const updatedItem = action.payload;

                // Duyệt qua tất cả các bài học trong lessonClass
                state.lessonClass.lessons = state.lessonClass.lessons.map((lesson) => {
                    return {
                        ...lesson,
                        learningItems: lesson.learningItems.map((item) =>
                            item.id === updatedItem.id ? updatedItem : item
                        ),
                    };
                });

                state.loadingPut = false;
            })
            .addCase(uploadLearningItemPdf.rejected, (state) => {
                state.loadingPut = false
            })
            .addCase(putLearningItem.pending, (state) => {
                state.loadingPut = true
            })
            .addCase(putLearningItem.fulfilled, (state, action) => {
                const updatedItem = action.payload;
                // Duyệt qua tất cả các bài học trong lessonClass
                state.lessonClass.lessons = state.lessonClass.lessons.map((lesson) => {
                    return {
                        ...lesson,
                        learningItems: lesson.learningItems.map((item) =>
                            item.id === updatedItem.id ? updatedItem : item
                        ),
                    };
                });
                state.loadingPut = false;
            })
            .addCase(putLearningItem.rejected, (state) => {
                state.loadingPut = false
            })
            .addCase(addStudentToClass.pending, (state) => {
                state.loadingAdd = true;
            })
            .addCase(addStudentToClass.fulfilled, (state, action) => {
                state.loadingAdd = false;
            })
            .addCase(addStudentToClass.rejected, (state) => {
                state.loadingAdd = false;
            })

            .addCase(fetchClassesPublic.pending, (state) => {
                state.classes = [];
            })
            .addCase(fetchClassesPublic.fulfilled, (state, action) => {
                state.classes = action.payload.data;
            })
            .addCase(findClasses.pending, (state) => {
                state.classesSearch = [];
            })
            .addCase(findClasses.fulfilled, (state, action) => {
                if (action.payload) {
                    state.classesSearch = action.payload.data;
                }
            })
            .addCase(findClassByCode.pending, (state) => {
                state.isFound = null;
                state.loadingFind = true;
            })
            .addCase(findClassByCode.fulfilled, (state, action) => {
                if (action.payload) {
                    state.isFound = action.payload.data ? true : false;
                } else {
                    state.isFound = false;
                }
                state.loadingFind = false;
            })
            .addCase(findClassByCode.rejected, (state) => {
                state.isFound = false;
                state.loadingFind = false;
            })

            .addCase(fetchLessonByClassId.pending, (state) => {
                state.lessons = [];
                state.loadingLessons = true;
            })
            .addCase(fetchLessonByClassId.fulfilled, (state, action) => {
                state.lessons = action.payload.data;
                state.loadingLessons = false;
            })
            .addCase(fetchLessonByClassId.rejected, (state) => {
                state.loadingLessons = false;
            })
            .addCase(deleteLesson.pending, (state) => {
                state.loadingPut = true
            })
            .addCase(deleteLesson.fulfilled, (state, action) => {
                // state.lessons = state.lessons.filter(lesson => lesson.id !== action.payload.data);
                state.loadingPut = false;
            })
            .addCase(deleteLesson.rejected, (state) => {
                state.loadingPut = false;
            })
            .addCase(deleteLearningItem.pending, (state) => {
                state.loadingPut = true
            })
            .addCase(deleteLearningItem.fulfilled, (state, action) => {
                // state.lessons = state.lessons.map(lesson => {
                //     lesson.learningItems = lesson.learningItems.filter(item => item.id !== action.payload.data);
                //     return lesson;
                // });
                state.loadingPut = false;
            })
            .addCase(deleteLearningItem.rejected, (state) => {
                state.loadingPut = false;
            })

    },
});

export const {
    setClass,
    setCurrentPage,
    setLimit,
    setSortOrder,
    setLoading,
    setSearch,
    setIsFound,
    setIsSearch,
    setOpenJoinClassModal,
    resetClassDetail,
} = classSlice.actions;
export default classSlice.reducer;