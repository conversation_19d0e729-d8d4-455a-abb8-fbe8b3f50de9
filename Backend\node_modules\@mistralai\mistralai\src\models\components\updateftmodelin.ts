/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type UpdateFTModelIn = {
  name?: string | null | undefined;
  description?: string | null | undefined;
};

/** @internal */
export const UpdateFTModelIn$inboundSchema: z.ZodType<
  UpdateFTModelIn,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
});

/** @internal */
export type UpdateFTModelIn$Outbound = {
  name?: string | null | undefined;
  description?: string | null | undefined;
};

/** @internal */
export const UpdateFTModelIn$outboundSchema: z.ZodType<
  UpdateFTModelIn$Outbound,
  z.ZodTypeDef,
  UpdateFTModelIn
> = z.object({
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UpdateFTModelIn$ {
  /** @deprecated use `UpdateFTModelIn$inboundSchema` instead. */
  export const inboundSchema = UpdateFTModelIn$inboundSchema;
  /** @deprecated use `UpdateFTModelIn$outboundSchema` instead. */
  export const outboundSchema = UpdateFTModelIn$outboundSchema;
  /** @deprecated use `UpdateFTModelIn$Outbound` instead. */
  export type Outbound = UpdateFTModelIn$Outbound;
}

export function updateFTModelInToJSON(
  updateFTModelIn: UpdateFTModelIn,
): string {
  return JSON.stringify(UpdateFTModelIn$outboundSchema.parse(updateFTModelIn));
}

export function updateFTModelInFromJSON(
  jsonString: string,
): SafeParseResult<UpdateFTModelIn, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UpdateFTModelIn$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UpdateFTModelIn' from JSON`,
  );
}
