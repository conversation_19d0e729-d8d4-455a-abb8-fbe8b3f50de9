import HeaderDoExamPage from "src/components/header/HeaderDoExamPage";
import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import { setErrorMessage, setSuccessMessage } from "src/features/state/stateApiSlice";
import { useNavigate } from "react-router-dom";
import ExamRegulationModal from "src/components/modal/ExamRegulationModal";
import ExamSidebar from "src/components/PageDoExam/ExamSideBar";
import ExamContent from "src/components/PageDoExam/ExamContent";
import { requestFullscreen, exitFullscreen } from "src/utils/fullscreenUtils";
import {
    setRemainingTime,
    summitExam,
    getRemainingTime,
    leaveExam,
    joinExam,
    reset,
    fetchPublicExamById,
    fetchPublicQuestionsByExamId,
    fetchAnswersByAttempt,
    setIsTimeUp,
    setShowSidebar,
} from "src/features/doExam/doExamSlice";
import { setShowModalSubmit } from "src/features/doExam/doExamSlice";
import ModalSubmitExam from "src/components/PageDoExam/ModalSubmitExam";
import OutsideClickWrapper from "src/components/common/OutsideClickWrapper";
import { ClipboardList } from "lucide-react";
import { formatTime } from "src/utils/formatters";

const DoExamPage = () => {
    const { examId } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { exam, loadingExam, darkMode, attemptId, startTime, isTimeUp, remainingTime, showModalSubmit, showSidebar } = useSelector((state) => state.doExam);
    const { user } = useSelector((state) => state.auth);
    const [isAgree, setIsAgree] = useState(false);
    const [resetDone, setResetDone] = useState(false);
    const [timeWarningShown, setTimeWarningShown] = useState({
        fiveMinutes: false,
        oneMinute: false
    });
    const [isTimeBlinking, setIsTimeBlinking] = useState(false);
    const remainingTimeRef = useRef(remainingTime);
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const questionRefs = useRef({});

    useEffect(() => {
        if (!resetDone) {
            dispatch(reset());
            setResetDone(true);
        }
    }, [dispatch, resetDone]);

    useEffect(() => {
        if (examId && resetDone) {
            dispatch(fetchPublicExamById(examId));
        }
    }, [dispatch, examId, resetDone]);

    useEffect(() => {
        if (exam?.acceptDoExam === false) {
            navigate(`/practice/exam/${examId}`)
        }
    }, [exam, navigate, examId]);

    useEffect(() => {
        if (attemptId && resetDone) {
            dispatch(getRemainingTime({ examId, attemptId }))
                .then((result) => {
                    if (result.payload?.data?.remainingTime !== undefined) {
                        dispatch(setRemainingTime(result.payload.data.remainingTime));
                    }
                })
                .catch((error) => {
                    console.error("Lỗi khi lấy thời gian từ server:", error);
                });
        }
    }, [attemptId, examId, dispatch, resetDone]);

    useEffect(() => {
        if (examId && attemptId && resetDone) {
            dispatch(fetchAnswersByAttempt(attemptId));
        }
    }, [dispatch, examId, attemptId, resetDone]);

    useEffect(() => {
        if (resetDone && attemptId && isTimeUp) {
            dispatch(summitExam(attemptId));
        }
    }, [dispatch, attemptId, resetDone, isTimeUp]);

    useEffect(() => {
        if (!exam?.testDuration || remainingTime === null || !isAgree || isTimeUp || !resetDone) return;

        // Kiểm tra và hiển thị cảnh báo thời gian
        const checkTimeWarnings = (time) => {
            // Cảnh báo khi còn 5 phút
            if (time === 300 && !timeWarningShown.fiveMinutes) {
                setTimeWarningShown(prev => ({ ...prev, fiveMinutes: true }));
                setIsTimeBlinking(true);
                dispatch(setErrorMessage("Còn 5 phút nữa là hết thời gian làm bài!"));

                // Tắt hiệu ứng nhấp nháy sau 10 giây
                setTimeout(() => {
                    setIsTimeBlinking(false);
                }, 10000);
            }

            // Cảnh báo khi còn 1 phút
            if (time === 60 && !timeWarningShown.oneMinute) {
                setTimeWarningShown(prev => ({ ...prev, oneMinute: true }));
                setIsTimeBlinking(true);
                dispatch(setErrorMessage("Còn 1 phút nữa là hết thời gian làm bài!"));

                // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian
            }
        };

        // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API
        const syncTimeInterval = setInterval(() => {
            if (attemptId) {
                dispatch(getRemainingTime({ examId, attemptId }));
            }
        }, 30000); // Đồng bộ thời gian mỗi 30 giây

        const interval = setInterval(() => {
            // Get current remaining time from ref (always up-to-date)
            const currentTime = remainingTime;

            if (currentTime <= 1) { // dùng <=1 để đảm bảo không bị âm
                clearInterval(interval);
                clearInterval(syncTimeInterval);
                // Đánh dấu là đã hết thời gian
                dispatch(setIsTimeUp(true));
                setIsTimeBlinking(false);
                // Thử nộp bài
                handleSubmit();
                dispatch(setRemainingTime(0));
                return;
            }

            // Kiểm tra cảnh báo thời gian
            checkTimeWarnings(currentTime);

            // Dispatch new value (not function)
            const newTime = currentTime - 1;
            dispatch(setRemainingTime(newTime));
        }, 1000);

        return () => {
            clearInterval(interval);
            clearInterval(syncTimeInterval);
        };
    }, [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, isTimeUp, resetDone]);// Chỉ phụ thuộc vào các giá trị cần thiết

    const handleSubmit = async () => {
        try {
            if (loadingSubmit) return;
            setLoadingSubmit(true);

            // Sử dụng API thay vì socket để nộp bài
            const result = await dispatch(summitExam(attemptId)).unwrap();
            // console.log("Nộp bài thành công:", result);

            // Xử lý khi nộp bài thành công
            dispatch(setSuccessMessage("Nộp bài thành công!"));

            // Thoát fullscreen mà không bắt lỗi
            try {
                exitFullscreen();
            } catch (err) {
                // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính
                console.warn("Không thể thoát fullscreen khi nộp bài:", err);
            }

            if (!attemptId) {
                console.error("Không có attemptId khi navigate!");
                return;
            }

            // Log để debug
            // console.log("Current exam state:", currentExam);
            // console.log("Attempt ID:", safeAttemptId);
            setLoadingSubmit(false);
            if (!exam || !exam.seeCorrectAnswer) {
                // console.log("Chuyển về trang danh sách do:", {
                //     examNull: !currentExam,
                //     cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer
                // });
                navigate(`/practice/exam/${examId}`);
                return;
            }

            navigate(`/practice/exam/attempt/${attemptId}/score`);
        } catch (error) {
            console.error("Lỗi khi nộp bài:", error);
            setLoadingSubmit(false);
            dispatch(setErrorMessage("Lỗi khi nộp bài. Vui lòng thử lại."));

            // // Thử nộp lại sau 3 giây nếu lỗi xảy ra
            // setTimeout(() => {
            //     if (!loadingSubmit && attemptRef.current) {
            //         // console.log("Thử nộp bài lại sau lỗi...");
            //         handleAutoSubmit();
            //     }
            // }, 5000);
        }
    };

    const handleFullScreen = async () => {
        try {
            // Sử dụng joinExam action thay vì fetch
            await dispatch(joinExam(examId)).unwrap();

            // Xử lý khi join exam thành công
            setIsAgree(true);

            if (examId) {
                dispatch(fetchPublicQuestionsByExamId(examId));
            }

            if (!exam?.isCheatingCheckEnabled) {
                return;
            }

            try {
                const success = await requestFullscreen();
                if (!success) {
                    console.warn("Không thể vào fullscreen, nhưng vẫn cho phép làm bài");
                }
            } catch (err) {
                console.error("❌ Lỗi khi bật fullscreen:", err);
                alert("Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.");
            }

        } catch (error) {
            console.error("Lỗi khi tham gia bài thi:", error);
            dispatch(setErrorMessage("Lỗi: " + error.message));
            navigate(`/practice/exam/${examId}`);
        }
    };

    return (
        <div className={`flex flex-col h-full transition-colors duration-300 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'
            }`}>
            <HeaderDoExamPage nameExam={exam?.name} />
            <div className="flex flex-col h-full lg:flex-row flex-1 w-full p-4 pb-4 mt-[64px]">
                {(!exam && !loadingExam) ? (
                    <div className="flex flex-col items-center justify-center h-96 text-center">
                        <ClipboardList size={48} className={`mb-4 transition-colors duration-200 ${darkMode ? 'text-gray-500' : 'text-gray-400'
                            }`} />
                        <h3 className={`text-lg font-medium mb-2 transition-colors duration-200 ${darkMode ? 'text-white' : 'text-gray-900'
                            }`}>Không tìm thấy đề thi</h3>
                    </div>
                ) : (
                    <div className="flex items-center justify-center">
                        <ExamRegulationModal
                            onClose={() => {
                                // Sử dụng API để leave exam nếu có attemptId
                                if (attemptId) {
                                    dispatch(leaveExam({ examId, attemptId }));
                                }
                                navigate(`/practice/exam/${examId}`);
                            }}
                            isOpen={!isAgree}
                            onStartExam={handleFullScreen}
                        />
                    </div>
                )}
                {isAgree && (
                    <div className="flex flex-1 h-full gap-4">
                        <ExamContent questionRefs={questionRefs} isTimeBlinking={isTimeBlinking} isAgree={isAgree} />
                        <div className={`
                            sticky top-[80px] h-full sm:flex hidden flex-col rounded p-4 border-l shadow w-80
                        ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-200 text-gray-900'}
                        `}>
                            <ExamSidebar questionRefs={questionRefs} />
                        </div>
                        {showSidebar && (
                            <div className="fixed top-[64px] inset-0 bg-black bg-opacity-40 sm:hidden flex justify-end z-50">
                                <OutsideClickWrapper
                                    tabIndex={0}
                                    ignoreOutsideClick="sideBarRef"
                                    onClickOutside={() => dispatch(setShowSidebar(false))}
                                    className={` h-full flex flex-col p-4 border-l shadow w-80 max-h-[calc(100vh - 64px)] overflow-y-auto
                                    ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-200 text-gray-900'}
                                    `}
                                >
                                    <ExamSidebar questionRefs={questionRefs} />
                                </OutsideClickWrapper>
                            </div>
                        )}
                    </div>
                )}
            </div>
            <ModalSubmitExam isOpen={showModalSubmit} onClose={() => dispatch(setShowModalSubmit(false))} onSubmit={handleSubmit} />
            {exam?.testDuration && isAgree && (
                <div className={`fixed bottom-2 rounded-md left-2 px-4 py-2
                    ${isTimeBlinking
                        ? 'bg-red-600 animate-pulse'
                        : 'bg-slate-700 bg-opacity-80'}
                    text-white z-40 transition-colors duration-300`}>
                    <div className="flex items-center gap-2">
                        <div className="text-sm font-bold">{formatTime(remainingTime)} phút</div>
                    </div>
                </div>
            )}
        </div>
    )
}

export default DoExamPage;