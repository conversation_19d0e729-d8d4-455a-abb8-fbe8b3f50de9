import React, { useState, useEffect } from 'react';
import UserLayout from '../../../layouts/UserLayout';
import { Bell, Check, ExternalLink, Trash2, Loader, ChevronDown, CreditCard } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  fetchNotifications,
  markAsRead as markNotificationAsRead,
  markAllAsRead as markAllNotificationsAsRead,
  deleteNotification as deleteNotificationAction,
  resetNotifications,
  resetDidInit
} from '../../../features/notification/notificationSlice';


const NotificationsPage = () => {
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'unread', 'read'
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const ITEMS_PER_PAGE = 10;

  // Get notifications from Redux store
  const { notifications, unreadCount, loading, hasMore, totalItems } = useSelector((state) => state.notifications);

  useEffect(() => {
    dispatch(resetDidInit());
  }, [dispatch]);

  // Load initial notifications when component mounts or tab changes
  useEffect(() => {
    // Reset page and notifications when tab changes
    setPage(1);
    dispatch(resetNotifications());

    // Fetch first page of notifications
    const params = {
      limit: ITEMS_PER_PAGE,
      offset: 0
    };

    // Add isRead filter if needed
    if (activeTab === 'unread') {
      params.isRead = false;
    } else if (activeTab === 'read') {
      params.isRead = true;
    }

    dispatch(fetchNotifications(params));
  }, [dispatch, activeTab]);

  // Load more notifications when page changes
  useEffect(() => {
    if (page > 1) {
      setIsLoadingMore(true);

      const params = {
        limit: ITEMS_PER_PAGE,
        offset: (page - 1) * ITEMS_PER_PAGE
      };

      // Add isRead filter if needed
      if (activeTab === 'unread') {
        params.isRead = false;
      } else if (activeTab === 'read') {
        params.isRead = true;
      }

      dispatch(fetchNotifications(params))
        .finally(() => {
          setIsLoadingMore(false);
        });
    }
  }, [dispatch, page, activeTab]);

  // Filter notifications based on active tab
  useEffect(() => {
    setFilteredNotifications(notifications);
  }, [notifications]);

  // Mark a notification as read
  const markAsRead = (id) => {
    dispatch(markNotificationAsRead([id]));
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    dispatch(markAllNotificationsAsRead());
  };

  // Delete a notification
  const deleteNotification = (id) => {
    dispatch(deleteNotificationAction(id));
  };

  // Load more notifications
  const loadMore = () => {
    setPage(prevPage => prevPage + 1);
  };

  // Handle notification click
  const handleNotificationClick = (notification) => {
    markAsRead(notification.id);
    if (notification.link) {
      navigate(notification.link);
    }
  };

  // Get icon based on notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'exam':
        return (
          <div className="p-3 bg-blue-100 rounded-full">
            <svg className="w-6 h-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
              <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
              <path d="M9 14l2 2 4-4"></path>
            </svg>
          </div>
        );
      case 'reminder':
        return (
          <div className="p-3 bg-yellow-100 rounded-full">
            <svg className="w-6 h-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
          </div>
        );
      case 'result':
        return (
          <div className="p-3 bg-green-100 rounded-full">
            <svg className="w-6 h-6 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
        );
      case 'tuition':
        return (
          <div className="p-3 bg-yellow-100 rounded-full">
            <CreditCard className="w-5 h-5 text-yellow-600" />
          </div>
        );
      default:
        return (
          <div className="p-3 bg-gray-100 rounded-full">
            <svg className="w-6 h-6 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
          </div>
        );
    }
  };

  return (
    <UserLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <Bell className="text-sky-600" />
            Thông báo
            {unreadCount > 0 && (
              <span className="ml-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                {unreadCount} mới
              </span>
            )}
          </h1>

          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="flex items-center gap-1 text-sm text-sky-600 hover:text-sky-700 px-3 py-1.5 bg-sky-50 hover:bg-sky-100 rounded-md transition-colors"
            >
              <Check size={16} />
              <span>Đánh dấu tất cả đã đọc</span>
            </button>
          )}
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => setActiveTab('all')}
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'all'
              ? 'text-sky-600 border-b-2 border-sky-600'
              : 'text-gray-500 hover:text-gray-700'
              }`}
          >
            Tất cả ({totalItems})
          </button>
          <button
            onClick={() => setActiveTab('unread')}
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'unread'
              ? 'text-sky-600 border-b-2 border-sky-600'
              : 'text-gray-500 hover:text-gray-700'
              }`}
          >
            Chưa đọc ({unreadCount})
          </button>
          <button
            onClick={() => setActiveTab('read')}
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'read'
              ? 'text-sky-600 border-b-2 border-sky-600'
              : 'text-gray-500 hover:text-gray-700'
              }`}
          >
            Đã đọc ({totalItems - unreadCount})
          </button>
        </div>

        {/* Notification list */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {loading && page === 1 ? (
            <div className="p-8 text-center text-gray-500">
              <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
              <p>Đang tải thông báo...</p>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <Bell size={40} className="mx-auto mb-4 text-gray-300" />
              <p>Không có thông báo nào.</p>
            </div>
          ) : (
            <>
              <div className="p-4 text-center text-sm text-gray-500 border-t border-gray-100">
                Hiển thị {filteredNotifications.length} trong tổng số {totalItems} thông báo
              </div>
              <div className="divide-y divide-gray-100">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 transition-colors ${!notification.isRead ? 'bg-sky-50' : ''}`}
                  >
                    <div className="flex gap-4">
                      {getNotificationIcon(notification.type)}
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <h4 className={`text-base font-semibold ${!notification.isRead ? 'text-sky-700' : 'text-gray-800'}`}>
                            {notification.title}
                          </h4>
                          <span className="text-sm text-gray-500">{notification.time}</span>
                        </div>
                        <p className="text-gray-600 mt-1">{notification.message}</p>
                        <div className="flex justify-between items-center mt-3">
                          {notification.link ? (
                            <button
                              onClick={() => handleNotificationClick(notification)}
                              className="text-sm text-sky-600 hover:text-sky-700 flex items-center"
                            >
                              <span>Xem chi tiết</span>
                              <ExternalLink size={14} className="ml-1" />
                            </button>
                          ) : (
                            <div></div>
                          )}
                          <div className="flex gap-2">
                            {!notification.isRead && (
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className="p-1.5 text-gray-500 hover:text-sky-600 hover:bg-sky-50 rounded-full transition-colors"
                                title="Đánh dấu đã đọc"
                              >
                                <Check size={16} />
                              </button>
                            )}
                            <button
                              onClick={() => deleteNotification(notification.id)}
                              className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                              title="Xóa thông báo"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Load more button */}
              {hasMore && (
                <div className="p-4 text-center">
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="px-4 py-2 bg-sky-50 hover:bg-sky-100 text-sky-600 rounded-md transition-colors flex items-center gap-2 mx-auto"
                  >
                    {isLoadingMore ? (
                      <>
                        <Loader size={16} className="animate-spin" />
                        <span>Đang tải thêm...</span>
                      </>
                    ) : (
                      <>
                        <ChevronDown size={16} />
                        <span>Tải thêm</span>
                      </>
                    )}
                  </button>
                </div>
              )}

              {/* Show total count */}

            </>
          )}
        </div>
      </div>
    </UserLayout>
  );
};

export default NotificationsPage;
