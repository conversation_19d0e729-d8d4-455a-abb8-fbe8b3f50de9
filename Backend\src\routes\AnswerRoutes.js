import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as AnswerController from '../controllers/AnswerController.js'

const router = express.Router()



// router.get('/v1/answer/:id', 
//     asyncHandler(AnswerController.getCauTraLoiById)
// )

// Lấy tất cả câu trả lời của người dùng theo lượt làm bài
router.get('/v1/user/answer/attempt/:attemptId',
    requireRoles(Roles.JustStudent),
    asyncHandler(AnswerController.getAnswerByAttempt)
)

// chấm lại bài làm của người dùng
router.get('/v1/user/answer/re-examination/:attemptId',
    requireRoles(Roles.JustStudent),
    asyncHandler(AnswerController.reExamination)
)

// Lấy tất cả câu hỏi và câu trả lời của người dùng theo lượt làm bài
router.get('/v1/user/answer/attempt/:attemptId/questions',
    requireRoles(Roles.JustStudent),
    asyncHandler(AnswerController.getQuestionsAndAnswersByAttempt)
)

router.get('/v1/admin/answer/re/all/examination/:examId',
    // requireRoles([...Roles.JustClassManagement, ...Roles.JustAssistant]),
    asyncHandler(AnswerController.reExaminations)
)


// router.post('/v1/answer',
//     asyncHandler(AnswerController.postCauTraLoi)
// )

// router.put('/v1/answer/:id',
//     asyncHandler(AnswerController.putCauTraLoi)
// )

// router.delete('/v1/answer/:id',
//     asyncHandler(AnswerController.deleteCauTraLoi)
// )

export default router