import express from 'express'
import asyncHand<PERSON> from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as NotificationController from '../controllers/NotificationController.js'
import Roles from '../constants/Roles.js'

const router = express.Router()

// Get user notifications
router.get('/v1/notifications',
    requireRoles(Roles.JustStudent),
    as<PERSON><PERSON><PERSON><PERSON>(NotificationController.getUserNotifications)
)

// Get unread notification count
router.get('/v1/notifications/unread-count',
    requireRoles(Roles.JustStudent),
    asyncHandler(NotificationController.getUnreadCount)
)

// Mark notifications as read
router.post('/v1/notifications/mark-as-read',
    requireRoles(Roles.JustStudent),
    asyncHandler(NotificationController.markAsRead)
)

// Mark all notifications as read
router.post('/v1/notifications/mark-all-as-read',
    requireRoles(Roles.JustStudent),
    as<PERSON><PERSON><PERSON><PERSON>(NotificationController.markAllAsRead)
)

// Delete notifications
router.delete('/v1/notifications',
    requireRoles(Roles.JustStudent),
    asyncHandler(NotificationController.deleteNotifications)
)

// Delete notification
router.delete('/v1/notifications/:id',
    requireRoles(Roles.JustStudent),
    asyncHandler(NotificationController.deleteNotification)
)

// Delete old notifications
router.delete('/v1/public/notifications/old',
    asyncHandler(NotificationController.deleteOldNotifications)
)


export default router
