import db from '../models/index.js';
import { Sequelize } from 'sequelize';

export const getExamRatingStatistics = async ({
    examId,
    userId = null,
    avgStar = true,
    starCount = true,
    isSave = true,
    isDone = true
}) => {
    try {
        const attributes = [];

        if (avgStar)
            attributes.push([
                Sequelize.literal(`AVG(CASE WHEN \`star\` IS NOT NULL THEN \`star\` ELSE NULL END)`),
                'avgStar'
            ]);

        if (starCount)
            attributes.push([
                Sequelize.literal(`COUNT(CASE WHEN \`star\` IS NOT NULL THEN 1 ELSE NULL END)`),
                'starCount'
            ]);

        if (isSave)
            attributes.push([
                Sequelize.literal(`SUM(CASE WHEN \`isSave\` = 1 THEN 1 ELSE 0 END)`),
                'isSaveCount'
            ]);

        if (isDone)
            attributes.push([
                Sequelize.literal(`SUM(CASE WHEN \`isDone\` = 1 THEN 1 ELSE 0 END)`),
                'isDoneCount'
            ]);

        const [stats, studentStatus] = await Promise.all([
            db.StudentExamStatus.findOne({
                where: { examId },
                attributes,
                raw: true
            }),
            userId
                ? db.StudentExamStatus.findOne({
                    where: { examId, studentId: userId },
                    attributes: ['star', 'isDone', 'isSave'],
                    raw: true
                })
                : Promise.resolve(null)
        ]);

        // Parse và format kết quả
        const result = {
            avgStar: avgStar ? (stats?.avgStar ? parseFloat(stats.avgStar).toFixed(1) : '0.0') : undefined,
            starCount: starCount ? parseInt(stats?.starCount || 0, 10) : undefined,
            isSaveCount: isSave ? parseInt(stats?.isSaveCount || 0, 10) : undefined,
            isDoneCount: isDone ? parseInt(stats?.isDoneCount || 0, 10) : undefined,
            studentStatus: {
                star: studentStatus?.star ?? null,
                isSave: Boolean(studentStatus?.isSave),
                isDone: Boolean(studentStatus?.isDone)
            }
        };

        return result;
    } catch (error) {
        console.error('Error calculating exam stats:', error);
        throw error;
    }
};

export const toggleSaveExam = async (userId, examId) => {
    if (!examId) {
        throw new Error('examId là bắt buộc.');
    }

    // Kiểm tra exam có tồn tại
    const exam = await db.Exam.findByPk(examId);
    if (!exam) {
        throw new Error('Không tìm thấy đề thi.');
    }

    // Tìm hoặc tạo bản ghi trạng thái
    const [status, created] = await db.StudentExamStatus.findOrCreate({
        where: { studentId: userId, examId },
        defaults: {
            isSave: true,
            isDone: false,
            star: null
        }
    });

    // Nếu đã tồn tại thì toggle isSave
    if (!created) {
        status.isSave = !status.isSave;
        await status.save();
    }

    return {
        examId,
        isSave: status.isSave
    };
};

export const rateExam = async (userId, examId, star) => {
    if (!examId || star === undefined || star === null) {
        throw new Error('examId và star là bắt buộc.');
    }

    // Validate giá trị star
    const starValue = parseFloat(star);
    if (isNaN(starValue) || starValue < 0 || starValue > 10) {
        throw new Error('Số sao phải nằm trong khoảng từ 0 đến 10.');
    }

    // Kiểm tra đề thi tồn tại
    const exam = await db.Exam.findByPk(examId);
    if (!exam) {
        throw new Error('Không tìm thấy đề thi.');
    }

    // Tìm hoặc tạo trạng thái
    const [status] = await db.StudentExamStatus.findOrCreate({
        where: { studentId: userId, examId },
        defaults: {
            star: starValue,
            isSave: false,
            isDone: false
        }
    });

    // Cập nhật star nếu đã tồn tại
    status.star = starValue;
    await status.save();

    return {
        examId,
        star: starValue
    };
};
