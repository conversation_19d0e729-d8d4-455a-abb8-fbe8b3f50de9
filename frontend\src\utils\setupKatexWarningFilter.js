/**
 * Global filter for KaTeX warnings
 * This should be imported and called once in the main App.js or index.js
 */

export const setupKatexWarningFilter = () => {
    // Store the original console.warn
    const originalWarn = console.warn;
    
    // Override console.warn with a filtered version
    console.warn = (...args) => {
        const message = args.join(' ');

        // Debug: Log tất cả warnings để xem
        // console.log('🔍 Console warn called:', message);

        // Filter out KaTeX-related warnings that we don't want to see
        const katexWarningPatterns = [
            'No character metrics for',
            'Unrecognized Unicode character',
            'LaTeX-incompatible input and strict mode is set to',
            'unknownSymbol',
            'katex.mjs:', // B<PERSON>t kỳ warning nào từ katex.mjs
            'KaTeX', // Bất kỳ warning nào có chứa KaTeX
            'reportNonstrict' // Function báo warning trong KaTeX
        ];

        // Check if this warning matches any KaTeX pattern
        const isKatexWarning = katexWarningPatterns.some(pattern =>
            message.includes(pattern)
        );

        // Debug: Log để kiểm tra filter có hoạt động không
        if (isKatexWarning) {
            // console.log('🔇 Filtered KaTeX warning:', message);
            return;
        }

        // Only show the warning if it's not a KaTeX warning we want to suppress
        // console.log('✅ Allowing warning:', message);
        originalWarn.apply(console, args);
    };
    
    // Return a function to restore original console.warn if needed
    return () => {
        console.warn = originalWarn;
    };
};
