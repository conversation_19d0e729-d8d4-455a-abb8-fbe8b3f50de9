import api from "./api";

export const joinExamApi = async (examId) => {
    return await api.get(`/v1/user/join-exam/${examId}`);
}

export const submitAnswerApi = async ({ questionId, answerContent, type }) => {
    return await api.post('/v1/user/submit-answer', { questionId, answerContent, type });
};

export const calculateScoreApi = async ({ attemptId, answers }) => {
    return await api.post(`/v1/user/calculate-score/${attemptId}`, { answers });
};

export const summitExamAPI = async ({ attemptId }) => {
    return await api.post(`/v1/user/submit-exam`, { attemptId });
}

// API để lấy thời gian còn lại của bài thi
export const getRemainingTimeApi = async ({ examId, attemptId }) => {
    return await api.get(`/v1/user/exam-time/${examId}/${attemptId}`);
};

// API để gửi log hoạt động của user (thay thế socket user_log)
export const logUserActivityApi = async ({ examId, attemptId, activityType, details }) => {
    return await api.post('/v1/user/log-activity', {
        examId,
        attemptId,
        activityType,
        details
    });
};

// API để submit answer với attemptId (thay thế socket select_answer)
export const submitAnswerWithAttemptApi = async ({ questionId, answerContent, type, attemptId }) => {
    return await api.post('/v1/user/submit-answer-attempt', {
        questionId,
        answerContent,
        type,
        attemptId
    });
};

// API để leave exam (thay thế socket leave_exam)
export const leaveExamApi = async ({ examId, attemptId }) => {
    return await api.post('/v1/user/leave-exam', { examId, attemptId });
};