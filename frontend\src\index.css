/* index.css */

/* CSS Reset */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .avoid-page-break {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .page-break {
    break-before: page;
    page-break-before: always;
  }

  .page-break-after {
    break-after: page;
    page-break-after: always;
  }
}

@media print {
  @page {
    size: A4;
    margin: 15mm 10mm 20mm 10mm;
    /* Top Right Bottom Left */
  }



  body {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    background-color: #f9fafb;
  }

  #root {
    min-height: 100vh;
  }

  .print-container {
    position: relative;
  }

  .print-section {
    margin-bottom: 10mm;
  }

  .print-question {
    margin-bottom: 5mm;
    padding-bottom: 3mm;
  }

  .print-footer {
    position: fixed;
    bottom: 5mm;
    width: 100%;
    text-align: right;
    font-size: 10px;
    color: gray;
    padding-top: 3mm;
    border-top: 1px solid #eee;
    background-color: white;
  }
}

@font-face {
  font-family: "iCielBC_Cubano";
  src: url("/public/iCielBCCubano-Normal.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@keyframes wave {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }

  50% {
    transform: scale(1.3);
    opacity: 0.3;
  }

  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

.animate-wave {
  animation: wave 1.5s infinite ease-out;
}

.markdown-preview img {
  max-width: 100px;
  height: auto;
  display: block;
  margin: 1rem auto;
}

.hide-scrollbar {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}

input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
  display: none;
}

input[type="password"]::-webkit-credentials-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
}


input.slider {
  -webkit-appearance: none;
  height: 6px;
  background: #d1d5db;
  /* màu track (xám nhạt - Tailwind: gray-300) */
  border-radius: 9999px;
  outline: none;
  cursor: pointer;
}

input.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #0ea5e9;
  /* màu thumb (Tailwind: sky-500) */
  border-radius: 50%;
  border: none;
  transition: background 0.3s ease;
}

input.slider::-webkit-slider-thumb:hover {
  background: #0369a1;
  /* hover color - sky-700 */
}

input.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #0ea5e9;
  border-radius: 50%;
  border: none;
}



/* Ẩn mũi tên tăng/giảm trong input number */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
  /* Firefox */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* overflow: hidden; */
}

html {
  font-size: 16px;
  line-height: 1.5;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

body {}

/* Link styling */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default list styles */
ul,
ol {
  list-style: none;
}

/* Common container style */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
}

p {}

/* Button base style */
button {
  cursor: pointer;

}

/* Utility classes */
.text-center {
  text-align: center;
}

.mt-1 {
  margin-top: 1rem;
}

.mb-1 {
  margin-bottom: 1rem;
}

/* Custom toàn bộ scrollbar */
* {
  scrollbar-width: thin;
  /* Firefox: Làm mỏng thanh cuộn */
  scrollbar-color: #b0b0b0 #f0f0f0;
  /* Firefox: Màu thumb và track */
}

/* Custom scrollbar cho Chrome, Edge, Safari */
::-webkit-scrollbar {
  width: 5px;
  /* Độ rộng của thanh cuộn */
}

/* Phần kéo (thumb) */
::-webkit-scrollbar-thumb {
  background-color: #b0b0b0;
  /* Màu của phần kéo */
  border-radius: 6px;
  /* Bo tròn thanh kéo */
  transition: background 0.3s;
  /* Hiệu ứng màu khi hover */
}

/* Khi hover vào thanh kéo */
::-webkit-scrollbar-thumb:hover {
  background-color: #909090;
  /* Màu tối hơn khi hover */
}

/* Phần nền của thanh cuộn */
::-webkit-scrollbar-track {
  background: #f0f0f0;
  /* Màu nền của thanh cuộn */
  border-radius: 6px;
}

/* Ẩn scrollbar khi không cần thiết */
::-webkit-scrollbar-track-piece {
  background: transparent;
}


/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }
}


/* src/components/Particles.css */
.particle {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: floatUp 10s linear infinite;
}

.particle-0 {
  left: 10%;
  animation-duration: 12s;
  animation-delay: 0s;
}

.particle-1 {
  left: 25%;
  animation-duration: 15s;
  animation-delay: 3s;
}

.particle-2 {
  left: 40%;
  animation-duration: 10s;
  animation-delay: 6s;
}

.particle-3 {
  left: 60%;
  animation-duration: 13s;
  animation-delay: 1s;
}

.particle-4 {
  left: 80%;
  animation-duration: 11s;
  animation-delay: 4s;
}

@keyframes floatUp {
  0% {
    transform: translateY(100vh) scale(1);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: translateY(-10vh) scale(0.5);
    opacity: 0;
  }
}