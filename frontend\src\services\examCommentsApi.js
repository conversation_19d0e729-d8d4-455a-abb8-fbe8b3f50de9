import api from "./api";

export const getCommentsByExamIdAPI = async ({ examId, page = 1 }) => {
    return await api.get(`/v1/user/comments/exam/${examId}`, {
        params: {
            page,
        }
    });
}

export const getRepliesByCommentIdAPI = async ({ commentId, page = 1 }) => {
    return await api.get(`/v1/user/comments/replies/${commentId}`, {
        params: {
            page,
        }
    });
}

export const postCommentAPI = async ({ examId, content, parentCommentId = null }) => {
    return await api.post(`/v1/user/comments/exam/${examId}`, { examId, content, parentCommentId });
}

export const putCommentAPI = async ({ commentId, content }) => {
    return await api.put(`/v1/user/comments/${commentId}`, { content });
}

export const deleteCommentAPI = async (commentId) => {
    return await api.delete(`/v1/user/comments/${commentId}`);
}

