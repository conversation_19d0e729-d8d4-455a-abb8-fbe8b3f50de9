import {<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>Worker}from'@lottiefiles/dotlottie-web';import {useRef,useCallback,useEffect}from'react';import {jsx}from'react/jsx-runtime';var p=({animationId:o,autoplay:x,backgroundColor:L,className:R,createDotLottie:B,data:s,dotLottieRefCallback:v,layout:i,loop:m,mode:D,playOnHover:C,renderConfig:d,segment:n,speed:g,src:f,style:E,themeData:W,themeId:a,useFrameInterpolation:k,workerId:S,...y})=>{let t=useRef(null),c=useRef(null),T=useRef(v),b={speed:g,mode:D,loop:m,useFrameInterpolation:k,segment:n,backgroundColor:L,autoplay:x,themeId:a,workerId:S,src:f,data:s,layout:i,renderConfig:d,animationId:o},l=useRef(b);T.current=v,l.current=b;let h=useCallback(r=>{c.current=r,r?t.current=B({...l.current,canvas:r}):(t.current?.destroy(),t.current=null),T.current?.(t.current);},[]);return useEffect(()=>{let r=w=>{C&&(w.type==="mouseenter"&&t.current?.play(),w.type==="mouseleave"&&t.current?.pause());};return c.current?.addEventListener("mouseenter",r),c.current?.addEventListener("mouseleave",r),()=>{c.current?.removeEventListener("mouseenter",r),c.current?.removeEventListener("mouseleave",r);}},[C]),useEffect(()=>{t.current?.setSpeed(g??1);},[g]),useEffect(()=>{t.current?.setMode(D??"forward");},[D]),useEffect(()=>{t.current?.setLoop(m??false);},[m]),useEffect(()=>{t.current?.setUseFrameInterpolation(k??true);},[k]),useEffect(()=>{typeof n?.[0]=="number"&&typeof n[1]=="number"&&t.current?.setSegment(n[0],n[1]);},[n]),useEffect(()=>{t.current?.setBackgroundColor(L??"");},[L]),useEffect(()=>{t.current?.setRenderConfig(d??{});},[JSON.stringify(d)]),useEffect(()=>{typeof s!="string"&&typeof s!="object"||t.current?.load({data:s,...l.current});},[s]),useEffect(()=>{typeof f=="string"&&t.current?.load({src:f,...l.current});},[f]),useEffect(()=>{t.current?.setMarker(y.marker??"");},[y.marker]),useEffect(()=>{t.current?.loadAnimation(o??"");},[o]),useEffect(()=>{typeof a=="string"&&t.current?.setTheme(a);},[a]),useEffect(()=>{t.current?.setThemeData(W??"");},[W]),useEffect(()=>{t.current?.setLayout(i??{});},[i?.fit,i?.align&&i.align[0],i?.align&&i.align[1]]),jsx("div",{className:R,...!R&&{style:{width:"100%",height:"100%",lineHeight:0,...E}},children:jsx("canvas",{ref:h,style:{width:"100%",height:"100%"},...y})})};var M=o=>new DotLottie(o),V=o=>jsx(p,{...o,createDotLottie:M});var U=o=>new DotLottieWorker(o),F=o=>jsx(p,{...o,createDotLottie:U});var ot=o=>{DotLottieWorker.setWasmUrl(o),DotLottie.setWasmUrl(o);};export{V as DotLottieReact,F as DotLottieWorkerReact,ot as setWasmUrl};//# sourceMappingURL=index.js.map
//# sourceMappingURL=index.js.map