import React from 'react';
import LatexRenderer from '../latex/RenderLatex';
import QuestionImage from './QuestionImage';
import { Bookmark } from 'lucide-react';
import ReportButton from '../button/ReportButton';
import NoTranslate from '../utils/NoTranslate';
import { useDispatch, useSelector } from "react-redux";
import QuestionContent from './QuestionContent';
import { setErrorMessage } from "src/features/state/stateApiSlice";
import { submitAnswerWithAttempt, setAnswers } from "src/features/doExam/doExamSlice";

const TrueFalseQuestion = ({ question, index }) => {
    const { darkMode, fontSize, imageSize, prefixStatementsDS, isTimeUp, attemptId, answersDS } = useSelector((state) => state.doExam);
    const dispatch = useDispatch();

    const handleSelectAnswer = (questionId, statementId, selectedAnswer) => {
        // Không cho phép làm bài nếu đã hết thời gian
        if (isTimeUp) {
            dispatch(setErrorMessage("Đ<PERSON> hết thời gian làm bài. Không thể thay đổi câu trả lời!"));
            return;
        }

        let currentAnswers = answersDS[questionId] || [];
        if (typeof currentAnswers === 'string' && currentAnswers.length > 0) {
            try {
                currentAnswers = JSON.parse(currentAnswers);
            } catch (e) {
                currentAnswers = [];
            }
        }


        const existing = currentAnswers.find(ans => ans.statementId === statementId);

        // 🔁 Nếu đáp án đã giống thì không gửi lại
        if (existing && existing.answer === selectedAnswer) {
            return
        }

        const updatedAnswers = currentAnswers.map(ans =>
            ans.statementId === statementId
                ? { ...ans, answer: selectedAnswer }
                : ans
        );

        // Nếu chưa có statement này
        if (!existing) {
            updatedAnswers.push({ statementId, answer: selectedAnswer });
        }

        dispatch(setAnswers({ questionId, answerContent: updatedAnswers, typeOfQuestion: "DS" }));

        // Sử dụng debounced API call cho DS
        dispatch(submitAnswerWithAttempt({
            questionId,
            answerContent: updatedAnswers,
            type: "DS",
            attemptId
        }));
    };

    const isChecked = (questionId, statementId, answer) => {
        let currentAnswers = answersDS[questionId] || [];

        // Nếu currentAnswers là chuỗi thì parse
        if (typeof currentAnswers === 'string') {
            try {
                currentAnswers = JSON.parse(currentAnswers);
            } catch (e) {
                currentAnswers = [];
            }
        }

        const existing = currentAnswers.find(ans => ans.statementId === statementId);
        return existing ? existing.answer === answer : false;
    };


    return (
        <div
            key={question.id + "DS"}
            // ref={(el) => setRef(question.id, el)}
            // data-question-id={question.id}
            className={`flex flex-col avoid-page-break gap-2 rounded-md p-3 transition`}>
            <QuestionContent question={question} index={index} />

            <div className="flex flex-col gap-3">
                {question.statements.map((statement, statementIndex) => (
                    <div key={statement.id} className="flex flex-col gap-2 pb-3">
                        {/* Responsive layout for statement and options */}
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                            {/* Statement content with prefix */}
                            <div className="flex items-start sm:items-center gap-2 mb-2 sm:mb-0">
                                <p className="font-bold whitespace-nowrap" style={{ fontSize: `${fontSize}px` }}>
                                    <NoTranslate>{prefixStatementsDS[statementIndex]}</NoTranslate>
                                </p>
                                <LatexRenderer text={statement.content} className="break-words flex-1" style={{ fontSize: `${fontSize}px` }} />
                            </div>

                            {/* Radio buttons for true/false */}
                            <div className="flex items-center gap-4 ml-6 sm:ml-0">
                                <label className="flex items-center gap-1 cursor-pointer">
                                    <input
                                        type="radio"
                                        name={`ds-${statement.id}`}
                                        checked={isChecked(question.id, statement.id, true)}
                                        value="true"
                                        className={`w-4 h-4 accent-sky-600 ${isTimeUp ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
                                        onChange={() => handleSelectAnswer(question.id, statement.id, true)}
                                        disabled={isTimeUp}
                                    />
                                    <span className={`${darkMode ? 'text-white' : 'text-black'} font-medium`} style={{ fontSize: `${fontSize}px` }}>
                                        Đúng
                                    </span>
                                </label>

                                <label className="flex items-center gap-1 cursor-pointer">
                                    <input
                                        type="radio"
                                        name={`ds-${statement.id}`}
                                        checked={isChecked(question.id, statement.id, false)}
                                        value="false"
                                        className={`w-4 h-4 accent-sky-600 ${isTimeUp ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
                                        onChange={() => handleSelectAnswer(question.id, statement.id, false)}
                                        disabled={isTimeUp}
                                    />
                                    <span className={`${darkMode ? 'text-white' : 'text-black'} font-medium`} style={{ fontSize: `${fontSize}px` }}>
                                        Sai
                                    </span>
                                </label>
                            </div>
                        </div>

                        {/* Statement image if available */}
                        {statement.imageUrl && (
                            <QuestionImage
                                imageUrl={statement.imageUrl}
                                isStatement={true}
                                altText="statement image"
                            />
                        )}
                    </div>
                ))}
            </div>
        </div>
    )
}

export default TrueFalseQuestion;