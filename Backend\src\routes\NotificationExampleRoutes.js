import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import Roles from '../constants/Roles.js'
import * as NotificationExampleController from '../controllers/NotificationExampleController.js'

const router = express.Router()

// Example routes for sending notifications (admin only)
router.post('/v1/admin/notifications/send-to-user',
    requireR<PERSON><PERSON>(Roles.JustAdmin),
    async<PERSON>andler(NotificationExampleController.sendNotificationToUserExample)
)

router.post('/v1/admin/notifications/send-to-class',
    requireRoles(Roles.JustAdmin),
    async<PERSON>andler(NotificationExampleController.sendNotificationToClassExample)
)

router.post('/v1/admin/notifications/send-to-exam',
    requireRoles(Roles.JustAdmin),
    as<PERSON><PERSON><PERSON><PERSON>(NotificationExampleController.sendNotificationToExamExample)
)

export default router
