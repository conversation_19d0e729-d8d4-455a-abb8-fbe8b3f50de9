import AdminLayout from "../../../layouts/AdminLayout";
import FunctionBarAdmin from "../../../components/bar/FunctionBarAdmin";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { setCurrentPage, setLimit, setSearch, setSortOrder } from "src/features/user/userSlice";
import { fetchStaff } from "src/features/user/userSlice";
import { TableAdmin, TdAdmin, ThAdmin, TheadAdmin } from "src/components/table/TableAdmin";
import { MoreVertical, Pencil, Copy, Trash2, Eye, EyeClosed } from "lucide-react";
import LoadingData from "src/components/loading/LoadingData";
import { TotalComponent } from "src/components/table/TotalComponent";
import Pagination from "src/components/Pagination";
import { fetchCodesByType } from "src/features/code/codeSlice";

const Option = ({ handleEdit, handleDuplicate, handleDelete }) => {
    const [menuOpen, setMenuOpen] = useState(false);
    const menuRef = useRef();

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setMenuOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    return (
        <div
            ref={menuRef}
            className="relative">
            <button
                onClick={(e) => {
                    e.stopPropagation(); // 👈 chặn sự kiện lan lên thẻ cha
                    setMenuOpen(!menuOpen);
                }}
                className="p-2 text-gray-500 hover:text-gray-700"
            >
                <MoreVertical className="w-5 h-5" />
            </button>

            {menuOpen && (
                <div className="absolute right-0 top-8 w-40 bg-white border rounded shadow z-10">
                    <button
                        onClick={handleEdit}
                        className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                    >
                        <Pencil className="w-4 h-4 text-gray-600" />
                        Chỉnh sửa
                    </button>
                    <button
                        onClick={handleDuplicate}
                        className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                    >
                        <Copy className="w-4 h-4 text-gray-600" />
                        Nhân đôi
                    </button>
                    <button
                        onClick={handleDelete}
                        className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50"
                    >
                        <Trash2 className="w-4 h-4" />
                        Xóa
                    </button>
                </div>

            )}
        </div>
    );
}

const TdPassWord = ({ children }) => {
    const [showPassword, setShowPassword] = useState(false);
    const handleTogglePassword = () => setShowPassword((prev) => !prev);

    return (
        <TdAdmin>
            <div className="flex items-center gap-2">
                <button
                    onClick={handleTogglePassword}
                    className="text-gray-500"
                >
                    {showPassword ? <EyeClosed size={18} /> : <Eye size={18} />}
                </button>
                {showPassword ? children?.length > 20 ? children?.substring(0, 20) + "..." : children : "..."}
            </div>
        </TdAdmin>
    );
}


const Table = () => {
    const { staff, pagination, loading } = useSelector(state => state.users);
    const { page, pageSize, sortOrder, total } = pagination;
    const { codes } = useSelector(state => state.codes);
    const dispatch = useDispatch();

    const handleEdit = () => {
        console.log('Chỉnh sửa nhân viên');
    };

    const handleDuplicate = () => {
        console.log('Nhân đôi nhân viên');
    };

    const handleDelete = () => {
        console.log('Xóa nhân viên');
    };

    return (
        <LoadingData
            loading={loading}
            loadText="Đang tải danh sách nhân viên"
            noDataText="Không có nhân viên nào."
            isNoData={staff.length > 0 ? false : true}>
            <TotalComponent
                total={total}
                page={page}
                pageSize={pageSize}
                setSortOrder={() => dispatch(setSortOrder())}
            />
            <TableAdmin>
                <TheadAdmin>
                    <ThAdmin>ID</ThAdmin>
                    <ThAdmin>Họ và tên</ThAdmin>
                    <ThAdmin>Chức vụ</ThAdmin>
                    <ThAdmin>Giới tính</ThAdmin>
                    <ThAdmin>Ngày sinh</ThAdmin>
                    <ThAdmin>Số điện thoại</ThAdmin>
                    <ThAdmin>Tài khoản</ThAdmin>
                    <ThAdmin>Mật khẩu</ThAdmin>
                    <ThAdmin>Trường</ThAdmin>
                    <ThAdmin>Thao tác</ThAdmin>
                </TheadAdmin>
                <tbody>
                    {staff.map((user, index) => (
                        <tr key={index} className="hover:bg-blue-50 transition">
                            <TdAdmin>{user.id}</TdAdmin>
                            <TdAdmin>{user.lastName} {user.firstName}</TdAdmin>
                            <TdAdmin>{codes["user type"]?.find(code => code.code === user.userType)?.description || user.userType}</TdAdmin>
                            <TdAdmin>{user.gender ? "Nam" : "Nữ"}</TdAdmin>
                            <TdAdmin>{user.birthDate ? new Date(user.birthDate).toLocaleDateString() : "Chưa cập nhật"}</TdAdmin>
                            <TdAdmin>{user.phone || "Chưa có"}</TdAdmin>
                            <TdPassWord>{user.username}</TdPassWord>
                            <TdPassWord>{user.password}</TdPassWord>
                            <TdAdmin>{user.highSchool || "Chưa cập nhật"}</TdAdmin>
                            <TdAdmin>
                                <Option
                                    handleEdit={handleEdit}
                                    handleDuplicate={handleDuplicate}
                                    handleDelete={handleDelete}
                                />
                            </TdAdmin>
                        </tr>
                    ))}
                </tbody>
            </TableAdmin>
        </LoadingData>
    )
}

const StaffManagement = () => {
    const dispatch = useDispatch();
    const { staff, pagination, search } = useSelector(state => state.users);
    const { page, pageSize, sortOrder } = pagination;

    useEffect(() => {
        dispatch(fetchStaff({ search, currentPage: page, limit: pageSize, sortOrder }));
    }, [dispatch, search, page, pageSize, sortOrder]);

    useEffect(() => {
        dispatch(fetchCodesByType("user type"));
    }, [dispatch]);

    return (
        <AdminLayout>
            <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9">
                Danh sách nhân viên
            </div>
            <FunctionBarAdmin
                currentPage={page}
                totalItems={pagination.total}
                totalPages={pagination.totalPages}
                limit={pageSize}
                setLimit={(newLimit) => {
                    dispatch(setLimit(newLimit))
                }}
                setCurrentPage={(newPage) => dispatch(setCurrentPage(newPage))}
                setSearch={(value) => dispatch(setSearch(value))}
            />
            <Table />
        </AdminLayout>
    );
}

export default StaffManagement;