import { useEffect, useState } from "react";
import LoadingSpinner from "../loading/LoadingSpinner";

const InputSearch = ({ onDebouncedChange, placeholder = "Nhập id câu hỏi", className }) => {
    const [value, setValue] = useState("");
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        setLoading(true);
        const handler = setTimeout(() => {
            onDebouncedChange?.(value);
            setLoading(false);
        }, 1000);

        return () => clearTimeout(handler);
    }, [value, onDebouncedChange]);

    return (

        <div className="relative w-full h-full">
            <input
                type="text"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                placeholder={placeholder}
                className="w-full h-10 pl-10 pr-10 text-sm text-gray-700 placeholder-gray-400 border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-400 focus:border-sky-400 transition-all duration-150"
            />

            {!loading && (
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-5 h-5 text-gray-400"
                        viewBox="0 0 24 24"
                        fill="none"
                    >
                        <path
                            d="M19.6 21L13.3 14.7C12.8 15.1 12.225 15.4167 11.575 15.65C10.925 15.8833 10.2333 16 9.5 16C7.68333 16 6.146 15.3707 4.888 14.112C3.63 12.8533 3.00067 11.316 3 9.5C2.99933 7.684 3.62867 6.14667 4.888 4.888C6.14733 3.62933 7.68467 3 9.5 3C11.3153 3 12.853 3.62933 14.113 4.888C15.373 6.14667 16.002 7.684 16 9.5C16 10.2333 15.8833 10.925 15.65 11.575C15.4167 12.225 15.1 12.8 14.7 13.3L21 19.6L19.6 21ZM9.5 14C10.75 14 11.8127 13.5627 12.688 12.688C13.5633 11.8133 14.0007 10.7507 14 9.5C13.9993 8.24933 13.562 7.187 12.688 6.313C11.814 5.439 10.7513 5.00133 9.5 5C8.24867 4.99867 7.18633 5.43633 6.313 6.313C5.43967 7.18967 5.002 8.252 5 9.5C4.998 10.748 5.43567 11.8107 6.313 12.688C7.19033 13.5653 8.25267 14.0027 9.5 14Z"
                            fill="#9CA3AF"
                        />
                    </svg>
                </div>
            )}

            {/* Loading spinner (phải) */}
            <div className="absolute inset-y-0 left-3 flex items-center">
                {loading && (
                    <LoadingSpinner
                        size="1.25rem"
                        thickness="border-2"
                    />
                )}
            </div>
        </div>
    );
};

export default InputSearch;
