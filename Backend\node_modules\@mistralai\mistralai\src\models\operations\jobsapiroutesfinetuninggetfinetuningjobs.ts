/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * The current job state to filter on. When set, the other results are not displayed.
 */
export const Status = {
  Queued: "QUEUED",
  Started: "STARTED",
  Validating: "VALIDATING",
  Validated: "VALIDATED",
  Running: "RUNNING",
  FailedValidation: "FAILED_VALIDATION",
  Failed: "FAILED",
  Success: "SUCCESS",
  Cancelled: "CANCELLED",
  CancellationRequested: "CANCELLATION_REQUESTED",
} as const;
/**
 * The current job state to filter on. When set, the other results are not displayed.
 */
export type Status = ClosedEnum<typeof Status>;

export type JobsApiRoutesFineTuningGetFineTuningJobsRequest = {
  /**
   * The page number of the results to be returned.
   */
  page?: number | undefined;
  /**
   * The number of items to return per page.
   */
  pageSize?: number | undefined;
  /**
   * The model name used for fine-tuning to filter on. When set, the other results are not displayed.
   */
  model?: string | null | undefined;
  /**
   * The date/time to filter on. When set, the results for previous creation times are not displayed.
   */
  createdAfter?: Date | null | undefined;
  createdBefore?: Date | null | undefined;
  /**
   * When set, only return results for jobs created by the API caller. Other results are not displayed.
   */
  createdByMe?: boolean | undefined;
  /**
   * The current job state to filter on. When set, the other results are not displayed.
   */
  status?: Status | null | undefined;
  /**
   * The Weights and Biases project to filter on. When set, the other results are not displayed.
   */
  wandbProject?: string | null | undefined;
  /**
   * The Weight and Biases run name to filter on. When set, the other results are not displayed.
   */
  wandbName?: string | null | undefined;
  /**
   * The model suffix to filter on. When set, the other results are not displayed.
   */
  suffix?: string | null | undefined;
};

/** @internal */
export const Status$inboundSchema: z.ZodNativeEnum<typeof Status> = z
  .nativeEnum(Status);

/** @internal */
export const Status$outboundSchema: z.ZodNativeEnum<typeof Status> =
  Status$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Status$ {
  /** @deprecated use `Status$inboundSchema` instead. */
  export const inboundSchema = Status$inboundSchema;
  /** @deprecated use `Status$outboundSchema` instead. */
  export const outboundSchema = Status$outboundSchema;
}

/** @internal */
export const JobsApiRoutesFineTuningGetFineTuningJobsRequest$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningGetFineTuningJobsRequest,
    z.ZodTypeDef,
    unknown
  > = z.object({
    page: z.number().int().default(0),
    page_size: z.number().int().default(100),
    model: z.nullable(z.string()).optional(),
    created_after: z.nullable(
      z.string().datetime({ offset: true }).transform(v => new Date(v)),
    ).optional(),
    created_before: z.nullable(
      z.string().datetime({ offset: true }).transform(v => new Date(v)),
    ).optional(),
    created_by_me: z.boolean().default(false),
    status: z.nullable(Status$inboundSchema).optional(),
    wandb_project: z.nullable(z.string()).optional(),
    wandb_name: z.nullable(z.string()).optional(),
    suffix: z.nullable(z.string()).optional(),
  }).transform((v) => {
    return remap$(v, {
      "page_size": "pageSize",
      "created_after": "createdAfter",
      "created_before": "createdBefore",
      "created_by_me": "createdByMe",
      "wandb_project": "wandbProject",
      "wandb_name": "wandbName",
    });
  });

/** @internal */
export type JobsApiRoutesFineTuningGetFineTuningJobsRequest$Outbound = {
  page: number;
  page_size: number;
  model?: string | null | undefined;
  created_after?: string | null | undefined;
  created_before?: string | null | undefined;
  created_by_me: boolean;
  status?: string | null | undefined;
  wandb_project?: string | null | undefined;
  wandb_name?: string | null | undefined;
  suffix?: string | null | undefined;
};

/** @internal */
export const JobsApiRoutesFineTuningGetFineTuningJobsRequest$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningGetFineTuningJobsRequest$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningGetFineTuningJobsRequest
  > = z.object({
    page: z.number().int().default(0),
    pageSize: z.number().int().default(100),
    model: z.nullable(z.string()).optional(),
    createdAfter: z.nullable(z.date().transform(v => v.toISOString()))
      .optional(),
    createdBefore: z.nullable(z.date().transform(v => v.toISOString()))
      .optional(),
    createdByMe: z.boolean().default(false),
    status: z.nullable(Status$outboundSchema).optional(),
    wandbProject: z.nullable(z.string()).optional(),
    wandbName: z.nullable(z.string()).optional(),
    suffix: z.nullable(z.string()).optional(),
  }).transform((v) => {
    return remap$(v, {
      pageSize: "page_size",
      createdAfter: "created_after",
      createdBefore: "created_before",
      createdByMe: "created_by_me",
      wandbProject: "wandb_project",
      wandbName: "wandb_name",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningGetFineTuningJobsRequest$ {
  /** @deprecated use `JobsApiRoutesFineTuningGetFineTuningJobsRequest$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningGetFineTuningJobsRequest$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningGetFineTuningJobsRequest$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningGetFineTuningJobsRequest$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningGetFineTuningJobsRequest$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningGetFineTuningJobsRequest$Outbound;
}

export function jobsApiRoutesFineTuningGetFineTuningJobsRequestToJSON(
  jobsApiRoutesFineTuningGetFineTuningJobsRequest:
    JobsApiRoutesFineTuningGetFineTuningJobsRequest,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningGetFineTuningJobsRequest$outboundSchema.parse(
      jobsApiRoutesFineTuningGetFineTuningJobsRequest,
    ),
  );
}

export function jobsApiRoutesFineTuningGetFineTuningJobsRequestFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningGetFineTuningJobsRequest,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningGetFineTuningJobsRequest$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningGetFineTuningJobsRequest' from JSON`,
  );
}
