import React, { useState } from 'react';
import { X } from 'lucide-react';
import ClassSearchInput from './ClassSearchInput';

/**
 * Component for selecting multiple classes
 * 
 * @param {Object} props
 * @param {Array} props.selectedClasses - Array of selected class objects
 * @param {function} props.onChange - Callback when selected classes change
 * @param {string} props.className - Additional CSS classes
 */
const MultiClassSelector = ({
  selectedClasses = [],
  onChange,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClassId, setSelectedClassId] = useState('');

  // Handle class selection
  const handleSelectClass = (classItem) => {
    // Check if class is already selected
    if (!selectedClasses.some(c => c.id === classItem.id)) {
      const updatedClasses = [...selectedClasses, classItem];
      onChange(updatedClasses);
    }
    setSearchTerm('');
    setSelectedClassId('');
  };

  // Handle removing a class
  const handleRemoveClass = (classId) => {
    const updatedClasses = selectedClasses.filter(c => c.id !== classId);
    onChange(updatedClasses);
  };

  // Handle clearing the search
  const handleClearSearch = () => {
    setSearchTerm('');
    setSelectedClassId('');
  };

  return (
    <div className={`${className}`}>
      <ClassSearchInput
        value={searchTerm}
        selectedClassId={selectedClassId}
        onChange={setSearchTerm}
        onSelect={handleSelectClass}
        onClear={handleClearSearch}
        placeholder="Tìm kiếm lớp học..."
        className="mb-2"
      />

      {selectedClasses.length > 0 && (
        <div className="mt-2">
          <div className="text-sm font-medium text-gray-700 mb-1">Các lớp đã chọn:</div>
          <div className="flex flex-wrap gap-2">
            {selectedClasses.map((classItem) => (
              <div
                key={classItem.id}
                className="flex items-center bg-sky-100 text-sky-800 px-2 py-1 rounded-md text-sm"
              >
                <span>{classItem.name}</span>
                <button
                  onClick={() => handleRemoveClass(classItem.id)}
                  className="ml-1 text-sky-600 hover:text-sky-800"
                >
                  <X size={14} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiClassSelector;
