import { useSelector, useDispatch } from "react-redux";
import { setSelectedId } from "src/features/questionsExam/questionsExamSlice";
import LatexRenderer from "../latex/RenderLatex";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";
import SortableStatementsContainer from "./SortableStatementsContainer";
import { deleteQuestion } from "src/features/questionsExam/questionsExamSlice";
import { Trash2 } from "lucide-react";
import YouTubePlayer from "../YouTubePlayer";

const QuestionContent = ({ question, index }) => {
    const dispatch = useDispatch();
    const { selectedId } = useSelector((state) => state.questionsExam);
    const { codes } = useSelector((state) => state.codes);

    return (
        <div
            key={question.id}
            className={`
            flex flex-col gap-3 p-3 rounded-lg border transition cursor-pointer
            ${selectedId === question.id ? "bg-blue-50 border-blue-500" : "bg-white hover:bg-gray-50 border-gray-300"}`}
            onClick={() => dispatch(setSelectedId(question.id))}
        >
            {/* Thông tin câu hỏi */}
            <div className="flex justify-between items-center">
                <div div className="flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1" >
                    <span className="font-medium text-gray-700">Câu {index + 1} ({question.id}) :</span>
                    {question.isNewQuestion && <div className="p-[0.2rem] rounded-full text-xs bg-green-500 text-white">new</div>}
                    <span>Lớp: <span className="text-gray-700">{question.class || "Chưa xác định"}</span></span>
                    <span>Độ khó: <span className="text-gray-700">{question.difficulty || "Chưa xác định"}</span></span>
                    <span>
                        Chương:{" "}
                        <span className="text-gray-700">
                            {codes["chapter"]?.find(c => c.code === question.chapter)?.description || "Chưa xác định"}
                        </span>
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    <button
                        onClick={() => dispatch(deleteQuestion(question.id))}
                        className="p-1 text-red-600 hover:bg-red-50 rounded"
                        title="Xóa"
                    >
                        <Trash2 size={14} />
                    </button>
                </div>
            </div>


            <div className="flex flex-col gap-2">
                <div className="text-base text-gray-800 leading-relaxed">
                    <LatexRenderer className="text-gray-800 text-xs" text={question.content} />
                </div>
                {question.imageUrl && (
                    <div className="flex flex-col items-center justify-center w-full h-[10rem] mt-1">
                        <img
                            src={question.imageUrl}
                            alt="question"
                            className="object-contain w-full h-full"
                        />
                    </div>
                )}
            </div>

            {/* Statements with drag and drop */}
            <SortableStatementsContainer question={question} />
            
            {question.solutionUrl && (
                <div className="mt-2">
                    <h6 className="text-xs font-bold">Lời giải</h6>
                    <YouTubePlayer url={question.solutionUrl} sizeClass="w-2/3 aspect-video" />
                </div>
            )}

            {question.solution && (
                <div className="mt-2">
                    <h6 className="text-xs font-bold">Lời giải</h6>
                    <MarkdownPreviewWithMath content={question.solution} className=" w-full" style={{ fontSize: '12px' }} />
                </div>
            )}

        </div>
    )
}

export default QuestionContent;