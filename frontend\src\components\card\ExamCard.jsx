import ExamDefaultImage from "../../assets/images/defaultExamImage.png";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { saveExamForUser } from "../../features/practice/practiceSlice";
import React from "react";
import {
    Calendar,
    Clock,
    BookOpen,
    GraduationCap,
    ChevronRight,
    Bookmark,
    CheckCircle,
    Lock,
    Play,
    Eye,
    BookmarkIcon,
    Star,
} from "lucide-react";
import { useSelector } from "react-redux";

const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
    });
};

const ExamCard = ({ exam }) => {
    const { name, typeOfExam, class: examClass, chapter, testDuration, year, createdAt, imageUrl, id, isSave, isDone, acceptDoExam = true } = exam;
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { codes } = useSelector((state) => state.codes);

    const handleClicked = () => navigate(`/practice/exam/${id}`);

    const handleSaveExam = (e) => {
        e.stopPropagation();
        dispatch(saveExamForUser({ examId: id }));
    };

    return (
        <div
            onClick={handleClicked}
            className="relative flex flex-col md:flex-row justify-center items-start md:items-center p-3 lg:p-4 gap-3 lg:gap-4 bg-white border border-gray-200 rounded-md hover:shadow-md hover:border-cyan-300 cursor-pointer transition-all duration-200 group"
        >
            <div className="w-full flex flex-col">
                <div className="flex flex-col md:flex-row w-full justify-between gap-2">
                    <div className="flex items-center gap-2 md:justify-start justify-between">
                        <h3 className="text-zinc-900 font-semibold font-bevietnam text-sm group-hover:text-cyan-700 transition-colors">
                            {name}
                        </h3>
                        <button
                            onClick={handleSaveExam}
                            className={`flex-shrink-0 ml-2 flex items-center px-2 py-1 rounded-full text-xs border transition-all duration-150 ${exam.statuses?.[0]?.isSave
                                ? "bg-blue-50 text-blue-600 border-blue-300 hover:bg-blue-100"
                                : "bg-gray-100 text-gray-500 border-gray-300 hover:bg-gray-200"
                                }`}
                        >
                            <BookmarkIcon size={14} className="mr-1" />
                            {exam.statuses?.[0]?.isSave ? "Đã lưu" : "Lưu đề"}
                        </button>
                    </div>

                    <div className="flex flex-row gap-2">
                        <div className={`items-center justify-center h-fit flex-shrink-0 py-1 px-2 border border-gray-200 rounded-full text-xs text-gray-500 ${exam.statuses.length > 0 && exam.statuses[0].isDone ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>
                            <span>{exam.statuses.length > 0 && exam.statuses[0].isDone ? 'Đã làm' : 'Chưa làm'}</span>
                        </div>

                        <div className="items-center justify-center h-fit flex flex-row flex-shrink-0 py-1 px-2 border border-gray-200 rounded-full text-xs text-gray-500">
                            <Eye size={14} className="mr-1 md:block hidden" />
                            <span>Lượt làm bài: {exam.studentCount}</span>
                        </div>

                        <div className="items-center justify-center h-fit flex flex-row flex-shrink-0 py-1 px-2 border border-gray-200 rounded-full text-xs text-gray-500">
                            <Star size={14} className="mr-1 text-yellow-500" />
                            <span>{exam.avgStar} ({exam.starCount})</span>
                        </div>
                    </div>


                </div>
                <div className="flex flex-row gap-2 mt-4 flex-wrap">
                    <p className="text-xs text-gray-500 ">Loại đề: {codes && codes['exam type']?.find(c => c.code === typeOfExam)?.description || typeOfExam || ''}</p>
                    <p className="text-xs text-gray-500 ">Khối: {examClass}</p>
                    <p className="text-xs text-gray-500 ">Thời gian: {testDuration ? testDuration + " phút" : "Vô thời hạn"} </p>
                </div>
                <div className="flex flex-row gap-2 flex-wrap mt-1">
                    <p className="text-xs text-gray-500 ">Chương: {codes && codes['chapter']?.find(c => c.code === chapter)?.description || chapter || ''}</p>
                    <p className="text-xs text-gray-500 ">Năm: {year}</p>
                </div>
            </div>
            <div className="absolute bottom-3 right-3">
                {exam.acceptDoExam ? (
                    <div className=" justify-center h-fit flex flex-row flex-shrink-0 items-center p-1 px-2 border border-green-300 rounded-full text-xs text-green-700 bg-green-50">
                        <Play size={14} className="md:mr-1" />
                        <span className="hidden md:block">Làm bài</span>
                    </div>
                ) : (
                    <div className=" justify-center h-fit flex flex-row flex-shrink-0 items-center p-1 px-2 border border-gray-300 rounded-full text-xs text-gray-500 bg-gray-100">
                        <Lock size={14} className="md:mr-1" />
                        <span className="hidden md:block">Đang khóa</span>
                    </div>
                )}
            </div>
        </div >
    );
};

export default ExamCard;
