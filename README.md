# 🐝 Toán <PERSON> Bee - Online Learning & Exam Management System  

## 📌 Introduction  
**<PERSON><PERSON> Bee** is an advanced **online learning and examination management system**, developed by **<PERSON><PERSON><PERSON><PERSON> and <PERSON>**. The platform is designed to help teachers and students **manage learning progress, take online exams, and access a diverse set of questions and tests**.  

This project was built to **enhance the digital learning experience**, making it easier for students to practice, take exams, and track their academic progress in an **interactive and structured** way.  

---

## 🚀 Features  
### 🎓 **For Students:**  
- 📖 Access a variety of **exam questions and practice tests**  
- 🎯 Take **online exams** with different question formats  
- 📊 View **performance analytics and learning history**  

### 👩‍🏫 **For Teachers:**  
- 📝 **Create, manage, and assign exams**  
- 🔍 **Monitor students' performance and progress**  
- 📂 **Organize test banks** with categorized questions  

### ⚙️ **General Features:**  
- 🔒 **Secure authentication system** for students and teachers  
- 🎨 **User-friendly dashboard** for an intuitive experience  
- 📂 **Categorized question banks** for effective test creation  

---

## 🏗️ Tech Stack  
The platform is developed using modern web technologies:  

- **Frontend:** React.js
- **Backend:** Node.js, Express.js  
- **Database:** MySQL (Sequelize ORM)  
- **Authentication:** 
- **Hosting:** 

---

## 🛠️ Installation & Setup  

### 📌 **1. Clone the Repository**  
```sh
git clone https://github.com/Minhduc7904/toan-thay-bee.git
cd toan-thay-bee
