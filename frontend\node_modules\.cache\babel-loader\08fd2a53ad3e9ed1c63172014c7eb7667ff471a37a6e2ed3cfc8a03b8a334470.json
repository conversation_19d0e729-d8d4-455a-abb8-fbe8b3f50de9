{"ast": null, "code": "var _jsxFileName = \"D:\\\\ToanThayBee\\\\frontend\\\\src\\\\components\\\\ai\\\\AIWidget.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Upload, Image as ImageIcon, Sparkles, X, Eye, Save } from 'lucide-react';\nimport LatexRenderer from '../latex/RenderLatex';\nimport { uploadBase64Images } from '../../features/image/imageSlice';\nimport { setErrorMessage, setSuccessMessage } from '../../features/state/stateApiSlice';\nimport { ocrImageWithMistralAPI } from '../../services/ocrExamApi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AIWidget = _ref => {\n  _s();\n  let {\n    onImageUploaded,\n    className = ''\n  } = _ref;\n  const dispatch = useDispatch();\n  const [isOpen, setIsOpen] = useState(false);\n  const [uploadedImage, setUploadedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [ocrText, setOcrText] = useState('');\n  const [ocrBase64Images, setOcrBase64Images] = useState([]); // Store base64 images from OCR response\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const [isDragging, setIsDragging] = useState(false);\n  const uploadRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Handle paste events\n  useEffect(() => {\n    const handlePaste = event => {\n      var _event$clipboardData;\n      if (!isOpen) return;\n      const items = (_event$clipboardData = event.clipboardData) === null || _event$clipboardData === void 0 ? void 0 : _event$clipboardData.items;\n      if (items) {\n        for (let i = 0; i < items.length; i++) {\n          const item = items[i];\n          if (item.type.indexOf(\"image\") !== -1) {\n            const file = item.getAsFile();\n            if (file) {\n              handleImageFile(file);\n            }\n          }\n        }\n      }\n    };\n    const uploadElement = uploadRef.current;\n    if (uploadElement) {\n      uploadElement.addEventListener(\"paste\", handlePaste);\n      return () => {\n        uploadElement.removeEventListener(\"paste\", handlePaste);\n      };\n    }\n  }, [isOpen]);\n  const validateImageFile = file => {\n    if (![\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)) {\n      dispatch(setErrorMessage(\"Chỉ cho phép định dạng JPEG hoặc PNG!\"));\n      return false;\n    }\n    if (file.size > 10 * 1024 * 1024) {\n      // 10MB limit\n      dispatch(setErrorMessage(\"Kích thước ảnh vượt quá 10MB!\"));\n      return false;\n    }\n    return true;\n  };\n  const handleImageFile = file => {\n    if (!validateImageFile(file)) return;\n    setUploadedImage(file);\n    setImagePreview(URL.createObjectURL(file));\n    setOcrText('');\n  };\n  const handleDragOver = event => {\n    event.preventDefault();\n    event.stopPropagation();\n    setIsDragging(true);\n  };\n  const handleDragLeave = event => {\n    event.preventDefault();\n    event.stopPropagation();\n    setIsDragging(false);\n  };\n  const handleDrop = event => {\n    event.preventDefault();\n    event.stopPropagation();\n    setIsDragging(false);\n    const files = event.dataTransfer.files;\n    if (files.length > 0) {\n      handleImageFile(files[0]);\n    }\n  };\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      handleImageFile(file);\n    }\n  };\n  const processOCR = async () => {\n    if (!uploadedImage) {\n      dispatch(setErrorMessage(\"Vui lòng chọn ảnh trước khi xử lý OCR\"));\n      return;\n    }\n    setIsProcessing(true);\n    try {\n      const response = await ocrImageWithMistralAPI(uploadedImage);\n      if (response && response.markdown) {\n        setOcrText(response.markdown);\n        // Store base64Images from OCR response for later upload\n        if (response.base64Images && response.base64Images.length > 0) {\n          setOcrBase64Images(response.base64Images);\n        }\n        dispatch(setSuccessMessage(\"OCR thành công!\"));\n      } else {\n        dispatch(setErrorMessage(\"Không thể trích xuất văn bản từ ảnh\"));\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('OCR Error:', error);\n      dispatch(setErrorMessage(\"Lỗi khi xử lý OCR: \" + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message)));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  const handleUploadToStorage = async () => {\n    if (!uploadedImage) {\n      dispatch(setErrorMessage(\"Không có ảnh để tải lên\"));\n      return;\n    }\n    setIsUploading(true);\n    try {\n      // Convert file to base64\n      const reader = new FileReader();\n      reader.onload = async e => {\n        const base64 = e.target.result;\n\n        // Upload using existing uploadBase64Images function\n        const result = await dispatch(uploadBase64Images({\n          images: [base64],\n          folder: \"questionImage\"\n        })).unwrap();\n        if (result && result.length > 0) {\n          dispatch(setSuccessMessage(\"Tải ảnh lên thành công!\"));\n          if (onImageUploaded) {\n            onImageUploaded(result[0]); // Pass the uploaded image URL\n          }\n          handleReset();\n        }\n      };\n      reader.readAsDataURL(uploadedImage);\n    } catch (error) {\n      console.error('Upload Error:', error);\n      dispatch(setErrorMessage(\"Lỗi khi tải ảnh lên: \" + (error.message || \"Không xác định\")));\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleReset = () => {\n    setUploadedImage(null);\n    setImagePreview(null);\n    setOcrText('');\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(true),\n      className: \"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-md hover:shadow-lg \".concat(className),\n      children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n        className: \"w-4 h-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 17\n      }, this), \"AI Widget\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white border border-gray-200 rounded-lg shadow-lg p-4 \".concat(className),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n          className: \"w-5 h-5 text-purple-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"AI Widget - OCR & Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setIsOpen(false),\n        className: \"p-1 hover:bg-gray-100 rounded transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(X, {\n          className: \"w-4 h-4 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: uploadRef,\n      tabIndex: 0,\n      className: \"border-2 border-dashed rounded-lg p-6 mb-4 transition-all duration-200 \".concat(isDragging ? \"border-blue-400 bg-blue-50\" : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"),\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      children: !imagePreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-gray-200 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(ImageIcon, {\n              className: \"w-8 h-8 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"K\\xE9o th\\u1EA3 \\u1EA3nh ho\\u1EB7c Ctrl+V \\u0111\\u1EC3 d\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"H\\u1ED7 tr\\u1EE3 JPEG, PNG (t\\u1ED1i \\u0111a 10MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            className: \"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 33\n            }, this), \"Ch\\u1ECDn \\u1EA3nh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: imagePreview,\n            alt: \"Preview\",\n            className: \"max-w-full max-h-64 mx-auto rounded-lg shadow-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            className: \"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processOCR,\n            disabled: isProcessing,\n            className: \"inline-flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: isProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 41\n              }, this), \"\\u0110ang x\\u1EED l\\xFD...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Eye, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 41\n              }, this), \"OCR\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleUploadToStorage,\n            disabled: isUploading,\n            className: \"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 41\n              }, this), \"\\u0110ang t\\u1EA3i...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Save, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 41\n              }, this), \"T\\u1EA3i l\\xEAn\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 13\n    }, this), ocrText && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"V\\u0103n b\\u1EA3n tr\\xEDch xu\\u1EA5t (c\\xF3 th\\u1EC3 ch\\u1EC9nh s\\u1EEDa):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: ocrText,\n          onChange: e => setOcrText(e.target.value),\n          className: \"w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          placeholder: \"V\\u0103n b\\u1EA3n t\\u1EEB OCR s\\u1EBD hi\\u1EC3n th\\u1ECB \\u1EDF \\u0111\\xE2y...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Xem tr\\u01B0\\u1EDBc LaTeX:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full min-h-[100px] p-3 border border-gray-300 rounded-md bg-white overflow-auto\",\n          children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n            text: ocrText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      accept: \"image/jpeg,image/png,image/jpg\",\n      onChange: handleFileSelect,\n      className: \"hidden\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 9\n  }, this);\n};\n_s(AIWidget, \"neDb87x3ZVL/YakxHCHReUXd1a0=\", false, function () {\n  return [useDispatch];\n});\n_c = AIWidget;\nexport default AIWidget;\nvar _c;\n$RefreshReg$(_c, \"AIWidget\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDispatch", "useSelector", "Upload", "Image", "ImageIcon", "<PERSON><PERSON><PERSON>", "X", "Eye", "Save", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadBase64Images", "setErrorMessage", "setSuccessMessage", "ocrImageWithMistralAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AIWidget", "_ref", "_s", "onImageUploaded", "className", "dispatch", "isOpen", "setIsOpen", "uploadedImage", "setUploadedImage", "imagePreview", "setImagePreview", "ocrText", "setOcrText", "ocrBase64Images", "setOcrBase64Images", "isProcessing", "setIsProcessing", "isUploading", "setIsUploading", "isDragging", "setIsDragging", "uploadRef", "fileInputRef", "handlePaste", "event", "_event$clipboardData", "items", "clipboardData", "i", "length", "item", "type", "indexOf", "file", "getAsFile", "handleImageFile", "uploadElement", "current", "addEventListener", "removeEventListener", "validateImageFile", "includes", "size", "URL", "createObjectURL", "handleDragOver", "preventDefault", "stopPropagation", "handleDragLeave", "handleDrop", "files", "dataTransfer", "handleFileSelect", "target", "processOCR", "response", "markdown", "base64Images", "error", "_error$response", "_error$response$data", "console", "data", "message", "handleUploadToStorage", "reader", "FileReader", "onload", "e", "base64", "result", "images", "folder", "unwrap", "handleReset", "readAsDataURL", "value", "onClick", "concat", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "tabIndex", "onDragOver", "onDragLeave", "onDrop", "_fileInputRef$current", "click", "src", "alt", "disabled", "onChange", "placeholder", "text", "accept", "_c", "$RefreshReg$"], "sources": ["D:/ToanThayBee/frontend/src/components/ai/AIWidget.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Upload, Image as ImageIcon, Sparkles, X, Eye, Save } from 'lucide-react';\nimport LatexRenderer from '../latex/RenderLatex';\nimport { uploadBase64Images } from '../../features/image/imageSlice';\nimport { setErrorMessage, setSuccessMessage } from '../../features/state/stateApiSlice';\nimport { ocrImageWithMistralAPI } from '../../services/ocrExamApi';\n\nconst AIWidget = ({ onImageUploaded, className = '' }) => {\n    const dispatch = useDispatch();\n    const [isOpen, setIsOpen] = useState(false);\n    const [uploadedImage, setUploadedImage] = useState(null);\n    const [imagePreview, setImagePreview] = useState(null);\n    const [ocrText, setOcrText] = useState('');\n    const [ocrBase64Images, setOcrBase64Images] = useState([]); // Store base64 images from OCR response\n    const [isProcessing, setIsProcessing] = useState(false);\n    const [isUploading, setIsUploading] = useState(false);\n    const [isDragging, setIsDragging] = useState(false);\n\n    const uploadRef = useRef(null);\n    const fileInputRef = useRef(null);\n\n    // Handle paste events\n    useEffect(() => {\n        const handlePaste = (event) => {\n            if (!isOpen) return;\n            \n            const items = event.clipboardData?.items;\n            if (items) {\n                for (let i = 0; i < items.length; i++) {\n                    const item = items[i];\n                    if (item.type.indexOf(\"image\") !== -1) {\n                        const file = item.getAsFile();\n                        if (file) {\n                            handleImageFile(file);\n                        }\n                    }\n                }\n            }\n        };\n\n        const uploadElement = uploadRef.current;\n        if (uploadElement) {\n            uploadElement.addEventListener(\"paste\", handlePaste);\n            return () => {\n                uploadElement.removeEventListener(\"paste\", handlePaste);\n            };\n        }\n    }, [isOpen]);\n\n    const validateImageFile = (file) => {\n        if (![\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)) {\n            dispatch(setErrorMessage(\"Chỉ cho phép định dạng JPEG hoặc PNG!\"));\n            return false;\n        }\n        if (file.size > 10 * 1024 * 1024) { // 10MB limit\n            dispatch(setErrorMessage(\"Kích thước ảnh vượt quá 10MB!\"));\n            return false;\n        }\n        return true;\n    };\n\n    const handleImageFile = (file) => {\n        if (!validateImageFile(file)) return;\n\n        setUploadedImage(file);\n        setImagePreview(URL.createObjectURL(file));\n        setOcrText('');\n    };\n\n    const handleDragOver = (event) => {\n        event.preventDefault();\n        event.stopPropagation();\n        setIsDragging(true);\n    };\n\n    const handleDragLeave = (event) => {\n        event.preventDefault();\n        event.stopPropagation();\n        setIsDragging(false);\n    };\n\n    const handleDrop = (event) => {\n        event.preventDefault();\n        event.stopPropagation();\n        setIsDragging(false);\n\n        const files = event.dataTransfer.files;\n        if (files.length > 0) {\n            handleImageFile(files[0]);\n        }\n    };\n\n    const handleFileSelect = (event) => {\n        const file = event.target.files[0];\n        if (file) {\n            handleImageFile(file);\n        }\n    };\n\n    const processOCR = async () => {\n        if (!uploadedImage) {\n            dispatch(setErrorMessage(\"Vui lòng chọn ảnh trước khi xử lý OCR\"));\n            return;\n        }\n\n        setIsProcessing(true);\n        try {\n            const response = await ocrImageWithMistralAPI(uploadedImage);\n\n            if (response && response.markdown) {\n                setOcrText(response.markdown);\n                // Store base64Images from OCR response for later upload\n                if (response.base64Images && response.base64Images.length > 0) {\n                    setOcrBase64Images(response.base64Images);\n                }\n                dispatch(setSuccessMessage(\"OCR thành công!\"));\n            } else {\n                dispatch(setErrorMessage(\"Không thể trích xuất văn bản từ ảnh\"));\n            }\n        } catch (error) {\n            console.error('OCR Error:', error);\n            dispatch(setErrorMessage(\"Lỗi khi xử lý OCR: \" + (error.response?.data?.message || error.message)));\n        } finally {\n            setIsProcessing(false);\n        }\n    };\n\n    const handleUploadToStorage = async () => {\n        if (!uploadedImage) {\n            dispatch(setErrorMessage(\"Không có ảnh để tải lên\"));\n            return;\n        }\n\n        setIsUploading(true);\n        try {\n            // Convert file to base64\n            const reader = new FileReader();\n            reader.onload = async (e) => {\n                const base64 = e.target.result;\n                \n                // Upload using existing uploadBase64Images function\n                const result = await dispatch(uploadBase64Images({ \n                    images: [base64], \n                    folder: \"questionImage\" \n                })).unwrap();\n\n                if (result && result.length > 0) {\n                    dispatch(setSuccessMessage(\"Tải ảnh lên thành công!\"));\n                    if (onImageUploaded) {\n                        onImageUploaded(result[0]); // Pass the uploaded image URL\n                    }\n                    handleReset();\n                }\n            };\n            reader.readAsDataURL(uploadedImage);\n        } catch (error) {\n            console.error('Upload Error:', error);\n            dispatch(setErrorMessage(\"Lỗi khi tải ảnh lên: \" + (error.message || \"Không xác định\")));\n        } finally {\n            setIsUploading(false);\n        }\n    };\n\n    const handleReset = () => {\n        setUploadedImage(null);\n        setImagePreview(null);\n        setOcrText('');\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n    };\n\n    if (!isOpen) {\n        return (\n            <button\n                onClick={() => setIsOpen(true)}\n                className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-md hover:shadow-lg ${className}`}\n            >\n                <Sparkles className=\"w-4 h-4\" />\n                AI Widget\n            </button>\n        );\n    }\n\n    return (\n        <div className={`bg-white border border-gray-200 rounded-lg shadow-lg p-4 ${className}`}>\n            {/* Header */}\n            <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center gap-2\">\n                    <Sparkles className=\"w-5 h-5 text-purple-500\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900\">AI Widget - OCR & Upload</h3>\n                </div>\n                <button\n                    onClick={() => setIsOpen(false)}\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n                >\n                    <X className=\"w-4 h-4 text-gray-500\" />\n                </button>\n            </div>\n\n            {/* Upload Area */}\n            <div\n                ref={uploadRef}\n                tabIndex={0}\n                className={`border-2 border-dashed rounded-lg p-6 mb-4 transition-all duration-200 ${\n                    isDragging\n                        ? \"border-blue-400 bg-blue-50\"\n                        : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"\n                }`}\n                onDragOver={handleDragOver}\n                onDragLeave={handleDragLeave}\n                onDrop={handleDrop}\n            >\n                {!imagePreview ? (\n                    <div className=\"text-center\">\n                        <div className=\"flex flex-col items-center space-y-2\">\n                            <div className=\"p-3 bg-gray-200 rounded-full\">\n                                <ImageIcon className=\"w-8 h-8 text-gray-600\" />\n                            </div>\n                            <div className=\"space-y-1\">\n                                <p className=\"text-sm font-medium text-gray-700\">\n                                    Kéo thả ảnh hoặc Ctrl+V để dán\n                                </p>\n                                <p className=\"text-xs text-gray-500\">\n                                    Hỗ trợ JPEG, PNG (tối đa 10MB)\n                                </p>\n                            </div>\n                            <button\n                                onClick={() => fileInputRef.current?.click()}\n                                className=\"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors\"\n                            >\n                                <Upload className=\"w-4 h-4\" />\n                                Chọn ảnh\n                            </button>\n                        </div>\n                    </div>\n                ) : (\n                    <div className=\"space-y-4\">\n                        {/* Image Preview */}\n                        <div className=\"relative\">\n                            <img\n                                src={imagePreview}\n                                alt=\"Preview\"\n                                className=\"max-w-full max-h-64 mx-auto rounded-lg shadow-sm\"\n                            />\n                            <button\n                                onClick={handleReset}\n                                className=\"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\"\n                            >\n                                <X className=\"w-4 h-4\" />\n                            </button>\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex gap-2 justify-center\">\n                            <button\n                                onClick={processOCR}\n                                disabled={isProcessing}\n                                className=\"inline-flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                            >\n                                {isProcessing ? (\n                                    <>\n                                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                                        Đang xử lý...\n                                    </>\n                                ) : (\n                                    <>\n                                        <Eye className=\"w-4 h-4\" />\n                                        OCR\n                                    </>\n                                )}\n                            </button>\n                            <button\n                                onClick={handleUploadToStorage}\n                                disabled={isUploading}\n                                className=\"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                            >\n                                {isUploading ? (\n                                    <>\n                                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                                        Đang tải...\n                                    </>\n                                ) : (\n                                    <>\n                                        <Save className=\"w-4 h-4\" />\n                                        Tải lên\n                                    </>\n                                )}\n                            </button>\n                        </div>\n                    </div>\n                )}\n            </div>\n\n            {/* OCR Results */}\n            {ocrText && (\n                <div className=\"space-y-4\">\n                    {/* OCR Text Input */}\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Văn bản trích xuất (có thể chỉnh sửa):\n                        </label>\n                        <textarea\n                            value={ocrText}\n                            onChange={(e) => setOcrText(e.target.value)}\n                            className=\"w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            placeholder=\"Văn bản từ OCR sẽ hiển thị ở đây...\"\n                        />\n                    </div>\n\n                    {/* LaTeX Preview */}\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Xem trước LaTeX:\n                        </label>\n                        <div className=\"w-full min-h-[100px] p-3 border border-gray-300 rounded-md bg-white overflow-auto\">\n                            <LatexRenderer text={ocrText} />\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Hidden file input */}\n            <input\n                ref={fileInputRef}\n                type=\"file\"\n                accept=\"image/jpeg,image/png,image/jpg\"\n                onChange={handleFileSelect}\n                className=\"hidden\"\n            />\n        </div>\n    );\n};\n\nexport default AIWidget;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,GAAG,EAAEC,IAAI,QAAQ,cAAc;AACjF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,oCAAoC;AACvF,SAASC,sBAAsB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,QAAQ,GAAGC,IAAA,IAAyC;EAAAC,EAAA;EAAA,IAAxC;IAAEC,eAAe;IAAEC,SAAS,GAAG;EAAG,CAAC,GAAAH,IAAA;EACjD,MAAMI,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM2C,SAAS,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM2C,YAAY,GAAG3C,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACZ,MAAM2C,WAAW,GAAIC,KAAK,IAAK;MAAA,IAAAC,oBAAA;MAC3B,IAAI,CAACpB,MAAM,EAAE;MAEb,MAAMqB,KAAK,IAAAD,oBAAA,GAAGD,KAAK,CAACG,aAAa,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBC,KAAK;MACxC,IAAIA,KAAK,EAAE;QACP,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACnC,MAAME,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC;UACrB,IAAIE,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;YACnC,MAAMC,IAAI,GAAGH,IAAI,CAACI,SAAS,CAAC,CAAC;YAC7B,IAAID,IAAI,EAAE;cACNE,eAAe,CAACF,IAAI,CAAC;YACzB;UACJ;QACJ;MACJ;IACJ,CAAC;IAED,MAAMG,aAAa,GAAGf,SAAS,CAACgB,OAAO;IACvC,IAAID,aAAa,EAAE;MACfA,aAAa,CAACE,gBAAgB,CAAC,OAAO,EAAEf,WAAW,CAAC;MACpD,OAAO,MAAM;QACTa,aAAa,CAACG,mBAAmB,CAAC,OAAO,EAAEhB,WAAW,CAAC;MAC3D,CAAC;IACL;EACJ,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;EAEZ,MAAMmC,iBAAiB,GAAIP,IAAI,IAAK;IAChC,IAAI,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAACQ,QAAQ,CAACR,IAAI,CAACF,IAAI,CAAC,EAAE;MAC/D3B,QAAQ,CAACZ,eAAe,CAAC,uCAAuC,CAAC,CAAC;MAClE,OAAO,KAAK;IAChB;IACA,IAAIyC,IAAI,CAACS,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;MAAE;MAChCtC,QAAQ,CAACZ,eAAe,CAAC,+BAA+B,CAAC,CAAC;MAC1D,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAM2C,eAAe,GAAIF,IAAI,IAAK;IAC9B,IAAI,CAACO,iBAAiB,CAACP,IAAI,CAAC,EAAE;IAE9BzB,gBAAgB,CAACyB,IAAI,CAAC;IACtBvB,eAAe,CAACiC,GAAG,CAACC,eAAe,CAACX,IAAI,CAAC,CAAC;IAC1CrB,UAAU,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMiC,cAAc,GAAIrB,KAAK,IAAK;IAC9BA,KAAK,CAACsB,cAAc,CAAC,CAAC;IACtBtB,KAAK,CAACuB,eAAe,CAAC,CAAC;IACvB3B,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,eAAe,GAAIxB,KAAK,IAAK;IAC/BA,KAAK,CAACsB,cAAc,CAAC,CAAC;IACtBtB,KAAK,CAACuB,eAAe,CAAC,CAAC;IACvB3B,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM6B,UAAU,GAAIzB,KAAK,IAAK;IAC1BA,KAAK,CAACsB,cAAc,CAAC,CAAC;IACtBtB,KAAK,CAACuB,eAAe,CAAC,CAAC;IACvB3B,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAM8B,KAAK,GAAG1B,KAAK,CAAC2B,YAAY,CAACD,KAAK;IACtC,IAAIA,KAAK,CAACrB,MAAM,GAAG,CAAC,EAAE;MAClBM,eAAe,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7B;EACJ,CAAC;EAED,MAAME,gBAAgB,GAAI5B,KAAK,IAAK;IAChC,MAAMS,IAAI,GAAGT,KAAK,CAAC6B,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIjB,IAAI,EAAE;MACNE,eAAe,CAACF,IAAI,CAAC;IACzB;EACJ,CAAC;EAED,MAAMqB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAAC/C,aAAa,EAAE;MAChBH,QAAQ,CAACZ,eAAe,CAAC,uCAAuC,CAAC,CAAC;MAClE;IACJ;IAEAwB,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACA,MAAMuC,QAAQ,GAAG,MAAM7D,sBAAsB,CAACa,aAAa,CAAC;MAE5D,IAAIgD,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;QAC/B5C,UAAU,CAAC2C,QAAQ,CAACC,QAAQ,CAAC;QAC7B;QACA,IAAID,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACE,YAAY,CAAC5B,MAAM,GAAG,CAAC,EAAE;UAC3Df,kBAAkB,CAACyC,QAAQ,CAACE,YAAY,CAAC;QAC7C;QACArD,QAAQ,CAACX,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;MAClD,CAAC,MAAM;QACHW,QAAQ,CAACZ,eAAe,CAAC,qCAAqC,CAAC,CAAC;MACpE;IACJ,CAAC,CAAC,OAAOkE,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACZC,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCtD,QAAQ,CAACZ,eAAe,CAAC,qBAAqB,IAAI,EAAAmE,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAIL,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;IACvG,CAAC,SAAS;MACN/C,eAAe,CAAC,KAAK,CAAC;IAC1B;EACJ,CAAC;EAED,MAAMgD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACzD,aAAa,EAAE;MAChBH,QAAQ,CAACZ,eAAe,CAAC,yBAAyB,CAAC,CAAC;MACpD;IACJ;IAEA0B,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACA;MACA,MAAM+C,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAOC,CAAC,IAAK;QACzB,MAAMC,MAAM,GAAGD,CAAC,CAACf,MAAM,CAACiB,MAAM;;QAE9B;QACA,MAAMA,MAAM,GAAG,MAAMlE,QAAQ,CAACb,kBAAkB,CAAC;UAC7CgF,MAAM,EAAE,CAACF,MAAM,CAAC;UAChBG,MAAM,EAAE;QACZ,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;QAEZ,IAAIH,MAAM,IAAIA,MAAM,CAACzC,MAAM,GAAG,CAAC,EAAE;UAC7BzB,QAAQ,CAACX,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;UACtD,IAAIS,eAAe,EAAE;YACjBA,eAAe,CAACoE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;UACAI,WAAW,CAAC,CAAC;QACjB;MACJ,CAAC;MACDT,MAAM,CAACU,aAAa,CAACpE,aAAa,CAAC;IACvC,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACZG,OAAO,CAACH,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCtD,QAAQ,CAACZ,eAAe,CAAC,uBAAuB,IAAIkE,KAAK,CAACK,OAAO,IAAI,gBAAgB,CAAC,CAAC,CAAC;IAC5F,CAAC,SAAS;MACN7C,cAAc,CAAC,KAAK,CAAC;IACzB;EACJ,CAAC;EAED,MAAMwD,WAAW,GAAGA,CAAA,KAAM;IACtBlE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrBE,UAAU,CAAC,EAAE,CAAC;IACd,IAAIU,YAAY,CAACe,OAAO,EAAE;MACtBf,YAAY,CAACe,OAAO,CAACuC,KAAK,GAAG,EAAE;IACnC;EACJ,CAAC;EAED,IAAI,CAACvE,MAAM,EAAE;IACT,oBACIT,OAAA;MACIiF,OAAO,EAAEA,CAAA,KAAMvE,SAAS,CAAC,IAAI,CAAE;MAC/BH,SAAS,+MAAA2E,MAAA,CAA+M3E,SAAS,CAAG;MAAA4E,QAAA,gBAEpOnF,OAAA,CAACV,QAAQ;QAACiB,SAAS,EAAC;MAAS;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,aAEpC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAEjB;EAEA,oBACIvF,OAAA;IAAKO,SAAS,8DAAA2E,MAAA,CAA8D3E,SAAS,CAAG;IAAA4E,QAAA,gBAEpFnF,OAAA;MAAKO,SAAS,EAAC,wCAAwC;MAAA4E,QAAA,gBACnDnF,OAAA;QAAKO,SAAS,EAAC,yBAAyB;QAAA4E,QAAA,gBACpCnF,OAAA,CAACV,QAAQ;UAACiB,SAAS,EAAC;QAAyB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDvF,OAAA;UAAIO,SAAS,EAAC,qCAAqC;UAAA4E,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eACNvF,OAAA;QACIiF,OAAO,EAAEA,CAAA,KAAMvE,SAAS,CAAC,KAAK,CAAE;QAChCH,SAAS,EAAC,iDAAiD;QAAA4E,QAAA,eAE3DnF,OAAA,CAACT,CAAC;UAACgB,SAAS,EAAC;QAAuB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNvF,OAAA;MACIwF,GAAG,EAAE/D,SAAU;MACfgE,QAAQ,EAAE,CAAE;MACZlF,SAAS,4EAAA2E,MAAA,CACL3D,UAAU,GACJ,4BAA4B,GAC5B,oEAAoE,CAC3E;MACHmE,UAAU,EAAEzC,cAAe;MAC3B0C,WAAW,EAAEvC,eAAgB;MAC7BwC,MAAM,EAAEvC,UAAW;MAAA8B,QAAA,EAElB,CAACtE,YAAY,gBACVb,OAAA;QAAKO,SAAS,EAAC,aAAa;QAAA4E,QAAA,eACxBnF,OAAA;UAAKO,SAAS,EAAC,sCAAsC;UAAA4E,QAAA,gBACjDnF,OAAA;YAAKO,SAAS,EAAC,8BAA8B;YAAA4E,QAAA,eACzCnF,OAAA,CAACX,SAAS;cAACkB,SAAS,EAAC;YAAuB;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNvF,OAAA;YAAKO,SAAS,EAAC,WAAW;YAAA4E,QAAA,gBACtBnF,OAAA;cAAGO,SAAS,EAAC,mCAAmC;cAAA4E,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJvF,OAAA;cAAGO,SAAS,EAAC,uBAAuB;cAAA4E,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvF,OAAA;YACIiF,OAAO,EAAEA,CAAA;cAAA,IAAAY,qBAAA;cAAA,QAAAA,qBAAA,GAAMnE,YAAY,CAACe,OAAO,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CvF,SAAS,EAAC,gHAAgH;YAAA4E,QAAA,gBAE1HnF,OAAA,CAACb,MAAM;cAACoB,SAAS,EAAC;YAAS;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,gBAENvF,OAAA;QAAKO,SAAS,EAAC,WAAW;QAAA4E,QAAA,gBAEtBnF,OAAA;UAAKO,SAAS,EAAC,UAAU;UAAA4E,QAAA,gBACrBnF,OAAA;YACI+F,GAAG,EAAElF,YAAa;YAClBmF,GAAG,EAAC,SAAS;YACbzF,SAAS,EAAC;UAAkD;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACFvF,OAAA;YACIiF,OAAO,EAAEH,WAAY;YACrBvE,SAAS,EAAC,kGAAkG;YAAA4E,QAAA,eAE5GnF,OAAA,CAACT,CAAC;cAACgB,SAAS,EAAC;YAAS;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGNvF,OAAA;UAAKO,SAAS,EAAC,2BAA2B;UAAA4E,QAAA,gBACtCnF,OAAA;YACIiF,OAAO,EAAEvB,UAAW;YACpBuC,QAAQ,EAAE9E,YAAa;YACvBZ,SAAS,EAAC,kKAAkK;YAAA4E,QAAA,EAE3KhE,YAAY,gBACTnB,OAAA,CAAAE,SAAA;cAAAiF,QAAA,gBACInF,OAAA;gBAAKO,SAAS,EAAC;cAA8E;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAEpG;YAAA,eAAE,CAAC,gBAEHvF,OAAA,CAAAE,SAAA;cAAAiF,QAAA,gBACInF,OAAA,CAACR,GAAG;gBAACe,SAAS,EAAC;cAAS;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,OAE/B;YAAA,eAAE;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACTvF,OAAA;YACIiF,OAAO,EAAEb,qBAAsB;YAC/B6B,QAAQ,EAAE5E,WAAY;YACtBd,SAAS,EAAC,gKAAgK;YAAA4E,QAAA,EAEzK9D,WAAW,gBACRrB,OAAA,CAAAE,SAAA;cAAAiF,QAAA,gBACInF,OAAA;gBAAKO,SAAS,EAAC;cAA8E;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEpG;YAAA,eAAE,CAAC,gBAEHvF,OAAA,CAAAE,SAAA;cAAAiF,QAAA,gBACInF,OAAA,CAACP,IAAI;gBAACc,SAAS,EAAC;cAAS;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEhC;YAAA,eAAE;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGLxE,OAAO,iBACJf,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAA4E,QAAA,gBAEtBnF,OAAA;QAAAmF,QAAA,gBACInF,OAAA;UAAOO,SAAS,EAAC,8CAA8C;UAAA4E,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRvF,OAAA;UACIgF,KAAK,EAAEjE,OAAQ;UACfmF,QAAQ,EAAG1B,CAAC,IAAKxD,UAAU,CAACwD,CAAC,CAACf,MAAM,CAACuB,KAAK,CAAE;UAC5CzE,SAAS,EAAC,yHAAyH;UACnI4F,WAAW,EAAC;QAAqC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvF,OAAA;QAAAmF,QAAA,gBACInF,OAAA;UAAOO,SAAS,EAAC,8CAA8C;UAAA4E,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRvF,OAAA;UAAKO,SAAS,EAAC,mFAAmF;UAAA4E,QAAA,eAC9FnF,OAAA,CAACN,aAAa;YAAC0G,IAAI,EAAErF;UAAQ;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAGDvF,OAAA;MACIwF,GAAG,EAAE9D,YAAa;MAClBS,IAAI,EAAC,MAAM;MACXkE,MAAM,EAAC,gCAAgC;MACvCH,QAAQ,EAAE1C,gBAAiB;MAC3BjD,SAAS,EAAC;IAAQ;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAClF,EAAA,CArUIF,QAAQ;EAAA,QACOlB,WAAW;AAAA;AAAAqH,EAAA,GAD1BnG,QAAQ;AAuUd,eAAeA,QAAQ;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}