/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import {
  catchUnrecognizedEnum,
  OpenEnum,
  Unrecognized,
} from "../../types/enums.js";

/**
 * The type of entity, used to share a library.
 */
export const EntityType = {
  User: "User",
  Workspace: "Workspace",
  Org: "Org",
} as const;
/**
 * The type of entity, used to share a library.
 */
export type EntityType = OpenEnum<typeof EntityType>;

/** @internal */
export const EntityType$inboundSchema: z.ZodType<
  EntityType,
  z.ZodTypeDef,
  unknown
> = z
  .union([
    z.nativeEnum(EntityType),
    z.string().transform(catchUnrecognizedEnum),
  ]);

/** @internal */
export const EntityType$outboundSchema: z.ZodType<
  EntityType,
  z.ZodTypeDef,
  EntityType
> = z.union([
  z.nativeEnum(EntityType),
  z.string().and(z.custom<Unrecognized<string>>()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace EntityType$ {
  /** @deprecated use `EntityType$inboundSchema` instead. */
  export const inboundSchema = EntityType$inboundSchema;
  /** @deprecated use `EntityType$outboundSchema` instead. */
  export const outboundSchema = EntityType$outboundSchema;
}
