import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchClassesByUserId } from "../../features/class/classSlice";
import LoadingData from "src/components/loading/LoadingData";
import { useNavigate } from "react-router-dom";
import { fetchCodesByType } from "../../features/code/codeSlice";

const ClassOfUserTable = ({ userId, onKickFromClass }) => {
    const dispatch = useDispatch();
    const { classes } = useSelector((state) => state.classes); // fetchClassesByUserId stores data in classes array
    const { loading } = useSelector(state => state.states);
    const { codes } = useSelector(state => state.codes);
    const navigate = useNavigate();

    const handleClick = (classId) => {
        navigate(`/admin/class-management/${classId}`)
    }

    useEffect(() => {
        dispatch(fetchCodesByType(["dow", "class status"]));
    }, [dispatch]);

    useEffect(() => {
        if (userId) {
            dispatch(fetchClassesByUserId(userId));
        }
    }, [dispatch, userId]);

    return (
        <div className="flex flex-col gap-4 min-h-0 text-sm">
            <div className="flex justify-start items-center">
                {classes && classes.length > 0 && (
                    <div className="flex justify-between w-full items-center">
                        <div className="flex justify-center items-center gap-2">
                            <p className="text-right text-gray-500">
                                Tổng số {classes.length} lớp học
                            </p>
                        </div>
                    </div>
                )}
            </div>

            <div className="flex-grow h-[60vh] overflow-y-auto hide-scrollbar">
                <LoadingData
                    loading={loading}
                    isNoData={classes.length > 0 ? false : true}
                    loadText="Đang tải danh sách lớp học"
                    noDataText="Không có lớp học nào."
                >
                    <table className="w-full border-collapse border border-[#E7E7ED]">
                        <thead className="bg-[#F6FAFD]">
                            <tr className="border border-[#E7E7ED]">
                                <th className="p-3 text-center">ID</th>
                                <th className="p-3 text-center">Mã lớp</th>
                                <th className="p-3 text-center">Tên lớp</th>
                                <th className="p-3 text-center">Thứ B1</th>
                                <th className="p-3 text-center">Thứ B2</th>
                                <th className="p-3 text-center">Thời gian B1</th>
                                <th className="p-3 text-center">Thời gian B2</th>
                                <th className="p-3 text-center">Năm học</th>
                                <th className="p-3 text-center">Trạng thái lớp</th>
                                <th className="p-3 text-center">Trạng thái tham gia</th>
                                <th className="p-3 text-center">Ngày tham gia</th>
                                <th className="p-3 text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {classes.map((item, index) => (
                                <tr key={index}
                                    onClick={() => handleClick(item.id)}
                                    className="border border-[#E7E7ED] hover:bg-gray-50 cursor-pointer">
                                    <td className="p-3 text-center">{item.id}</td>
                                    <td className="p-3 text-center">{item.class_code}</td>
                                    <td className="p-3 text-center">{item.name}</td>
                                    <td className="p-3 text-center">
                                        {item.dayOfWeek1 ?
                                            codes["dow"]?.find(code => code.code === item.dayOfWeek1)?.description || item.dayOfWeek1
                                            : '-'
                                        }
                                    </td>
                                    <td className="p-3 text-center">
                                        {item.dayOfWeek2 ?
                                            codes["dow"]?.find(code => code.code === item.dayOfWeek2)?.description || item.dayOfWeek2
                                            : '-'
                                        }
                                    </td>
                                    <td className="p-3 text-center">
                                        {item.startTime1 && item.endTime1 ? `${item.startTime1} - ${item.endTime1}` : '-'}
                                    </td>
                                    <td className="p-3 text-center">
                                        {item.startTime2 && item.endTime2 ? `${item.startTime2} - ${item.endTime2}` : '-'}
                                    </td>
                                    <td className="p-3 text-center">{item.academicYear}</td>
                                    <td className="p-3 text-center">
                                        {codes["class status"]?.find(code => code.code === item.status)?.description || item.status}
                                    </td>
                                    <td className={`p-3 text-center ${item.studentClassStatus === 'JS' ? "text-green-600" :
                                        item.studentClassStatus === 'WS' ? "text-yellow-600" :
                                            "text-red-600"
                                        }`}>
                                        {item.studentClassStatus === 'JS' ? 'Đã tham gia' :
                                            item.studentClassStatus === 'WS' ? 'Chờ phê duyệt' :
                                                'Không xác định'}
                                    </td>
                                    <td className="p-3 text-center">
                                        {item.joinedAt ? new Date(item.joinedAt).toLocaleDateString() :
                                            item.createdAt ? new Date(item.createdAt).toLocaleDateString() : '-'}
                                    </td>
                                    <td className="p-3 text-center">
                                        <div className="flex justify-center gap-2">
                                            {item.studentClassStatus === 'JS' && onKickFromClass && (
                                                <button
                                                    onClick={(e) => {
                                                        e.stopPropagation(); // Prevent row click
                                                        onKickFromClass(item.id, item.name);
                                                    }}
                                                    className="text-red-600 hover:text-red-800 underline text-sm"
                                                >
                                                    Rời khỏi lớp
                                                </button>
                                            )}
                                            {item.studentClassStatus !== 'JS' && (
                                                <span className="text-gray-400 text-sm">-</span>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </LoadingData>
            </div>
        </div>
    )
}

export default ClassOfUserTable;
