import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchAchievementDataForHomepage } from '../../../features/achievement/achievementSlice';
import { Award, Trophy, Medal, Crown, Star, ArrowLeft, Calendar, Users, Target, TrendingUp } from 'lucide-react';
import SlideShow from '../../../components/image/SlideShow';
import LoadingSpinner from '../../../components/loading/LoadingSpinner';
import UserLayoutHome from '../../../layouts/UserLayoutHome';

const AllAchievementsPage = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { homepageData, homepageLoading } = useSelector(state => state.achievements);
    const [selectedCategory, setSelectedCategory] = useState(null);

    // Fetch achievement data when component mounts
    useEffect(() => {
        dispatch(fetchAchievementDataForHomepage());
    }, [dispatch]);

    // Set first category as selected when data loads
    useEffect(() => {
        if (homepageData && homepageData.length > 0 && !selectedCategory) {
            setSelectedCategory(homepageData[0]);
        }
    }, [homepageData, selectedCategory]);

    // Get images and captions for a category
    const getCategoryImagesData = (category) => {
        if (!category || !category.images) return { images: [], captions: [] };

        const images = category.images.map(image => image.image_url);
        const captions = category.images.map(image =>
            image.caption ? image.caption.replace(/\\n/g, '\n') : ""
        );

        return { images, captions };
    };

    if (homepageLoading) {
        return (
            <UserLayoutHome>
                <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center">
                    <div className="text-center">
                        <div className="inline-block px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-4">
                            Thành tích học sinh
                        </div>
                        <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-8">
                            Đang tải dữ liệu thành tích...
                        </h2>
                        <LoadingSpinner size="4rem" showText={false} />
                    </div>
                </div>
            </UserLayoutHome>
        );
    }

    if (!homepageData || homepageData.length === 0) {
        return (
            <UserLayoutHome>
                <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center">
                    <div className="text-center">
                        <div className="inline-block px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-4">
                            Thành tích học sinh
                        </div>
                        <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4 flex items-center justify-center gap-3">
                            <Award className="w-8 h-8 text-amber-600" />
                            Thành tích xuất sắc
                        </h2>
                        <p className="text-gray-600 max-w-2xl mx-auto text-lg mb-8">
                            Chưa có dữ liệu thành tích. Vui lòng quay lại sau.
                        </p>
                        <motion.button
                            onClick={() => navigate('/')}
                            className="px-6 py-3 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-full hover:from-amber-600 hover:to-orange-600 transition-all duration-300 flex items-center gap-2 mx-auto shadow-lg hover:shadow-xl font-semibold"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <ArrowLeft size={20} />
                            Quay về trang chủ
                        </motion.button>
                    </div>
                </div>
            </UserLayoutHome>
        );
    }

    return (
        <UserLayoutHome>
            <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50">
                {/* Header */}
                <section className="w-full px-4 py-16">
                    <div className="max-w-screen-xl mx-auto">
                        <motion.div
                            className="text-center mb-12"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            {/* Back Button */}
                            <motion.button
                                onClick={() => navigate('/')}
                                className="inline-flex items-center gap-2 px-4 py-2 text-amber-700 hover:text-amber-800 transition-colors duration-300 mb-6"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.5 }}
                                whileHover={{ x: -5 }}
                            >
                                <ArrowLeft size={20} />
                                Quay về trang chủ
                            </motion.button>

                            <motion.div
                                className="inline-block px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-4"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                            >
                                Thành tích học sinh
                            </motion.div>

                            <motion.h1
                                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 font-cubano mb-6 flex items-center justify-center gap-4"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                            >
                                <Trophy className="w-12 h-12 text-amber-600" />
                                Tất cả thành tích
                            </motion.h1>

                            <motion.p
                                className="text-gray-600 max-w-3xl mx-auto text-lg lg:text-xl"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.5 }}
                            >
                                Khám phá toàn bộ thành tích xuất sắc của học sinh lớp Toán thầy Bee qua các kỳ thi quan trọng.
                                Những kết quả đáng tự hào là minh chứng cho chất lượng giảng dạy và sự nỗ lực của các em học sinh.
                            </motion.p>
                        </motion.div>
                    </div>
                </section>

                {/* Categories Grid */}
                <section className="w-full px-4 pb-16">
                    <div className="max-w-screen-xl mx-auto">
                        {/* Categories Navigation */}
                        <motion.div
                            className="flex flex-wrap justify-center gap-4 mb-16"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.7 }}
                        >
                            {homepageData.map((category, index) => (
                                <motion.button
                                    key={category.id}
                                    onClick={() => setSelectedCategory(category)}
                                    className={`px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg ${
                                        selectedCategory?.id === category.id
                                            ? "bg-gradient-to-r from-amber-500 to-orange-500 text-white scale-105"
                                            : "bg-white text-amber-700 hover:bg-amber-50 border border-amber-200"
                                    }`}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.4, delay: 0.8 + index * 0.1 }}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    {category.label}
                                </motion.button>
                            ))}
                        </motion.div>

                        {/* Selected Category Detail */}
                        {selectedCategory && (
                            <motion.div
                                key={selectedCategory.id}
                                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start mb-16"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                {/* Left - Images */}
                                <motion.div
                                    initial={{ opacity: 0, x: -50 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.2 }}
                                >
                                    <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-amber-100">
                                        <div className="absolute inset-0 bg-gradient-to-br from-amber-50/20 to-orange-50/20 z-10 pointer-events-none"></div>

                                        <div className="absolute top-6 left-6 w-16 h-16 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center z-20 shadow-lg">
                                            <Trophy className="w-8 h-8 text-white" />
                                        </div>

                                        <div className="absolute top-6 right-6 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
                                            {getCategoryImagesData(selectedCategory).images.length > 0 ?
                                                `1 / ${getCategoryImagesData(selectedCategory).images.length}` : '0 / 0'}
                                        </div>

                                        <div className="w-full aspect-square">
                                            <SlideShow
                                                key={selectedCategory.id}
                                                interval={4000}
                                                images={getCategoryImagesData(selectedCategory).images}
                                                captions={getCategoryImagesData(selectedCategory).captions}
                                                h="h-full"
                                                objectFit='object-contain'
                                            />
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Right - Content */}
                                <motion.div
                                    className="space-y-8"
                                    initial={{ opacity: 0, x: 50 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.4 }}
                                >
                                    <div>
                                        <h2 className="text-3xl lg:text-4xl font-bold text-amber-600 mb-4">
                                            {selectedCategory.title}
                                        </h2>
                                        <p className="text-gray-700 text-lg leading-relaxed whitespace-pre-line">
                                            {selectedCategory.description ? selectedCategory.description.replace(/\\n/g, '\n') : ""}
                                        </p>
                                    </div>

                                    {/* Stats Grid */}
                                    {selectedCategory.stats && selectedCategory.stats.length > 0 && (
                                        <div className="grid grid-cols-2 gap-4">
                                            {selectedCategory.stats.map((stat, index) => (
                                                <motion.div
                                                    key={index}
                                                    className="bg-white p-6 rounded-xl shadow-lg border border-amber-100 hover:shadow-xl transition-all duration-300"
                                                    initial={{ opacity: 0, scale: 0.9 }}
                                                    animate={{ opacity: 1, scale: 1 }}
                                                    transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                                                    whileHover={{ scale: 1.05 }}
                                                >
                                                    <div className="text-3xl font-bold text-amber-500 mb-2">{stat.value}</div>
                                                    <div className="text-gray-600 text-sm font-medium">{stat.label}</div>
                                                </motion.div>
                                            ))}
                                        </div>
                                    )}
                                </motion.div>
                            </motion.div>
                        )}

                        {/* All Categories Overview */}
                        <motion.div
                            className="mt-20"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 1.0 }}
                        >
                            <div className="text-center mb-12">
                                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4 flex items-center justify-center gap-3">
                                    <Medal className="w-8 h-8 text-amber-600" />
                                    Tổng quan thành tích
                                </h2>
                                <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                                    Xem tổng quan tất cả các danh mục thành tích của học sinh qua các năm học.
                                </p>
                            </div>

                            {/* Categories Grid */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {homepageData.map((category, index) => (
                                    <motion.div
                                        key={category.id}
                                        className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-amber-100 group cursor-pointer"
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: 1.2 + index * 0.1 }}
                                        whileHover={{ scale: 1.02, y: -5 }}
                                        onClick={() => setSelectedCategory(category)}
                                    >
                                        {/* Image Section */}
                                        <div className="relative h-48 overflow-hidden">
                                            <div className="absolute inset-0 bg-gradient-to-br from-amber-50/20 to-orange-50/20 z-10"></div>

                                            {/* Category Icon */}
                                            <div className="absolute top-4 left-4 w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center z-20 shadow-lg">
                                                <Trophy className="w-6 h-6 text-white" />
                                            </div>

                                            {/* Image Count */}
                                            <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded-full text-xs font-medium z-20">
                                                {getCategoryImagesData(category).images.length} ảnh
                                            </div>

                                            {/* Preview Image */}
                                            {getCategoryImagesData(category).images.length > 0 ? (
                                                <img
                                                    src={getCategoryImagesData(category).images[0]}
                                                    alt={category.title}
                                                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                                />
                                            ) : (
                                                <div className="w-full h-full bg-gradient-to-br from-amber-100 to-orange-100 flex items-center justify-center">
                                                    <Trophy className="w-16 h-16 text-amber-400" />
                                                </div>
                                            )}
                                        </div>

                                        {/* Content Section */}
                                        <div className="p-6">
                                            <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-amber-600 transition-colors duration-300">
                                                {category.title}
                                            </h3>
                                            <p className="text-gray-600 text-sm leading-relaxed mb-4" style={{
                                                display: '-webkit-box',
                                                WebkitLineClamp: 3,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden'
                                            }}>
                                                {category.description ? category.description.replace(/\\n/g, ' ').substring(0, 120) + '...' : ""}
                                            </p>

                                            {/* Stats Preview */}
                                            {category.stats && category.stats.length > 0 && (
                                                <div className="flex flex-wrap gap-2 mb-4">
                                                    {category.stats.slice(0, 2).map((stat, statIndex) => (
                                                        <div key={statIndex} className="bg-amber-50 px-3 py-1 rounded-full">
                                                            <span className="text-amber-600 font-semibold text-sm">{stat.value}</span>
                                                            <span className="text-gray-600 text-xs ml-1">{stat.label}</span>
                                                        </div>
                                                    ))}
                                                    {category.stats.length > 2 && (
                                                        <div className="bg-gray-100 px-3 py-1 rounded-full">
                                                            <span className="text-gray-600 text-xs">+{category.stats.length - 2} khác</span>
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            {/* View Button */}
                                            <motion.button
                                                className="w-full px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-lg hover:from-amber-600 hover:to-orange-600 transition-all duration-300 text-sm font-semibold shadow-md hover:shadow-lg"
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setSelectedCategory(category);
                                                    // Scroll to selected category
                                                    window.scrollTo({ top: 0, behavior: 'smooth' });
                                                }}
                                            >
                                                Xem chi tiết
                                            </motion.button>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </motion.div>

                        {/* Statistics Summary */}
                        <motion.div
                            className="mt-20 bg-gradient-to-r from-amber-500 to-orange-500 rounded-2xl p-8 text-white"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 1.5 }}
                        >
                            <div className="text-center mb-8">
                                <h2 className="text-3xl font-bold mb-4 flex items-center justify-center gap-3">
                                    <Crown className="w-8 h-8" />
                                    Tổng kết thành tích
                                </h2>
                                <p className="text-amber-100 text-lg">
                                    Những con số ấn tượng từ hành trình học tập của các em học sinh
                                </p>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                                <motion.div
                                    className="text-center"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 1.7 }}
                                >
                                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <Trophy className="w-8 h-8 text-white" />
                                    </div>
                                    <div className="text-3xl font-bold mb-1">{homepageData.length}</div>
                                    <div className="text-amber-100 text-sm">Danh mục thành tích</div>
                                </motion.div>

                                <motion.div
                                    className="text-center"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 1.8 }}
                                >
                                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <Medal className="w-8 h-8 text-white" />
                                    </div>
                                    <div className="text-3xl font-bold mb-1">
                                        {homepageData.reduce((total, category) => total + (getCategoryImagesData(category).images.length || 0), 0)}
                                    </div>
                                    <div className="text-amber-100 text-sm">Hình ảnh thành tích</div>
                                </motion.div>

                                <motion.div
                                    className="text-center"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 1.9 }}
                                >
                                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <Users className="w-8 h-8 text-white" />
                                    </div>
                                    <div className="text-3xl font-bold mb-1">500+</div>
                                    <div className="text-amber-100 text-sm">Học sinh xuất sắc</div>
                                </motion.div>

                                <motion.div
                                    className="text-center"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 2.0 }}
                                >
                                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <Star className="w-8 h-8 text-white" />
                                    </div>
                                    <div className="text-3xl font-bold mb-1">5+</div>
                                    <div className="text-amber-100 text-sm">Năm kinh nghiệm</div>
                                </motion.div>
                            </div>
                        </motion.div>
                    </div>
                </section>
            </div>
        </UserLayoutHome>
    );
};

export default AllAchievementsPage;
