/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  MessageOutputContentChunks,
  MessageOutputContentChunks$inboundSchema,
  MessageOutputContentChunks$Outbound,
  MessageOutputContentChunks$outboundSchema,
} from "./messageoutputcontentchunks.js";

export const MessageOutputEntryObject = {
  Entry: "entry",
} as const;
export type MessageOutputEntryObject = ClosedEnum<
  typeof MessageOutputEntryObject
>;

export const MessageOutputEntryType = {
  MessageOutput: "message.output",
} as const;
export type MessageOutputEntryType = ClosedEnum<typeof MessageOutputEntryType>;

export const MessageOutputEntryRole = {
  Assistant: "assistant",
} as const;
export type MessageOutputEntryRole = ClosedEnum<typeof MessageOutputEntryRole>;

export type MessageOutputEntryContent =
  | string
  | Array<MessageOutputContentChunks>;

export type MessageOutputEntry = {
  object?: MessageOutputEntryObject | undefined;
  type?: MessageOutputEntryType | undefined;
  createdAt?: Date | undefined;
  completedAt?: Date | null | undefined;
  id?: string | undefined;
  agentId?: string | null | undefined;
  model?: string | null | undefined;
  role?: MessageOutputEntryRole | undefined;
  content: string | Array<MessageOutputContentChunks>;
};

/** @internal */
export const MessageOutputEntryObject$inboundSchema: z.ZodNativeEnum<
  typeof MessageOutputEntryObject
> = z.nativeEnum(MessageOutputEntryObject);

/** @internal */
export const MessageOutputEntryObject$outboundSchema: z.ZodNativeEnum<
  typeof MessageOutputEntryObject
> = MessageOutputEntryObject$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageOutputEntryObject$ {
  /** @deprecated use `MessageOutputEntryObject$inboundSchema` instead. */
  export const inboundSchema = MessageOutputEntryObject$inboundSchema;
  /** @deprecated use `MessageOutputEntryObject$outboundSchema` instead. */
  export const outboundSchema = MessageOutputEntryObject$outboundSchema;
}

/** @internal */
export const MessageOutputEntryType$inboundSchema: z.ZodNativeEnum<
  typeof MessageOutputEntryType
> = z.nativeEnum(MessageOutputEntryType);

/** @internal */
export const MessageOutputEntryType$outboundSchema: z.ZodNativeEnum<
  typeof MessageOutputEntryType
> = MessageOutputEntryType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageOutputEntryType$ {
  /** @deprecated use `MessageOutputEntryType$inboundSchema` instead. */
  export const inboundSchema = MessageOutputEntryType$inboundSchema;
  /** @deprecated use `MessageOutputEntryType$outboundSchema` instead. */
  export const outboundSchema = MessageOutputEntryType$outboundSchema;
}

/** @internal */
export const MessageOutputEntryRole$inboundSchema: z.ZodNativeEnum<
  typeof MessageOutputEntryRole
> = z.nativeEnum(MessageOutputEntryRole);

/** @internal */
export const MessageOutputEntryRole$outboundSchema: z.ZodNativeEnum<
  typeof MessageOutputEntryRole
> = MessageOutputEntryRole$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageOutputEntryRole$ {
  /** @deprecated use `MessageOutputEntryRole$inboundSchema` instead. */
  export const inboundSchema = MessageOutputEntryRole$inboundSchema;
  /** @deprecated use `MessageOutputEntryRole$outboundSchema` instead. */
  export const outboundSchema = MessageOutputEntryRole$outboundSchema;
}

/** @internal */
export const MessageOutputEntryContent$inboundSchema: z.ZodType<
  MessageOutputEntryContent,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(MessageOutputContentChunks$inboundSchema)]);

/** @internal */
export type MessageOutputEntryContent$Outbound =
  | string
  | Array<MessageOutputContentChunks$Outbound>;

/** @internal */
export const MessageOutputEntryContent$outboundSchema: z.ZodType<
  MessageOutputEntryContent$Outbound,
  z.ZodTypeDef,
  MessageOutputEntryContent
> = z.union([z.string(), z.array(MessageOutputContentChunks$outboundSchema)]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageOutputEntryContent$ {
  /** @deprecated use `MessageOutputEntryContent$inboundSchema` instead. */
  export const inboundSchema = MessageOutputEntryContent$inboundSchema;
  /** @deprecated use `MessageOutputEntryContent$outboundSchema` instead. */
  export const outboundSchema = MessageOutputEntryContent$outboundSchema;
  /** @deprecated use `MessageOutputEntryContent$Outbound` instead. */
  export type Outbound = MessageOutputEntryContent$Outbound;
}

export function messageOutputEntryContentToJSON(
  messageOutputEntryContent: MessageOutputEntryContent,
): string {
  return JSON.stringify(
    MessageOutputEntryContent$outboundSchema.parse(messageOutputEntryContent),
  );
}

export function messageOutputEntryContentFromJSON(
  jsonString: string,
): SafeParseResult<MessageOutputEntryContent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MessageOutputEntryContent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MessageOutputEntryContent' from JSON`,
  );
}

/** @internal */
export const MessageOutputEntry$inboundSchema: z.ZodType<
  MessageOutputEntry,
  z.ZodTypeDef,
  unknown
> = z.object({
  object: MessageOutputEntryObject$inboundSchema.default("entry"),
  type: MessageOutputEntryType$inboundSchema.default("message.output"),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  completed_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  id: z.string().optional(),
  agent_id: z.nullable(z.string()).optional(),
  model: z.nullable(z.string()).optional(),
  role: MessageOutputEntryRole$inboundSchema.default("assistant"),
  content: z.union([
    z.string(),
    z.array(MessageOutputContentChunks$inboundSchema),
  ]),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "completed_at": "completedAt",
    "agent_id": "agentId",
  });
});

/** @internal */
export type MessageOutputEntry$Outbound = {
  object: string;
  type: string;
  created_at?: string | undefined;
  completed_at?: string | null | undefined;
  id?: string | undefined;
  agent_id?: string | null | undefined;
  model?: string | null | undefined;
  role: string;
  content: string | Array<MessageOutputContentChunks$Outbound>;
};

/** @internal */
export const MessageOutputEntry$outboundSchema: z.ZodType<
  MessageOutputEntry$Outbound,
  z.ZodTypeDef,
  MessageOutputEntry
> = z.object({
  object: MessageOutputEntryObject$outboundSchema.default("entry"),
  type: MessageOutputEntryType$outboundSchema.default("message.output"),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  id: z.string().optional(),
  agentId: z.nullable(z.string()).optional(),
  model: z.nullable(z.string()).optional(),
  role: MessageOutputEntryRole$outboundSchema.default("assistant"),
  content: z.union([
    z.string(),
    z.array(MessageOutputContentChunks$outboundSchema),
  ]),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    completedAt: "completed_at",
    agentId: "agent_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageOutputEntry$ {
  /** @deprecated use `MessageOutputEntry$inboundSchema` instead. */
  export const inboundSchema = MessageOutputEntry$inboundSchema;
  /** @deprecated use `MessageOutputEntry$outboundSchema` instead. */
  export const outboundSchema = MessageOutputEntry$outboundSchema;
  /** @deprecated use `MessageOutputEntry$Outbound` instead. */
  export type Outbound = MessageOutputEntry$Outbound;
}

export function messageOutputEntryToJSON(
  messageOutputEntry: MessageOutputEntry,
): string {
  return JSON.stringify(
    MessageOutputEntry$outboundSchema.parse(messageOutputEntry),
  );
}

export function messageOutputEntryFromJSON(
  jsonString: string,
): SafeParseResult<MessageOutputEntry, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MessageOutputEntry$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MessageOutputEntry' from JSON`,
  );
}
