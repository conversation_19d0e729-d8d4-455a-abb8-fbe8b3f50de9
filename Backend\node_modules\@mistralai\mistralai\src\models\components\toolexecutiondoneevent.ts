/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BuiltInConnectors,
  BuiltInConnectors$inboundSchema,
  BuiltInConnectors$outboundSchema,
} from "./builtinconnectors.js";

export const ToolExecutionDoneEventType = {
  ToolExecutionDone: "tool.execution.done",
} as const;
export type ToolExecutionDoneEventType = ClosedEnum<
  typeof ToolExecutionDoneEventType
>;

export type ToolExecutionDoneEvent = {
  type?: ToolExecutionDoneEventType | undefined;
  createdAt?: Date | undefined;
  outputIndex?: number | undefined;
  id: string;
  name: BuiltInConnectors;
  info?: { [k: string]: any } | undefined;
};

/** @internal */
export const ToolExecutionDoneEventType$inboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionDoneEventType
> = z.nativeEnum(ToolExecutionDoneEventType);

/** @internal */
export const ToolExecutionDoneEventType$outboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionDoneEventType
> = ToolExecutionDoneEventType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionDoneEventType$ {
  /** @deprecated use `ToolExecutionDoneEventType$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionDoneEventType$inboundSchema;
  /** @deprecated use `ToolExecutionDoneEventType$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionDoneEventType$outboundSchema;
}

/** @internal */
export const ToolExecutionDoneEvent$inboundSchema: z.ZodType<
  ToolExecutionDoneEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ToolExecutionDoneEventType$inboundSchema.default("tool.execution.done"),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  output_index: z.number().int().default(0),
  id: z.string(),
  name: BuiltInConnectors$inboundSchema,
  info: z.record(z.any()).optional(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "output_index": "outputIndex",
  });
});

/** @internal */
export type ToolExecutionDoneEvent$Outbound = {
  type: string;
  created_at?: string | undefined;
  output_index: number;
  id: string;
  name: string;
  info?: { [k: string]: any } | undefined;
};

/** @internal */
export const ToolExecutionDoneEvent$outboundSchema: z.ZodType<
  ToolExecutionDoneEvent$Outbound,
  z.ZodTypeDef,
  ToolExecutionDoneEvent
> = z.object({
  type: ToolExecutionDoneEventType$outboundSchema.default(
    "tool.execution.done",
  ),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  outputIndex: z.number().int().default(0),
  id: z.string(),
  name: BuiltInConnectors$outboundSchema,
  info: z.record(z.any()).optional(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    outputIndex: "output_index",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionDoneEvent$ {
  /** @deprecated use `ToolExecutionDoneEvent$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionDoneEvent$inboundSchema;
  /** @deprecated use `ToolExecutionDoneEvent$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionDoneEvent$outboundSchema;
  /** @deprecated use `ToolExecutionDoneEvent$Outbound` instead. */
  export type Outbound = ToolExecutionDoneEvent$Outbound;
}

export function toolExecutionDoneEventToJSON(
  toolExecutionDoneEvent: ToolExecutionDoneEvent,
): string {
  return JSON.stringify(
    ToolExecutionDoneEvent$outboundSchema.parse(toolExecutionDoneEvent),
  );
}

export function toolExecutionDoneEventFromJSON(
  jsonString: string,
): SafeParseResult<ToolExecutionDoneEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolExecutionDoneEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolExecutionDoneEvent' from JSON`,
  );
}
