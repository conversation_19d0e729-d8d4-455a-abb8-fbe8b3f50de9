{"ast": null, "code": "var _jsxFileName = \"D:\\\\ToanThayBee\\\\frontend\\\\src\\\\components\\\\ai\\\\AIWidget.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Upload, Image as ImageIcon, Sparkles, X, Eye, Save } from 'lucide-react';\nimport LatexRenderer from '../latex/RenderLatex';\nimport { uploadBase64Images } from '../../features/image/imageSlice';\nimport { setErrorMessage, setSuccessMessage } from '../../features/state/stateApiSlice';\nimport { ocrImageWithMistralAPI } from '../../services/ocrExamApi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AIWidget = _ref => {\n  _s();\n  let {\n    onImageUploaded,\n    className = ''\n  } = _ref;\n  const dispatch = useDispatch();\n  const [isOpen, setIsOpen] = useState(false);\n  const [uploadedImage, setUploadedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [ocrText, setOcrText] = useState('');\n  const [ocrBase64Images, setOcrBase64Images] = useState([]); // Store base64 images from OCR response\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const [isDragging, setIsDragging] = useState(false);\n  const uploadRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Handle paste events\n  useEffect(() => {\n    const handlePaste = event => {\n      var _event$clipboardData;\n      if (!isOpen) return;\n      const items = (_event$clipboardData = event.clipboardData) === null || _event$clipboardData === void 0 ? void 0 : _event$clipboardData.items;\n      if (items) {\n        for (let i = 0; i < items.length; i++) {\n          const item = items[i];\n          if (item.type.indexOf(\"image\") !== -1) {\n            const file = item.getAsFile();\n            if (file) {\n              handleImageFile(file);\n            }\n          }\n        }\n      }\n    };\n    const uploadElement = uploadRef.current;\n    if (uploadElement) {\n      uploadElement.addEventListener(\"paste\", handlePaste);\n      return () => {\n        uploadElement.removeEventListener(\"paste\", handlePaste);\n      };\n    }\n  }, [isOpen]);\n  const validateImageFile = file => {\n    if (![\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)) {\n      dispatch(setErrorMessage(\"Chỉ cho phép định dạng JPEG hoặc PNG!\"));\n      return false;\n    }\n    if (file.size > 10 * 1024 * 1024) {\n      // 10MB limit\n      dispatch(setErrorMessage(\"Kích thước ảnh vượt quá 10MB!\"));\n      return false;\n    }\n    return true;\n  };\n  const handleImageFile = file => {\n    if (!validateImageFile(file)) return;\n    setUploadedImage(file);\n    setImagePreview(URL.createObjectURL(file));\n    setOcrText('');\n  };\n  const handleDragOver = event => {\n    event.preventDefault();\n    event.stopPropagation();\n    setIsDragging(true);\n  };\n  const handleDragLeave = event => {\n    event.preventDefault();\n    event.stopPropagation();\n    setIsDragging(false);\n  };\n  const handleDrop = event => {\n    event.preventDefault();\n    event.stopPropagation();\n    setIsDragging(false);\n    const files = event.dataTransfer.files;\n    if (files.length > 0) {\n      handleImageFile(files[0]);\n    }\n  };\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      handleImageFile(file);\n    }\n  };\n  const processOCR = async () => {\n    if (!uploadedImage) {\n      dispatch(setErrorMessage(\"Vui lòng chọn ảnh trước khi xử lý OCR\"));\n      return;\n    }\n    setIsProcessing(true);\n    try {\n      const response = await ocrImageWithMistralAPI(uploadedImage);\n      if (response && response.markdown) {\n        setOcrText(response.markdown);\n        // Store base64Images from OCR response for later upload\n        if (response.base64Images && response.base64Images.length > 0) {\n          setOcrBase64Images(response.base64Images);\n        }\n        dispatch(setSuccessMessage(\"OCR thành công!\"));\n      } else {\n        dispatch(setErrorMessage(\"Không thể trích xuất văn bản từ ảnh\"));\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('OCR Error:', error);\n      dispatch(setErrorMessage(\"Lỗi khi xử lý OCR: \" + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message)));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  const handleUploadToStorage = async () => {\n    if (!ocrBase64Images || ocrBase64Images.length === 0) {\n      dispatch(setErrorMessage(\"Không có ảnh từ OCR để tải lên. Vui lòng thực hiện OCR trước.\"));\n      return;\n    }\n    setIsUploading(true);\n    try {\n      // Upload base64 images from OCR response\n      const result = await dispatch(uploadBase64Images({\n        images: ocrBase64Images,\n        folder: \"questionImage\"\n      })).unwrap();\n      if (result && result.length > 0) {\n        dispatch(setSuccessMessage(\"Tải ảnh lên thành công!\"));\n        if (onImageUploaded) {\n          // Pass all uploaded image URLs\n          result.forEach(imageUrl => onImageUploaded(imageUrl));\n        }\n        handleReset();\n      }\n    } catch (error) {\n      console.error('Upload Error:', error);\n      dispatch(setErrorMessage(\"Lỗi khi tải ảnh lên: \" + (error.message || \"Không xác định\")));\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleReset = () => {\n    setUploadedImage(null);\n    setImagePreview(null);\n    setOcrText('');\n    setOcrBase64Images([]);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(true),\n      className: \"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-md hover:shadow-lg \".concat(className),\n      children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n        className: \"w-4 h-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this), \"AI Widget\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white border border-gray-200 rounded-lg shadow-lg p-4 \".concat(className),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n          className: \"w-5 h-5 text-purple-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"AI Widget - OCR & Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setIsOpen(false),\n        className: \"p-1 hover:bg-gray-100 rounded transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(X, {\n          className: \"w-4 h-4 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: uploadRef,\n      tabIndex: 0,\n      className: \"border-2 border-dashed rounded-lg p-6 mb-4 transition-all duration-200 \".concat(isDragging ? \"border-blue-400 bg-blue-50\" : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"),\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      children: !imagePreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-gray-200 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(ImageIcon, {\n              className: \"w-8 h-8 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"K\\xE9o th\\u1EA3 \\u1EA3nh ho\\u1EB7c Ctrl+V \\u0111\\u1EC3 d\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"H\\u1ED7 tr\\u1EE3 JPEG, PNG (t\\u1ED1i \\u0111a 10MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            className: \"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 33\n            }, this), \"Ch\\u1ECDn \\u1EA3nh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: imagePreview,\n            alt: \"Preview\",\n            className: \"max-w-full max-h-64 mx-auto rounded-lg shadow-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            className: \"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processOCR,\n            disabled: isProcessing,\n            className: \"inline-flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: isProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 41\n              }, this), \"\\u0110ang x\\u1EED l\\xFD...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Eye, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 41\n              }, this), \"OCR\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 29\n          }, this), ocrBase64Images.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleUploadToStorage,\n            disabled: isUploading,\n            className: \"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 45\n              }, this), \"\\u0110ang t\\u1EA3i...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Save, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 45\n              }, this), \"T\\u1EA3i l\\xEAn (\", ocrBase64Images.length, \" \\u1EA3nh)\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this), ocrText && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"V\\u0103n b\\u1EA3n tr\\xEDch xu\\u1EA5t (c\\xF3 th\\u1EC3 ch\\u1EC9nh s\\u1EEDa):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: ocrText,\n          onChange: e => setOcrText(e.target.value),\n          className: \"w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          placeholder: \"V\\u0103n b\\u1EA3n t\\u1EEB OCR s\\u1EBD hi\\u1EC3n th\\u1ECB \\u1EDF \\u0111\\xE2y...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Xem tr\\u01B0\\u1EDBc LaTeX:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full min-h-[100px] p-3 border border-gray-300 rounded-md bg-white overflow-auto\",\n          children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n            text: ocrText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      accept: \"image/jpeg,image/png,image/jpg\",\n      onChange: handleFileSelect,\n      className: \"hidden\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 9\n  }, this);\n};\n_s(AIWidget, \"neDb87x3ZVL/YakxHCHReUXd1a0=\", false, function () {\n  return [useDispatch];\n});\n_c = AIWidget;\nexport default AIWidget;\nvar _c;\n$RefreshReg$(_c, \"AIWidget\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDispatch", "useSelector", "Upload", "Image", "ImageIcon", "<PERSON><PERSON><PERSON>", "X", "Eye", "Save", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadBase64Images", "setErrorMessage", "setSuccessMessage", "ocrImageWithMistralAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AIWidget", "_ref", "_s", "onImageUploaded", "className", "dispatch", "isOpen", "setIsOpen", "uploadedImage", "setUploadedImage", "imagePreview", "setImagePreview", "ocrText", "setOcrText", "ocrBase64Images", "setOcrBase64Images", "isProcessing", "setIsProcessing", "isUploading", "setIsUploading", "isDragging", "setIsDragging", "uploadRef", "fileInputRef", "handlePaste", "event", "_event$clipboardData", "items", "clipboardData", "i", "length", "item", "type", "indexOf", "file", "getAsFile", "handleImageFile", "uploadElement", "current", "addEventListener", "removeEventListener", "validateImageFile", "includes", "size", "URL", "createObjectURL", "handleDragOver", "preventDefault", "stopPropagation", "handleDragLeave", "handleDrop", "files", "dataTransfer", "handleFileSelect", "target", "processOCR", "response", "markdown", "base64Images", "error", "_error$response", "_error$response$data", "console", "data", "message", "handleUploadToStorage", "result", "images", "folder", "unwrap", "for<PERSON>ach", "imageUrl", "handleReset", "value", "onClick", "concat", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "tabIndex", "onDragOver", "onDragLeave", "onDrop", "_fileInputRef$current", "click", "src", "alt", "disabled", "onChange", "e", "placeholder", "text", "accept", "_c", "$RefreshReg$"], "sources": ["D:/ToanThayBee/frontend/src/components/ai/AIWidget.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Upload, Image as ImageIcon, Sparkles, X, Eye, Save } from 'lucide-react';\nimport LatexRenderer from '../latex/RenderLatex';\nimport { uploadBase64Images } from '../../features/image/imageSlice';\nimport { setErrorMessage, setSuccessMessage } from '../../features/state/stateApiSlice';\nimport { ocrImageWithMistralAPI } from '../../services/ocrExamApi';\n\nconst AIWidget = ({ onImageUploaded, className = '' }) => {\n    const dispatch = useDispatch();\n    const [isOpen, setIsOpen] = useState(false);\n    const [uploadedImage, setUploadedImage] = useState(null);\n    const [imagePreview, setImagePreview] = useState(null);\n    const [ocrText, setOcrText] = useState('');\n    const [ocrBase64Images, setOcrBase64Images] = useState([]); // Store base64 images from OCR response\n    const [isProcessing, setIsProcessing] = useState(false);\n    const [isUploading, setIsUploading] = useState(false);\n    const [isDragging, setIsDragging] = useState(false);\n\n    const uploadRef = useRef(null);\n    const fileInputRef = useRef(null);\n\n    // Handle paste events\n    useEffect(() => {\n        const handlePaste = (event) => {\n            if (!isOpen) return;\n            \n            const items = event.clipboardData?.items;\n            if (items) {\n                for (let i = 0; i < items.length; i++) {\n                    const item = items[i];\n                    if (item.type.indexOf(\"image\") !== -1) {\n                        const file = item.getAsFile();\n                        if (file) {\n                            handleImageFile(file);\n                        }\n                    }\n                }\n            }\n        };\n\n        const uploadElement = uploadRef.current;\n        if (uploadElement) {\n            uploadElement.addEventListener(\"paste\", handlePaste);\n            return () => {\n                uploadElement.removeEventListener(\"paste\", handlePaste);\n            };\n        }\n    }, [isOpen]);\n\n    const validateImageFile = (file) => {\n        if (![\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)) {\n            dispatch(setErrorMessage(\"Chỉ cho phép định dạng JPEG hoặc PNG!\"));\n            return false;\n        }\n        if (file.size > 10 * 1024 * 1024) { // 10MB limit\n            dispatch(setErrorMessage(\"Kích thước ảnh vượt quá 10MB!\"));\n            return false;\n        }\n        return true;\n    };\n\n    const handleImageFile = (file) => {\n        if (!validateImageFile(file)) return;\n\n        setUploadedImage(file);\n        setImagePreview(URL.createObjectURL(file));\n        setOcrText('');\n    };\n\n    const handleDragOver = (event) => {\n        event.preventDefault();\n        event.stopPropagation();\n        setIsDragging(true);\n    };\n\n    const handleDragLeave = (event) => {\n        event.preventDefault();\n        event.stopPropagation();\n        setIsDragging(false);\n    };\n\n    const handleDrop = (event) => {\n        event.preventDefault();\n        event.stopPropagation();\n        setIsDragging(false);\n\n        const files = event.dataTransfer.files;\n        if (files.length > 0) {\n            handleImageFile(files[0]);\n        }\n    };\n\n    const handleFileSelect = (event) => {\n        const file = event.target.files[0];\n        if (file) {\n            handleImageFile(file);\n        }\n    };\n\n    const processOCR = async () => {\n        if (!uploadedImage) {\n            dispatch(setErrorMessage(\"Vui lòng chọn ảnh trước khi xử lý OCR\"));\n            return;\n        }\n\n        setIsProcessing(true);\n        try {\n            const response = await ocrImageWithMistralAPI(uploadedImage);\n\n            if (response && response.markdown) {\n                setOcrText(response.markdown);\n                // Store base64Images from OCR response for later upload\n                if (response.base64Images && response.base64Images.length > 0) {\n                    setOcrBase64Images(response.base64Images);\n                }\n                dispatch(setSuccessMessage(\"OCR thành công!\"));\n            } else {\n                dispatch(setErrorMessage(\"Không thể trích xuất văn bản từ ảnh\"));\n            }\n        } catch (error) {\n            console.error('OCR Error:', error);\n            dispatch(setErrorMessage(\"Lỗi khi xử lý OCR: \" + (error.response?.data?.message || error.message)));\n        } finally {\n            setIsProcessing(false);\n        }\n    };\n\n    const handleUploadToStorage = async () => {\n        if (!ocrBase64Images || ocrBase64Images.length === 0) {\n            dispatch(setErrorMessage(\"Không có ảnh từ OCR để tải lên. Vui lòng thực hiện OCR trước.\"));\n            return;\n        }\n\n        setIsUploading(true);\n        try {\n            // Upload base64 images from OCR response\n            const result = await dispatch(uploadBase64Images({\n                images: ocrBase64Images,\n                folder: \"questionImage\"\n            })).unwrap();\n\n            if (result && result.length > 0) {\n                dispatch(setSuccessMessage(\"Tải ảnh lên thành công!\"));\n                if (onImageUploaded) {\n                    // Pass all uploaded image URLs\n                    result.forEach(imageUrl => onImageUploaded(imageUrl));\n                }\n                handleReset();\n            }\n        } catch (error) {\n            console.error('Upload Error:', error);\n            dispatch(setErrorMessage(\"Lỗi khi tải ảnh lên: \" + (error.message || \"Không xác định\")));\n        } finally {\n            setIsUploading(false);\n        }\n    };\n\n    const handleReset = () => {\n        setUploadedImage(null);\n        setImagePreview(null);\n        setOcrText('');\n        setOcrBase64Images([]);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n    };\n\n    if (!isOpen) {\n        return (\n            <button\n                onClick={() => setIsOpen(true)}\n                className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-md hover:shadow-lg ${className}`}\n            >\n                <Sparkles className=\"w-4 h-4\" />\n                AI Widget\n            </button>\n        );\n    }\n\n    return (\n        <div className={`bg-white border border-gray-200 rounded-lg shadow-lg p-4 ${className}`}>\n            {/* Header */}\n            <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center gap-2\">\n                    <Sparkles className=\"w-5 h-5 text-purple-500\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900\">AI Widget - OCR & Upload</h3>\n                </div>\n                <button\n                    onClick={() => setIsOpen(false)}\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n                >\n                    <X className=\"w-4 h-4 text-gray-500\" />\n                </button>\n            </div>\n\n            {/* Upload Area */}\n            <div\n                ref={uploadRef}\n                tabIndex={0}\n                className={`border-2 border-dashed rounded-lg p-6 mb-4 transition-all duration-200 ${\n                    isDragging\n                        ? \"border-blue-400 bg-blue-50\"\n                        : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"\n                }`}\n                onDragOver={handleDragOver}\n                onDragLeave={handleDragLeave}\n                onDrop={handleDrop}\n            >\n                {!imagePreview ? (\n                    <div className=\"text-center\">\n                        <div className=\"flex flex-col items-center space-y-2\">\n                            <div className=\"p-3 bg-gray-200 rounded-full\">\n                                <ImageIcon className=\"w-8 h-8 text-gray-600\" />\n                            </div>\n                            <div className=\"space-y-1\">\n                                <p className=\"text-sm font-medium text-gray-700\">\n                                    Kéo thả ảnh hoặc Ctrl+V để dán\n                                </p>\n                                <p className=\"text-xs text-gray-500\">\n                                    Hỗ trợ JPEG, PNG (tối đa 10MB)\n                                </p>\n                            </div>\n                            <button\n                                onClick={() => fileInputRef.current?.click()}\n                                className=\"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors\"\n                            >\n                                <Upload className=\"w-4 h-4\" />\n                                Chọn ảnh\n                            </button>\n                        </div>\n                    </div>\n                ) : (\n                    <div className=\"space-y-4\">\n                        {/* Image Preview */}\n                        <div className=\"relative\">\n                            <img\n                                src={imagePreview}\n                                alt=\"Preview\"\n                                className=\"max-w-full max-h-64 mx-auto rounded-lg shadow-sm\"\n                            />\n                            <button\n                                onClick={handleReset}\n                                className=\"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\"\n                            >\n                                <X className=\"w-4 h-4\" />\n                            </button>\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex gap-2 justify-center\">\n                            <button\n                                onClick={processOCR}\n                                disabled={isProcessing}\n                                className=\"inline-flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                            >\n                                {isProcessing ? (\n                                    <>\n                                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                                        Đang xử lý...\n                                    </>\n                                ) : (\n                                    <>\n                                        <Eye className=\"w-4 h-4\" />\n                                        OCR\n                                    </>\n                                )}\n                            </button>\n                            {ocrBase64Images.length > 0 && (\n                                <button\n                                    onClick={handleUploadToStorage}\n                                    disabled={isUploading}\n                                    className=\"inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                                >\n                                    {isUploading ? (\n                                        <>\n                                            <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                                            Đang tải...\n                                        </>\n                                    ) : (\n                                        <>\n                                            <Save className=\"w-4 h-4\" />\n                                            Tải lên ({ocrBase64Images.length} ảnh)\n                                        </>\n                                    )}\n                                </button>\n                            )}\n                        </div>\n                    </div>\n                )}\n            </div>\n\n            {/* OCR Results */}\n            {ocrText && (\n                <div className=\"space-y-4\">\n                    {/* OCR Text Input */}\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Văn bản trích xuất (có thể chỉnh sửa):\n                        </label>\n                        <textarea\n                            value={ocrText}\n                            onChange={(e) => setOcrText(e.target.value)}\n                            className=\"w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            placeholder=\"Văn bản từ OCR sẽ hiển thị ở đây...\"\n                        />\n                    </div>\n\n                    {/* LaTeX Preview */}\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Xem trước LaTeX:\n                        </label>\n                        <div className=\"w-full min-h-[100px] p-3 border border-gray-300 rounded-md bg-white overflow-auto\">\n                            <LatexRenderer text={ocrText} />\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Hidden file input */}\n            <input\n                ref={fileInputRef}\n                type=\"file\"\n                accept=\"image/jpeg,image/png,image/jpg\"\n                onChange={handleFileSelect}\n                className=\"hidden\"\n            />\n        </div>\n    );\n};\n\nexport default AIWidget;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,GAAG,EAAEC,IAAI,QAAQ,cAAc;AACjF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,oCAAoC;AACvF,SAASC,sBAAsB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,QAAQ,GAAGC,IAAA,IAAyC;EAAAC,EAAA;EAAA,IAAxC;IAAEC,eAAe;IAAEC,SAAS,GAAG;EAAG,CAAC,GAAAH,IAAA;EACjD,MAAMI,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM2C,SAAS,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM2C,YAAY,GAAG3C,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACZ,MAAM2C,WAAW,GAAIC,KAAK,IAAK;MAAA,IAAAC,oBAAA;MAC3B,IAAI,CAACpB,MAAM,EAAE;MAEb,MAAMqB,KAAK,IAAAD,oBAAA,GAAGD,KAAK,CAACG,aAAa,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBC,KAAK;MACxC,IAAIA,KAAK,EAAE;QACP,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACnC,MAAME,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC;UACrB,IAAIE,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;YACnC,MAAMC,IAAI,GAAGH,IAAI,CAACI,SAAS,CAAC,CAAC;YAC7B,IAAID,IAAI,EAAE;cACNE,eAAe,CAACF,IAAI,CAAC;YACzB;UACJ;QACJ;MACJ;IACJ,CAAC;IAED,MAAMG,aAAa,GAAGf,SAAS,CAACgB,OAAO;IACvC,IAAID,aAAa,EAAE;MACfA,aAAa,CAACE,gBAAgB,CAAC,OAAO,EAAEf,WAAW,CAAC;MACpD,OAAO,MAAM;QACTa,aAAa,CAACG,mBAAmB,CAAC,OAAO,EAAEhB,WAAW,CAAC;MAC3D,CAAC;IACL;EACJ,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;EAEZ,MAAMmC,iBAAiB,GAAIP,IAAI,IAAK;IAChC,IAAI,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAACQ,QAAQ,CAACR,IAAI,CAACF,IAAI,CAAC,EAAE;MAC/D3B,QAAQ,CAACZ,eAAe,CAAC,uCAAuC,CAAC,CAAC;MAClE,OAAO,KAAK;IAChB;IACA,IAAIyC,IAAI,CAACS,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;MAAE;MAChCtC,QAAQ,CAACZ,eAAe,CAAC,+BAA+B,CAAC,CAAC;MAC1D,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAM2C,eAAe,GAAIF,IAAI,IAAK;IAC9B,IAAI,CAACO,iBAAiB,CAACP,IAAI,CAAC,EAAE;IAE9BzB,gBAAgB,CAACyB,IAAI,CAAC;IACtBvB,eAAe,CAACiC,GAAG,CAACC,eAAe,CAACX,IAAI,CAAC,CAAC;IAC1CrB,UAAU,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMiC,cAAc,GAAIrB,KAAK,IAAK;IAC9BA,KAAK,CAACsB,cAAc,CAAC,CAAC;IACtBtB,KAAK,CAACuB,eAAe,CAAC,CAAC;IACvB3B,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,eAAe,GAAIxB,KAAK,IAAK;IAC/BA,KAAK,CAACsB,cAAc,CAAC,CAAC;IACtBtB,KAAK,CAACuB,eAAe,CAAC,CAAC;IACvB3B,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM6B,UAAU,GAAIzB,KAAK,IAAK;IAC1BA,KAAK,CAACsB,cAAc,CAAC,CAAC;IACtBtB,KAAK,CAACuB,eAAe,CAAC,CAAC;IACvB3B,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAM8B,KAAK,GAAG1B,KAAK,CAAC2B,YAAY,CAACD,KAAK;IACtC,IAAIA,KAAK,CAACrB,MAAM,GAAG,CAAC,EAAE;MAClBM,eAAe,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7B;EACJ,CAAC;EAED,MAAME,gBAAgB,GAAI5B,KAAK,IAAK;IAChC,MAAMS,IAAI,GAAGT,KAAK,CAAC6B,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIjB,IAAI,EAAE;MACNE,eAAe,CAACF,IAAI,CAAC;IACzB;EACJ,CAAC;EAED,MAAMqB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAAC/C,aAAa,EAAE;MAChBH,QAAQ,CAACZ,eAAe,CAAC,uCAAuC,CAAC,CAAC;MAClE;IACJ;IAEAwB,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACA,MAAMuC,QAAQ,GAAG,MAAM7D,sBAAsB,CAACa,aAAa,CAAC;MAE5D,IAAIgD,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;QAC/B5C,UAAU,CAAC2C,QAAQ,CAACC,QAAQ,CAAC;QAC7B;QACA,IAAID,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACE,YAAY,CAAC5B,MAAM,GAAG,CAAC,EAAE;UAC3Df,kBAAkB,CAACyC,QAAQ,CAACE,YAAY,CAAC;QAC7C;QACArD,QAAQ,CAACX,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;MAClD,CAAC,MAAM;QACHW,QAAQ,CAACZ,eAAe,CAAC,qCAAqC,CAAC,CAAC;MACpE;IACJ,CAAC,CAAC,OAAOkE,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACZC,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCtD,QAAQ,CAACZ,eAAe,CAAC,qBAAqB,IAAI,EAAAmE,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAIL,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;IACvG,CAAC,SAAS;MACN/C,eAAe,CAAC,KAAK,CAAC;IAC1B;EACJ,CAAC;EAED,MAAMgD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACnD,eAAe,IAAIA,eAAe,CAACgB,MAAM,KAAK,CAAC,EAAE;MAClDzB,QAAQ,CAACZ,eAAe,CAAC,+DAA+D,CAAC,CAAC;MAC1F;IACJ;IAEA0B,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACA;MACA,MAAM+C,MAAM,GAAG,MAAM7D,QAAQ,CAACb,kBAAkB,CAAC;QAC7C2E,MAAM,EAAErD,eAAe;QACvBsD,MAAM,EAAE;MACZ,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MAEZ,IAAIH,MAAM,IAAIA,MAAM,CAACpC,MAAM,GAAG,CAAC,EAAE;QAC7BzB,QAAQ,CAACX,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;QACtD,IAAIS,eAAe,EAAE;UACjB;UACA+D,MAAM,CAACI,OAAO,CAACC,QAAQ,IAAIpE,eAAe,CAACoE,QAAQ,CAAC,CAAC;QACzD;QACAC,WAAW,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;MACZG,OAAO,CAACH,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCtD,QAAQ,CAACZ,eAAe,CAAC,uBAAuB,IAAIkE,KAAK,CAACK,OAAO,IAAI,gBAAgB,CAAC,CAAC,CAAC;IAC5F,CAAC,SAAS;MACN7C,cAAc,CAAC,KAAK,CAAC;IACzB;EACJ,CAAC;EAED,MAAMqD,WAAW,GAAGA,CAAA,KAAM;IACtB/D,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrBE,UAAU,CAAC,EAAE,CAAC;IACdE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAIQ,YAAY,CAACe,OAAO,EAAE;MACtBf,YAAY,CAACe,OAAO,CAACmC,KAAK,GAAG,EAAE;IACnC;EACJ,CAAC;EAED,IAAI,CAACnE,MAAM,EAAE;IACT,oBACIT,OAAA;MACI6E,OAAO,EAAEA,CAAA,KAAMnE,SAAS,CAAC,IAAI,CAAE;MAC/BH,SAAS,+MAAAuE,MAAA,CAA+MvE,SAAS,CAAG;MAAAwE,QAAA,gBAEpO/E,OAAA,CAACV,QAAQ;QAACiB,SAAS,EAAC;MAAS;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,aAEpC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAEjB;EAEA,oBACInF,OAAA;IAAKO,SAAS,8DAAAuE,MAAA,CAA8DvE,SAAS,CAAG;IAAAwE,QAAA,gBAEpF/E,OAAA;MAAKO,SAAS,EAAC,wCAAwC;MAAAwE,QAAA,gBACnD/E,OAAA;QAAKO,SAAS,EAAC,yBAAyB;QAAAwE,QAAA,gBACpC/E,OAAA,CAACV,QAAQ;UAACiB,SAAS,EAAC;QAAyB;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDnF,OAAA;UAAIO,SAAS,EAAC,qCAAqC;UAAAwE,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eACNnF,OAAA;QACI6E,OAAO,EAAEA,CAAA,KAAMnE,SAAS,CAAC,KAAK,CAAE;QAChCH,SAAS,EAAC,iDAAiD;QAAAwE,QAAA,eAE3D/E,OAAA,CAACT,CAAC;UAACgB,SAAS,EAAC;QAAuB;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNnF,OAAA;MACIoF,GAAG,EAAE3D,SAAU;MACf4D,QAAQ,EAAE,CAAE;MACZ9E,SAAS,4EAAAuE,MAAA,CACLvD,UAAU,GACJ,4BAA4B,GAC5B,oEAAoE,CAC3E;MACH+D,UAAU,EAAErC,cAAe;MAC3BsC,WAAW,EAAEnC,eAAgB;MAC7BoC,MAAM,EAAEnC,UAAW;MAAA0B,QAAA,EAElB,CAAClE,YAAY,gBACVb,OAAA;QAAKO,SAAS,EAAC,aAAa;QAAAwE,QAAA,eACxB/E,OAAA;UAAKO,SAAS,EAAC,sCAAsC;UAAAwE,QAAA,gBACjD/E,OAAA;YAAKO,SAAS,EAAC,8BAA8B;YAAAwE,QAAA,eACzC/E,OAAA,CAACX,SAAS;cAACkB,SAAS,EAAC;YAAuB;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNnF,OAAA;YAAKO,SAAS,EAAC,WAAW;YAAAwE,QAAA,gBACtB/E,OAAA;cAAGO,SAAS,EAAC,mCAAmC;cAAAwE,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnF,OAAA;cAAGO,SAAS,EAAC,uBAAuB;cAAAwE,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnF,OAAA;YACI6E,OAAO,EAAEA,CAAA;cAAA,IAAAY,qBAAA;cAAA,QAAAA,qBAAA,GAAM/D,YAAY,CAACe,OAAO,cAAAgD,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CnF,SAAS,EAAC,gHAAgH;YAAAwE,QAAA,gBAE1H/E,OAAA,CAACb,MAAM;cAACoB,SAAS,EAAC;YAAS;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,gBAENnF,OAAA;QAAKO,SAAS,EAAC,WAAW;QAAAwE,QAAA,gBAEtB/E,OAAA;UAAKO,SAAS,EAAC,UAAU;UAAAwE,QAAA,gBACrB/E,OAAA;YACI2F,GAAG,EAAE9E,YAAa;YAClB+E,GAAG,EAAC,SAAS;YACbrF,SAAS,EAAC;UAAkD;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACFnF,OAAA;YACI6E,OAAO,EAAEF,WAAY;YACrBpE,SAAS,EAAC,kGAAkG;YAAAwE,QAAA,eAE5G/E,OAAA,CAACT,CAAC;cAACgB,SAAS,EAAC;YAAS;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGNnF,OAAA;UAAKO,SAAS,EAAC,2BAA2B;UAAAwE,QAAA,gBACtC/E,OAAA;YACI6E,OAAO,EAAEnB,UAAW;YACpBmC,QAAQ,EAAE1E,YAAa;YACvBZ,SAAS,EAAC,kKAAkK;YAAAwE,QAAA,EAE3K5D,YAAY,gBACTnB,OAAA,CAAAE,SAAA;cAAA6E,QAAA,gBACI/E,OAAA;gBAAKO,SAAS,EAAC;cAA8E;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAEpG;YAAA,eAAE,CAAC,gBAEHnF,OAAA,CAAAE,SAAA;cAAA6E,QAAA,gBACI/E,OAAA,CAACR,GAAG;gBAACe,SAAS,EAAC;cAAS;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,OAE/B;YAAA,eAAE;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,EACRlE,eAAe,CAACgB,MAAM,GAAG,CAAC,iBACvBjC,OAAA;YACI6E,OAAO,EAAET,qBAAsB;YAC/ByB,QAAQ,EAAExE,WAAY;YACtBd,SAAS,EAAC,gKAAgK;YAAAwE,QAAA,EAEzK1D,WAAW,gBACRrB,OAAA,CAAAE,SAAA;cAAA6E,QAAA,gBACI/E,OAAA;gBAAKO,SAAS,EAAC;cAA8E;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEpG;YAAA,eAAE,CAAC,gBAEHnF,OAAA,CAAAE,SAAA;cAAA6E,QAAA,gBACI/E,OAAA,CAACP,IAAI;gBAACc,SAAS,EAAC;cAAS;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBACnB,EAAClE,eAAe,CAACgB,MAAM,EAAC,YACrC;YAAA,eAAE;UACL;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGLpE,OAAO,iBACJf,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAwE,QAAA,gBAEtB/E,OAAA;QAAA+E,QAAA,gBACI/E,OAAA;UAAOO,SAAS,EAAC,8CAA8C;UAAAwE,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRnF,OAAA;UACI4E,KAAK,EAAE7D,OAAQ;UACf+E,QAAQ,EAAGC,CAAC,IAAK/E,UAAU,CAAC+E,CAAC,CAACtC,MAAM,CAACmB,KAAK,CAAE;UAC5CrE,SAAS,EAAC,yHAAyH;UACnIyF,WAAW,EAAC;QAAqC;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNnF,OAAA;QAAA+E,QAAA,gBACI/E,OAAA;UAAOO,SAAS,EAAC,8CAA8C;UAAAwE,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRnF,OAAA;UAAKO,SAAS,EAAC,mFAAmF;UAAAwE,QAAA,eAC9F/E,OAAA,CAACN,aAAa;YAACuG,IAAI,EAAElF;UAAQ;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAGDnF,OAAA;MACIoF,GAAG,EAAE1D,YAAa;MAClBS,IAAI,EAAC,MAAM;MACX+D,MAAM,EAAC,gCAAgC;MACvCJ,QAAQ,EAAEtC,gBAAiB;MAC3BjD,SAAS,EAAC;IAAQ;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC9E,EAAA,CAlUIF,QAAQ;EAAA,QACOlB,WAAW;AAAA;AAAAkH,EAAA,GAD1BhG,QAAQ;AAoUd,eAAeA,QAAQ;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}