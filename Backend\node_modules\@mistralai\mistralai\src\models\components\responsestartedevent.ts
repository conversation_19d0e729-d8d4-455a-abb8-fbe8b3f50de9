/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const ResponseStartedEventType = {
  ConversationResponseStarted: "conversation.response.started",
} as const;
export type ResponseStartedEventType = ClosedEnum<
  typeof ResponseStartedEventType
>;

export type ResponseStartedEvent = {
  type?: ResponseStartedEventType | undefined;
  createdAt?: Date | undefined;
  conversationId: string;
};

/** @internal */
export const ResponseStartedEventType$inboundSchema: z.ZodNativeEnum<
  typeof ResponseStartedEventType
> = z.nativeEnum(ResponseStartedEventType);

/** @internal */
export const ResponseStartedEventType$outboundSchema: z.ZodNativeEnum<
  typeof ResponseStartedEventType
> = ResponseStartedEventType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseStartedEventType$ {
  /** @deprecated use `ResponseStartedEventType$inboundSchema` instead. */
  export const inboundSchema = ResponseStartedEventType$inboundSchema;
  /** @deprecated use `ResponseStartedEventType$outboundSchema` instead. */
  export const outboundSchema = ResponseStartedEventType$outboundSchema;
}

/** @internal */
export const ResponseStartedEvent$inboundSchema: z.ZodType<
  ResponseStartedEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ResponseStartedEventType$inboundSchema.default(
    "conversation.response.started",
  ),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  conversation_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "conversation_id": "conversationId",
  });
});

/** @internal */
export type ResponseStartedEvent$Outbound = {
  type: string;
  created_at?: string | undefined;
  conversation_id: string;
};

/** @internal */
export const ResponseStartedEvent$outboundSchema: z.ZodType<
  ResponseStartedEvent$Outbound,
  z.ZodTypeDef,
  ResponseStartedEvent
> = z.object({
  type: ResponseStartedEventType$outboundSchema.default(
    "conversation.response.started",
  ),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  conversationId: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    conversationId: "conversation_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseStartedEvent$ {
  /** @deprecated use `ResponseStartedEvent$inboundSchema` instead. */
  export const inboundSchema = ResponseStartedEvent$inboundSchema;
  /** @deprecated use `ResponseStartedEvent$outboundSchema` instead. */
  export const outboundSchema = ResponseStartedEvent$outboundSchema;
  /** @deprecated use `ResponseStartedEvent$Outbound` instead. */
  export type Outbound = ResponseStartedEvent$Outbound;
}

export function responseStartedEventToJSON(
  responseStartedEvent: ResponseStartedEvent,
): string {
  return JSON.stringify(
    ResponseStartedEvent$outboundSchema.parse(responseStartedEvent),
  );
}

export function responseStartedEventFromJSON(
  jsonString: string,
): SafeParseResult<ResponseStartedEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ResponseStartedEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ResponseStartedEvent' from JSON`,
  );
}
