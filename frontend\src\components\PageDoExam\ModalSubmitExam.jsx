import React from "react";
import OutsideClickWrapper from "src/components/common/OutsideClickWrapper";
import { AlertTriangle } from "lucide-react";

const ModalSubmitExam = ({ isOpen, onClose, onSubmit }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 px-4 sideBarRef">
            <OutsideClickWrapper
                onClickOutside={onClose}
                className="bg-white rounded-2xl p-6 shadow-xl w-full max-w-md animate-fadeIn"
            >
                <div className="flex flex-col items-center text-center gap-4">
                    <AlertTriangle className="text-yellow-500 w-12 h-12" />
                    <h2 className="text-xl font-semibold text-gray-800">
                        Bạn có chắc muốn nộp bài?
                    </h2>
                    <p className="text-sm text-gray-500">
                        <PERSON>u <PERSON>hi nộp, bạn sẽ không thể thay đổi câu trả lời.
                    </p>
                    <div className="flex w-full justify-between gap-4 mt-6">
                        <button
                            onClick={onClose}
                            className="w-full py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
                        >
                            Hủy
                        </button>
                        <button
                            onClick={onSubmit}
                            className="w-full py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition font-semibold"
                        >
                            Nộp bài
                        </button>
                    </div>
                </div>
            </OutsideClickWrapper>
        </div>
    );
};

export default ModalSubmitExam;
