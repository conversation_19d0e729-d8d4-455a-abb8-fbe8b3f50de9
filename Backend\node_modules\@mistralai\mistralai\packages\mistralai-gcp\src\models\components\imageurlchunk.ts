/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ImageURL,
  ImageURL$inboundSchema,
  ImageURL$Outbound,
  ImageURL$outboundSchema,
} from "./imageurl.js";

export type ImageURLChunkImageURL = ImageURL | string;

export const ImageURLChunkType = {
  ImageUrl: "image_url",
} as const;
export type ImageURLChunkType = ClosedEnum<typeof ImageURLChunkType>;

/**
 * {"type":"image_url","image_url":{"url":"data:image/png;base64,iVBORw0
 */
export type ImageURLChunk = {
  imageUrl: ImageURL | string;
  type?: ImageURLChunkType | undefined;
};

/** @internal */
export const ImageURLChunkImageURL$inboundSchema: z.ZodType<
  ImageURLChunkImageURL,
  z.ZodTypeDef,
  unknown
> = z.union([ImageURL$inboundSchema, z.string()]);

/** @internal */
export type ImageURLChunkImageURL$Outbound = ImageURL$Outbound | string;

/** @internal */
export const ImageURLChunkImageURL$outboundSchema: z.ZodType<
  ImageURLChunkImageURL$Outbound,
  z.ZodTypeDef,
  ImageURLChunkImageURL
> = z.union([ImageURL$outboundSchema, z.string()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ImageURLChunkImageURL$ {
  /** @deprecated use `ImageURLChunkImageURL$inboundSchema` instead. */
  export const inboundSchema = ImageURLChunkImageURL$inboundSchema;
  /** @deprecated use `ImageURLChunkImageURL$outboundSchema` instead. */
  export const outboundSchema = ImageURLChunkImageURL$outboundSchema;
  /** @deprecated use `ImageURLChunkImageURL$Outbound` instead. */
  export type Outbound = ImageURLChunkImageURL$Outbound;
}

export function imageURLChunkImageURLToJSON(
  imageURLChunkImageURL: ImageURLChunkImageURL,
): string {
  return JSON.stringify(
    ImageURLChunkImageURL$outboundSchema.parse(imageURLChunkImageURL),
  );
}

export function imageURLChunkImageURLFromJSON(
  jsonString: string,
): SafeParseResult<ImageURLChunkImageURL, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ImageURLChunkImageURL$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ImageURLChunkImageURL' from JSON`,
  );
}

/** @internal */
export const ImageURLChunkType$inboundSchema: z.ZodNativeEnum<
  typeof ImageURLChunkType
> = z.nativeEnum(ImageURLChunkType);

/** @internal */
export const ImageURLChunkType$outboundSchema: z.ZodNativeEnum<
  typeof ImageURLChunkType
> = ImageURLChunkType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ImageURLChunkType$ {
  /** @deprecated use `ImageURLChunkType$inboundSchema` instead. */
  export const inboundSchema = ImageURLChunkType$inboundSchema;
  /** @deprecated use `ImageURLChunkType$outboundSchema` instead. */
  export const outboundSchema = ImageURLChunkType$outboundSchema;
}

/** @internal */
export const ImageURLChunk$inboundSchema: z.ZodType<
  ImageURLChunk,
  z.ZodTypeDef,
  unknown
> = z.object({
  image_url: z.union([ImageURL$inboundSchema, z.string()]),
  type: ImageURLChunkType$inboundSchema.default("image_url"),
}).transform((v) => {
  return remap$(v, {
    "image_url": "imageUrl",
  });
});

/** @internal */
export type ImageURLChunk$Outbound = {
  image_url: ImageURL$Outbound | string;
  type: string;
};

/** @internal */
export const ImageURLChunk$outboundSchema: z.ZodType<
  ImageURLChunk$Outbound,
  z.ZodTypeDef,
  ImageURLChunk
> = z.object({
  imageUrl: z.union([ImageURL$outboundSchema, z.string()]),
  type: ImageURLChunkType$outboundSchema.default("image_url"),
}).transform((v) => {
  return remap$(v, {
    imageUrl: "image_url",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ImageURLChunk$ {
  /** @deprecated use `ImageURLChunk$inboundSchema` instead. */
  export const inboundSchema = ImageURLChunk$inboundSchema;
  /** @deprecated use `ImageURLChunk$outboundSchema` instead. */
  export const outboundSchema = ImageURLChunk$outboundSchema;
  /** @deprecated use `ImageURLChunk$Outbound` instead. */
  export type Outbound = ImageURLChunk$Outbound;
}

export function imageURLChunkToJSON(imageURLChunk: ImageURLChunk): string {
  return JSON.stringify(ImageURLChunk$outboundSchema.parse(imageURLChunk));
}

export function imageURLChunkFromJSON(
  jsonString: string,
): SafeParseResult<ImageURLChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ImageURLChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ImageURLChunk' from JSON`,
  );
}
