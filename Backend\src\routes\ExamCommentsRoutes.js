import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as ExamCommentController from '../controllers/ExamCommentController.js'

const router = express.Router()

router.get('/v1/user/comments/replies/:commentId',
    requireRoles(Roles.JustStudent),
    asyncHandler(ExamCommentController.getReplies)
)

router.get('/v1/user/comments/exam/:examId',
    requireRoles(Roles.JustStudent),
    async<PERSON>andler(ExamCommentController.getComments)
)


router.post('/v1/user/comments/exam/:examId',
    requireRoles(Roles.JustStudent),
    async<PERSON>and<PERSON>(ExamCommentController.postComment)
)

router.put('/v1/user/comments/:commentId',
    requireRoles(Roles.JustStudent),
    asyncHandler(ExamCommentController.putComment)
)

router.delete('/v1/user/comments/:commentId',
    requireRoles(Roles.JustStudent),
    asyncHandler(ExamCommentController.deleteComment)
)

export default router