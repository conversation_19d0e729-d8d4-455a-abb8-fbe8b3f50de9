import { useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
    ClipboardList,
    Moon,
    Sun,
    Home,
    Menu,
    LogOut,
    Settings,
    X,
} from "lucide-react";
import FullScreen from "../button/ScreenButton";
import {
    toggleDarkMode,
    setFontSize,
    setImageSize,
    setSingleQuestionMode,
    setShowMarkedOnly,
    toggleGuide,
    setShowGuide,
    toggleSidebar
} from "src/features/doExam/doExamSlice";
import LoadingText from "../loading/LoadingText";
import { fetchPublicQuestionsByExamId } from "src/features/doExam/doExamSlice";

import SizeSlider from "../sidebar/SizeSlider";
import { BookmarkCheck, List, RefreshCw, BookOpen } from "lucide-react";
import ViewModeToggle from "../sidebar/ViewModeToggle";
import ButtonHeader from "./ButtonHeader";
import OutsideClickWrapper from "../common/OutsideClickWrapper";

const GuidePanel = () => {
    const { darkMode, showGuide } = useSelector((state) => state.doExam);
    const dispatch = useDispatch();

    return (
        <OutsideClickWrapper
            ignoreOutsideClick="guideRef"
            onClickOutside={() => dispatch(setShowGuide(false))}
            className={`absolute p-4 right-0 top-16 mt-2 w-60 sm:w-80 rounded-md shadow-lg z-50 transition-opacity duration-200 
                ${showGuide ? "opacity-100 visible" : "opacity-0 invisible"}
                ${darkMode ? "bg-gray-800 border border-gray-700 text-white" : "bg-white border text-gray-900 border-gray-200"}
            `}
        >
            <h2 className="text-lg font-semibold mb-2">Hướng dẫn làm bài</h2>
            <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>Đọc kỹ từng câu hỏi trước khi trả lời.</li>
                <li>Dùng các nút <strong>Đánh dấu</strong> để quay lại sau.</li>
                <li>Click vào <strong>Toàn màn hình</strong> để tập trung hơn.</li>
                <li>Có thể chỉnh <strong>cỡ chữ</strong> và <strong>cỡ ảnh</strong> trong menu cài đặt.</li>
            </ul>
        </OutsideClickWrapper>
    );
};

const MenuHeader = ({ isOpen, setIsOpen }) => {
    const { fontSize, imageSize, singleQuestionMode, showMarkedOnly, loadingQuestions } = useSelector((state) => state.doExam);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { exam } = useSelector((state) => state.exams);
    const { darkMode } = useSelector((state) => state.doExam);
    const [isModalOpen, setIsModalOpen] = useState(false);

    return (
        <OutsideClickWrapper
            ignoreOutsideClick="menuRef"
            onClickOutside={() => setIsOpen(false)}
            className={`absolute p-4 right-0 top-16 mt-2 w-60 sm:w-80 rounded-md shadow-lg z-50 transition-opacity duration-200 
                ${isOpen ? "opacity-100 visible" : "opacity-0 invisible"}
                ${darkMode ? "bg-gray-800 border border-gray-700" : "bg-white border border-gray-200"}
            `}
        >
            <div className={`transition-all ${darkMode ? "text-white" : "text-gray-900"} text-sm duration-500 overflow-hidden `}>
                <div className="flex justify-between items-center mb-2">
                    <span className="font-semibold">Chế độ:</span>
                    <ButtonHeader
                        darkMode={darkMode}
                        onClick={() => dispatch(toggleDarkMode())}
                        Icon={darkMode ? Sun : Moon}
                        title={darkMode ? "Chế độ sáng" : "Chế độ tối"}
                    />
                </div>
                <div className="flex justify-between items-center mb-2">
                    <span className="font-semibold">Toàn màn hình:</span>
                    <FullScreen isDarkMode={darkMode} />
                </div>

                <hr className="my-4" />
                <div className="flex flex-col gap-2">
                    <SizeSlider
                        label="Chỉnh cỡ chữ"
                        value={fontSize}
                        onChange={(e) => dispatch(setFontSize(Number(e.target.value)))}
                        min={12}
                        max={24}
                        isDarkMode={darkMode}
                        unit="px"
                    />

                    <SizeSlider
                        label="Chỉnh cỡ ảnh"
                        value={imageSize}
                        onChange={(e) => dispatch(setImageSize(Number(e.target.value)))}
                        min={6}
                        max={20}
                        isDarkMode={darkMode}
                        unit="rem"
                    />
                </div>

                <hr className="my-4" />
                <ViewModeToggle
                    singleQuestionMode={singleQuestionMode}
                    setSingleQuestionMode={(value) => dispatch(setSingleQuestionMode(value))}
                    isDarkMode={darkMode}
                />
                <div className="flex justify-between items-center mb-2">
                    <span className="font-semibold">Load lại: </span>
                    <ButtonHeader
                        darkMode={darkMode}
                        onClick={() => dispatch(fetchPublicQuestionsByExamId(exam.id))}
                        Icon={RefreshCw}
                        loading={loadingQuestions}
                    />
                </div>

                {/* Filter toggle */}
                <div className="flex justify-between items-center mb-2">
                    <span className="font-semibold">Hiển thị:</span>
                    <ButtonHeader
                        darkMode={darkMode}
                        onClick={() => dispatch(setShowMarkedOnly(!showMarkedOnly))}
                        Icon={showMarkedOnly ? BookmarkCheck : List}
                        title={showMarkedOnly ? "Hiển thị tất cả câu hỏi" : "Đã đánh dấu"}
                    />
                </div>
                <hr className="my-4" />
                <div className="flex justify-end items-center mb-2">
                    <ButtonHeader
                        darkMode={darkMode}
                        onClick={() => navigate('/practice')}
                        Icon={Home}
                        title="Về trang Luyện tập"
                    />
                </div>
            </div>
        </OutsideClickWrapper>
    )
}


const HeaderDoExamPage = ({ nameExam }) => {
    const { darkMode, showGuide, loadingExam, showSidebar } = useSelector((state) => state.doExam);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [isOpenMenu, setIsOpenMenu] = useState(false);

    const handleToggleDarkMode = () => dispatch(toggleDarkMode());

    const handleNavigatePractice = () => navigate("/practice");

    return (
        <header className={`fixed  top-0 left-0 right-0 shadow-sm border-b z-30 transition-colors duration-300 ${darkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"}`}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16 relative">
                    {/* Left - Exam Info */}
                    <div className="flex items-center gap-3">
                        <ClipboardList size={24} className="text-sky-600" />
                        <div>
                            <LoadingText
                                loading={loadingExam}
                                color={`${darkMode ? "bg-white" : "bg-gray-200"}`}
                            >
                                <h1 className={`sm:text-lg text-base font-semibold ${darkMode ? "text-white" : "text-gray-900"}`}>
                                    {nameExam || "Không có"}
                                </h1>
                            </LoadingText>
                            <p className={`text-sm hidden sm:block ${darkMode ? "text-gray-400" : "text-gray-500"}`}>
                                Đề thi trực tuyến
                            </p>
                        </div>
                    </div>

                    {/* Right - Buttons */}
                    <div className="flex items-center gap-3">

                        <div className="guideRef">
                            <ButtonHeader
                                darkMode={darkMode}
                                onClick={() => dispatch(toggleGuide())}
                                Icon={showGuide ? X : BookOpen}
                                title="Hướng dẫn"
                            />
                        </div>
                        <ButtonHeader
                            darkMode={darkMode}
                            onClick={() => navigate('/practice')}
                            title="Về trang Luyện tập"
                            Icon={Home}
                            hidden={true}
                        />
                        <div className="menuRef">
                            <ButtonHeader
                                darkMode={darkMode}
                                onClick={() => setIsOpenMenu(!isOpenMenu)}
                                Icon={isOpenMenu ? X : Settings}
                                hidden={false}
                            />
                        </div>
                        <div className="sideBarRef sm:hidden block">
                            <ButtonHeader
                                darkMode={darkMode}
                                onClick={() => dispatch(toggleSidebar())}
                                Icon={showSidebar ? X : Menu}
                                hidden={false}
                            />
                        </div>
                    </div>

                    {/* Guide Panel */}
                    <GuidePanel
                        darkMode={darkMode}
                        isOpen={showGuide}
                        setIsOpen={() => dispatch(toggleGuide())}
                    />

                    {/* Menu Dropdown */}
                    <MenuHeader
                        darkMode={darkMode}
                        isOpen={isOpenMenu}
                        setIsOpen={setIsOpenMenu}
                        onNavigatePractice={handleNavigatePractice}
                        onToggleDark={handleToggleDarkMode}
                    />
                </div>
            </div>
        </header>
    );
};

export default HeaderDoExamPage;
