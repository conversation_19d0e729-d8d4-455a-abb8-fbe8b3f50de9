import { Calendar, ChevronDown, ArrowLeft } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { resetCalendar, setCalendarView } from 'src/features/calendar/calendarSlice';
import CalendarMonth from 'src/components/calendar/CalenderMonth';
import { LayoutPanelTop, Columns3, LayoutGrid } from 'lucide-react';

const SidebarCalendar = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate(); // Dùng cho quay lại trang trước
    const { view } = useSelector((state) => state.calendar);

    const handleViewChange = (newView) => {
        dispatch(setCalendarView(newView));
    };

    const vietnameseViews = {
        day: 'Ngày',
        week: 'Tuần',
        month: 'Tháng',
    };

    return (
        <div className="sticky top-20 p-4 w-full xl:w-[300px] lg:h-[90vh] overflow-y-auto hide-scrollbar ">
            <div className="flex items-center gap-3 mb-3">
                {/* Nút quay lại */}
                <button
                    onClick={() => navigate(-1)}
                    className="p-2 rounded hover:bg-gray-100 transition"
                    aria-label="Quay lại"
                >
                    <ArrowLeft className="w-5 h-5 text-gray-700" />
                </button>

                {/* Icon và tiêu đề */}
                <Calendar className="w-6 h-6 text-sky-600" />
                <h1 className="text-2xl font-bold text-gray-800">Lịch Học</h1>
            </div>

            {/* Hàng chứa nút "Hôm nay" và Dropdown */}
            <div className="flex items-center justify-between mb-3 gap-2">
                <button
                    onClick={() => dispatch(resetCalendar())}
                    className="flex-1 bg-sky-600 text-white px-3 py-2 rounded-md text-sm font-medium shadow hover:bg-sky-700 transition"
                >
                    Hôm nay
                </button>

                {/* Dropdown View */}
                <div className="relative w-[110px] lg:block hidden">
                    <select
                        value={view}
                        onChange={(e) => handleViewChange(e.target.value)}
                        className="block w-full appearance-none bg-white border border-gray-300 text-gray-700 text-sm rounded-md px-3 py-2 pr-8 shadow-sm focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500"
                    >
                        {Object.entries(vietnameseViews).map(([key, label]) => (
                            <option key={key} value={key}>{label}</option>
                        ))}
                    </select>
                    <ChevronDown className="absolute right-2 top-2.5 w-4 h-4 text-gray-500 pointer-events-none" />
                </div>
            </div>

            <CalendarMonth />
            <div className="flex flex-row gap-2 mt-4 lg:hidden">
                <button
                    onClick={() => handleViewChange('day')}
                    className={`px-3 py-2 rounded-md text-sm font-medium shadow  transition ${view === 'day' ? 'bg-sky-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}>
                    <LayoutPanelTop className="inline-block mr-1 w-4 h-4" />
                    Ngày
                </button>
                <button
                    onClick={() => handleViewChange('week')}
                    className={`px-3 py-2 rounded-md text-sm font-medium shadow  transition ${view === 'week' ? 'bg-sky-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}>
                    <Columns3 className="inline-block mr-1 w-4 h-4" />
                    Tuần
                </button>
                <button
                    onClick={() => handleViewChange('month')}
                    className={`px-3 py-2 rounded-md text-sm font-medium shadow  transition ${view === 'month' ? 'bg-sky-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}>
                    <LayoutGrid className="inline-block mr-1 w-4 h-4" />
                    Tháng
                </button>
            </div>
        </div>
    );
};

export default SidebarCalendar;
