import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { debounce } from 'lodash';
import { X } from 'lucide-react';
import { findExams } from '../features/exam/examSlice';

/**
 * Component for searching and selecting exam
 * 
 * @param {Object} props
 * @param {string} props.value - The current search term
 * @param {string} props.selectedExamId - The ID of the selected exam
 * @param {function} props.onChange - Callback when search term changes
 * @param {function} props.onSelect - Callback when a exam is selected
 * @param {function} props.onClear - Callback when selection is cleared
 * @param {string} props.placeholder - Placeholder text for the input
 * @param {string} props.className - Additional CSS classes
 */
const ExamSearchInput = ({
    value = '',
    selectedExamId = '',
    onChange,
    onSelect,
    onClear,
    placeholder = 'Tìm kiếm đề thi...',
    className = 'w-full',
    setExam = () => { },
    clearExam = () => { }
}) => {
    const dispatch = useDispatch();
    const [searchTerm, setSearchTerm] = useState(value);
    const [showDropdown, setShowDropdown] = useState(false);
    const { examsSearch } = useSelector((state) => state.exams);
    const dropdownRef = useRef(null);

    // Handle exam search with debounce
    const handleExamSearch = useCallback(
        debounce((searchTerm) => {
            dispatch(findExams(searchTerm))
        }, 1000),
        [dispatch]
    );

    // Handle input change
    const handleInputChange = (e) => {
        const value = e.target.value;
        setSearchTerm(value);
        if (onChange) onChange(value);
        if (value === '') clearExam();
        handleExamSearch(value);
        setShowDropdown(true);
    };

    // Handle exam selection
    const handleSelectExam = (examItem) => {
        if (onSelect) onSelect(examItem);
        setExam(examItem);
        setShowDropdown(false);
    };

    // Handle clear selection
    const handleClearSelection = () => {
        setSearchTerm('');
        clearExam();
        if (onClear) onClear();
    };

    // Update local state when props change
    useEffect(() => {
        setSearchTerm(value);
    }, [value]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            <input
                type="text"
                placeholder={placeholder}
                value={searchTerm}
                onChange={handleInputChange}
                onFocus={() => setShowDropdown(true)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
            {showDropdown && (
                examsSearch.length > 0 ? (
                    <div className="absolute z-60 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {examsSearch.map((examItem) => (
                            <div
                                key={examItem.id}
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => handleSelectExam(examItem)}
                            >
                                <div className="font-medium">{examItem.name}</div>
                                <div className="text-xs text-gray-500">
                                    Id: {examItem.id} | Khối {examItem.class} | Năm {examItem.year}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="absolute z-60 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        <div className="px-4 py-2 text-gray-500">Không tìm thấy đề thi</div>
                    </div>
                )
            )}
            {selectedExamId && (
                <button
                    onClick={handleClearSelection}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                    <X size={16} />
                </button>
            )}
        </div>
    );
};

export default ExamSearchInput;
