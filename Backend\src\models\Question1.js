'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
    class Question1 extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            Question1.belongsToMany(models.Exam1, {
                through: 'ExamQuestions1',
                foreignKey: 'questionId',
                otherKey: 'examId',
                as: 'exam1s',
            })
            Question1.hasMany(models.Statement1, { foreignKey: 'questionId', as: 'statement1s' })
        }
    }
    Question1.init({
        content: DataTypes.TEXT,
        typeOfQuestion: DataTypes.STRING,
        correctAnswer: DataTypes.STRING,
        solution: DataTypes.TEXT,
        chapter: DataTypes.STRING,
        class: DataTypes.STRING,
        difficulty: DataTypes.STRING,
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE,
        order: DataTypes.INTEGER,
        imageUrl: DataTypes.TEXT,
        solutionImageUrl: DataTypes.TEXT,
    }, {
        sequelize,
        modelName: 'Question1',
        tableName: 'question1'
    })
    return Question1
}
