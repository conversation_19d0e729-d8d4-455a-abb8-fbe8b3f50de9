import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type JobsApiRoutesBatchGetBatchJobRequest = {
    jobId: string;
};
/** @internal */
export declare const JobsApiRoutesBatchGetBatchJobRequest$inboundSchema: z.ZodType<JobsApiRoutesBatchGetBatchJobRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type JobsApiRoutesBatchGetBatchJobRequest$Outbound = {
    job_id: string;
};
/** @internal */
export declare const JobsApiRoutesBatchGetBatchJobRequest$outboundSchema: z.ZodType<JobsApiRoutesBatchGetBatchJobRequest$Outbound, z.ZodTypeDef, JobsApiRoutesBatchGetBatchJobRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace JobsApiRoutesBatchGetBatchJobRequest$ {
    /** @deprecated use `JobsApiRoutesBatchGetBatchJobRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<JobsApiRoutesBatchGetBatchJobRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `JobsApiRoutesBatchGetBatchJobRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<JobsApiRoutesBatchGetBatchJobRequest$Outbound, z.ZodTypeDef, JobsApiRoutesBatchGetBatchJobRequest>;
    /** @deprecated use `JobsApiRoutesBatchGetBatchJobRequest$Outbound` instead. */
    type Outbound = JobsApiRoutesBatchGetBatchJobRequest$Outbound;
}
export declare function jobsApiRoutesBatchGetBatchJobRequestToJSON(jobsApiRoutesBatchGetBatchJobRequest: JobsApiRoutesBatchGetBatchJobRequest): string;
export declare function jobsApiRoutesBatchGetBatchJobRequestFromJSON(jsonString: string): SafeParseResult<JobsApiRoutesBatchGetBatchJobRequest, SDKValidationError>;
//# sourceMappingURL=jobsapiroutesbatchgetbatchjob.d.ts.map