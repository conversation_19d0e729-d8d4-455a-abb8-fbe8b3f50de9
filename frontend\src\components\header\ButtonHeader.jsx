const ButtonHeader = ({ darkMode = false, onClick, title, Icon, hidden = false, loading }) => (
    <button
        onClick={onClick}
        className={`${hidden ? "sm:flex hidden" : "flex"} items-center gap-2 p-[7px] text-sm font-medium border rounded-md transition-colors duration-200
        ${darkMode
                ? "text-gray-300 bg-gray-800 border-gray-600 hover:bg-gray-700 hover:text-white"
                : "text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-sky-600"
            }`}
        disabled={loading}
    >
        <Icon size={16} className={loading ? "animate-spin" : ""} />
        {title && <span className="hidden sm:inline">{title}</span>}
    </button>
);

export default ButtonHeader;
