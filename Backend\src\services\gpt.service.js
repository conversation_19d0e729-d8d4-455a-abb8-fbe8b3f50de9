import { openai } from '../config/openAiConfig.js';
import db from '../models/index.js';
import UserQuestion from '../constants/UserQuestion.js';
import PrefixStatement from '../constants/PrefixStatement.js';
import { getImageBase64FromUrl } from './image.service.js';

export async function callGPT(messages, model = 'gpt-4o') {
    const completion = await openai.chat.completions.create({
        model,
        messages,
        temperature: 0.7,
    });

    return completion.choices[0].message.content;
}

export const CreateResponseGPTQuestion = async ({ questionId, messageId, response }) => {
    try {
        const newResponse = await db.ResponseGPTQuestions.create({
            questionId,
            messageId,
            response
        });

        return newResponse;
    } catch (error) {
        console.error('Error in CreateResponseGPTQuestion:', error);
        throw error;
    }
}

export async function classifyQuestions(questions) {
    try {
        const chapters = await db.AllCode.findAll({
            where: {
                type: "chapter",
                [db.Sequelize.Op.and]: [
                    db.Sequelize.where(
                        db.Sequelize.fn("LENGTH", db.Sequelize.col("code")),
                        5
                    ),
                ],
            },
        });

        // Tạo danh sách chương cho GPT tham khảo
        const chapterList = chapters
            .map((ch) => `- ${ch.code}: ${ch.description}`)
            .join("\n");

        const systemPrompt = `Bạn là một chuyên gia giáo dục toán học. Nhiệm vụ của bạn là phân loại các câu hỏi toán học theo:
            1. class (lớp học): chỉ có "10", "11", "12"
            2. chapter (chương): chọn từ danh sách sau (trả về mã code):
            ${chapterList}
            3. difficulty (độ khó): "NB" (Nhận biết), "TH" (Thông hiểu), "VD" (Vận dụng), "VDC" (Vận dụng cao)

            ## Lưu ý : những câu hỏi đầu tiên của phần trắc nghiệm thường rất dễ, có độ khó là NB

            Hãy trả về kết quả dưới dạng JSON array với format:
            [
            {
                "originalIndex": 0,
                "class": "10",
                "chapter": "12C1",
                "difficulty": "TH"
            }
            ]

            ⚠️ Chỉ trả về JSON hợp lệ, không thêm bất kỳ văn bản giải thích nào.`;

        // Soạn nội dung câu hỏi để phân loại
        let questionContent = "Hãy phân loại các câu hỏi sau:\n\n";

        questions.forEach((question, index) => {
            questionContent += `Câu ${index + 1} (index: ${index}): ${question.content}\n`;

            if (question.typeOfQuestion) {
                const typeName =
                    question.typeOfQuestion === "TN"
                        ? "Trắc nghiệm"
                        : question.typeOfQuestion === "DS"
                            ? "Đúng/Sai"
                            : "Tự luận ngắn";
                questionContent += `Loại câu hỏi: ${typeName}\n`;
            }

            if (Array.isArray(question.statements) && question.statements.length > 0) {
                questionContent += `Các lựa chọn:\n`;
                question.statements.forEach((s, j) => {
                    const prefix =
                        question.typeOfQuestion === "TN"
                            ? PrefixStatement.TN[j]
                            : PrefixStatement.DS[j] || `MĐ${j + 1}`;
                    questionContent += `${prefix} ${s.content}\n`;
                });
            }

            // Gợi ý thông tin hiện tại nếu có
            if (question.chapter) {
                questionContent += `Chương hiện tại: ${question.chapter}\n`;
            }
            if (question.difficulty) {
                questionContent += `Độ khó hiện tại: ${question.difficulty}\n`;
            }

            questionContent += "\n";
        });

        // Gửi request đến GPT
        const completion = await openai.chat.completions.create({
            model: "gpt-4o",
            messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: questionContent },
            ],
            temperature: 0.3,
            max_tokens: 3000,
        });

        const responseContent = completion.choices[0].message.content;

        // Parse kết quả GPT trả về
        let classificationResults;
        try {
            classificationResults = JSON.parse(
                responseContent
                    .trim()
                    .replace(/^```json\s*/i, "")
                    .replace(/```$/, "")
            );
        } catch (parseError) {
            console.error("❌ Lỗi parse JSON từ GPT:", parseError);
            console.error("🔎 GPT Response:", responseContent);
            throw new Error("Không thể phân tích kết quả JSON từ GPT");
        }

        return classificationResults;
    } catch (error) {
        console.error("❌ Lỗi classifyQuestions:", error);
        throw error;
    }
}

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Hàm xử lý câu hỏi từ database với AI
export async function askQuestionWithAI(questionId, messageId = 1) {
    try {

        const response = await db.ResponseGPTQuestions.findOne({
            where: {
                questionId,
                messageId
            }
        });

        if (response) {
            // Delay 1.5 giây (1500ms) trước khi trả về
            await delay(2000);

            return {
                aiResponse: response.response
            };
        }

        // Lấy câu hỏi từ database
        const question = await db.Question.findByPk(questionId, {
            include: [
                {
                    model: db.Statement,
                    as: 'statements',
                    attributes: ['id', 'content', 'isCorrect', 'imageUrl'],
                    order: [['order', 'ASC']]
                }
            ]
        });

        if (!question) {
            throw new Error('Không tìm thấy câu hỏi');
        }

        // Tạo prompt cho AI
        const systemPrompt = `Bạn là một giáo viên toán học chuyên nghiệp. Nhiệm vụ của bạn là:
            1. Giải thích chi tiết cách làm bài toán
            2. Phân tích từng bước giải
            3. Đưa ra lời giải dễ hiểu
            4. Nếu có nhiều đáp án, hãy giải thích tại sao đáp án đó đúng
            5. Sử dụng tiếng Việt trong toàn bộ câu trả lời`;

        // Tạo nội dung câu hỏi
        let questionContent = `Câu hỏi: ${question.content}\n`;

        // Thêm thông tin về loại câu hỏi
        if (question.typeOfQuestion) {
            questionContent += `Loại câu hỏi: ${question.typeOfQuestion}\n`;
        }

        // Thêm các đáp án nếu có
        if ((question.typeOfQuestion === 'TN' || question.typeOfQuestion === 'DS') && question.statements && question.statements.length > 0) {
            questionContent += `\nCác mệnh đề:\n`;
            question.statements.forEach((statement, index) => {
                const prefix = PrefixStatement[question.typeOfQuestion][index] || `${String.fromCharCode(65 + index)}.`;
                questionContent += `${prefix} ${statement.content}\n`;
            });
            if (question.typeOfQuestion === 'TN') {
                const correctStatement = question.statements.find(s => s.isCorrect);
                if (correctStatement) {
                    questionContent += `\nĐáp án đúng: ${correctStatement.content}\n`;
                } else {
                    questionContent += `\nKhông có đáp án đúng\n`;
                }
            } else if (question.typeOfQuestion === 'DS') {
                const correctStatements = question.statements.filter(s => s.isCorrect);
                if (correctStatements.length > 0) {
                    questionContent += `\n Các mệnh đề đúng : ${correctStatements.map(s => PrefixStatement.DS[s.order - 1]).join(", ")}\n`;
                }
                const incorrectStatements = question.statements.filter(s => !s.isCorrect);
                if (incorrectStatements.length > 0) {
                    questionContent += `\n Các mệnh đề sai : ${incorrectStatements.map(s => PrefixStatement.DS[s.order - 1]).join(", ")}\n`;
                }
            }
        }

        if (question.typeOfQuestion === 'TLN') {
            if (question.correctAnswer) {
                questionContent += `\nĐáp án đúng: ${question.correctAnswer}\n`;
            } else {
                questionContent += `\nKhông có đáp án đúng\n`;
            }
        }

        if (question.solution && question.solution.trim()) {
            questionContent += `\nTham khảo lời giải: ${question.solution}\n`;
        }

        // Thêm câu hỏi của người dùng nếu có
        if (messageId && UserQuestion[messageId]) {
            questionContent += `\nCâu hỏi cụ thể của học sinh: ${UserQuestion[messageId]}`;
        }

        // Tạo messages cho API
        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: questionContent }
        ];

        // Kiểm tra xem có hình ảnh không
        const hasImage = question.imageUrl ||
            (question.statements && question.statements.some(s => s.imageUrl));

        let model = 'gpt-4o';

        if (hasImage) {
            // Sử dụng model có khả năng xử lý hình ảnh
            model = 'gpt-4o'; // hoặc 'gpt-4-vision-preview'

            // Tạo lại messages với hình ảnh
            const contentArray = [
                { type: 'text', text: questionContent }
            ];

            // Thêm hình ảnh câu hỏi nếu có
            if (question.imageUrl) {
                try {
                    const base64Image = await getImageBase64FromUrl(question.imageUrl);
                    if (base64Image && base64Image.startsWith("data:image/")) {
                        contentArray.push({
                            type: 'image_url',
                            image_url: {
                                url: base64Image,
                                detail: 'high'
                            }
                        });
                    } else {
                        console.warn("⚠️ Ảnh không hợp lệ (không phải base64 image):", question.imageUrl);
                    }
                } catch (err) {
                    console.warn("⚠️ Không thể tải ảnh câu hỏi:", question.imageUrl, err.message);
                }
            }


            // Thêm hình ảnh từ statements nếu có
            if (question.statements) {
                for (const statement of question.statements) {
                    if (statement.imageUrl) {
                        const base64Image = await getImageBase64FromUrl(statement.imageUrl);
                        contentArray.push({
                            type: 'image_url',
                            image_url: {
                                url: base64Image,
                                detail: 'high'
                            }
                        });
                    }
                }
            }

            messages[1] = {
                role: 'user',
                content: contentArray
            };
        }

        // Gọi API OpenAI
        const completion = await openai.chat.completions.create({
            model,
            messages,
            temperature: 0.3, // Giảm temperature để có câu trả lời chính xác hơn
            max_tokens: 2000,
        });

        await CreateResponseGPTQuestion({
            questionId,
            messageId,
            response: completion.choices[0].message.content
        });

        return {
            aiResponse: completion.choices[0].message.content,
            hasImage,
            model: model,
            questionContent
        };

    } catch (error) {
        console.error('Error in askQuestionWithAI:', error);
        throw error;
    }
}

// Hàm sửa chính tả và ký hiệu LaTeX bằng GPT
export async function fixTextAndLatex(text) {
    try {
        // Tạo prompt cho AI để sửa chính tả và LaTeX
        const systemPrompt = `Bạn là một chuyên gia về tiếng Việt và LaTeX. Nhiệm vụ của bạn là:
            1. Sửa chính tả tiếng Việt (lỗi đánh máy, dấu thanh, ngữ pháp)
            2. Sửa các ký hiệu LaTeX sai (cú pháp, dấu ngoặc, lệnh không đúng)
            3. Cải thiện cách trình bày toán học với LaTeX
            4. Giữ nguyên ý nghĩa và nội dung gốc

            Chỉ trả về văn bản đã được sửa, không thêm giải thích hay ghi chú.
            Nếu không có lỗi gì thì trả về văn bản gốc.`;

        // Tạo messages cho API
        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `Hãy sửa chính tả và ký hiệu LaTeX trong đoạn văn sau:\n\n${text}` }
        ];

        // Gọi API OpenAI
        const completion = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages,
            temperature: 0.1, // Giảm temperature để có kết quả nhất quán
            max_tokens: 2000,
        });

        const fixedText = completion.choices[0].message.content.trim();

        return {
            originalText: text,
            fixedText: fixedText,
            hasChanges: text !== fixedText,
            model: 'gpt-4o'
        };

    } catch (error) {
        console.error('Error in fixTextAndLatex:', error);
        throw error;
    }
}

