import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type FilesApiRoutesRetrieveFileRequest = {
    fileId: string;
};
/** @internal */
export declare const FilesApiRoutesRetrieveFileRequest$inboundSchema: z.ZodType<FilesApiRoutesRetrieveFileRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type FilesApiRoutesRetrieveFileRequest$Outbound = {
    file_id: string;
};
/** @internal */
export declare const FilesApiRoutesRetrieveFileRequest$outboundSchema: z.ZodType<FilesApiRoutesRetrieveFileRequest$Outbound, z.ZodTypeDef, FilesApiRoutesRetrieveFileRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace FilesApiRoutesRetrieveFileRequest$ {
    /** @deprecated use `FilesApiRoutesRetrieveFileRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<FilesApiRoutesRetrieveFileRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `FilesApiRoutesRetrieveFileRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<FilesApiRoutesRetrieveFileRequest$Outbound, z.ZodTypeDef, FilesApiRoutesRetrieveFileRequest>;
    /** @deprecated use `FilesApiRoutesRetrieveFileRequest$Outbound` instead. */
    type Outbound = FilesApiRoutesRetrieveFileRequest$Outbound;
}
export declare function filesApiRoutesRetrieveFileRequestToJSON(filesApiRoutesRetrieveFileRequest: FilesApiRoutesRetrieveFileRequest): string;
export declare function filesApiRoutesRetrieveFileRequestFromJSON(jsonString: string): SafeParseResult<FilesApiRoutesRetrieveFileRequest, SDKValidationError>;
//# sourceMappingURL=filesapiroutesretrievefile.d.ts.map