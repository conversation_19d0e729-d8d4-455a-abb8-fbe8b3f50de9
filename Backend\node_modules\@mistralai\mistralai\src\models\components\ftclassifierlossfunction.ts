/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const FTClassifierLossFunction = {
  SingleClass: "single_class",
  MultiClass: "multi_class",
} as const;
export type FTClassifierLossFunction = ClosedEnum<
  typeof FTClassifierLossFunction
>;

/** @internal */
export const FTClassifierLossFunction$inboundSchema: z.ZodNativeEnum<
  typeof FTClassifierLossFunction
> = z.nativeEnum(FTClassifierLossFunction);

/** @internal */
export const FTClassifierLossFunction$outboundSchema: z.ZodNativeEnum<
  typeof FTClassifierLossFunction
> = FTClassifierLossFunction$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FTClassifierLossFunction$ {
  /** @deprecated use `FTClassifierLossFunction$inboundSchema` instead. */
  export const inboundSchema = FTClassifierLossFunction$inboundSchema;
  /** @deprecated use `FTClassifierLossFunction$outboundSchema` instead. */
  export const outboundSchema = FTClassifierLossFunction$outboundSchema;
}
