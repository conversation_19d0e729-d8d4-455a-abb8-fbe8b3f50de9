{"ast": null, "code": "var _jsxFileName = \"D:\\\\ToanThayBee\\\\frontend\\\\src\\\\components\\\\detail\\\\PreviewExam.jsx\",\n  _s = $RefreshSig$();\nimport { fetchExamQuestions } from \"../../features/question/questionSlice\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect, useRef } from \"react\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\nimport { useReactToPrint } from 'react-to-print';\nimport header from \"../../assets/images/Screenshot 2025-03-18 010039.jpg\";\nimport { BeeMathLogo } from \"../logo/BeeMathLogo\";\nimport QRCodeComponent from \"../QrCode\";\nimport NoTranslate from \"../utils/NoTranslate\";\nimport \"../../styles/print-styles.css\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport LoadingData from \"../loading/LoadingData\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PreviewExam = _ref => {\n  _s();\n  let {\n    questions,\n    exam\n  } = _ref;\n  const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  let indexTN = 1;\n  let indexDS = 1;\n  let indexTLN = 1;\n  const {\n    loading\n  } = useSelector(state => state.states);\n  const examRef = useRef(null);\n  // const reactPrint = useReactToPrint();\n\n  const handlePrint = useReactToPrint({\n    contentRef: examRef,\n    documentTitle: (exam === null || exam === void 0 ? void 0 : exam.name) || \"De Thi\",\n    onBeforeGetContent: () => {\n      // Any preparation before printing\n      document.body.classList.add('printing');\n    },\n    onAfterPrint: () => {\n      // Cleanup after printing\n      document.body.classList.remove('printing');\n    },\n    pageStyle: \"\\n            @page {\\n                size: A4;\\n                margin: 15mm 10mm 20mm 10mm;\\n                counter-increment: page;\\n                padding: 4mm;\\n                @bottom-left {\\n                    content: \\\"To\\xE1n th\\u1EA7y Bee 0333726202 100 B\\u1EA1ch Mai, Hai B\\xE0 Tr\\u01B0ng, H\\xE0 N\\u1ED9i\\\";\\n                    font-size: 10px;\\n                    color: gray;\\n                }\\n\\n                @bottom-right {\\n                    content: counter(page) \\\" / \\\" counter(pages);\\n                    font-size: 10px;\\n                    color: gray;\\n                }\\n            }\\n            @media print {\\n                body {\\n                    -webkit-print-color-adjust: exact !important;\\n                    color-adjust: exact !important;\\n                    counter-reset: page;\\n                }\\n                /* Ensure content doesn't overlap */\\n                p, div, h1, h2, h3, h4, h5, h6 {\\n                    orphans: 3;\\n                    widows: 3;\\n                }\\n                /* Keep section headers with content */\\n                .print-section > div:first-child {\\n                    break-after: avoid !important;\\n                    page-break-after: avoid !important;\\n                }\\n                /* Minimal space between sections */\\n                .print-section {\\n                    margin-bottom: 2mm;\\n                    padding-top: 0.5mm;\\n                }\\n                /* First section doesn't need top padding */\\n                .first-section {\\n                    padding-top: 0 !important;\\n                }\\n                .pageNumber:before {\\n                    content: counter(page);\\n                }\\n                .totalPages:before {\\n                    content: counter(pages);\\n                }\\n            }\\n        \"\n  });\n  return /*#__PURE__*/_jsxDEV(LoadingData, {\n    loading: loading,\n    loadText: \"\\u0110ang t\\u1EA3i \\u0111\\u1EC1 thi\",\n    isNoData: questions ? false : true,\n    noDataText: \"Kh\\xF4ng c\\xF3 \\u0111\\u1EC1 thi.\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-4 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex w-full items-center no-print\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrint,\n          className: \"bg-sky-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-sky-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 15.575C11.8667 15.575 11.7417 15.5543 11.625 15.513C11.5083 15.4717 11.4 15.4007 11.3 15.3L7.7 11.7C7.5 11.5 7.404 11.2667 7.412 11C7.42 10.7333 7.516 10.5 7.7 10.3C7.9 10.1 8.13767 9.996 8.413 9.988C8.68833 9.98 8.92567 10.0757 9.125 10.275L11 12.15V5C11 4.71667 11.096 4.47934 11.288 4.288C11.48 4.09667 11.7173 4.00067 12 4C12.2827 3.99934 12.5203 4.09534 12.713 4.288C12.9057 4.48067 13.0013 4.718 13 5V12.15L14.875 10.275C15.075 10.075 15.3127 9.979 15.588 9.987C15.8633 9.995 16.1007 10.0993 16.3 10.3C16.4833 10.5 16.5793 10.7333 16.588 11C16.5967 11.2667 16.5007 11.5 16.3 11.7L12.7 15.3C12.6 15.4 12.4917 15.471 12.375 15.513C12.2583 15.555 12.1333 15.5757 12 15.575ZM6 20C5.45 20 4.97933 19.8043 4.588 19.413C4.19667 19.0217 4.00067 18.5507 4 18V16C4 15.7167 4.096 15.4793 4.288 15.288C4.48 15.0967 4.71733 15.0007 5 15C5.28267 14.9993 5.52033 15.0953 5.713 15.288C5.90567 15.4807 6.00133 15.718 6 16V18H18V16C18 15.7167 18.096 15.4793 18.288 15.288C18.48 15.0967 18.7173 15.0007 19 15C19.2827 14.9993 19.5203 15.0953 19.713 15.288C19.9057 15.4807 20.0013 15.718 20 16V18C20 18.55 19.8043 19.021 19.413 19.413C19.0217 19.805 18.5507 20.0007 18 20H6Z\",\n              fill: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-bevietnam\",\n            children: \" Xu\\u1EA5t PDF \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: examRef,\n        className: \"flex flex-col gap-4 bg-white print-container max-w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col w-full border border-black overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row h-auto sm:h-[12rem] items-center border-b border-black\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col justify-center items-center w-full sm:w-[25%] border-b sm:border-b-0 sm:border-r border-black p-3 sm:p-5 h-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold font-bevietnam text-center\",\n                children: \"L\\u1EDBp to\\xE1n th\\u1EA7y Bee\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[0.75rem] font-bevietnam text-center\",\n                children: \"GV. Ong Kh\\u1EAFc Ng\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(BeeMathLogo, {\n                className: \"w-8 h-8 sm:w-10 sm:h-10 mt-1 sm:mt-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col justify-center items-center text-center w-full sm:w-[50%] border-b sm:border-b-0 sm:border-r border-black p-3 sm:p-5 h-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm italic\",\n                children: \"Th\\u1EE9 ..., Ng\\xE0y ..., Th\\xE1ng ... n\\u0103m 2025\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm font-bold break-words\",\n                children: [\"L\\u1EDBp \", exam === null || exam === void 0 ? void 0 : exam.class, \" - \", exam === null || exam === void 0 ? void 0 : exam.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[0.65rem] sm:text-[0.75rem] font-bold\",\n                children: [\"Th\\u1EDDi gian: \", exam !== null && exam !== void 0 && exam.testDuration ? (exam === null || exam === void 0 ? void 0 : exam.testDuration) + ' phút' : 'vô thời hạn']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col justify-center items-center w-full sm:w-[25%] gap-1 sm:gap-2 p-3 sm:p-5 h-full\",\n              children: exam !== null && exam !== void 0 && exam.solutionUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(QRCodeComponent, {\n                  url: exam === null || exam === void 0 ? void 0 : exam.solutionUrl,\n                  size: 60\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[0.65rem] sm:text-[0.75rem] text-center\",\n                  children: \"Scan \\u0111\\u1EC3 xem \\u0111\\xE1p \\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[0.65rem] sm:text-[0.75rem] text-center\",\n                children: \"Kh\\xF4ng c\\xF3 \\u0111\\xE1p \\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col w-full sm:w-[75%] h-full border-b sm:border-b-0 sm:border-r border-black p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[0.65rem] sm:text-[0.75rem] truncate sm:whitespace-normal\",\n                children: \"H\\u1ECD v\\xE0 t\\xEAn: .............................................................................................................................\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[0.65rem] sm:text-[0.75rem] truncate sm:whitespace-normal\",\n                children: \"Tr\\u01B0\\u1EDDng: ............................................................................................................................\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center w-full sm:w-[25%] h-full p-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[0.75rem] sm:text-sm\",\n                children: \"\\u0110I\\u1EC2M: .......\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-break-header\",\n          style: {\n            breakInside: 'avoid',\n            pageBreakInside: 'avoid'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex w-full flex-col h-auto print-section first-section\",\n            style: {\n              breakBefore: 'avoid',\n              pageBreakBefore: 'avoid'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold mb-2\",\n                children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                  children: \"Ph\\u1EA7n I - Tr\\u1EAFc nghi\\u1EC7m\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col gap-4\",\n                children: questions.map(question => {\n                  if (question.typeOfQuestion === \"TN\") {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col print-question w-full overflow-hidden\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"question-content w-full\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-bold mb-1\",\n                          children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                            children: [\"C\\xE2u \", indexTN++, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-xs text-gray-500\",\n                              children: [\"(ID: \", question.id, \")\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 158,\n                              columnNumber: 90\n                            }, this), \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 158,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 157,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                          text: question.content,\n                          className: \"text-sm break-words w-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 160,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"question-media-and-statements avoid-page-break w-full mt-2\",\n                        children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-col items-center justify-center w-full h-[10rem] sm:h-[12rem] p-2 sm:p-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: question.imageUrl,\n                            alt: \"question\",\n                            className: \"object-contain w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 168,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"grid grid-cols-1 sm:grid-cols-2 \".concat(question.statements.some(s => s.content.length > 30) ? \"md:grid-cols-2\" : \"md:grid-cols-4\", \" gap-2\"),\n                          children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col w-full\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex gap-2 w-full\",\n                              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                                className: \"text-sm font-bold whitespace-nowrap\",\n                                children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                                  children: prefixStatementTN[index]\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 180,\n                                  columnNumber: 77\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 179,\n                                columnNumber: 73\n                              }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                                text: statement.content,\n                                className: \"break-words w-full text-sm\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 182,\n                                columnNumber: 73\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 178,\n                              columnNumber: 69\n                            }, this), statement.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex flex-col items-center justify-center w-full h-[10rem] mt-1\",\n                              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                                src: statement.imageUrl,\n                                alt: \"statement\",\n                                className: \"object-contain w-full h-full\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 186,\n                                columnNumber: 77\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 185,\n                              columnNumber: 73\n                            }, this)]\n                          }, statement._id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 177,\n                            columnNumber: 65\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 175,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 53\n                      }, this)]\n                    }, question.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 49\n                    }, this);\n                  }\n                  return null;\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex w-full flex-col h-auto print-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl font-bold mb-2\",\n              children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                children: \"Ph\\u1EA7n II - \\u0110\\xFAng sai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-4\",\n              children: questions.map(question => {\n                if (question.typeOfQuestion === \"DS\") {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col print-question w-full overflow-hidden\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"question-content w-full\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-bold mb-1\",\n                        children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                          children: [\"C\\xE2u \", indexDS++, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\"(ID: \", question.id, \")\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 219,\n                            columnNumber: 86\n                          }, this), \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 219,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                        text: question.content,\n                        className: \"text-sm break-words w-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"question-media-and-statements avoid-page-break w-full mt-2\",\n                      children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center w-full h-[10rem] sm:h-[12rem] p-2 sm:p-5\",\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: question.imageUrl,\n                          alt: \"question\",\n                          className: \"object-contain w-full h-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 228,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-col\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-start gap-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm font-bold whitespace-nowrap\",\n                              children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                                children: prefixStatementDS[index]\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 240,\n                                columnNumber: 73\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 239,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                              text: statement.content,\n                              className: \"text-sm break-words flex-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 242,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 65\n                          }, this), statement.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-start h-[10rem] mt-1 ml-6\",\n                            children: /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: statement.imageUrl,\n                              alt: \"statement\",\n                              className: \"object-contain w-full h-full\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 246,\n                              columnNumber: 73\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 245,\n                            columnNumber: 69\n                          }, this)]\n                        }, statement._id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 237,\n                          columnNumber: 61\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 49\n                    }, this)]\n                  }, question._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 45\n                  }, this);\n                }\n                return null;\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex w-full flex-col h-auto print-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl font-bold mb-2\",\n              children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                children: \"Ph\\u1EA7n III - Tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-4\",\n              children: questions.map(question => {\n                if (question.typeOfQuestion === \"TLN\") {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col print-question w-full overflow-hidden\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"question-content w-full\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-bold mb-1\",\n                        children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                          children: [\"C\\xE2u \", indexTLN++, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\"(ID: \", question.id, \")\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 278,\n                            columnNumber: 87\n                          }, this), \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 278,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                        text: question.content,\n                        className: \"text-sm break-words w-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"question-media-and-statements avoid-page-break w-full mt-2\",\n                      children: question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center w-full h-[10rem] sm:h-[12rem] p-2 sm:p-5\",\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: question.imageUrl,\n                          alt: \"question\",\n                          className: \"object-contain w-full h-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 287,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 286,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 49\n                    }, this)]\n                  }, question._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 45\n                  }, this);\n                }\n                return null;\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 9\n  }, this);\n};\n_s(PreviewExam, \"jHybZQ8qoie9gqS2XQavXxRTfwU=\", false, function () {\n  return [useSelector, useReactToPrint];\n});\n_c = PreviewExam;\nexport default PreviewExam;\nvar _c;\n$RefreshReg$(_c, \"PreviewExam\");", "map": {"version": 3, "names": ["fetchExamQuestions", "useDispatch", "useSelector", "useEffect", "useRef", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "useReactToPrint", "header", "BeeMathLogo", "QRCodeComponent", "NoTranslate", "MarkdownPreviewWithMath", "LoadingData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PreviewExam", "_ref", "_s", "questions", "exam", "prefixStatementTN", "prefixStatementDS", "indexTN", "indexDS", "indexTLN", "loading", "state", "states", "examRef", "handlePrint", "contentRef", "documentTitle", "name", "onBeforeGetContent", "document", "body", "classList", "add", "onAfterPrint", "remove", "pageStyle", "loadText", "isNoData", "noDataText", "children", "className", "onClick", "xmlns", "width", "height", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "class", "testDuration", "solutionUrl", "url", "size", "style", "breakInside", "pageBreakInside", "breakBefore", "pageBreakBefore", "map", "question", "typeOfQuestion", "id", "text", "content", "imageUrl", "src", "alt", "concat", "statements", "some", "s", "length", "statement", "index", "_id", "_c", "$RefreshReg$"], "sources": ["D:/ToanThayBee/frontend/src/components/detail/PreviewExam.jsx"], "sourcesContent": ["import { fetchExamQuestions } from \"../../features/question/questionSlice\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useEffect, useRef } from \"react\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\r\nimport { useReactToPrint } from 'react-to-print';\r\nimport header from \"../../assets/images/Screenshot 2025-03-18 010039.jpg\";\r\nimport { BeeMathLogo } from \"../logo/BeeMathLogo\";\r\nimport QRCodeComponent from \"../QrCode\";\r\nimport NoTranslate from \"../utils/NoTranslate\";\r\nimport \"../../styles/print-styles.css\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\nimport LoadingData from \"../loading/LoadingData\";\r\nconst PreviewExam = ({ questions, exam }) => {\r\n    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    let indexTN = 1;\r\n    let indexDS = 1;\r\n    let indexTLN = 1;\r\n    const { loading } = useSelector(state => state.states);\r\n    const examRef = useRef(null);\r\n    // const reactPrint = useReactToPrint();\r\n\r\n    const handlePrint = useReactToPrint({\r\n        contentRef: examRef,\r\n        documentTitle: exam?.name || \"De Thi\",\r\n        onBeforeGetContent: () => {\r\n            // Any preparation before printing\r\n            document.body.classList.add('printing');\r\n        },\r\n        onAfterPrint: () => {\r\n            // Cleanup after printing\r\n            document.body.classList.remove('printing');\r\n        },\r\n        pageStyle: `\r\n            @page {\r\n                size: A4;\r\n                margin: 15mm 10mm 20mm 10mm;\r\n                counter-increment: page;\r\n                padding: 4mm;\r\n                @bottom-left {\r\n                    content: \"Toán thầy Bee 0333726202 100 Bạch Mai, Hai Bà Trưng, Hà Nội\";\r\n                    font-size: 10px;\r\n                    color: gray;\r\n                }\r\n\r\n                @bottom-right {\r\n                    content: counter(page) \" / \" counter(pages);\r\n                    font-size: 10px;\r\n                    color: gray;\r\n                }\r\n            }\r\n            @media print {\r\n                body {\r\n                    -webkit-print-color-adjust: exact !important;\r\n                    color-adjust: exact !important;\r\n                    counter-reset: page;\r\n                }\r\n                /* Ensure content doesn't overlap */\r\n                p, div, h1, h2, h3, h4, h5, h6 {\r\n                    orphans: 3;\r\n                    widows: 3;\r\n                }\r\n                /* Keep section headers with content */\r\n                .print-section > div:first-child {\r\n                    break-after: avoid !important;\r\n                    page-break-after: avoid !important;\r\n                }\r\n                /* Minimal space between sections */\r\n                .print-section {\r\n                    margin-bottom: 2mm;\r\n                    padding-top: 0.5mm;\r\n                }\r\n                /* First section doesn't need top padding */\r\n                .first-section {\r\n                    padding-top: 0 !important;\r\n                }\r\n                .pageNumber:before {\r\n                    content: counter(page);\r\n                }\r\n                .totalPages:before {\r\n                    content: counter(pages);\r\n                }\r\n            }\r\n        `,\r\n    });\r\n\r\n    return (\r\n        <LoadingData loading={loading} loadText=\"Đang tải đề thi\" isNoData={questions ? false : true} noDataText=\"Không có đề thi.\">\r\n            <div className=\"flex flex-col gap-4 w-full\">\r\n                <div className=\"flex w-full items-center no-print\">\r\n                    <button\r\n                        onClick={handlePrint}\r\n                        className=\"bg-sky-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-sky-600\"\r\n                    >\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                            <path d=\"M12 15.575C11.8667 15.575 11.7417 15.5543 11.625 15.513C11.5083 15.4717 11.4 15.4007 11.3 15.3L7.7 11.7C7.5 11.5 7.404 11.2667 7.412 11C7.42 10.7333 7.516 10.5 7.7 10.3C7.9 10.1 8.13767 9.996 8.413 9.988C8.68833 9.98 8.92567 10.0757 9.125 10.275L11 12.15V5C11 4.71667 11.096 4.47934 11.288 4.288C11.48 4.09667 11.7173 4.00067 12 4C12.2827 3.99934 12.5203 4.09534 12.713 4.288C12.9057 4.48067 13.0013 4.718 13 5V12.15L14.875 10.275C15.075 10.075 15.3127 9.979 15.588 9.987C15.8633 9.995 16.1007 10.0993 16.3 10.3C16.4833 10.5 16.5793 10.7333 16.588 11C16.5967 11.2667 16.5007 11.5 16.3 11.7L12.7 15.3C12.6 15.4 12.4917 15.471 12.375 15.513C12.2583 15.555 12.1333 15.5757 12 15.575ZM6 20C5.45 20 4.97933 19.8043 4.588 19.413C4.19667 19.0217 4.00067 18.5507 4 18V16C4 15.7167 4.096 15.4793 4.288 15.288C4.48 15.0967 4.71733 15.0007 5 15C5.28267 14.9993 5.52033 15.0953 5.713 15.288C5.90567 15.4807 6.00133 15.718 6 16V18H18V16C18 15.7167 18.096 15.4793 18.288 15.288C18.48 15.0967 18.7173 15.0007 19 15C19.2827 14.9993 19.5203 15.0953 19.713 15.288C19.9057 15.4807 20.0013 15.718 20 16V18C20 18.55 19.8043 19.021 19.413 19.413C19.0217 19.805 18.5507 20.0007 18 20H6Z\" fill=\"white\" />\r\n                        </svg>\r\n                        <p className=\"text-sm font-bevietnam\"> Xuất PDF </p>\r\n                    </button>\r\n                </div>\r\n\r\n\r\n                <div ref={examRef} className=\"flex flex-col gap-4 bg-white print-container max-w-full\">\r\n                    <div className=\"flex flex-col w-full border border-black overflow-hidden\">\r\n                        <div className=\"flex flex-col sm:flex-row h-auto sm:h-[12rem] items-center border-b border-black\">\r\n                            {/* Cột trái */}\r\n                            <div className=\"flex flex-col justify-center items-center w-full sm:w-[25%] border-b sm:border-b-0 sm:border-r border-black p-3 sm:p-5 h-full\">\r\n                                <div className=\"text-sm font-bold font-bevietnam text-center\">Lớp toán thầy Bee</div>\r\n                                <div className=\"text-[0.75rem] font-bevietnam text-center\">GV. Ong Khắc Ngọc</div>\r\n                                <BeeMathLogo className=\"w-8 h-8 sm:w-10 sm:h-10 mt-1 sm:mt-2\" />\r\n                            </div>\r\n\r\n                            {/* Cột giữa */}\r\n                            <div className=\"flex flex-col justify-center items-center text-center w-full sm:w-[50%] border-b sm:border-b-0 sm:border-r border-black p-3 sm:p-5 h-full\">\r\n                                <div className=\"text-xs sm:text-sm italic\">Thứ ..., Ngày ..., Tháng ... năm 2025</div>\r\n                                <div className=\"text-xs sm:text-sm font-bold break-words\">Lớp {exam?.class} - {exam?.name}</div>\r\n                                <div className=\"text-[0.65rem] sm:text-[0.75rem] font-bold\">Thời gian: {exam?.testDuration ? exam?.testDuration + ' phút' : 'vô thời hạn'}</div>\r\n                            </div>\r\n\r\n                            {/* Cột phải */}\r\n                            <div className=\"flex flex-col justify-center items-center w-full sm:w-[25%] gap-1 sm:gap-2 p-3 sm:p-5 h-full\">\r\n                                {exam?.solutionUrl ? (\r\n                                    <>\r\n                                        <QRCodeComponent url={exam?.solutionUrl} size={60} />\r\n                                        <div className=\"text-[0.65rem] sm:text-[0.75rem] text-center\">Scan để xem đáp án</div>\r\n                                    </>\r\n                                ) : (\r\n                                    <div className=\"text-[0.65rem] sm:text-[0.75rem] text-center\">Không có đáp án</div>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex flex-col sm:flex-row items-center\">\r\n                            <div className=\"flex flex-col w-full sm:w-[75%] h-full border-b sm:border-b-0 sm:border-r border-black p-2\">\r\n                                <div className=\"text-[0.65rem] sm:text-[0.75rem] truncate sm:whitespace-normal\">Họ và tên: .............................................................................................................................</div>\r\n                                <div className=\"text-[0.65rem] sm:text-[0.75rem] truncate sm:whitespace-normal\">Trường: ............................................................................................................................</div>\r\n                            </div>\r\n                            <div className=\"flex flex-col items-center w-full sm:w-[25%] h-full p-2\">\r\n                                <div className=\"text-[0.75rem] sm:text-sm\">ĐIỂM: .......</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"no-break-header\" style={{ breakInside: 'avoid', pageBreakInside: 'avoid' }}>\r\n                        <div className=\"flex w-full flex-col h-auto print-section first-section\" style={{ breakBefore: 'avoid', pageBreakBefore: 'avoid' }}>\r\n                            <div className=\"w-full\">\r\n                                <div className=\"text-xl font-bold mb-2\">\r\n                                    <NoTranslate>Phần I - Trắc nghiệm</NoTranslate>\r\n                                </div>\r\n                                <div className=\"flex flex-col gap-4\">\r\n                                    {questions.map((question) => {\r\n                                        if (question.typeOfQuestion === \"TN\") {\r\n                                            return (\r\n                                                <div key={question.id} className=\"flex flex-col print-question w-full overflow-hidden\">\r\n                                                    {/* <!-- notranslate --> Phần nội dung câu hỏi - không tự động sang trang */}\r\n                                                    <div className=\"question-content w-full\">\r\n                                                        <p className=\"text-sm font-bold mb-1\">\r\n                                                            <NoTranslate>Câu {indexTN++} <span className=\"text-xs text-gray-500\">(ID: {question.id})</span>:</NoTranslate>\r\n                                                        </p>\r\n                                                        <LatexRenderer text={question.content} className=\"text-sm break-words w-full\" />\r\n                                                        {/* <MarkdownPreviewWithMath content={question.content} className=\"text-sm break-words w-full\"/> */}\r\n                                                    </div>\r\n\r\n                                                    {/* <!-- notranslate --> Phần hình ảnh và mệnh đề - có thể sang trang khi cần */}\r\n                                                    <div className=\"question-media-and-statements avoid-page-break w-full mt-2\">\r\n                                                        {question.imageUrl && (\r\n                                                            <div className=\"flex flex-col items-center justify-center w-full h-[10rem] sm:h-[12rem] p-2 sm:p-5\">\r\n                                                                <img\r\n                                                                    src={question.imageUrl}\r\n                                                                    alt=\"question\"\r\n                                                                    className=\"object-contain w-full h-full\"\r\n                                                                />\r\n                                                            </div>\r\n                                                        )}\r\n                                                        <div className={`grid grid-cols-1 sm:grid-cols-2 ${question.statements.some(s => s.content.length > 30) ? \"md:grid-cols-2\" : \"md:grid-cols-4\"} gap-2`}>\r\n                                                            {question.statements.map((statement, index) => (\r\n                                                                <div key={statement._id} className=\"flex flex-col w-full\">\r\n                                                                    <div className=\"flex gap-2 w-full\">\r\n                                                                        <p className=\"text-sm font-bold whitespace-nowrap\">\r\n                                                                            <NoTranslate>{prefixStatementTN[index]}</NoTranslate>\r\n                                                                        </p>\r\n                                                                        <LatexRenderer text={statement.content} className=\"break-words w-full text-sm\" />\r\n                                                                    </div>\r\n                                                                    {statement.imageUrl && (\r\n                                                                        <div className=\"flex flex-col items-center justify-center w-full h-[10rem] mt-1\">\r\n                                                                            <img\r\n                                                                                src={statement.imageUrl}\r\n                                                                                alt=\"statement\"\r\n                                                                                className=\"object-contain w-full h-full\"\r\n                                                                            />\r\n                                                                        </div>\r\n                                                                    )}\r\n                                                                </div>\r\n                                                            ))}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            );\r\n                                        }\r\n                                        return null;\r\n                                    })}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"flex w-full flex-col h-auto print-section\">\r\n                        <div className=\"w-full\">\r\n                            <div className=\"text-xl font-bold mb-2\">\r\n                                <NoTranslate>Phần II - Đúng sai</NoTranslate>\r\n                            </div>\r\n                            <div className=\"flex flex-col gap-4\">\r\n                                {questions.map((question) => {\r\n                                    if (question.typeOfQuestion === \"DS\") {\r\n                                        return (\r\n                                            <div key={question._id} className=\"flex flex-col print-question w-full overflow-hidden\">\r\n                                                {/* <!-- notranslate --> Phần nội dung câu hỏi - không tự động sang trang */}\r\n                                                <div className=\"question-content w-full\">\r\n                                                    <div className=\"text-sm font-bold mb-1\">\r\n                                                        <NoTranslate>Câu {indexDS++} <span className=\"text-xs text-gray-500\">(ID: {question.id})</span>:</NoTranslate>\r\n                                                    </div>\r\n                                                    <LatexRenderer text={question.content} className=\"text-sm break-words w-full\" />\r\n                                                </div>\r\n\r\n                                                {/* <!-- notranslate --> Phần hình ảnh và mệnh đề - có thể sang trang khi cần */}\r\n                                                <div className=\"question-media-and-statements avoid-page-break w-full mt-2\">\r\n                                                    {question.imageUrl && (\r\n                                                        <div className=\"flex items-center justify-center w-full h-[10rem] sm:h-[12rem] p-2 sm:p-5\">\r\n                                                            <img\r\n                                                                src={question.imageUrl}\r\n                                                                alt=\"question\"\r\n                                                                className=\"object-contain w-full h-full\"\r\n                                                            />\r\n                                                        </div>\r\n                                                    )}\r\n                                                    <div className=\"flex flex-col gap-2\">\r\n                                                        {question.statements.map((statement, index) => (\r\n                                                            <div key={statement._id} className=\"flex flex-col\">\r\n                                                                <div className=\"flex items-start gap-2\">\r\n                                                                    <p className=\"text-sm font-bold whitespace-nowrap\">\r\n                                                                        <NoTranslate>{prefixStatementDS[index]}</NoTranslate>\r\n                                                                    </p>\r\n                                                                    <LatexRenderer text={statement.content} className=\"text-sm break-words flex-1\" />\r\n                                                                </div>\r\n                                                                {statement.imageUrl && (\r\n                                                                    <div className=\"flex justify-start h-[10rem] mt-1 ml-6\">\r\n                                                                        <img\r\n                                                                            src={statement.imageUrl}\r\n                                                                            alt=\"statement\"\r\n                                                                            className=\"object-contain w-full h-full\"\r\n                                                                        />\r\n                                                                    </div>\r\n                                                                )}\r\n                                                            </div>\r\n                                                        ))}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        );\r\n                                    }\r\n                                    return null;\r\n                                })}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"flex w-full flex-col h-auto print-section\">\r\n                        <div className=\"w-full\">\r\n                            <div className=\"text-xl font-bold mb-2\">\r\n                                <NoTranslate>Phần III - Trả lời ngắn</NoTranslate>\r\n                            </div>\r\n                            <div className=\"flex flex-col gap-4\">\r\n                                {questions.map((question) => {\r\n                                    if (question.typeOfQuestion === \"TLN\") {\r\n                                        return (\r\n                                            <div key={question._id} className=\"flex flex-col print-question w-full overflow-hidden\">\r\n                                                {/* <!-- notranslate --> Phần nội dung câu hỏi - không tự động sang trang */}\r\n                                                <div className=\"question-content w-full\">\r\n                                                    <div className=\"text-sm font-bold mb-1\">\r\n                                                        <NoTranslate>Câu {indexTLN++} <span className=\"text-xs text-gray-500\">(ID: {question.id})</span>:</NoTranslate>\r\n                                                    </div>\r\n                                                    <LatexRenderer text={question.content} className=\"text-sm break-words w-full\" />\r\n                                                </div>\r\n\r\n                                                {/* <!-- notranslate --> Phần hình ảnh - có thể sang trang khi cần */}\r\n                                                <div className=\"question-media-and-statements avoid-page-break w-full mt-2\">\r\n                                                    {question.imageUrl &&\r\n                                                        <div className=\"flex items-center justify-center w-full h-[10rem] sm:h-[12rem] p-2 sm:p-5\">\r\n                                                            <img\r\n                                                                src={question.imageUrl}\r\n                                                                alt=\"question\"\r\n                                                                className=\"object-contain w-full h-full\"\r\n                                                            />\r\n                                                        </div>\r\n                                                    }\r\n                                                </div>\r\n                                            </div>\r\n                                        );\r\n                                    }\r\n                                    return null;\r\n                                })}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n\r\n            </div>\r\n        </LoadingData >\r\n    );\r\n};\r\n\r\nexport default PreviewExam;\r\n"], "mappings": ";;AAAA,SAASA,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,MAAM,MAAM,sDAAsD;AACzE,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAOC,eAAe,MAAM,WAAW;AACvC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAO,+BAA+B;AACtC,OAAOC,uBAAuB,MAAM,0BAA0B;AAC9D,OAAOC,WAAW,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACjD,MAAMC,WAAW,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAxB;IAAEC,SAAS;IAAEC;EAAK,CAAC,GAAAH,IAAA;EACpC,MAAMI,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtF,MAAMC,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtF,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,QAAQ,GAAG,CAAC;EAChB,MAAM;IAAEC;EAAQ,CAAC,GAAG1B,WAAW,CAAC2B,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EACtD,MAAMC,OAAO,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAC5B;;EAEA,MAAM4B,WAAW,GAAGzB,eAAe,CAAC;IAChC0B,UAAU,EAAEF,OAAO;IACnBG,aAAa,EAAE,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,IAAI,KAAI,QAAQ;IACrCC,kBAAkB,EAAEA,CAAA,KAAM;MACtB;MACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IAC3C,CAAC;IACDC,YAAY,EAAEA,CAAA,KAAM;MAChB;MACAJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,MAAM,CAAC,UAAU,CAAC;IAC9C,CAAC;IACDC,SAAS;EAmDb,CAAC,CAAC;EAEF,oBACI5B,OAAA,CAACF,WAAW;IAACe,OAAO,EAAEA,OAAQ;IAACgB,QAAQ,EAAC,qCAAiB;IAACC,QAAQ,EAAExB,SAAS,GAAG,KAAK,GAAG,IAAK;IAACyB,UAAU,EAAC,kCAAkB;IAAAC,QAAA,eACvHhC,OAAA;MAAKiC,SAAS,EAAC,4BAA4B;MAAAD,QAAA,gBACvChC,OAAA;QAAKiC,SAAS,EAAC,mCAAmC;QAAAD,QAAA,eAC9ChC,OAAA;UACIkC,OAAO,EAAEjB,WAAY;UACrBgB,SAAS,EAAC,kFAAkF;UAAAD,QAAA,gBAE5FhC,OAAA;YAAKmC,KAAK,EAAC,4BAA4B;YAACC,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAP,QAAA,eAC1FhC,OAAA;cAAMwC,CAAC,EAAC,6oCAA6oC;cAACD,IAAI,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpqC,CAAC,eACN5C,OAAA;YAAGiC,SAAS,EAAC,wBAAwB;YAAAD,QAAA,EAAC;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGN5C,OAAA;QAAK6C,GAAG,EAAE7B,OAAQ;QAACiB,SAAS,EAAC,yDAAyD;QAAAD,QAAA,gBAClFhC,OAAA;UAAKiC,SAAS,EAAC,0DAA0D;UAAAD,QAAA,gBACrEhC,OAAA;YAAKiC,SAAS,EAAC,kFAAkF;YAAAD,QAAA,gBAE7FhC,OAAA;cAAKiC,SAAS,EAAC,+HAA+H;cAAAD,QAAA,gBAC1IhC,OAAA;gBAAKiC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAiB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrF5C,OAAA;gBAAKiC,SAAS,EAAC,2CAA2C;gBAAAD,QAAA,EAAC;cAAiB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClF5C,OAAA,CAACN,WAAW;gBAACuC,SAAS,EAAC;cAAsC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eAGN5C,OAAA;cAAKiC,SAAS,EAAC,2IAA2I;cAAAD,QAAA,gBACtJhC,OAAA;gBAAKiC,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,EAAC;cAAqC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtF5C,OAAA;gBAAKiC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,WAAI,EAACzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,KAAK,EAAC,KAAG,EAACvC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,IAAI;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChG5C,OAAA;gBAAKiC,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,GAAC,kBAAW,EAACzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwC,YAAY,GAAG,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,YAAY,IAAG,OAAO,GAAG,aAAa;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/I,CAAC,eAGN5C,OAAA;cAAKiC,SAAS,EAAC,8FAA8F;cAAAD,QAAA,EACxGzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyC,WAAW,gBACdhD,OAAA,CAAAE,SAAA;gBAAA8B,QAAA,gBACIhC,OAAA,CAACL,eAAe;kBAACsD,GAAG,EAAE1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,WAAY;kBAACE,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrD5C,OAAA;kBAAKiC,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,eACxF,CAAC,gBAEH5C,OAAA;gBAAKiC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YACrF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN5C,OAAA;YAAKiC,SAAS,EAAC,wCAAwC;YAAAD,QAAA,gBACnDhC,OAAA;cAAKiC,SAAS,EAAC,4FAA4F;cAAAD,QAAA,gBACvGhC,OAAA;gBAAKiC,SAAS,EAAC,gEAAgE;gBAAAD,QAAA,EAAC;cAAwI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9N5C,OAAA;gBAAKiC,SAAS,EAAC,gEAAgE;gBAAAD,QAAA,EAAC;cAAoI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzN,CAAC,eACN5C,OAAA;cAAKiC,SAAS,EAAC,yDAAyD;cAAAD,QAAA,eACpEhC,OAAA;gBAAKiC,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN5C,OAAA;UAAKiC,SAAS,EAAC,iBAAiB;UAACkB,KAAK,EAAE;YAAEC,WAAW,EAAE,OAAO;YAAEC,eAAe,EAAE;UAAQ,CAAE;UAAArB,QAAA,eACvFhC,OAAA;YAAKiC,SAAS,EAAC,yDAAyD;YAACkB,KAAK,EAAE;cAAEG,WAAW,EAAE,OAAO;cAAEC,eAAe,EAAE;YAAQ,CAAE;YAAAvB,QAAA,eAC/HhC,OAAA;cAAKiC,SAAS,EAAC,QAAQ;cAAAD,QAAA,gBACnBhC,OAAA;gBAAKiC,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,eACnChC,OAAA,CAACJ,WAAW;kBAAAoC,QAAA,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACN5C,OAAA;gBAAKiC,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAC/B1B,SAAS,CAACkD,GAAG,CAAEC,QAAQ,IAAK;kBACzB,IAAIA,QAAQ,CAACC,cAAc,KAAK,IAAI,EAAE;oBAClC,oBACI1D,OAAA;sBAAuBiC,SAAS,EAAC,qDAAqD;sBAAAD,QAAA,gBAElFhC,OAAA;wBAAKiC,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,gBACpChC,OAAA;0BAAGiC,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,eACjChC,OAAA,CAACJ,WAAW;4BAAAoC,QAAA,GAAC,SAAI,EAACtB,OAAO,EAAE,EAAC,GAAC,eAAAV,OAAA;8BAAMiC,SAAS,EAAC,uBAAuB;8BAAAD,QAAA,GAAC,OAAK,EAACyB,QAAQ,CAACE,EAAE,EAAC,GAAC;4BAAA;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,KAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAa;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/G,CAAC,eACJ5C,OAAA,CAACV,aAAa;0BAACsE,IAAI,EAAEH,QAAQ,CAACI,OAAQ;0BAAC5B,SAAS,EAAC;wBAA4B;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAE/E,CAAC,eAGN5C,OAAA;wBAAKiC,SAAS,EAAC,4DAA4D;wBAAAD,QAAA,GACtEyB,QAAQ,CAACK,QAAQ,iBACd9D,OAAA;0BAAKiC,SAAS,EAAC,oFAAoF;0BAAAD,QAAA,eAC/FhC,OAAA;4BACI+D,GAAG,EAAEN,QAAQ,CAACK,QAAS;4BACvBE,GAAG,EAAC,UAAU;4BACd/B,SAAS,EAAC;0BAA8B;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CACR,eACD5C,OAAA;0BAAKiC,SAAS,qCAAAgC,MAAA,CAAqCR,QAAQ,CAACS,UAAU,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,OAAO,CAACQ,MAAM,GAAG,EAAE,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,WAAS;0BAAArC,QAAA,EACjJyB,QAAQ,CAACS,UAAU,CAACV,GAAG,CAAC,CAACc,SAAS,EAAEC,KAAK,kBACtCvE,OAAA;4BAAyBiC,SAAS,EAAC,sBAAsB;4BAAAD,QAAA,gBACrDhC,OAAA;8BAAKiC,SAAS,EAAC,mBAAmB;8BAAAD,QAAA,gBAC9BhC,OAAA;gCAAGiC,SAAS,EAAC,qCAAqC;gCAAAD,QAAA,eAC9ChC,OAAA,CAACJ,WAAW;kCAAAoC,QAAA,EAAExB,iBAAiB,CAAC+D,KAAK;gCAAC;kCAAA9B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAc;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACtD,CAAC,eACJ5C,OAAA,CAACV,aAAa;gCAACsE,IAAI,EAAEU,SAAS,CAACT,OAAQ;gCAAC5B,SAAS,EAAC;8BAA4B;gCAAAQ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChF,CAAC,EACL0B,SAAS,CAACR,QAAQ,iBACf9D,OAAA;8BAAKiC,SAAS,EAAC,iEAAiE;8BAAAD,QAAA,eAC5EhC,OAAA;gCACI+D,GAAG,EAAEO,SAAS,CAACR,QAAS;gCACxBE,GAAG,EAAC,WAAW;gCACf/B,SAAS,EAAC;8BAA8B;gCAAAQ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3C;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACD,CACR;0BAAA,GAfK0B,SAAS,CAACE,GAAG;4BAAA/B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAgBlB,CACR;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA,GA1CAa,QAAQ,CAACE,EAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA2ChB,CAAC;kBAEd;kBACA,OAAO,IAAI;gBACf,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN5C,OAAA;UAAKiC,SAAS,EAAC,2CAA2C;UAAAD,QAAA,eACtDhC,OAAA;YAAKiC,SAAS,EAAC,QAAQ;YAAAD,QAAA,gBACnBhC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAD,QAAA,eACnChC,OAAA,CAACJ,WAAW;gBAAAoC,QAAA,EAAC;cAAkB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN5C,OAAA;cAAKiC,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAC/B1B,SAAS,CAACkD,GAAG,CAAEC,QAAQ,IAAK;gBACzB,IAAIA,QAAQ,CAACC,cAAc,KAAK,IAAI,EAAE;kBAClC,oBACI1D,OAAA;oBAAwBiC,SAAS,EAAC,qDAAqD;oBAAAD,QAAA,gBAEnFhC,OAAA;sBAAKiC,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,gBACpChC,OAAA;wBAAKiC,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eACnChC,OAAA,CAACJ,WAAW;0BAAAoC,QAAA,GAAC,SAAI,EAACrB,OAAO,EAAE,EAAC,GAAC,eAAAX,OAAA;4BAAMiC,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,GAAC,OAAK,EAACyB,QAAQ,CAACE,EAAE,EAAC,GAAC;0BAAA;4BAAAlB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,KAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7G,CAAC,eACN5C,OAAA,CAACV,aAAa;wBAACsE,IAAI,EAAEH,QAAQ,CAACI,OAAQ;wBAAC5B,SAAS,EAAC;sBAA4B;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,eAGN5C,OAAA;sBAAKiC,SAAS,EAAC,4DAA4D;sBAAAD,QAAA,GACtEyB,QAAQ,CAACK,QAAQ,iBACd9D,OAAA;wBAAKiC,SAAS,EAAC,2EAA2E;wBAAAD,QAAA,eACtFhC,OAAA;0BACI+D,GAAG,EAAEN,QAAQ,CAACK,QAAS;0BACvBE,GAAG,EAAC,UAAU;0BACd/B,SAAS,EAAC;wBAA8B;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CACR,eACD5C,OAAA;wBAAKiC,SAAS,EAAC,qBAAqB;wBAAAD,QAAA,EAC/ByB,QAAQ,CAACS,UAAU,CAACV,GAAG,CAAC,CAACc,SAAS,EAAEC,KAAK,kBACtCvE,OAAA;0BAAyBiC,SAAS,EAAC,eAAe;0BAAAD,QAAA,gBAC9ChC,OAAA;4BAAKiC,SAAS,EAAC,wBAAwB;4BAAAD,QAAA,gBACnChC,OAAA;8BAAGiC,SAAS,EAAC,qCAAqC;8BAAAD,QAAA,eAC9ChC,OAAA,CAACJ,WAAW;gCAAAoC,QAAA,EAAEvB,iBAAiB,CAAC8D,KAAK;8BAAC;gCAAA9B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAc;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtD,CAAC,eACJ5C,OAAA,CAACV,aAAa;8BAACsE,IAAI,EAAEU,SAAS,CAACT,OAAQ;8BAAC5B,SAAS,EAAC;4BAA4B;8BAAAQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChF,CAAC,EACL0B,SAAS,CAACR,QAAQ,iBACf9D,OAAA;4BAAKiC,SAAS,EAAC,wCAAwC;4BAAAD,QAAA,eACnDhC,OAAA;8BACI+D,GAAG,EAAEO,SAAS,CAACR,QAAS;8BACxBE,GAAG,EAAC,WAAW;8BACf/B,SAAS,EAAC;4BAA8B;8BAAAQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3C;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CACR;wBAAA,GAfK0B,SAAS,CAACE,GAAG;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAgBlB,CACR;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA,GAzCAa,QAAQ,CAACe,GAAG;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0CjB,CAAC;gBAEd;gBACA,OAAO,IAAI;cACf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN5C,OAAA;UAAKiC,SAAS,EAAC,2CAA2C;UAAAD,QAAA,eACtDhC,OAAA;YAAKiC,SAAS,EAAC,QAAQ;YAAAD,QAAA,gBACnBhC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAD,QAAA,eACnChC,OAAA,CAACJ,WAAW;gBAAAoC,QAAA,EAAC;cAAuB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACN5C,OAAA;cAAKiC,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAC/B1B,SAAS,CAACkD,GAAG,CAAEC,QAAQ,IAAK;gBACzB,IAAIA,QAAQ,CAACC,cAAc,KAAK,KAAK,EAAE;kBACnC,oBACI1D,OAAA;oBAAwBiC,SAAS,EAAC,qDAAqD;oBAAAD,QAAA,gBAEnFhC,OAAA;sBAAKiC,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,gBACpChC,OAAA;wBAAKiC,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eACnChC,OAAA,CAACJ,WAAW;0BAAAoC,QAAA,GAAC,SAAI,EAACpB,QAAQ,EAAE,EAAC,GAAC,eAAAZ,OAAA;4BAAMiC,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,GAAC,OAAK,EAACyB,QAAQ,CAACE,EAAE,EAAC,GAAC;0BAAA;4BAAAlB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,KAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9G,CAAC,eACN5C,OAAA,CAACV,aAAa;wBAACsE,IAAI,EAAEH,QAAQ,CAACI,OAAQ;wBAAC5B,SAAS,EAAC;sBAA4B;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,eAGN5C,OAAA;sBAAKiC,SAAS,EAAC,4DAA4D;sBAAAD,QAAA,EACtEyB,QAAQ,CAACK,QAAQ,iBACd9D,OAAA;wBAAKiC,SAAS,EAAC,2EAA2E;wBAAAD,QAAA,eACtFhC,OAAA;0BACI+D,GAAG,EAAEN,QAAQ,CAACK,QAAS;0BACvBE,GAAG,EAAC,UAAU;0BACd/B,SAAS,EAAC;wBAA8B;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAET,CAAC;kBAAA,GApBAa,QAAQ,CAACe,GAAG;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBjB,CAAC;gBAEd;gBACA,OAAO,IAAI;cACf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEvB,CAAC;AAACvC,EAAA,CAvSIF,WAAW;EAAA,QAMOhB,WAAW,EAIXK,eAAe;AAAA;AAAAiF,EAAA,GAVjCtE,WAAW;AAySjB,eAAeA,WAAW;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}