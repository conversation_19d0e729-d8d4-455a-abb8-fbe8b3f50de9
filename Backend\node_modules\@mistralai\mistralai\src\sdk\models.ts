/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { modelsArchive } from "../funcs/modelsArchive.js";
import { modelsDelete } from "../funcs/modelsDelete.js";
import { modelsList } from "../funcs/modelsList.js";
import { modelsRetrieve } from "../funcs/modelsRetrieve.js";
import { modelsUnarchive } from "../funcs/modelsUnarchive.js";
import { modelsUpdate } from "../funcs/modelsUpdate.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
import { unwrapAsync } from "../types/fp.js";

export class Models extends ClientSDK {
  /**
   * List Models
   *
   * @remarks
   * List all models available to the user.
   */
  async list(
    options?: RequestOptions,
  ): Promise<components.ModelList> {
    return unwrapAsync(modelsList(
      this,
      options,
    ));
  }

  /**
   * Retrieve Model
   *
   * @remarks
   * Retrieve information about a model.
   */
  async retrieve(
    request: operations.RetrieveModelV1ModelsModelIdGetRequest,
    options?: RequestOptions,
  ): Promise<
    operations.RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet
  > {
    return unwrapAsync(modelsRetrieve(
      this,
      request,
      options,
    ));
  }

  /**
   * Delete Model
   *
   * @remarks
   * Delete a fine-tuned model.
   */
  async delete(
    request: operations.DeleteModelV1ModelsModelIdDeleteRequest,
    options?: RequestOptions,
  ): Promise<components.DeleteModelOut> {
    return unwrapAsync(modelsDelete(
      this,
      request,
      options,
    ));
  }

  /**
   * Update Fine Tuned Model
   *
   * @remarks
   * Update a model name or description.
   */
  async update(
    request: operations.JobsApiRoutesFineTuningUpdateFineTunedModelRequest,
    options?: RequestOptions,
  ): Promise<operations.JobsApiRoutesFineTuningUpdateFineTunedModelResponse> {
    return unwrapAsync(modelsUpdate(
      this,
      request,
      options,
    ));
  }

  /**
   * Archive Fine Tuned Model
   *
   * @remarks
   * Archive a fine-tuned model.
   */
  async archive(
    request: operations.JobsApiRoutesFineTuningArchiveFineTunedModelRequest,
    options?: RequestOptions,
  ): Promise<components.ArchiveFTModelOut> {
    return unwrapAsync(modelsArchive(
      this,
      request,
      options,
    ));
  }

  /**
   * Unarchive Fine Tuned Model
   *
   * @remarks
   * Un-archive a fine-tuned model.
   */
  async unarchive(
    request: operations.JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest,
    options?: RequestOptions,
  ): Promise<components.UnarchiveFTModelOut> {
    return unwrapAsync(modelsUnarchive(
      this,
      request,
      options,
    ));
  }
}
