import OutsideClickWrapper from "../common/OutsideClickWrapper";
import { useState, useEffect } from "react";
import { ChevronDown, Check } from "lucide-react";

const FilterBar = ({
    w = "60",
    selected,
    onChange,
    filterOptions = [],
    Icon,
    title,
    ignoreOutsideClick
}) => {
    const [showSortDropdown, setShowSortDropdown] = useState(false);
    const [selectedOption, setSelectedOption] = useState(null);

    const extendedOptions = [{ code: "", description: "Tất cả" }, ...filterOptions];

    const toggleDropdown = () => setShowSortDropdown(prev => !prev);

    const handleOptionClick = (option) => {
        onChange?.(option.code);
        setSelectedOption(option);
        setShowSortDropdown(false);
    };

    useEffect(() => {
        const newSelected = extendedOptions.find(opt => opt.code === selected);
        if (newSelected) setSelectedOption(newSelected);
        else setSelectedOption(extendedOptions[0]);
    }, [selected, filterOptions]); // filterOptions dependency để tự động cập nhật lại danh sách

    return (
        <div className="min-w-[200px] relative inline-block text-left">
            <div
                onClick={toggleDropdown}
                className={`flex ${ignoreOutsideClick} items-center justify-between gap-1 cursor-pointer border border-gray-300 rounded-md px-2 py-[7px] text-gray-700 hover:border-sky-500 hover:bg-sky-50 transition-all duration-150`}
            >
                <div className="flex items-center gap-1">
                    {Icon && <Icon size={16} className="text-sky-600 flex-shrink-0" />}
                    <span className="text-xs">
                        {title}:{" "}
                        <span className="font-semibold text-gray-800">
                            {selectedOption?.description || "Tất cả"}
                        </span>
                    </span>
                </div>
                <ChevronDown size={14} />
            </div>

            {showSortDropdown && (
                <OutsideClickWrapper
                    ignoreOutsideClick={ignoreOutsideClick}
                    onClickOutside={() => setShowSortDropdown(false)}
                    className={`absolute z-10 mt-1 w-${w} bg-white border border-gray-300 rounded-md shadow-md right-0 max-h-[400px] overflow-y-auto`}
                >
                    {extendedOptions.map((option) => (
                        <div
                            key={option.code}
                            onClick={() => handleOptionClick(option)}
                            className={`flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-sky-100 cursor-pointer ${selected === option.code
                                    ? "bg-sky-50 font-medium text-sky-700"
                                    : ""
                                }`}
                        >
                            {option.description}
                            {selected === option.code && (
                                <Check size={14} className="ml-2 flex-shrink-0" />
                            )}
                        </div>
                    ))}
                </OutsideClickWrapper>
            )}
        </div>
    );
};

export default FilterBar;
