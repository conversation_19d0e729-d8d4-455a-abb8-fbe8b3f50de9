import { useSelector, useDispatch } from "react-redux";
import { fetchPublicQuestionsByExamId } from "../../features/question/questionSlice";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PreviewExam from "../detail/PreviewExam";
import LoadingText from "../loading/LoadingText";
import { Eye, FileText, Clock, User } from "lucide-react";

const PreviewView = ({ examId }) => {
    const { exam } = useSelector((state) => state.examDetail);
    const { questions, loading } = useSelector((state) => state.questions);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    useEffect(() => {
        dispatch(fetchPublicQuestionsByExamId(examId));
    }, [dispatch, examId]);

    useEffect(() => {
        if (exam?.seeCorrectAnswer === false) {
            navigate(`/practice/exam/${examId}`);
        }
    }, [exam, examId, navigate]);

    if (loading) {
        return (
            <div className="flex flex-col border border-gray-300 rounded-md">
                {/* Header */}
                <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                    <div className="flex items-center gap-2">
                        <Eye size={16} className="text-sky-600" />
                        <p className="font-Inter text-sm font-semibold">Xem trước đề thi</p>
                    </div>
                    <div className="flex items-center gap-2 text-gray-500">
                        <FileText size={16} className="flex-shrink-0" />
                        <LoadingText loading={true} w="w-16" h="h-3" />
                    </div>
                </div>

                {/* Content Skeleton */}
                <div className="p-4">
                    {/* Print Button Skeleton */}
                    <div className="flex w-full items-center no-print mb-4">
                        <LoadingText loading={true} w="w-24" h="h-10" rounded="rounded" />
                    </div>

                    {/* Exam Header Skeleton */}
                    <div className="flex flex-col gap-4 bg-white max-w-full">
                        <div className="flex flex-col w-full border border-gray-200 overflow-hidden rounded">
                            <div className="flex flex-col sm:flex-row h-auto sm:h-[12rem] items-center border-b border-gray-200">
                                {/* Left Column */}
                                <div className="flex flex-col justify-center items-center w-full sm:w-[25%] border-b sm:border-b-0 sm:border-r border-gray-200 p-3 sm:p-5 h-full gap-2">
                                    <LoadingText loading={true} w="w-32" h="h-4" />
                                    <LoadingText loading={true} w="w-24" h="h-3" />
                                    <LoadingText loading={true} w="w-10" h="h-10" rounded="rounded" />
                                </div>

                                {/* Middle Column */}
                                <div className="flex flex-col justify-center items-center w-full sm:w-[50%] gap-2 p-3 sm:p-5 h-full">
                                    <LoadingText loading={true} w="w-48" h="h-6" />
                                    <LoadingText loading={true} w="w-32" h="h-4" />
                                    <div className="flex gap-4 mt-2">
                                        <div className="flex items-center gap-1">
                                            <Clock size={14} className="text-gray-400" />
                                            <LoadingText loading={true} w="w-16" h="h-3" />
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <User size={14} className="text-gray-400" />
                                            <LoadingText loading={true} w="w-20" h="h-3" />
                                        </div>
                                    </div>
                                </div>

                                {/* Right Column */}
                                <div className="flex flex-col justify-center items-center w-full sm:w-[25%] gap-1 sm:gap-2 p-3 sm:p-5 h-full">
                                    <LoadingText loading={true} w="w-16" h="w-16" rounded="rounded" />
                                    <LoadingText loading={true} w="w-24" h="h-3" />
                                </div>
                            </div>
                        </div>

                        {/* Questions Skeleton */}
                        <div className="flex flex-col gap-4 mt-4">
                            {[...Array(3)].map((_, index) => (
                                <div key={index} className="border border-gray-200 rounded p-4">
                                    <div className="flex items-start gap-2 mb-3">
                                        <LoadingText loading={true} w="w-16" h="h-5" />
                                        <div className="flex-1">
                                            <LoadingText loading={true} w="w-full" h="h-4" />
                                            <LoadingText loading={true} w="w-3/4" h="h-4" />
                                        </div>
                                    </div>

                                    {/* Question Image Skeleton */}
                                    <div className="flex items-center justify-center w-full h-32 bg-gray-100 rounded mb-3">
                                        <LoadingText loading={true} w="w-full" h="h-full" rounded="rounded" />
                                    </div>

                                    {/* Answer Options Skeleton */}
                                    <div className="flex flex-col gap-2">
                                        {[...Array(4)].map((_, optionIndex) => (
                                            <div key={optionIndex} className="flex items-center gap-2">
                                                <LoadingText loading={true} w="w-6" h="h-4" />
                                                <LoadingText loading={true} w="w-48" h="h-4" />
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            {/* Header */}
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <div className="flex items-center gap-2">
                    <Eye size={16} className="text-sky-600" />
                    <p className="font-Inter text-sm font-semibold">Xem trước đề thi</p>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                    <FileText size={16} className="flex-shrink-0" />
                    <p className="text-xs">{questions?.length || 0} câu hỏi</p>
                </div>
            </div>

            {/* Content */}
            <div className="p-4">
                <PreviewExam exam={exam} questions={questions} />
            </div>
        </div>
    );
};

export default PreviewView;
