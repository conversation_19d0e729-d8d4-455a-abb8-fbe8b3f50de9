/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const DocumentURLChunkType = {
  DocumentUrl: "document_url",
} as const;
export type DocumentURLChunkType = ClosedEnum<typeof DocumentURLChunkType>;

export type DocumentURLChunk = {
  documentUrl: string;
  /**
   * The filename of the document
   */
  documentName?: string | null | undefined;
  type?: DocumentURLChunkType | undefined;
};

/** @internal */
export const DocumentURLChunkType$inboundSchema: z.ZodNativeEnum<
  typeof DocumentURLChunkType
> = z.nativeEnum(DocumentURLChunkType);

/** @internal */
export const DocumentURLChunkType$outboundSchema: z.ZodNativeEnum<
  typeof DocumentURLChunkType
> = DocumentURLChunkType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DocumentURLChunkType$ {
  /** @deprecated use `DocumentURLChunkType$inboundSchema` instead. */
  export const inboundSchema = DocumentURLChunkType$inboundSchema;
  /** @deprecated use `DocumentURLChunkType$outboundSchema` instead. */
  export const outboundSchema = DocumentURLChunkType$outboundSchema;
}

/** @internal */
export const DocumentURLChunk$inboundSchema: z.ZodType<
  DocumentURLChunk,
  z.ZodTypeDef,
  unknown
> = z.object({
  document_url: z.string(),
  document_name: z.nullable(z.string()).optional(),
  type: DocumentURLChunkType$inboundSchema.default("document_url"),
}).transform((v) => {
  return remap$(v, {
    "document_url": "documentUrl",
    "document_name": "documentName",
  });
});

/** @internal */
export type DocumentURLChunk$Outbound = {
  document_url: string;
  document_name?: string | null | undefined;
  type: string;
};

/** @internal */
export const DocumentURLChunk$outboundSchema: z.ZodType<
  DocumentURLChunk$Outbound,
  z.ZodTypeDef,
  DocumentURLChunk
> = z.object({
  documentUrl: z.string(),
  documentName: z.nullable(z.string()).optional(),
  type: DocumentURLChunkType$outboundSchema.default("document_url"),
}).transform((v) => {
  return remap$(v, {
    documentUrl: "document_url",
    documentName: "document_name",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DocumentURLChunk$ {
  /** @deprecated use `DocumentURLChunk$inboundSchema` instead. */
  export const inboundSchema = DocumentURLChunk$inboundSchema;
  /** @deprecated use `DocumentURLChunk$outboundSchema` instead. */
  export const outboundSchema = DocumentURLChunk$outboundSchema;
  /** @deprecated use `DocumentURLChunk$Outbound` instead. */
  export type Outbound = DocumentURLChunk$Outbound;
}

export function documentURLChunkToJSON(
  documentURLChunk: DocumentURLChunk,
): string {
  return JSON.stringify(
    DocumentURLChunk$outboundSchema.parse(documentURLChunk),
  );
}

export function documentURLChunkFromJSON(
  jsonString: string,
): SafeParseResult<DocumentURLChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DocumentURLChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DocumentURLChunk' from JSON`,
  );
}
