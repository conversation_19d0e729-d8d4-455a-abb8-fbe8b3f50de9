import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Upload, Image as ImageIcon, Sparkles, X, Eye, Save } from 'lucide-react';
import LatexRenderer from '../latex/RenderLatex';
import { uploadBase64Images } from '../../features/image/imageSlice';
import { setErrorMessage, setSuccessMessage } from '../../features/state/stateApiSlice';
import { ocrImageWithMistralAPI } from '../../services/ocrExamApi';

const AIWidget = ({ onImageUploaded, className = '' }) => {
    const dispatch = useDispatch();
    const [isOpen, setIsOpen] = useState(false);
    const [uploadedImage, setUploadedImage] = useState(null);
    const [imagePreview, setImagePreview] = useState(null);
    const [ocrText, setOcrText] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    
    const uploadRef = useRef(null);
    const fileInputRef = useRef(null);

    // Handle paste events
    useEffect(() => {
        const handlePaste = (event) => {
            if (!isOpen) return;
            
            const items = event.clipboardData?.items;
            if (items) {
                for (let i = 0; i < items.length; i++) {
                    const item = items[i];
                    if (item.type.indexOf("image") !== -1) {
                        const file = item.getAsFile();
                        if (file) {
                            handleImageFile(file);
                        }
                    }
                }
            }
        };

        const uploadElement = uploadRef.current;
        if (uploadElement) {
            uploadElement.addEventListener("paste", handlePaste);
            return () => {
                uploadElement.removeEventListener("paste", handlePaste);
            };
        }
    }, [isOpen]);

    const validateImageFile = (file) => {
        if (!["image/jpeg", "image/png", "image/jpg"].includes(file.type)) {
            dispatch(setErrorMessage("Chỉ cho phép định dạng JPEG hoặc PNG!"));
            return false;
        }
        if (file.size > 10 * 1024 * 1024) { // 10MB limit
            dispatch(setErrorMessage("Kích thước ảnh vượt quá 10MB!"));
            return false;
        }
        return true;
    };

    const handleImageFile = (file) => {
        if (!validateImageFile(file)) return;

        setUploadedImage(file);
        setImagePreview(URL.createObjectURL(file));
        setOcrText('');
    };

    const handleDragOver = (event) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (event) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);
    };

    const handleDrop = (event) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            handleImageFile(files[0]);
        }
    };

    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (file) {
            handleImageFile(file);
        }
    };

    const processOCR = async () => {
        if (!uploadedImage) {
            dispatch(setErrorMessage("Vui lòng chọn ảnh trước khi xử lý OCR"));
            return;
        }

        setIsProcessing(true);
        try {
            const response = await ocrImageWithMistralAPI(uploadedImage);

            if (response && response.markdown) {
                setOcrText(response.markdown);
                dispatch(setSuccessMessage("OCR thành công!"));
            } else {
                dispatch(setErrorMessage("Không thể trích xuất văn bản từ ảnh"));
            }
        } catch (error) {
            console.error('OCR Error:', error);
            dispatch(setErrorMessage("Lỗi khi xử lý OCR: " + (error.response?.data?.message || error.message)));
        } finally {
            setIsProcessing(false);
        }
    };

    const handleUploadToStorage = async () => {
        if (!uploadedImage) {
            dispatch(setErrorMessage("Không có ảnh để tải lên"));
            return;
        }

        setIsUploading(true);
        try {
            // Convert file to base64
            const reader = new FileReader();
            reader.onload = async (e) => {
                const base64 = e.target.result;
                
                // Upload using existing uploadBase64Images function
                const result = await dispatch(uploadBase64Images({ 
                    images: [base64], 
                    folder: "questionImage" 
                })).unwrap();

                if (result && result.length > 0) {
                    dispatch(setSuccessMessage("Tải ảnh lên thành công!"));
                    if (onImageUploaded) {
                        onImageUploaded(result[0]); // Pass the uploaded image URL
                    }
                    handleReset();
                }
            };
            reader.readAsDataURL(uploadedImage);
        } catch (error) {
            console.error('Upload Error:', error);
            dispatch(setErrorMessage("Lỗi khi tải ảnh lên: " + (error.message || "Không xác định")));
        } finally {
            setIsUploading(false);
        }
    };

    const handleReset = () => {
        setUploadedImage(null);
        setImagePreview(null);
        setOcrText('');
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    if (!isOpen) {
        return (
            <button
                onClick={() => setIsOpen(true)}
                className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-md hover:shadow-lg ${className}`}
            >
                <Sparkles className="w-4 h-4" />
                AI Widget
            </button>
        );
    }

    return (
        <div className={`bg-white border border-gray-200 rounded-lg shadow-lg p-4 ${className}`}>
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-purple-500" />
                    <h3 className="text-lg font-semibold text-gray-900">AI Widget - OCR & Upload</h3>
                </div>
                <button
                    onClick={() => setIsOpen(false)}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                    <X className="w-4 h-4 text-gray-500" />
                </button>
            </div>

            {/* Upload Area */}
            <div
                ref={uploadRef}
                tabIndex={0}
                className={`border-2 border-dashed rounded-lg p-6 mb-4 transition-all duration-200 ${
                    isDragging
                        ? "border-blue-400 bg-blue-50"
                        : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                {!imagePreview ? (
                    <div className="text-center">
                        <div className="flex flex-col items-center space-y-2">
                            <div className="p-3 bg-gray-200 rounded-full">
                                <ImageIcon className="w-8 h-8 text-gray-600" />
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-gray-700">
                                    Kéo thả ảnh hoặc Ctrl+V để dán
                                </p>
                                <p className="text-xs text-gray-500">
                                    Hỗ trợ JPEG, PNG (tối đa 10MB)
                                </p>
                            </div>
                            <button
                                onClick={() => fileInputRef.current?.click()}
                                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                            >
                                <Upload className="w-4 h-4" />
                                Chọn ảnh
                            </button>
                        </div>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {/* Image Preview */}
                        <div className="relative">
                            <img
                                src={imagePreview}
                                alt="Preview"
                                className="max-w-full max-h-64 mx-auto rounded-lg shadow-sm"
                            />
                            <button
                                onClick={handleReset}
                                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                            >
                                <X className="w-4 h-4" />
                            </button>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 justify-center">
                            <button
                                onClick={processOCR}
                                disabled={isProcessing}
                                className="inline-flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                {isProcessing ? (
                                    <>
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                        Đang xử lý...
                                    </>
                                ) : (
                                    <>
                                        <Eye className="w-4 h-4" />
                                        OCR
                                    </>
                                )}
                            </button>
                            <button
                                onClick={handleUploadToStorage}
                                disabled={isUploading}
                                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                {isUploading ? (
                                    <>
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                        Đang tải...
                                    </>
                                ) : (
                                    <>
                                        <Save className="w-4 h-4" />
                                        Tải lên
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                )}
            </div>

            {/* OCR Results */}
            {ocrText && (
                <div className="space-y-4">
                    {/* OCR Text Input */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Văn bản trích xuất (có thể chỉnh sửa):
                        </label>
                        <textarea
                            value={ocrText}
                            onChange={(e) => setOcrText(e.target.value)}
                            className="w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Văn bản từ OCR sẽ hiển thị ở đây..."
                        />
                    </div>

                    {/* LaTeX Preview */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Xem trước LaTeX:
                        </label>
                        <div className="w-full min-h-[100px] p-3 border border-gray-300 rounded-md bg-white overflow-auto">
                            <LatexRenderer text={ocrText} />
                        </div>
                    </div>
                </div>
            )}

            {/* Hidden file input */}
            <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/jpg"
                onChange={handleFileSelect}
                className="hidden"
            />
        </div>
    );
};

export default AIWidget;
