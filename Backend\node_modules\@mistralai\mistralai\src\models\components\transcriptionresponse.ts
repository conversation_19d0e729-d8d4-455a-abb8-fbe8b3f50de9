/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  TranscriptionSegmentChunk,
  TranscriptionSegmentChunk$inboundSchema,
  TranscriptionSegmentChunk$Outbound,
  TranscriptionSegmentChunk$outboundSchema,
} from "./transcriptionsegmentchunk.js";
import {
  UsageInfo,
  UsageInfo$inboundSchema,
  UsageInfo$Outbound,
  UsageInfo$outboundSchema,
} from "./usageinfo.js";

export type TranscriptionResponse = {
  model: string;
  text: string;
  segments?: Array<TranscriptionSegmentChunk> | undefined;
  usage: UsageInfo;
  language: string | null;
  additionalProperties?: { [k: string]: any };
};

/** @internal */
export const TranscriptionResponse$inboundSchema: z.ZodType<
  TranscriptionResponse,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    model: z.string(),
    text: z.string(),
    segments: z.array(TranscriptionSegmentChunk$inboundSchema).optional(),
    usage: UsageInfo$inboundSchema,
    language: z.nullable(z.string()),
  }).catchall(z.any()),
  "additionalProperties",
  true,
);

/** @internal */
export type TranscriptionResponse$Outbound = {
  model: string;
  text: string;
  segments?: Array<TranscriptionSegmentChunk$Outbound> | undefined;
  usage: UsageInfo$Outbound;
  language: string | null;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const TranscriptionResponse$outboundSchema: z.ZodType<
  TranscriptionResponse$Outbound,
  z.ZodTypeDef,
  TranscriptionResponse
> = z.object({
  model: z.string(),
  text: z.string(),
  segments: z.array(TranscriptionSegmentChunk$outboundSchema).optional(),
  usage: UsageInfo$outboundSchema,
  language: z.nullable(z.string()),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionResponse$ {
  /** @deprecated use `TranscriptionResponse$inboundSchema` instead. */
  export const inboundSchema = TranscriptionResponse$inboundSchema;
  /** @deprecated use `TranscriptionResponse$outboundSchema` instead. */
  export const outboundSchema = TranscriptionResponse$outboundSchema;
  /** @deprecated use `TranscriptionResponse$Outbound` instead. */
  export type Outbound = TranscriptionResponse$Outbound;
}

export function transcriptionResponseToJSON(
  transcriptionResponse: TranscriptionResponse,
): string {
  return JSON.stringify(
    TranscriptionResponse$outboundSchema.parse(transcriptionResponse),
  );
}

export function transcriptionResponseFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionResponse' from JSON`,
  );
}
