import { useMemo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import QuestionTableRow from "./QuestionTableRow";
import StatementTableRow from "./StatementTableRow";
import { fetchCodesByType } from "../../features/code/codeSlice";
import TooltipTd from "./TooltipTd";
import ConfirmDeleteModal from "../modal/ConfirmDeleteModal";
import { useNavigate } from "react-router-dom";
import { deleteQuestion, setSortOrder } from "../../features/question/questionSlice";
import { resetFilters } from "../../features/filter/filterSlice";
import { TotalComponent } from "./TotalComponent";
import LoadingData from "../loading/LoadingData";

const QuestionTable = ({ fetchQuestions, examId = null }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { questions, search, pagination } = useSelector(state => state.questions);
    const { page, pageSize, total, totalPages, sortOrder } = pagination;

    const prefixStatementTN = ["A.", "B.", "C.", "D.", "E.", "F.", "G.", "H.", "I.", "J."];
    const prefixStatementDS = ["a)", "b)", "c)", "d)", "e)", "f)", "g)", "h)", "i)", "j)"];
    const { codes } = useSelector((state) => state.codes);
    const [deleteMode, setDeleteMode] = useState(false);
    const [isOpenConfirmDeleteModal, setIsOpenConfirmDeleteModal] = useState(false);
    const [id, setId] = useState(null);
    const { loading } = useSelector(state => state.states);

    const params = useMemo(() => ({
        search,
        page,
        pageSize,
        sortOrder,
        id: examId
    }), [search, page, pageSize, sortOrder, examId]);

    useEffect(() => {
        dispatch(fetchCodesByType(["chapter", "difficulty", "question type", "grade"]))
    }, [dispatch]);

    useEffect(() => {
        dispatch(fetchQuestions(params))
            .unwrap()
    }, [dispatch, params]);

    const handleClickedRow = (id) => {
        if (deleteMode) {
            setIsOpenConfirmDeleteModal(true);
            setId(id);
        } else {
            navigate(`/admin/question-management/${id}`);
        }
    };

    const confirmDeleteModal = () => {
        if (id === null) return;
        dispatch(deleteQuestion(id))
            .unwrap()
            .then(() => {
                dispatch(fetchQuestions((params))).unwrap()
                setIsOpenConfirmDeleteModal(false);
            });
    };

    return (
        <LoadingData
            loading={loading}
            isNoData={questions.length > 0 ? false : true}
            loadText="Đang tải danh sách câu hỏi"
            noDataText="Không có câu hỏi nào."
        >
            <div className="flex flex-col gap-4 h-full min-h-0 text-sm">
                <ConfirmDeleteModal
                    isOpen={isOpenConfirmDeleteModal}
                    onClose={() => setIsOpenConfirmDeleteModal(false)}
                    onConfirm={confirmDeleteModal}
                />
                <TotalComponent
                    total={total}
                    page={page}
                    pageSize={pageSize}
                    setSortOrder={() => dispatch(setSortOrder())}
                    deleteMode={deleteMode}
                    isDelete={true}
                    setDeleteMode={setDeleteMode}
                />

                <div className="flex-grow h-[70vh] overflow-y-auto ">
                    <table className="w-full border-collapse border border-[#E7E7ED]">
                        <thead className="bg-[#F6FAFD] sticky top-0 z-10">
                            <tr className="border border-[#E7E7ED]">
                                <th className="py-3 text-center w-16">ID</th>
                                <th className="py-3 text-center w-64">Nội dung</th>
                                <th className="py-3 text-center w-32">Kiểu câu hỏi</th>
                                <th className="py-3 text-center w-40">Mệnh đề</th>
                                <th className="py-3 text-center w-40">Đáp án</th>
                                <th className="py-3 text-center w-24">Độ khó</th>
                                <th className="py-3 text-center w-24">Lớp</th>
                                <th className="py-3 text-center w-24">Chương</th>
                                <th className="py-3 text-center w-32">Đã có lời giải</th>
                                <th className="py-3 text-center w-40">Ngày đăng</th>
                                <th className="py-3 text-center w-40">Cập nhật lúc</th>
                            </tr>

                        </thead>
                        <tbody>
                            {questions.map((question, index) => (
                                <tr
                                    onClick={() => handleClickedRow(question.id)}
                                    key={question.id} className={`border border-[#E7E7ED] cursor-pointer ${deleteMode ? 'hover:bg-red-50' : 'hover:bg-gray-50'}`}>
                                    <td className="py-3 text-center">{question.id}</td>
                                    <QuestionTableRow question={question} />
                                    <TooltipTd
                                        value={question.typeOfQuestion}
                                        tooltipText={
                                            codes['question type']?.find((code) => code.code === question.typeOfQuestion)?.description || ""
                                        }
                                    />
                                    {question.typeOfQuestion === "TN" && (
                                        <>
                                            <StatementTableRow statements={question.statements} prefixStatements={prefixStatementTN} />
                                            <td className="py-3 text-center">
                                                {question.statements.map((statement, index) => (
                                                    <p key={statement.id || index} className={`${statement.isCorrect ? 'text-green-500 font-semibold' : 'text-red-500 font-semibold'}`}>{statement.isCorrect ? prefixStatementTN[index].replace(".", "") : ''}</p>
                                                ))}
                                            </td>
                                        </>
                                    )}
                                    {question.typeOfQuestion === "DS" && (
                                        <>
                                            <StatementTableRow statements={question.statements} prefixStatements={prefixStatementDS} />

                                            <td className="py-3 text-center">
                                                {question.statements.map((statement, index) => (
                                                    <p key={statement.id || index} className={`${statement.isCorrect ? 'text-green-500 font-semibold' : 'text-red-500 font-semibold'}`}>{statement.isCorrect ? 'Đ' : 'S'}</p>
                                                ))}
                                            </td>
                                        </>
                                    )}
                                    {question.typeOfQuestion === "TLN" && (
                                        <>
                                            <td className="py-3 ">
                                            </td>
                                            <td className="py-3 text-center text-green-500 font-semibold">
                                                {question.correctAnswer}
                                            </td>
                                        </>
                                    )}
                                    <TooltipTd
                                        value={question.difficulty}
                                        tooltipText={
                                            codes['difficulty']?.find((code) => code.code === question.difficulty)?.description || ""
                                        }
                                    />
                                    <td className="py-3 text-center">{question.class}</td>
                                    <TooltipTd
                                        value={question.chapter}
                                        tooltipText={
                                            codes['chapter']?.find((code) => code.code === question.chapter)?.description || ""
                                        }
                                    />
                                    <TooltipTd
                                        value={question.solution ? "Rồi" : 'Chưa'}
                                        className={`${question.solution ? 'text-green-500 font-semibold' : 'text-yellow-500 font-semibold'}`}
                                        tooltipText={
                                            question.solution
                                        }
                                        imageUrl={question.solutionImageUrl}
                                    />

                                    <td className="py-3 text-center">
                                        {new Date(question.createdAt).toLocaleDateString()}
                                    </td>
                                    <td className="py-3 text-center">
                                        {new Date(question.updatedAt).toLocaleDateString()}
                                    </td>

                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </LoadingData>
    );

};

export default QuestionTable;
