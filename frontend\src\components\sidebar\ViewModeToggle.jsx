import React from 'react';
import { Layers, ArrowLeftRight } from 'lucide-react';
import ButtonHeader from "../header/ButtonHeader";

/**
 * Component nút chuyển đổi chế độ hiển thị câu hỏi
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.singleQuestionMode - Chế độ hiển thị hiện tại (true: từng câu, false: tất cả)
 * @param {Function} props.setSingleQuestionMode - Hàm thay đổi chế độ hiển thị
 * @param {boolean} props.isDarkMode - Chế độ tối
 */
const ViewModeToggle = ({ singleQuestionMode, setSingleQuestionMode, isDarkMode }) => {
  return (
    <div className="flex justify-between items-center mb-2">
      <span className="font-semibold">Chế độ hiển thị:</span>
      <ButtonHeader
        darkMode={isDarkMode}
        onClick={() => setSingleQuestionMode(!singleQuestionMode)}
        Icon={singleQuestionMode ? Layers : ArrowLeftRight}
        title={singleQuestionMode ? "Xem tất cả" : "Xem từng câu"}
      />
    </div>
  );
};

export default ViewModeToggle;
