/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  AgentHandoffEntry,
  AgentHandoffEntry$inboundSchema,
  AgentHandoffEntry$Outbound,
  AgentHandoffEntry$outboundSchema,
} from "./agenthandoffentry.js";
import {
  FunctionCallEntry,
  FunctionCallEntry$inboundSchema,
  FunctionCallEntry$Outbound,
  FunctionCallEntry$outboundSchema,
} from "./functioncallentry.js";
import {
  FunctionResultEntry,
  FunctionResultEntry$inboundSchema,
  FunctionResultEntry$Outbound,
  FunctionResultEntry$outboundSchema,
} from "./functionresultentry.js";
import {
  MessageInputEntry,
  MessageInputEntry$inboundSchema,
  MessageInputEntry$Outbound,
  MessageInputEntry$outboundSchema,
} from "./messageinputentry.js";
import {
  MessageOutputEntry,
  MessageOutputEntry$inboundSchema,
  MessageOutputEntry$Outbound,
  MessageOutputEntry$outboundSchema,
} from "./messageoutputentry.js";
import {
  ToolExecutionEntry,
  ToolExecutionEntry$inboundSchema,
  ToolExecutionEntry$Outbound,
  ToolExecutionEntry$outboundSchema,
} from "./toolexecutionentry.js";

export type InputEntries =
  | FunctionResultEntry
  | MessageInputEntry
  | FunctionCallEntry
  | ToolExecutionEntry
  | MessageOutputEntry
  | AgentHandoffEntry;

/** @internal */
export const InputEntries$inboundSchema: z.ZodType<
  InputEntries,
  z.ZodTypeDef,
  unknown
> = z.union([
  FunctionResultEntry$inboundSchema,
  MessageInputEntry$inboundSchema,
  FunctionCallEntry$inboundSchema,
  ToolExecutionEntry$inboundSchema,
  MessageOutputEntry$inboundSchema,
  AgentHandoffEntry$inboundSchema,
]);

/** @internal */
export type InputEntries$Outbound =
  | FunctionResultEntry$Outbound
  | MessageInputEntry$Outbound
  | FunctionCallEntry$Outbound
  | ToolExecutionEntry$Outbound
  | MessageOutputEntry$Outbound
  | AgentHandoffEntry$Outbound;

/** @internal */
export const InputEntries$outboundSchema: z.ZodType<
  InputEntries$Outbound,
  z.ZodTypeDef,
  InputEntries
> = z.union([
  FunctionResultEntry$outboundSchema,
  MessageInputEntry$outboundSchema,
  FunctionCallEntry$outboundSchema,
  ToolExecutionEntry$outboundSchema,
  MessageOutputEntry$outboundSchema,
  AgentHandoffEntry$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace InputEntries$ {
  /** @deprecated use `InputEntries$inboundSchema` instead. */
  export const inboundSchema = InputEntries$inboundSchema;
  /** @deprecated use `InputEntries$outboundSchema` instead. */
  export const outboundSchema = InputEntries$outboundSchema;
  /** @deprecated use `InputEntries$Outbound` instead. */
  export type Outbound = InputEntries$Outbound;
}

export function inputEntriesToJSON(inputEntries: InputEntries): string {
  return JSON.stringify(InputEntries$outboundSchema.parse(inputEntries));
}

export function inputEntriesFromJSON(
  jsonString: string,
): SafeParseResult<InputEntries, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => InputEntries$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'InputEntries' from JSON`,
  );
}
