'use strict';

export default {
    async up(queryInterface, Sequelize) {
        await queryInterface.addColumn('studentExamStatus', 'star', {
            type: Sequelize.DECIMAL(3, 1),
            allowNull: true,
        });

        await queryInterface.removeColumn('studentExamStatus', 'completionTime');
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.removeColumn('studentExamStatus', 'star');
        await queryInterface.addColumn('studentExamStatus', 'completionTime', {
            type: Sequelize.DATE,
            allowNull: true,
        });
    },
};
