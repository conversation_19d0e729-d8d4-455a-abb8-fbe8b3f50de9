import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as StudentExamController from '../controllers/StudentExamController.js'

const router = express.Router()

router.get('/v1/user/exam/:examId/rating',
    requireRoles(Roles.JustStudent),
    async<PERSON>andler(StudentExamController.getExamRatingStatistics)
)

router.post('/v1/user/save-exam',
    requireRoles(Roles.JustStudent),
    as<PERSON><PERSON><PERSON><PERSON>(StudentExamController.saveExamForUser)
)

router.post('/v1/user/rate-exam',
    requireRoles(Roles.JustStudent),
    as<PERSON><PERSON>and<PERSON>(StudentExamController.rateExamForUser)
)

export default router