{"ast": null, "code": "import api from \"./api\";\nexport const getAllUsersAPI = _ref => {\n  let {\n    search = \"\",\n    currentPage = 1,\n    limit = 10,\n    sortOrder = 'desc',\n    graduationYear = null,\n    gradeFilter = null,\n    classFilter = null\n  } = _ref;\n  const params = {\n    search,\n    page: currentPage,\n    limit,\n    sortOrder\n  };\n\n  // Chỉ thêm filter parameters nếu có giá trị\n  if (graduationYear !== null && graduationYear !== undefined) {\n    params.graduationYear = graduationYear;\n  }\n  if (gradeFilter !== null && gradeFilter !== undefined && gradeFilter !== '') {\n    params.grade = gradeFilter;\n  }\n  if (classFilter !== null && classFilter !== undefined && classFilter !== '') {\n    params.classId = classFilter;\n  }\n  return api.get(\"/v1/admin/user\", {\n    params\n  });\n};\nexport const getAllStaffAPI = _ref2 => {\n  let {\n    search = \"\",\n    currentPage = 1,\n    limit = 10,\n    sortOrder = 'desc'\n  } = _ref2;\n  return api.get(\"/v1/admin/staff\", {\n    params: {\n      search,\n      page: currentPage,\n      limit,\n      sortOrder\n    }\n  });\n};\nexport const getUserByIdAPI = id => {\n  return api.get(\"/v1/admin/user/\".concat(id));\n};\nexport const findUsersAPI = search => {\n  return api.get(\"/v1/admin/user/search\", {\n    params: {\n      search\n    }\n  });\n};\nexport const getUserClassesAPI = _ref3 => {\n  let {\n    id,\n    search = \"\",\n    currentPage = 1,\n    limit = 10,\n    sortOrder = 'desc'\n  } = _ref3;\n  return api.get(\"/v1/admin/user/class/\".concat(id), {\n    params: {\n      search,\n      page: currentPage,\n      limit,\n      sortOrder\n    }\n  });\n};\nexport const putUserAPI = _ref4 => {\n  let {\n    id,\n    user\n  } = _ref4;\n  return api.put(\"/v1/admin/user/\".concat(id), user);\n};\nexport const putUserTypeAPI = _ref5 => {\n  let {\n    id,\n    type\n  } = _ref5;\n  return api.put(\"/v1/admin/user/\".concat(id, \"/user-type\"), {\n    userType: type\n  });\n};\nexport const putUserStatusAPI = _ref6 => {\n  let {\n    id,\n    status\n  } = _ref6;\n  return api.put(\"/v1/admin/user/\".concat(id, \"/status\"), {\n    status\n  });\n};\nexport const deleteUserAPI = id => {\n  return api.delete(\"/v1/admin/user/\".concat(id));\n};", "map": {"version": 3, "names": ["api", "getAllUsersAPI", "_ref", "search", "currentPage", "limit", "sortOrder", "graduationYear", "gradeFilter", "classFilter", "params", "page", "undefined", "grade", "classId", "get", "getAllStaffAPI", "_ref2", "getUserByIdAPI", "id", "concat", "findUsersAPI", "getUserClassesAPI", "_ref3", "putUserAPI", "_ref4", "user", "put", "putUserTypeAPI", "_ref5", "type", "userType", "putUserStatusAPI", "_ref6", "status", "deleteUserAPI", "delete"], "sources": ["D:/ToanThayBee/frontend/src/services/userApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\nexport const getAllUsersAPI = ({ search = \"\", currentPage = 1, limit = 10, sortOrder = 'desc', graduationYear = null, gradeFilter = null, classFilter = null }) => {\r\n    const params = {\r\n        search,\r\n        page: currentPage,\r\n        limit,\r\n        sortOrder,\r\n    };\r\n\r\n    // Chỉ thêm filter parameters nếu có giá trị\r\n    if (graduationYear !== null && graduationYear !== undefined) {\r\n        params.graduationYear = graduationYear;\r\n    }\r\n\r\n    if (gradeFilter !== null && gradeFilter !== undefined && gradeFilter !== '') {\r\n        params.grade = gradeFilter;\r\n    }\r\n\r\n    if (classFilter !== null && classFilter !== undefined && classFilter !== '') {\r\n        params.classId = classFilter;\r\n    }\r\n\r\n    return api.get(\"/v1/admin/user\", { params });\r\n};\r\n\r\nexport const getAllStaffAPI = ({ search = \"\", currentPage = 1, limit = 10, sortOrder = 'desc' }) => {\r\n    return api.get(\"/v1/admin/staff\", {\r\n        params: {\r\n            search,\r\n            page: currentPage,\r\n            limit,\r\n            sortOrder,\r\n        }\r\n    });\r\n};\r\n\r\nexport const getUserByIdAPI = (id) => {\r\n    return api.get(`/v1/admin/user/${id}`);\r\n};\r\n\r\nexport const findUsersAPI = (search) => {\r\n    return api.get(\"/v1/admin/user/search\", {\r\n        params: {\r\n            search,\r\n        }\r\n    });\r\n}\r\n\r\nexport const getUserClassesAPI = ({ id, search = \"\", currentPage = 1, limit = 10, sortOrder = 'desc' }) => {\r\n    return api.get(`/v1/admin/user/class/${id}`, {\r\n        params: {\r\n            search,\r\n            page: currentPage,\r\n            limit,\r\n            sortOrder,\r\n        }\r\n    });\r\n}\r\n\r\nexport const putUserAPI = ({ id, user }) => {\r\n    return api.put(`/v1/admin/user/${id}`, user);\r\n}\r\n\r\nexport const putUserTypeAPI = ({ id, type }) => {\r\n    return api.put(`/v1/admin/user/${id}/user-type`, { userType: type });\r\n}\r\n\r\nexport const putUserStatusAPI = ({ id, status }) => {\r\n    return api.put(`/v1/admin/user/${id}/status`, { status });\r\n}\r\n\r\nexport const deleteUserAPI = (id) => {\r\n    return api.delete(`/v1/admin/user/${id}`);\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,cAAc,GAAGC,IAAA,IAAqI;EAAA,IAApI;IAAEC,MAAM,GAAG,EAAE;IAAEC,WAAW,GAAG,CAAC;IAAEC,KAAK,GAAG,EAAE;IAAEC,SAAS,GAAG,MAAM;IAAEC,cAAc,GAAG,IAAI;IAAEC,WAAW,GAAG,IAAI;IAAEC,WAAW,GAAG;EAAK,CAAC,GAAAP,IAAA;EAC1J,MAAMQ,MAAM,GAAG;IACXP,MAAM;IACNQ,IAAI,EAAEP,WAAW;IACjBC,KAAK;IACLC;EACJ,CAAC;;EAED;EACA,IAAIC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKK,SAAS,EAAE;IACzDF,MAAM,CAACH,cAAc,GAAGA,cAAc;EAC1C;EAEA,IAAIC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKI,SAAS,IAAIJ,WAAW,KAAK,EAAE,EAAE;IACzEE,MAAM,CAACG,KAAK,GAAGL,WAAW;EAC9B;EAEA,IAAIC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKG,SAAS,IAAIH,WAAW,KAAK,EAAE,EAAE;IACzEC,MAAM,CAACI,OAAO,GAAGL,WAAW;EAChC;EAEA,OAAOT,GAAG,CAACe,GAAG,CAAC,gBAAgB,EAAE;IAAEL;EAAO,CAAC,CAAC;AAChD,CAAC;AAED,OAAO,MAAMM,cAAc,GAAGC,KAAA,IAAsE;EAAA,IAArE;IAAEd,MAAM,GAAG,EAAE;IAAEC,WAAW,GAAG,CAAC;IAAEC,KAAK,GAAG,EAAE;IAAEC,SAAS,GAAG;EAAO,CAAC,GAAAW,KAAA;EAC3F,OAAOjB,GAAG,CAACe,GAAG,CAAC,iBAAiB,EAAE;IAC9BL,MAAM,EAAE;MACJP,MAAM;MACNQ,IAAI,EAAEP,WAAW;MACjBC,KAAK;MACLC;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMY,cAAc,GAAIC,EAAE,IAAK;EAClC,OAAOnB,GAAG,CAACe,GAAG,mBAAAK,MAAA,CAAmBD,EAAE,CAAE,CAAC;AAC1C,CAAC;AAED,OAAO,MAAME,YAAY,GAAIlB,MAAM,IAAK;EACpC,OAAOH,GAAG,CAACe,GAAG,CAAC,uBAAuB,EAAE;IACpCL,MAAM,EAAE;MACJP;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMmB,iBAAiB,GAAGC,KAAA,IAA0E;EAAA,IAAzE;IAAEJ,EAAE;IAAEhB,MAAM,GAAG,EAAE;IAAEC,WAAW,GAAG,CAAC;IAAEC,KAAK,GAAG,EAAE;IAAEC,SAAS,GAAG;EAAO,CAAC,GAAAiB,KAAA;EAClG,OAAOvB,GAAG,CAACe,GAAG,yBAAAK,MAAA,CAAyBD,EAAE,GAAI;IACzCT,MAAM,EAAE;MACJP,MAAM;MACNQ,IAAI,EAAEP,WAAW;MACjBC,KAAK;MACLC;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMkB,UAAU,GAAGC,KAAA,IAAkB;EAAA,IAAjB;IAAEN,EAAE;IAAEO;EAAK,CAAC,GAAAD,KAAA;EACnC,OAAOzB,GAAG,CAAC2B,GAAG,mBAAAP,MAAA,CAAmBD,EAAE,GAAIO,IAAI,CAAC;AAChD,CAAC;AAED,OAAO,MAAME,cAAc,GAAGC,KAAA,IAAkB;EAAA,IAAjB;IAAEV,EAAE;IAAEW;EAAK,CAAC,GAAAD,KAAA;EACvC,OAAO7B,GAAG,CAAC2B,GAAG,mBAAAP,MAAA,CAAmBD,EAAE,iBAAc;IAAEY,QAAQ,EAAED;EAAK,CAAC,CAAC;AACxE,CAAC;AAED,OAAO,MAAME,gBAAgB,GAAGC,KAAA,IAAoB;EAAA,IAAnB;IAAEd,EAAE;IAAEe;EAAO,CAAC,GAAAD,KAAA;EAC3C,OAAOjC,GAAG,CAAC2B,GAAG,mBAAAP,MAAA,CAAmBD,EAAE,cAAW;IAAEe;EAAO,CAAC,CAAC;AAC7D,CAAC;AAED,OAAO,MAAMC,aAAa,GAAIhB,EAAE,IAAK;EACjC,OAAOnB,GAAG,CAACoC,MAAM,mBAAAhB,MAAA,CAAmBD,EAAE,CAAE,CAAC;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}