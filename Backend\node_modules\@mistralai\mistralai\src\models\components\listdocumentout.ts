/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DocumentOut,
  DocumentOut$inboundSchema,
  DocumentOut$Outbound,
  DocumentOut$outboundSchema,
} from "./documentout.js";
import {
  PaginationInfo,
  PaginationInfo$inboundSchema,
  PaginationInfo$Outbound,
  PaginationInfo$outboundSchema,
} from "./paginationinfo.js";

export type ListDocumentOut = {
  pagination: PaginationInfo;
  data: Array<DocumentOut>;
};

/** @internal */
export const ListDocumentOut$inboundSchema: z.ZodType<
  ListDocumentOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  pagination: PaginationInfo$inboundSchema,
  data: z.array(DocumentOut$inboundSchema),
});

/** @internal */
export type ListDocumentOut$Outbound = {
  pagination: PaginationInfo$Outbound;
  data: Array<DocumentOut$Outbound>;
};

/** @internal */
export const ListDocumentOut$outboundSchema: z.ZodType<
  ListDocumentOut$Outbound,
  z.ZodTypeDef,
  ListDocumentOut
> = z.object({
  pagination: PaginationInfo$outboundSchema,
  data: z.array(DocumentOut$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListDocumentOut$ {
  /** @deprecated use `ListDocumentOut$inboundSchema` instead. */
  export const inboundSchema = ListDocumentOut$inboundSchema;
  /** @deprecated use `ListDocumentOut$outboundSchema` instead. */
  export const outboundSchema = ListDocumentOut$outboundSchema;
  /** @deprecated use `ListDocumentOut$Outbound` instead. */
  export type Outbound = ListDocumentOut$Outbound;
}

export function listDocumentOutToJSON(
  listDocumentOut: ListDocumentOut,
): string {
  return JSON.stringify(ListDocumentOut$outboundSchema.parse(listDocumentOut));
}

export function listDocumentOutFromJSON(
  jsonString: string,
): SafeParseResult<ListDocumentOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListDocumentOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListDocumentOut' from JSON`,
  );
}
