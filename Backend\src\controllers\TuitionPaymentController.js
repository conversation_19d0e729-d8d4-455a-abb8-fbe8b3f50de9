import db from "../models/index.js"
import { Op, literal, fn, col } from 'sequelize'
import UserType from "../constants/UserType.js"
import { sendUserNotification } from "../utils/notificationUtils.js"
import StudentClassStatus from "../constants/StudentClassStatus.js"
import TuitionStatus from "../constants/TuitionStatus.js"
import * as tuitionService from "../services/tuition.service.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"
import * as notificationService from "../services/notification.service.js"

/**
 * <PERSON><PERSON><PERSON> danh sách tất cả các khoản đóng học phí
 */
export const getAllTuitionPayments = async (req, res) => {
    const search = req.query.search.trim() || ''
    const page = parseInt(req.query.page, 10) || 1
    const pageSize = parseInt(req.query.pageSize, 10) || 10
    const offset = (page - 1) * pageSize
    const sortOrder = req.query.sortOrder || 'DESC'
    const sortBy = req.query.sortBy || 'createdAt'
    const userId = req.query.userId || null
    const isPaid = req.query.isPaid || null
    const month = req.query.month || null
    const userClass = req.query.userClass || null // Lớp học của người dùng (10, 11, 12)
    const overdue = req.query.overdue == 'true' // Lấy các khoản học phí đã quá hạn
    const classId = req.query.classId || null // ID của lớp học cụ thể
    const graduationYear = req.query.graduationYear || null // Năm tốt nghiệp

    // Xây dựng điều kiện tìm kiếm
    let whereClause = {}

    // Tìm kiếm theo học sinh nếu có
    if (userId) {
        whereClause.userId = userId
    }

    // Tìm kiếm theo trạng thái thanh toán (isPaid)
    if (isPaid !== null) {
        whereClause.isPaid = isPaid == 'true' ? true : false
    }

    // Tìm kiếm theo tháng nếu có
    if (month) {
        whereClause.month = month
    }

    // Tìm kiếm theo lớp học của người dùng (10, 11, 12)
    if (userClass) {
        whereClause['$user.class$'] = userClass
    }

    // Tìm kiếm theo năm tốt nghiệp nếu có
    if (graduationYear) {
        whereClause['$user.graduationYear$'] = graduationYear
    }

    // Tìm kiếm theo lớp học cụ thể (classId)
    if (classId) {
        // Lấy danh sách học sinh đã tham gia lớp học với trạng thái "JS" (Joined Successfully)
        const studentsInClass = await db.StudentClassStatus.findAll({
            where: {
                classId,
                status: StudentClassStatus.JOINED // Joined Successfully
            },
            attributes: ['studentId'],
            raw: true
        });

        if (studentsInClass.length > 0) {
            // Lấy danh sách ID của học sinh trong lớp
            const studentIds = studentsInClass.map(student => student.studentId);

            // Thêm điều kiện tìm kiếm theo danh sách học sinh
            whereClause.userId = {
                [Op.in]: studentIds
            };
        } else {
            // Nếu không có học sinh nào trong lớp, trả về danh sách rỗng
            whereClause.userId = -1; // ID không tồn tại
        }
    }

    // Tìm kiếm các khoản học phí đã quá hạn
    if (overdue) {
        const today = new Date();
        whereClause = {
            ...whereClause,
            dueDate: {
                [Op.lt]: today
            },
            isPaid: false
        }
    }
    // console.log(whereClause)
    // Tìm kiếm theo từ khóa
    if (search.trim() !== '') {
        const trimmedSearch = search.trim();

        const searchConditions = [
            literal(`CONCAT(user.lastName, ' ', user.firstName) LIKE '%${trimmedSearch}%'`),
            { note: { [Op.like]: `%${trimmedSearch}%` } },
            { '$user.phone$': { [Op.like]: `%${trimmedSearch}%` } },
            { '$user.password$': { [Op.like]: `%${trimmedSearch}%` } },
        ];

        // Nếu search là số nguyên dương → tìm theo user.id
        if (!isNaN(trimmedSearch) && Number.isInteger(Number(trimmedSearch))) {
            searchConditions.push({ '$user.id$': parseInt(trimmedSearch) });
        }

        whereClause = {
            ...whereClause,
            [Op.or]: searchConditions
        };
    }


    // console.log(whereClause)

    // Thực hiện truy vấn với include để lấy thông tin học sinh
    const [payments, total] = await Promise.all([
        db.TuitionPayment.findAll({
            where: whereClause,
            include: [
                {
                    model: db.User,
                    as: 'user',
                    attributes: ['id', 'lastName', 'firstName', 'class', 'highSchool', 'phone', 'password', 'graduationYear'],
                    required: !!userClass // Nếu có lọc theo lớp thì required = true
                }
            ],
            offset,
            limit: pageSize,
            order: [[sortBy, sortOrder]]
        }),
        db.TuitionPayment.count({
            where: whereClause,
            include: [
                {
                    model: db.User,
                    as: 'user',
                    required: !!userClass // Nếu có lọc theo lớp thì required = true
                }
            ]
        })
    ])

    // Format lại dữ liệu trước khi trả về
    const formattedPayments = payments.map(payment => {
        const plainPayment = payment.get({ plain: true })
        const [year, month] = plainPayment.month.split('-')
        const monthNames = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ]

        // Kiểm tra xem khoản học phí có quá hạn không
        const isOverdue = plainPayment.dueDate &&
            !plainPayment.isPaid &&
            new Date() > new Date(plainPayment.dueDate);

        // console.log('plainPayment:', plainPayment)

        return {
            ...plainPayment,
            monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
            paymentDateFormatted: plainPayment.paymentDate
                ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                : null,
            dueDateFormatted: plainPayment.dueDate
                ? new Date(plainPayment.dueDate).toLocaleDateString('vi-VN')
                : null,
            studentName: `${plainPayment.user?.lastName || ''} ${plainPayment.user?.firstName || ''}`.trim(),
            userClass: plainPayment.user.class,
            isOverdue: isOverdue
        }
    })

    return res.status(200).json({
        message: 'Danh sách đóng học phí',
        ...new ResponseDataPagination(formattedPayments, {
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
            sortOrder
        })
    })
}

/**
 * Lấy danh sách đóng học phí của một lớp cụ thể
 */
export const getTuitionPaymentsByClassId = async (req, res) => {
    const { classId } = req.params
    const page = parseInt(req.query.page, 10) || 1
    const limit = parseInt(req.query.limit, 10) || 10
    const offset = (page - 1) * limit
    const sortOrder = req.query.sortOrder || 'DESC'
    const sortBy = req.query.sortBy || 'createdAt'
    const month = req.query.month || null
    const isPaid = req.query.isPaid || null

    // Kiểm tra lớp học có tồn tại không
    const classExists = await db.Class.findByPk(classId)
    if (!classExists) {
        return res.status(404).json({ message: 'Lớp học không tồn tại' })
    }

    // Lấy danh sách học sinh đã tham gia lớp học với trạng thái "JS" (Joined Successfully)
    const studentsInClass = await db.StudentClassStatus.findAll({
        where: {
            classId,
            status: StudentClassStatus.JOINED // Joined Successfully
        },
        attributes: ['studentId'],
        raw: true
    })

    if (studentsInClass.length === 0) {
        return res.status(200).json({
            message: `Không có học sinh nào trong lớp ${classExists.name}`,
            class: {
                id: classExists.id,
                name: classExists.name,
                class_code: classExists.class_code
            },
            ...new ResponseDataPagination([], {
                total: 0,
                page,
                pageSize: limit,
                totalPages: 0,
                sortOrder
            })
        })
    }

    // Lấy danh sách ID của học sinh trong lớp
    const studentIds = studentsInClass.map(student => student.studentId)

    // Xây dựng điều kiện tìm kiếm
    let whereClause = {
        userId: {
            [Op.in]: studentIds
        }
    }

    // Tìm kiếm theo trạng thái thanh toán (isPaid)
    if (isPaid !== null) {
        whereClause.isPaid = isPaid
    }

    // Tìm kiếm theo tháng nếu có
    if (month) {
        whereClause.month = month
    }

    // Lấy danh sách đóng học phí của học sinh trong lớp
    const [payments, total] = await Promise.all([
        db.TuitionPayment.findAll({
            where: whereClause,
            include: [
                {
                    model: db.User,
                    as: 'user',
                    attributes: ['id', 'lastName', 'firstName', 'username', 'class', 'highSchool', 'phone', 'password']
                }
            ],
            offset,
            limit,
            order: [[sortBy, sortOrder]]
        }),
        db.TuitionPayment.count({ where: whereClause })
    ])

    // Format lại dữ liệu trước khi trả về
    const formattedPayments = payments.map(payment => {
        const plainPayment = payment.get({ plain: true })
        const [year, month] = plainPayment.month.split('-')
        const monthNames = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ]

        // Kiểm tra xem khoản học phí có quá hạn không
        const isOverdue = plainPayment.dueDate &&
            !plainPayment.isPaid &&
            new Date() > new Date(plainPayment.dueDate);

        return {
            ...plainPayment,
            monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
            paymentDateFormatted: plainPayment.paymentDate
                ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                : null,
            dueDateFormatted: plainPayment.dueDate
                ? new Date(plainPayment.dueDate).toLocaleDateString('vi-VN')
                : null,
            studentName: `${plainPayment.user.lastName} ${plainPayment.user.firstName}`,
            userClass: plainPayment.user.class,
            isOverdue: isOverdue
        }
    })

    return res.status(200).json({
        message: `Danh sách đóng học phí của lớp ${classExists.name}`,
        class: {
            id: classExists.id,
            name: classExists.name,
            class_code: classExists.class_code
        },
        ...new ResponseDataPagination(formattedPayments, {
            total,
            page,
            pageSize: limit,
            totalPages: Math.ceil(total / limit),
            sortOrder
        })
    })
}

/**
 * Lấy danh sách đóng học phí của một học sinh cụ thể
 */
export const getTuitionPaymentsByUserId = async (req, res) => {
    const { userId } = req.params
    const page = parseInt(req.query.page, 10) || 1
    const limit = parseInt(req.query.limit, 10) || 10
    const offset = (page - 1) * limit
    const sortOrder = req.query.sortOrder || 'DESC'
    const sortBy = req.query.sortBy || 'createdAt'
    const isPaid = req.query.isPaid || null // Lọc theo trạng thái thanh toán
    const overdue = req.query.overdue == 'true' || false // Lấy các khoản học phí đã quá hạn
    const month = req.query.month || null // Lọc theo tháng

    // Kiểm tra học sinh có tồn tại không
    const userExists = await db.User.findByPk(userId)
    if (!userExists) {
        return res.status(404).json({ message: 'Học sinh không tồn tại' })
    }

    // Xây dựng điều kiện tìm kiếm
    let whereClause = { userId }

    // Tìm kiếm theo trạng thái thanh toán (isPaid)
    if (isPaid !== null) {
        whereClause.isPaid = isPaid == 'true' ? true : false
    }

    // Tìm kiếm theo tháng nếu có
    if (month) {
        whereClause.month = month
    }

    // Tìm kiếm các khoản học phí đã quá hạn
    if (overdue) {
        const today = new Date();
        whereClause = {
            ...whereClause,
            dueDate: {
                [Op.lt]: today
            },
            isPaid: false
        }
    }

    // console.log('whereClause:', whereClause);

    // Lấy danh sách đóng học phí của học sinh
    const [payments, total] = await Promise.all([
        db.TuitionPayment.findAll({
            where: whereClause,
            offset,
            limit,
            order: [[sortBy, sortOrder]]
        }),
        db.TuitionPayment.count({ where: whereClause })
    ])

    // console.log('payments:', payments);

    // Format lại dữ liệu trước khi trả về
    const formattedPayments = payments.map(payment => {
        const plainPayment = payment.get({ plain: true })
        const [year, month] = plainPayment.month.split('-')
        const monthNames = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ]
        return {
            ...plainPayment,
            isOverdue: plainPayment.dueDate &&
                !plainPayment.isPaid &&
                new Date() > new Date(plainPayment.dueDate),
            monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
            paymentDateFormatted: plainPayment.paymentDate
                ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                : null,
            remainingAmount: plainPayment.expectedAmount - plainPayment.paidAmount,
            dueDateFormatted: plainPayment.dueDate
                ? new Date(plainPayment.dueDate).toLocaleDateString('vi-VN')
                : null
        }
    })

    return res.status(200).json({
        message: `Danh sách đóng học phí của học sinh ${userExists.lastName} ${userExists.firstName}`,
        user: {
            id: userExists.id,
            lastName: userExists.lastName,
            firstName: userExists.firstName,
            username: userExists.username,
            avatarUrl: userExists.avatarUrl
        },
        ...new ResponseDataPagination(formattedPayments, {
            total,
            page,
            pageSize: limit,
            totalPages: Math.ceil(total / limit),
            sortOrder
        })
    })

}

/**
 * Lấy thông tin chi tiết một khoản đóng học phí
 */
export const getTuitionPaymentById = async (req, res) => {
    const { id } = req.params
    const payment = await db.TuitionPayment.findByPk(id, {
        include: [
            {
                model: db.User,
                as: 'user',
                attributes: ['id', 'lastName', 'firstName', 'username', 'class', 'highSchool', 'phone', 'password']
            }
        ]
    })

    if (!payment) {
        return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
    }

    // Format lại dữ liệu trước khi trả về
    const plainPayment = payment.get({ plain: true })
    const [year, month] = plainPayment.month.split('-')
    const monthNames = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ]
    const formattedPayment = {
        ...plainPayment,
        monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
        paymentDateFormatted: plainPayment.paymentDate
            ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
            : null,
        studentName: `${plainPayment.user.lastName} ${plainPayment.user.firstName}`,
        userClass: plainPayment.user.class,
        dueDateFormatted: plainPayment.dueDate
            ? new Date(plainPayment.dueDate).toLocaleDateString('vi-VN')
            : null
    }

    return res.status(200).json({
        message: 'Chi tiết khoản đóng học phí',
        data: formattedPayment
    })
}

export const checkTuitionPaymentNotPaid = async (req, res) => {
    const { id } = req.user

    const today = new Date()

    const payments = await db.TuitionPayment.findAll({
        where: {
            userId: id,
            isPaid: false
        }
    })

    const filteredPayments = payments.filter(payment => {
        if (!payment.dueDate) return false;
        const dueDate = new Date(payment.dueDate);
        return dueDate < today;
    })

    if (filteredPayments.length > 0) {
        return res.status(200).json({
            message: 'Còn học phí chưa thanh toán',
            data: filteredPayments
        })
    } else {
        return res.status(200).json({
            message: 'Không còn học phí chưa thanh toán',
            data: []
        })
    }
}

/**
 * Học sinh xem chi tiết khoản đóng học phí của mình
 */
export const getUserTuitionPaymentById = async (req, res) => {
    const { id } = req.params
    const userId = req.user.id

    // Tìm khoản đóng học phí
    const payment = await db.TuitionPayment.findByPk(id, {
        include: [
            {
                model: db.User,
                as: 'user',
                attributes: ['id', 'lastName', 'firstName', 'username', 'class', 'highSchool', 'phone', 'password']
            }
        ]
    })

    if (!payment) {
        return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
    }

    // Kiểm tra quyền truy cập
    if (payment.userId !== userId) {
        return res.status(403).json({ message: 'Bạn không có quyền xem khoản đóng học phí này' })
    }

    // Format lại dữ liệu trước khi trả về
    const plainPayment = payment.get({ plain: true })
    const [year, month] = plainPayment.month.split('-')
    const monthNames = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ]
    const formattedPayment = {
        ...plainPayment,
        monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
        paymentDateFormatted: plainPayment.paymentDate
            ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
            : null,
        studentName: `${plainPayment.user.lastName} ${plainPayment.user.firstName}`,
        userClass: plainPayment.user.class,
        dueDateFormatted: plainPayment.dueDate
            ? new Date(plainPayment.dueDate).toLocaleDateString('vi-VN')
            : null,
        isOverdue: plainPayment.dueDate &&
            !plainPayment.isPaid &&
            new Date() > new Date(plainPayment.dueDate)
    }

    return res.status(200).json({
        message: 'Chi tiết khoản đóng học phí',
        data: formattedPayment
    })
}

/**
 * Tạo mới khoản đóng học phí cho học sinh
 */
export const createTuitionPayment = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { userId, month, paymentDate, isPaid, dueDate, note } = req.body

        // Kiểm tra học sinh có tồn tại không
        const userExists = await db.User.findByPk(userId, { transaction })
        if (!userExists) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Học sinh không tồn tại' })
        }

        if (userExists.isActive === false) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Học sinh này không còn hoạt động' })
        }

        // Kiểm tra xem đã có khoản đóng học phí cho học sinh này trong tháng này chưa
        const existingPayment = await db.TuitionPayment.findOne({
            where: {
                userId,
                month
            },
            transaction
        })

        if (existingPayment) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Đã tồn tại khoản đóng học phí cho học sinh này trong tháng này' })
        }

        // Tạo mới khoản đóng học phí
        const newPayment = await db.TuitionPayment.create({
            userId,
            month,
            paymentDate: paymentDate ? new Date(paymentDate) : null,
            dueDate: dueDate ? new Date(dueDate) : null,
            isPaid: isPaid || false,
            note,
        }, { transaction })

        await transaction.commit()

        // Gửi thông báo cho học sinh
        try {
            // const io = req.app.get('io')
            // if (io) {
            const [year, monthNum] = month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]
            const monthFormatted = `${monthNames[parseInt(monthNum) - 1]} ${year}`

            await notificationService.createNotification({
                userId,
                title: "Thông báo học phí",
                content: `Bạn có khoản học phí tháng ${monthFormatted} ${isPaid ? 'đã được thanh toán' : 'cần thanh toán'}`,
                type: "TUITION",
                relatedId: newPayment.id,
                relatedType: "PAYMENT",
                actionUrl: `/tuition-payment/${newPayment.id}`,
                isRead: false
            })

            // await sendUserNotification(
            //     io,
            //     userId,
            //     "Thông báo học phí",
            //     `Bạn có khoản học phí tháng ${monthFormatted} cần thanh toán`,
            //     "TUITION",
            //     newPayment.id,
            //     "CLASS",
            //     `/tuition-payment/${newPayment.id}`
            // )
            // }
        } catch (notificationError) {
            console.error("Lỗi khi gửi thông báo học phí:", notificationError)
            // Không ảnh hưởng đến kết quả trả về
        }

        return res.status(201).json({
            message: 'Tạo khoản đóng học phí thành công',
            data: newPayment
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi tạo khoản đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Cập nhật thông tin khoản đóng học phí
 */
export const updateTuitionPayment = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { id } = req.params
        const { paymentDate, isPaid, dueDate, note } = req.body
        // console.log(req.body)
        // Kiểm tra khoản đóng học phí có tồn tại không
        const payment = await db.TuitionPayment.findByPk(id, {
            include: [],
            transaction
        })

        if (!payment) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
        }

        const wasPaid = payment.isPaid

        // Cập nhật thông tin khoản đóng học phí
        const updatedFields = {};

        // Kiểm tra ngày hợp lệ
        if (paymentDate && !isNaN(Date.parse(paymentDate))) {
            updatedFields.paymentDate = new Date(paymentDate);
        } else if (paymentDate === null) {
            updatedFields.paymentDate = null;
        }

        if (dueDate && !isNaN(Date.parse(dueDate))) {
            updatedFields.dueDate = new Date(dueDate);
        }

        // Kiểm tra chuỗi
        if (typeof note === 'string') {
            updatedFields.note = note.trim();
        }

        // Kiểm tra boolean
        if (typeof isPaid === 'boolean') {
            updatedFields.isPaid = isPaid;
        }

        // Nếu không có gì để cập nhật
        if (Object.keys(updatedFields).length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Không có trường hợp lệ để cập nhật.' });
        }

        // Cập nhật
        await payment.update(updatedFields, { transaction });


        await transaction.commit()

        // Gửi thông báo cho học sinh nếu đã thanh toán đủ
        if (isPaid && !wasPaid) {
            try {
                // const io = req.app.get('io')
                // if (io) {
                const [year, monthNum] = payment.month.split('-')
                const monthNames = [
                    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
                ]
                const monthFormatted = `${monthNames[parseInt(monthNum) - 1]} ${year}`

                await notificationService.createNotification({
                    userId: payment.userId,
                    title: "Xác nhận thanh toán học phí",
                    content: `Học phí tháng ${monthFormatted} đã được thanh toán đầy đủ`,
                    type: "TUITION",
                    relatedId: payment.id,
                    relatedType: "PAYMENT",
                    actionUrl: `/tuition-payment/${payment.id}`,
                    isRead: false
                })

                // await sendUserNotification(
                //     io,
                //     payment.userId,
                //     "Xác nhận thanh toán học phí",
                //     `Học phí tháng ${monthFormatted} đã được thanh toán đầy đủ`,
                //     "TUITION",
                //     payment.id,
                //     "CLASS",
                //     `/tuition-payment/${payment.id}`
                // )
                // }
            } catch (notificationError) {
                console.error("Lỗi khi gửi thông báo xác nhận học phí:", notificationError)
                // Không ảnh hưởng đến kết quả trả về
            }
        }

        return res.status(200).json({
            message: 'Cập nhật khoản đóng học phí thành công',
            data: payment
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi cập nhật khoản đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Xóa khoản đóng học phí
 */
export const deleteTuitionPayment = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { id } = req.params

        // Kiểm tra khoản đóng học phí có tồn tại không
        const payment = await db.TuitionPayment.findByPk(id, { transaction })
        if (!payment) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
        }

        // Kiểm tra xem khoản đóng học phí đã được thanh toán chưa
        if (payment.isPaid) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Không thể xóa khoản đóng học phí đã được thanh toán' })
        }

        // Xóa khoản đóng học phí
        await payment.destroy({ transaction })

        await transaction.commit()

        return res.status(200).json({
            message: 'Xóa khoản đóng học phí thành công',
            data: id
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi xóa khoản đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Tạo khoản đóng học phí cho tất cả học sinh trong hệ thống
 */
export const createTuitionPaymentsForAllStudents = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { month, note, dueDate, batchClass, isPaid, graduationYear } = req.body

        if (!month) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Tháng không được để trống' })
        }

        if (!batchClass) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Lớp không được để trống' })
        }

        // Kiểm tra định dạng tháng (YYYY-MM)
        const monthRegex = /^\d{4}-\d{2}$/
        if (!monthRegex.test(month)) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Định dạng tháng không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM' })
        }

        // Lấy danh sách tất cả học sinh đang hoạt động
        const students = await db.User.findAll({
            where: {
                userType: UserType.STUDENT,
                isActive: true,
                class: batchClass,
                ...(graduationYear && { graduationYear })
            },
            attributes: ['id'],
            transaction
        })

        if (students.length === 0) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Không có học sinh nào trong hệ thống' })
        }

        // Tạo cache để lưu trữ học phí đã truy vấn
        const cache = {
            classTuitions: {} // Lưu trữ học phí theo định dạng: { 'classId_month': amount }
        };

        // Tạo khoản đóng học phí cho từng học sinh
        const createdPayments = []
        const skippedPayments = []

        for (const student of students) {
            // Kiểm tra xem học sinh đã có khoản đóng học phí trong tháng này chưa
            const existingPayment = await db.TuitionPayment.findOne({
                where: {
                    userId: student.id,
                    month
                },
                transaction
            })

            // Nếu chưa có, tạo mới
            if (!existingPayment) {

                const newPayment = await db.TuitionPayment.create({
                    userId: student.id,
                    month,
                    dueDate: dueDate ? new Date(dueDate) : null,
                    note: note || null,
                    isPaid: isPaid || false
                }, { transaction })

                createdPayments.push({
                    id: newPayment.id,
                    userId: student.id,
                    studentName: `${student.lastName} ${student.firstName}`
                })
            } else {
                skippedPayments.push({
                    userId: student.id,
                    studentName: `${student.lastName} ${student.firstName}`
                })
            }
        }

        await transaction.commit()

        // Gửi thông báo cho tất cả học sinh đã được tạo khoản học phí
        try {
            // const io = req.app.get('io')
            // if (io) {
            const [year, monthNum] = month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]
            const monthFormatted = `${monthNames[parseInt(monthNum) - 1]} ${year}`

            for (const payment of createdPayments) {
                await notificationService.createNotification({
                    userId: payment.userId,
                    title: "Thông báo học phí",
                    content: `Bạn có khoản học phí tháng ${monthFormatted} ${isPaid ? 'đã được thanh toán' : 'cần thanh toán'}`,
                    type: "TUITION",
                    relatedId: payment.id,
                    relatedType: "PAYMENT",
                    actionUrl: `/tuition-payment/${payment.id}`,
                    isRead: false
                })

                // await sendUserNotification(
                //     io,
                //     payment.userId,
                //     "Thông báo học phí",
                //     `Bạn có khoản học phí tháng ${monthFormatted} cần thanh toán`,
                //     "TUITION",
                //     payment.id,
                //     "PAYMENT",
                //     `/tuition-payment/${payment.id}`
                // )
            }
            // }
        } catch (notificationError) {
            console.error("Lỗi khi gửi thông báo học phí:", notificationError)
            // Không ảnh hưởng đến kết quả trả về
        }

        // console.log(createdPayments)

        return res.status(201).json({
            message: `Đã tạo ${createdPayments.length} khoản đóng học phí cho học sinh`,
            data: {
                createdCount: createdPayments.length,
                skippedCount: skippedPayments.length,
                totalStudents: students.length,
                createdPayments,
                skippedPayments
            }
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi tạo khoản đóng học phí cho học sinh:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Thống kê doanh thu học phí theo tháng và lớp
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - Thống kê doanh thu học phí
 */
/**
 * Lấy thống kê tổng quan về các khoản học phí của một học sinh
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - Thống kê tổng quan về các khoản học phí
 */
export const getUserTuitionSummary = async (req, res) => {
    const { userId } = req.params;

    // Kiểm tra userId có tồn tại không
    if (!userId) {
        return res.status(400).json({ message: 'Thiếu thông tin userId' });
    }

    // Kiểm tra user có tồn tại không
    const user = await db.User.findByPk(userId);
    if (!user) {
        return res.status(404).json({ message: 'Không tìm thấy người dùng' });
    }

    // Lấy tất cả các khoản học phí của học sinh
    const tuitionPayments = await db.TuitionPayment.findAll({
        where: { userId }
    });

    // Nếu không có khoản học phí nào
    if (!tuitionPayments || tuitionPayments.length === 0) {
        return res.status(200).json({
            message: 'Không có khoản học phí nào',
            data: {
                totalPayments: 0,
                overduePayments: 0,
                paidPayments: 0,
                unpaidPayments: 0,
                // totalExpectedAmount: 0,
                // totalPaidAmount: 0,
                // remainingAmount: 0,
                paymentRate: 0
            }
        });
    }

    // Tính toán các thống kê
    const currentDate = new Date();

    // Số lượng các khoản học phí
    const totalPayments = tuitionPayments.length;

    // Số khoản quá hạn (chưa thanh toán đủ và đã quá hạn)
    const overduePayments = tuitionPayments.filter(payment => {
        const dueDate = new Date(payment.dueDate);
        return (!payment.isPaid && currentDate > dueDate);
    }).length;

    // Số khoản đã thanh toán
    const paidPayments = tuitionPayments.filter(payment => payment.isPaid).length;

    // Số khoản chưa thanh toán hoặc thanh toán một phần
    const unpaidPayments = tuitionPayments.filter(payment => !payment.isPaid).length;

    // // Tổng số tiền dự kiến
    // const totalExpectedAmount = tuitionPayments.reduce((sum, payment) => sum + parseFloat(payment.expectedAmount || 0), 0);

    // // Tổng số tiền đã thanh toán
    // const totalPaidAmount = tuitionPayments.reduce((sum, payment) => sum + parseFloat(payment.paidAmount || 0), 0);

    // // Số tiền còn lại
    // const remainingAmount = totalExpectedAmount - totalPaidAmount;

    // Tỷ lệ thanh toán
    // const paymentRate = totalExpectedAmount > 0 ? (totalPaidAmount / totalExpectedAmount * 100).toFixed(2) : 0;

    // Trả về kết quả
    return res.status(200).json({
        message: 'Thống kê tổng quan về các khoản học phí',
        data: {
            totalPayments,
            overduePayments,
            paidPayments,
            unpaidPayments,
            // totalExpectedAmount,
            // totalPaidAmount,
            // remainingAmount,
            // paymentRate
        }
    });
};

export const getTuitionStatistics = async (req, res) => {
    try {
        const { startMonth, endMonth, userClass } = req.query

        // Validate startMonth và endMonth (định dạng YYYY-MM)
        const monthRegex = /^\d{4}-\d{2}$/
        if (startMonth && !monthRegex.test(startMonth)) {
            return res.status(400).json({ message: 'Định dạng tháng bắt đầu không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM' })
        }
        if (endMonth && !monthRegex.test(endMonth)) {
            return res.status(400).json({ message: 'Định dạng tháng kết thúc không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM' })
        }

        // Xây dựng điều kiện tìm kiếm
        let whereClause = {}

        // Lọc theo tháng
        if (startMonth && endMonth) {
            whereClause.month = {
                [Op.between]: [startMonth, endMonth]
            }
        } else if (startMonth) {
            whereClause.month = {
                [Op.gte]: startMonth
            }
        } else if (endMonth) {
            whereClause.month = {
                [Op.lte]: endMonth
            }
        }

        // Lọc theo lớp
        if (userClass) {
            whereClause['$user.class$'] = userClass
        }

        // Thực hiện truy vấn thống kê với isPaid
        const statistics = await db.TuitionPayment.findAll({
            attributes: [
                'month',
                'isPaid',
                [fn('COUNT', col('TuitionPayment.id')), 'totalCount']
            ],
            include: [
                {
                    model: db.User,
                    as: 'user',
                    attributes: ['class'],
                    required: !!userClass
                }
            ],
            where: whereClause,
            group: ['month', 'isPaid', 'user.class'],
            order: [['month', 'ASC']],
            subQuery: false,
            raw: true
        })

        // Format lại dữ liệu để dễ sử dụng
        const formattedStatistics = statistics.map(stat => {
            const [year, month] = stat.month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]

            return {
                month: stat.month,
                monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
                userClass: stat['user.class'] || 'Tất cả',
                isPaid: stat.isPaid,
                totalCount: parseInt(stat.totalCount) || 0,
            }
        })

        // Tổng hợp thống kê theo tháng (tất cả các lớp)
        const monthlyStatistics = {}
        formattedStatistics.forEach(stat => {
            if (!monthlyStatistics[stat.month]) {
                monthlyStatistics[stat.month] = {
                    month: stat.month,
                    monthFormatted: stat.monthFormatted,
                    totalStudents: 0,
                    paidStudents: 0,
                    unpaidStudents: 0,
                    classes: {}
                }
            }

            // Cập nhật thống kê theo tháng
            monthlyStatistics[stat.month].totalStudents += stat.totalCount
            if (stat.isPaid) {
                monthlyStatistics[stat.month].paidStudents += stat.totalCount
            } else {
                monthlyStatistics[stat.month].unpaidStudents += stat.totalCount
            }

            // Thêm thống kê theo lớp
            if (stat.userClass && stat.userClass !== 'Tất cả') {
                if (!monthlyStatistics[stat.month].classes[stat.userClass]) {
                    monthlyStatistics[stat.month].classes[stat.userClass] = {
                        totalStudents: 0,
                        paidStudents: 0,
                        unpaidStudents: 0
                    }
                }

                monthlyStatistics[stat.month].classes[stat.userClass].totalStudents += stat.totalCount
                if (stat.isPaid) {
                    monthlyStatistics[stat.month].classes[stat.userClass].paidStudents += stat.totalCount
                } else {
                    monthlyStatistics[stat.month].classes[stat.userClass].unpaidStudents += stat.totalCount
                }
            }
        })

        // Chuyển đổi thành mảng và tính tỷ lệ
        const monthlyStatisticsArray = Object.values(monthlyStatistics).map(stat => ({
            ...stat,
            paymentRate: stat.totalStudents > 0 ? ((stat.paidStudents / stat.totalStudents) * 100).toFixed(1) : 0
        }))

        // Tổng hợp thống kê theo lớp (tất cả các tháng)
        const classStatistics = {}
        formattedStatistics.forEach(stat => {
            if (stat.userClass && stat.userClass !== 'Tất cả') {
                if (!classStatistics[stat.userClass]) {
                    classStatistics[stat.userClass] = {
                        userClass: stat.userClass,
                        totalStudents: 0,
                        paidStudents: 0,
                        unpaidStudents: 0,
                        months: {}
                    }
                }

                // Cập nhật tổng thống kê theo lớp
                classStatistics[stat.userClass].totalStudents += stat.totalCount
                if (stat.isPaid) {
                    classStatistics[stat.userClass].paidStudents += stat.totalCount
                } else {
                    classStatistics[stat.userClass].unpaidStudents += stat.totalCount
                }

                // Thêm thống kê theo tháng
                if (!classStatistics[stat.userClass].months[stat.month]) {
                    classStatistics[stat.userClass].months[stat.month] = {
                        month: stat.month,
                        monthFormatted: stat.monthFormatted,
                        totalStudents: 0,
                        paidStudents: 0,
                        unpaidStudents: 0
                    }
                }

                classStatistics[stat.userClass].months[stat.month].totalStudents += stat.totalCount
                if (stat.isPaid) {
                    classStatistics[stat.userClass].months[stat.month].paidStudents += stat.totalCount
                } else {
                    classStatistics[stat.userClass].months[stat.month].unpaidStudents += stat.totalCount
                }
            }
        })

        // Chuyển đổi thành mảng và tính tỷ lệ
        const classStatisticsArray = Object.values(classStatistics).map(stat => ({
            ...stat,
            paymentRate: stat.totalStudents > 0 ? ((stat.paidStudents / stat.totalStudents) * 100).toFixed(1) : 0,
            months: Object.values(stat.months).map(monthStat => ({
                ...monthStat,
                paymentRate: monthStat.totalStudents > 0 ? ((monthStat.paidStudents / monthStat.totalStudents) * 100).toFixed(1) : 0
            }))
        }))

        // Tính tổng thống kê cho tất cả các tháng và lớp
        const totalStatistics = {
            totalStudents: formattedStatistics.reduce((sum, stat) => sum + stat.totalCount, 0),
            paidStudents: formattedStatistics.filter(stat => stat.isPaid).reduce((sum, stat) => sum + stat.totalCount, 0),
            unpaidStudents: formattedStatistics.filter(stat => !stat.isPaid).reduce((sum, stat) => sum + stat.totalCount, 0)
        }
        totalStatistics.paymentRate = totalStatistics.totalStudents > 0 ?
            ((totalStatistics.paidStudents / totalStatistics.totalStudents) * 100).toFixed(1) : 0

        return res.status(200).json({
            message: 'Thống kê thanh toán học phí',
            data: {
                detailedStatistics: formattedStatistics,
                monthlyStatistics: monthlyStatisticsArray,
                classStatistics: classStatisticsArray,
                totalStatistics,
            }
        })
    } catch (error) {
        console.error('Lỗi khi lấy thống kê học phí:', error)
        return res.status(500).json({
            message: 'Lỗi server khi lấy thống kê',
            error: error.message
        })
    }
}

/**
 * Tính toán lại số tiền học phí dự kiến (expectedAmount) cho một khoản đóng học phí
 */
// export const recalculateExpectedAmount = async (req, res) => {
//     const transaction = await db.sequelize.transaction();
//     try {
//         const { id } = req.params;

//         // Kiểm tra khoản đóng học phí có tồn tại không
//         const payment = await db.TuitionPayment.findByPk(id, { transaction });

//         if (!payment) {
//             await transaction.rollback();
//             return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' });
//         }

//         // Sử dụng service để tính toán và cập nhật số tiền học phí dự kiến
//         const updatedPayment = await tuitionService.updateExpectedAmountForTuitionPayment(id, { transaction });

//         await transaction.commit();

//         return res.status(200).json({
//             message: 'Đã tính toán lại số tiền học phí dự kiến',
//             data: updatedPayment
//         });
//     } catch (error) {
//         await transaction.rollback();
//         console.error('Lỗi khi tính toán lại số tiền học phí dự kiến:', error);
//         return res.status(500).json({ message: 'Lỗi server', error: error.message });
//     }
// };

/**
 * Lấy danh sách học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
 */
// export const getStudentClassTuitionsByMonth = async (req, res) => {
//     try {
//         const userId = req.user.id; // Lấy ID của học sinh đang đăng nhập
//         const { month } = req.params; // Lấy tháng từ params

//         // Kiểm tra định dạng tháng
//         if (!month || !/^\d{4}-\d{2}$/.test(month)) {
//             return res.status(400).json({
//                 message: 'Định dạng tháng không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM'
//             });
//         }

//         // Gọi service để lấy danh sách học phí
//         const classTuitions = await tuitionService.getClassTuitionsByStudentAndMonth(userId, month);

//         // Tính tổng số tiền học phí
//         const totalAmount = classTuitions.reduce((sum, tuition) => sum + tuition.amount, 0);

//         return res.status(200).json({
//             message: 'Danh sách học phí của các lớp',
//             data: {
//                 classTuitions,
//                 totalAmount,
//                 month,
//                 monthFormatted: formatMonthToVietnamese(month)
//             }
//         });
//     } catch (error) {
//         console.error('Lỗi khi lấy danh sách học phí của các lớp:', error);
//         return res.status(500).json({ message: 'Lỗi server', error: error.message });
//     }
// };

// /**
//  * Admin xem danh sách học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
//  */
// export const getStudentClassTuitionsByMonthAdmin = async (req, res) => {
//     try {
//         const { userId, month } = req.params; // Lấy ID học sinh và tháng từ params
//         console.log('userId', userId);
//         console.log('month', month);
//         // Kiểm tra định dạng tháng
//         if (!month || !/^\d{4}-\d{2}$/.test(month)) {
//             return res.status(400).json({
//                 message: 'Định dạng tháng không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM'
//             });
//         }

//         // Kiểm tra userId có tồn tại không
//         if (!userId) {
//             return res.status(400).json({ message: 'ID học sinh không được để trống' });
//         }

//         // Kiểm tra học sinh có tồn tại không
//         const student = await db.User.findByPk(userId);
//         if (!student) {
//             return res.status(404).json({ message: 'Không tìm thấy học sinh' });
//         }

//         // Gọi service để lấy danh sách học phí
//         const classTuitions = await tuitionService.getClassTuitionsByStudentAndMonth(userId, month);

//         // Tính tổng số tiền học phí
//         const totalAmount = classTuitions.reduce((sum, tuition) => sum + tuition.amount, 0);

//         // Lấy thông tin học sinh
//         const studentInfo = {
//             id: student.id,
//             name: student.name,
//             email: student.email,
//             phone: student.phone
//         };

//         return res.status(200).json({
//             message: 'Danh sách học phí của các lớp',
//             data: {
//                 student: studentInfo,
//                 classTuitions,
//                 totalAmount,
//                 month,
//                 monthFormatted: formatMonthToVietnamese(month)
//             }
//         });
//     } catch (error) {
//         console.error('Lỗi khi lấy danh sách học phí của các lớp:', error);
//         return res.status(500).json({ message: 'Lỗi server', error: error.message });
//     }
// };

/**
 * Hàm hỗ trợ để định dạng tháng sang tiếng Việt
 */
const formatMonthToVietnamese = (monthStr) => {
    const [year, month] = monthStr.split('-');
    const monthNames = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
};

export default {
    getAllTuitionPayments,
    getTuitionPaymentsByClassId,
    getTuitionPaymentsByUserId,
    createTuitionPayment,
    updateTuitionPayment,
    deleteTuitionPayment,
    createTuitionPaymentsForAllStudents,
    getTuitionStatistics,
    // recalculateExpectedAmount,
    // getStudentClassTuitionsByMonth,
    // getStudentClassTuitionsByMonthAdmin
}
