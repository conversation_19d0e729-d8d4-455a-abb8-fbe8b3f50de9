/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BuiltInConnectors,
  BuiltInConnectors$inboundSchema,
  BuiltInConnectors$outboundSchema,
} from "./builtinconnectors.js";

export const ToolReferenceChunkType = {
  ToolReference: "tool_reference",
} as const;
export type ToolReferenceChunkType = ClosedEnum<typeof ToolReferenceChunkType>;

export type ToolReferenceChunk = {
  type?: ToolReferenceChunkType | undefined;
  tool: BuiltInConnectors;
  title: string;
  url?: string | null | undefined;
  favicon?: string | null | undefined;
  description?: string | null | undefined;
};

/** @internal */
export const ToolReferenceChunkType$inboundSchema: z.ZodNativeEnum<
  typeof ToolReferenceChunkType
> = z.nativeEnum(ToolReferenceChunkType);

/** @internal */
export const ToolReferenceChunkType$outboundSchema: z.ZodNativeEnum<
  typeof ToolReferenceChunkType
> = ToolReferenceChunkType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolReferenceChunkType$ {
  /** @deprecated use `ToolReferenceChunkType$inboundSchema` instead. */
  export const inboundSchema = ToolReferenceChunkType$inboundSchema;
  /** @deprecated use `ToolReferenceChunkType$outboundSchema` instead. */
  export const outboundSchema = ToolReferenceChunkType$outboundSchema;
}

/** @internal */
export const ToolReferenceChunk$inboundSchema: z.ZodType<
  ToolReferenceChunk,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ToolReferenceChunkType$inboundSchema.default("tool_reference"),
  tool: BuiltInConnectors$inboundSchema,
  title: z.string(),
  url: z.nullable(z.string()).optional(),
  favicon: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
});

/** @internal */
export type ToolReferenceChunk$Outbound = {
  type: string;
  tool: string;
  title: string;
  url?: string | null | undefined;
  favicon?: string | null | undefined;
  description?: string | null | undefined;
};

/** @internal */
export const ToolReferenceChunk$outboundSchema: z.ZodType<
  ToolReferenceChunk$Outbound,
  z.ZodTypeDef,
  ToolReferenceChunk
> = z.object({
  type: ToolReferenceChunkType$outboundSchema.default("tool_reference"),
  tool: BuiltInConnectors$outboundSchema,
  title: z.string(),
  url: z.nullable(z.string()).optional(),
  favicon: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolReferenceChunk$ {
  /** @deprecated use `ToolReferenceChunk$inboundSchema` instead. */
  export const inboundSchema = ToolReferenceChunk$inboundSchema;
  /** @deprecated use `ToolReferenceChunk$outboundSchema` instead. */
  export const outboundSchema = ToolReferenceChunk$outboundSchema;
  /** @deprecated use `ToolReferenceChunk$Outbound` instead. */
  export type Outbound = ToolReferenceChunk$Outbound;
}

export function toolReferenceChunkToJSON(
  toolReferenceChunk: ToolReferenceChunk,
): string {
  return JSON.stringify(
    ToolReferenceChunk$outboundSchema.parse(toolReferenceChunk),
  );
}

export function toolReferenceChunkFromJSON(
  jsonString: string,
): SafeParseResult<ToolReferenceChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolReferenceChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolReferenceChunk' from JSON`,
  );
}
