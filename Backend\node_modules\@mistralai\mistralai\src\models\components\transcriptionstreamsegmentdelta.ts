/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const TranscriptionStreamSegmentDeltaType = {
  TranscriptionSegment: "transcription.segment",
} as const;
export type TranscriptionStreamSegmentDeltaType = ClosedEnum<
  typeof TranscriptionStreamSegmentDeltaType
>;

export type TranscriptionStreamSegmentDelta = {
  text: string;
  start: number;
  end: number;
  type?: TranscriptionStreamSegmentDeltaType | undefined;
  additionalProperties?: { [k: string]: any };
};

/** @internal */
export const TranscriptionStreamSegmentDeltaType$inboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamSegmentDeltaType
> = z.nativeEnum(TranscriptionStreamSegmentDeltaType);

/** @internal */
export const TranscriptionStreamSegmentDeltaType$outboundSchema:
  z.ZodNativeEnum<typeof TranscriptionStreamSegmentDeltaType> =
    TranscriptionStreamSegmentDeltaType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamSegmentDeltaType$ {
  /** @deprecated use `TranscriptionStreamSegmentDeltaType$inboundSchema` instead. */
  export const inboundSchema =
    TranscriptionStreamSegmentDeltaType$inboundSchema;
  /** @deprecated use `TranscriptionStreamSegmentDeltaType$outboundSchema` instead. */
  export const outboundSchema =
    TranscriptionStreamSegmentDeltaType$outboundSchema;
}

/** @internal */
export const TranscriptionStreamSegmentDelta$inboundSchema: z.ZodType<
  TranscriptionStreamSegmentDelta,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    text: z.string(),
    start: z.number(),
    end: z.number(),
    type: TranscriptionStreamSegmentDeltaType$inboundSchema.default(
      "transcription.segment",
    ),
  }).catchall(z.any()),
  "additionalProperties",
  true,
);

/** @internal */
export type TranscriptionStreamSegmentDelta$Outbound = {
  text: string;
  start: number;
  end: number;
  type: string;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const TranscriptionStreamSegmentDelta$outboundSchema: z.ZodType<
  TranscriptionStreamSegmentDelta$Outbound,
  z.ZodTypeDef,
  TranscriptionStreamSegmentDelta
> = z.object({
  text: z.string(),
  start: z.number(),
  end: z.number(),
  type: TranscriptionStreamSegmentDeltaType$outboundSchema.default(
    "transcription.segment",
  ),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamSegmentDelta$ {
  /** @deprecated use `TranscriptionStreamSegmentDelta$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamSegmentDelta$inboundSchema;
  /** @deprecated use `TranscriptionStreamSegmentDelta$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamSegmentDelta$outboundSchema;
  /** @deprecated use `TranscriptionStreamSegmentDelta$Outbound` instead. */
  export type Outbound = TranscriptionStreamSegmentDelta$Outbound;
}

export function transcriptionStreamSegmentDeltaToJSON(
  transcriptionStreamSegmentDelta: TranscriptionStreamSegmentDelta,
): string {
  return JSON.stringify(
    TranscriptionStreamSegmentDelta$outboundSchema.parse(
      transcriptionStreamSegmentDelta,
    ),
  );
}

export function transcriptionStreamSegmentDeltaFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionStreamSegmentDelta, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionStreamSegmentDelta$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionStreamSegmentDelta' from JSON`,
  );
}
