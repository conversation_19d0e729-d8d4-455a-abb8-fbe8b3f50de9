/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FunctionT = {
  name: string;
  description?: string | undefined;
  strict?: boolean | undefined;
  parameters: { [k: string]: any };
};

/** @internal */
export const FunctionT$inboundSchema: z.ZodType<
  FunctionT,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.string(),
  description: z.string().optional(),
  strict: z.boolean().optional(),
  parameters: z.record(z.any()),
});

/** @internal */
export type FunctionT$Outbound = {
  name: string;
  description?: string | undefined;
  strict?: boolean | undefined;
  parameters: { [k: string]: any };
};

/** @internal */
export const FunctionT$outboundSchema: z.ZodType<
  FunctionT$Outbound,
  z.ZodTypeDef,
  FunctionT
> = z.object({
  name: z.string(),
  description: z.string().optional(),
  strict: z.boolean().optional(),
  parameters: z.record(z.any()),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FunctionT$ {
  /** @deprecated use `FunctionT$inboundSchema` instead. */
  export const inboundSchema = FunctionT$inboundSchema;
  /** @deprecated use `FunctionT$outboundSchema` instead. */
  export const outboundSchema = FunctionT$outboundSchema;
  /** @deprecated use `FunctionT$Outbound` instead. */
  export type Outbound = FunctionT$Outbound;
}

export function functionToJSON(functionT: FunctionT): string {
  return JSON.stringify(FunctionT$outboundSchema.parse(functionT));
}

export function functionFromJSON(
  jsonString: string,
): SafeParseResult<FunctionT, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FunctionT$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FunctionT' from JSON`,
  );
}
