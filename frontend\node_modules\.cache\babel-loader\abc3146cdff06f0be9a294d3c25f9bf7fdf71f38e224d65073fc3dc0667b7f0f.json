{"ast": null, "code": "var _jsxFileName = \"D:\\\\ToanThayBee\\\\frontend\\\\src\\\\components\\\\PageAddExam\\\\SolutionEditor.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport { ImagePlus, Sparkles } from \"lucide-react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { fixTextAndLatex } from \"../../features/ai/aiSlice\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SolutionEditor = _ref => {\n  _s();\n  let {\n    solution,\n    onSolutionChange,\n    preview = true\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const textareaRef = useRef(null);\n  const dispatch = useDispatch();\n  const {\n    fixTextResult\n  } = useSelector(state => state.ai);\n  const [loading, setLoading] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (!draggedImage || !onSolutionChange) return;\n\n    // Lấy vị trí cursor từ textarea\n    let insertPosition = 0;\n    if (textareaRef.current) {\n      // Lấy vị trí cursor hiện tại trong textarea\n      const cursorPos = textareaRef.current.selectionStart;\n      insertPosition = cursorPos !== undefined ? cursorPos : solution ? solution.length : 0;\n    } else {\n      // Fallback: chèn vào cuối\n      insertPosition = solution ? solution.length : 0;\n    }\n\n    // Tạo markdown image syntax\n    const imageMarkdown = \"\\n![\\u1EA2nh](\".concat(draggedImage, \")\\n\");\n\n    // Chèn vào vị trí cursor\n    const newSolution = solution ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition) : imageMarkdown;\n    onSolutionChange(newSolution);\n\n    // Đặt lại cursor sau khi chèn ảnh\n    setTimeout(() => {\n      if (textareaRef.current) {\n        const newCursorPos = insertPosition + imageMarkdown.length;\n        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);\n        textareaRef.current.focus();\n      }\n    }, 0);\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!e.currentTarget.contains(e.relatedTarget)) {\n      setIsDraggingOver(false);\n      // setDragPosition(null);\n    }\n  };\n\n  // Ref lưu timeout ID để debounce\n  const debounceRef = useRef(null);\n  const handleTextareaChange = e => {\n    const newValue = e.target.value;\n\n    // Hủy timeout cũ nếu có\n    if (debounceRef.current) {\n      clearTimeout(debounceRef.current);\n    }\n\n    // Thiết lập timeout mới (gọi sau 500ms)\n    debounceRef.current = setTimeout(() => {\n      onSolutionChange(newValue);\n    }, 1000);\n  };\n  const handleAIFix = async () => {\n    if (!solution || !solution.trim()) {\n      alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\n      return;\n    }\n    try {\n      setLoading(true);\n      const result = await dispatch(fixTextAndLatex(solution)).unwrap();\n      setLoading(false);\n      if (result.data.hasChanges) {\n        onSolutionChange(result.data.fixedText);\n      } else {\n        alert('Không tìm thấy lỗi nào cần sửa');\n      }\n    } catch (error) {\n      console.error('Error fixing text:', error);\n      alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm font-semibold text-green-700\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAIFix,\n        disabled: loading || !(solution !== null && solution !== void 0 && solution.trim()),\n        className: \"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n        title: \"S\\u1EED d\\u1EE5ng AI \\u0111\\u1EC3 s\\u1EEDa ch\\xEDnh t\\u1EA3 v\\xE0 k\\xFD hi\\u1EC7u LaTeX\",\n        children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n          className: \"w-3 h-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 21\n        }, this), loading ? 'Đang sửa...' : 'AI Fix']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative border rounded-lg transition-all duration-200 \".concat(isDraggingOver ? \"border-2 border-dashed border-blue-400 bg-blue-50\" : \"border-gray-300 hover:border-blue-300\"),\n      onDragOver: handleDragOver,\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n        ref: textareaRef,\n        value: solution || '',\n        onChange: handleTextareaChange,\n        placeholder: \"Nh\\u1EADp l\\u1EDDi gi\\u1EA3i (h\\u1ED7 tr\\u1EE3 Markdown v\\xE0 LaTeX)...\",\n        className: \"w-full px-2 py-1.5 text-xs border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px]\",\n        style: {\n          lineHeight: '24px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 17\n      }, this), !solution && !isDraggingOver && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\",\n        children: [/*#__PURE__*/_jsxDEV(ImagePlus, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"K\\xE9o \\u1EA3nh v\\xE0o \\u0111\\u1EC3 ch\\xE8n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 13\n    }, this), solution && preview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-3 bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mb-2\",\n        children: \"Xem tr\\u01B0\\u1EDBc:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 9\n  }, this);\n};\n_s(SolutionEditor, \"Y2MzEWty9Fe1wuBOJLTT1zFbyWI=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = SolutionEditor;\nexport default SolutionEditor;\nvar _c;\n$RefreshReg$(_c, \"SolutionEditor\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "ImagePlus", "<PERSON><PERSON><PERSON>", "useDispatch", "useSelector", "fixTextAndLatex", "MarkdownPreviewWithMath", "jsxDEV", "_jsxDEV", "SolutionEditor", "_ref", "_s", "solution", "onSolutionChange", "preview", "isDraggingOver", "setIsDraggingOver", "textareaRef", "dispatch", "fixTextResult", "state", "ai", "loading", "setLoading", "handleDrop", "e", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "insertPosition", "current", "cursorPos", "selectionStart", "undefined", "length", "imageMarkdown", "concat", "newSolution", "slice", "setTimeout", "newCursorPos", "setSelectionRange", "focus", "handleDragOver", "handleDragEnter", "types", "includes", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "debounceRef", "handleTextareaChange", "newValue", "target", "value", "clearTimeout", "handleAIFix", "trim", "alert", "result", "unwrap", "data", "has<PERSON><PERSON><PERSON>", "fixedText", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "ref", "onChange", "placeholder", "style", "lineHeight", "content", "_c", "$RefreshReg$"], "sources": ["D:/ToanThayBee/frontend/src/components/PageAddExam/SolutionEditor.jsx"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport { <PERSON><PERSON><PERSON>, Sparkles } from \"lucide-react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { fixTextAndLatex } from \"../../features/ai/aiSlice\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\n\r\nconst SolutionEditor = ({ solution, onSolutionChange, preview = true }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n    const textareaRef = useRef(null);\r\n    const dispatch = useDispatch();\r\n    const { fixTextResult } = useSelector((state) => state.ai);\r\n    const [loading, setLoading] = useState(false);\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\r\n        if (!draggedImage || !onSolutionChange) return;\r\n\r\n        // Lấy vị trí cursor từ textarea\r\n        let insertPosition = 0;\r\n\r\n        if (textareaRef.current) {\r\n            // Lấy vị trí cursor hiện tại trong textarea\r\n            const cursorPos = textareaRef.current.selectionStart;\r\n            insertPosition = cursorPos !== undefined ? cursorPos : (solution ? solution.length : 0);\r\n        } else {\r\n            // Fallback: chèn vào cuối\r\n            insertPosition = solution ? solution.length : 0;\r\n        }\r\n\r\n        // Tạo markdown image syntax\r\n        const imageMarkdown = `\\n![Ảnh](${draggedImage})\\n`;\r\n\r\n        // Chèn vào vị trí cursor\r\n        const newSolution = solution\r\n            ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition)\r\n            : imageMarkdown;\r\n\r\n        onSolutionChange(newSolution);\r\n\r\n        // Đặt lại cursor sau khi chèn ảnh\r\n        setTimeout(() => {\r\n            if (textareaRef.current) {\r\n                const newCursorPos = insertPosition + imageMarkdown.length;\r\n                textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);\r\n                textareaRef.current.focus();\r\n            }\r\n        }, 0);\r\n    };\r\n\r\n    const handleDragOver = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n    };\r\n\r\n    const handleDragEnter = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragLeave = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (!e.currentTarget.contains(e.relatedTarget)) {\r\n            setIsDraggingOver(false);\r\n            // setDragPosition(null);\r\n        }\r\n    };\r\n\r\n    // Ref lưu timeout ID để debounce\r\n    const debounceRef = useRef(null);\r\n\r\n    const handleTextareaChange = (e) => {\r\n        const newValue = e.target.value;\r\n\r\n        // Hủy timeout cũ nếu có\r\n        if (debounceRef.current) {\r\n            clearTimeout(debounceRef.current);\r\n        }\r\n\r\n        // Thiết lập timeout mới (gọi sau 500ms)\r\n        debounceRef.current = setTimeout(() => {\r\n            onSolutionChange(newValue);\r\n        }, 1000);\r\n    };\r\n\r\n\r\n    const handleAIFix = async () => {\r\n        if (!solution || !solution.trim()) {\r\n            alert('Vui lòng nhập nội dung trước khi sử dụng AI sửa lỗi');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setLoading(true);\r\n            const result = await dispatch(fixTextAndLatex(solution)).unwrap();\r\n            setLoading(false);\r\n            if (result.data.hasChanges) {\r\n                onSolutionChange(result.data.fixedText);\r\n            } else {\r\n                alert('Không tìm thấy lỗi nào cần sửa');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error fixing text:', error);\r\n            alert('Có lỗi xảy ra khi sử dụng AI sửa lỗi');\r\n        }\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"mt-2 space-y-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n                <div className=\"text-sm font-semibold text-green-700\">Lời giải:</div>\r\n                <button\r\n                    onClick={handleAIFix}\r\n                    disabled={loading || !solution?.trim()}\r\n                    className=\"flex items-center gap-1 px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                    title=\"Sử dụng AI để sửa chính tả và ký hiệu LaTeX\"\r\n                >\r\n                    <Sparkles className=\"w-3 h-3\" />\r\n                    {loading ? 'Đang sửa...' : 'AI Fix'}\r\n                </button>\r\n            </div>\r\n            {/* Editor với drop zone */}\r\n            <div\r\n                className={`relative border rounded-lg transition-all duration-200 ${isDraggingOver\r\n                    ? \"border-2 border-dashed border-blue-400 bg-blue-50\"\r\n                    : \"border-gray-300 hover:border-blue-300\"\r\n                    }`}\r\n                onDragOver={handleDragOver}\r\n                onDragEnter={handleDragEnter}\r\n                onDragLeave={handleDragLeave}\r\n                onDrop={handleDrop}\r\n            >\r\n                {/* Textarea */}\r\n                <textarea\r\n                    ref={textareaRef}\r\n                    value={solution || ''}\r\n                    onChange={handleTextareaChange}\r\n                    placeholder=\"Nhập lời giải (hỗ trợ Markdown và LaTeX)...\"\r\n                    className=\"w-full px-2 py-1.5 text-xs border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px]\"\r\n                    style={{ lineHeight: '24px' }}\r\n                />\r\n\r\n                {/* Hint */}\r\n                {!solution && !isDraggingOver && (\r\n                    <div className=\"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\">\r\n                        <ImagePlus className=\"w-4 h-4\" />\r\n                        <span>Kéo ảnh vào để chèn</span>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Preview */}\r\n            {solution && preview && (\r\n                <div className=\"border border-gray-200 rounded-lg p-3 bg-gray-50\">\r\n                    <div className=\"text-xs text-gray-500 mb-2\">Xem trước:</div>\r\n                    <MarkdownPreviewWithMath content={solution} />\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default SolutionEditor;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,SAAS,EAAEC,QAAQ,QAAQ,cAAc;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAOC,uBAAuB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,cAAc,GAAGC,IAAA,IAAoD;EAAAC,EAAA;EAAA,IAAnD;IAAEC,QAAQ;IAAEC,gBAAgB;IAAEC,OAAO,GAAG;EAAK,CAAC,GAAAJ,IAAA;EAClE,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMkB,WAAW,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAc,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,EAAE,CAAC;EAC1D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMyB,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBX,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMY,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAI,CAACF,YAAY,IAAI,CAACf,gBAAgB,EAAE;;IAExC;IACA,IAAIkB,cAAc,GAAG,CAAC;IAEtB,IAAId,WAAW,CAACe,OAAO,EAAE;MACrB;MACA,MAAMC,SAAS,GAAGhB,WAAW,CAACe,OAAO,CAACE,cAAc;MACpDH,cAAc,GAAGE,SAAS,KAAKE,SAAS,GAAGF,SAAS,GAAIrB,QAAQ,GAAGA,QAAQ,CAACwB,MAAM,GAAG,CAAE;IAC3F,CAAC,MAAM;MACH;MACAL,cAAc,GAAGnB,QAAQ,GAAGA,QAAQ,CAACwB,MAAM,GAAG,CAAC;IACnD;;IAEA;IACA,MAAMC,aAAa,oBAAAC,MAAA,CAAeV,YAAY,QAAK;;IAEnD;IACA,MAAMW,WAAW,GAAG3B,QAAQ,GACtBA,QAAQ,CAAC4B,KAAK,CAAC,CAAC,EAAET,cAAc,CAAC,GAAGM,aAAa,GAAGzB,QAAQ,CAAC4B,KAAK,CAACT,cAAc,CAAC,GAClFM,aAAa;IAEnBxB,gBAAgB,CAAC0B,WAAW,CAAC;;IAE7B;IACAE,UAAU,CAAC,MAAM;MACb,IAAIxB,WAAW,CAACe,OAAO,EAAE;QACrB,MAAMU,YAAY,GAAGX,cAAc,GAAGM,aAAa,CAACD,MAAM;QAC1DnB,WAAW,CAACe,OAAO,CAACW,iBAAiB,CAACD,YAAY,EAAEA,YAAY,CAAC;QACjEzB,WAAW,CAACe,OAAO,CAACY,KAAK,CAAC,CAAC;MAC/B;IACJ,CAAC,EAAE,CAAC,CAAC;EACT,CAAC;EAED,MAAMC,cAAc,GAAIpB,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMmB,eAAe,GAAIrB,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAACI,YAAY,CAACkB,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7ChC,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMiC,eAAe,GAAIxB,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAI,CAACF,CAAC,CAACyB,aAAa,CAACC,QAAQ,CAAC1B,CAAC,CAAC2B,aAAa,CAAC,EAAE;MAC5CpC,iBAAiB,CAAC,KAAK,CAAC;MACxB;IACJ;EACJ,CAAC;;EAED;EACA,MAAMqC,WAAW,GAAGrD,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAMsD,oBAAoB,GAAI7B,CAAC,IAAK;IAChC,MAAM8B,QAAQ,GAAG9B,CAAC,CAAC+B,MAAM,CAACC,KAAK;;IAE/B;IACA,IAAIJ,WAAW,CAACrB,OAAO,EAAE;MACrB0B,YAAY,CAACL,WAAW,CAACrB,OAAO,CAAC;IACrC;;IAEA;IACAqB,WAAW,CAACrB,OAAO,GAAGS,UAAU,CAAC,MAAM;MACnC5B,gBAAgB,CAAC0C,QAAQ,CAAC;IAC9B,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC;EAGD,MAAMI,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAC/C,QAAQ,IAAI,CAACA,QAAQ,CAACgD,IAAI,CAAC,CAAC,EAAE;MAC/BC,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACJ;IAEA,IAAI;MACAtC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,MAAM,GAAG,MAAM5C,QAAQ,CAACb,eAAe,CAACO,QAAQ,CAAC,CAAC,CAACmD,MAAM,CAAC,CAAC;MACjExC,UAAU,CAAC,KAAK,CAAC;MACjB,IAAIuC,MAAM,CAACE,IAAI,CAACC,UAAU,EAAE;QACxBpD,gBAAgB,CAACiD,MAAM,CAACE,IAAI,CAACE,SAAS,CAAC;MAC3C,CAAC,MAAM;QACHL,KAAK,CAAC,gCAAgC,CAAC;MAC3C;IACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CN,KAAK,CAAC,sCAAsC,CAAC;IACjD;EACJ,CAAC;EAGD,oBACIrD,OAAA;IAAK6D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3B9D,OAAA;MAAK6D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAC9C9D,OAAA;QAAK6D,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrElE,OAAA;QACImE,OAAO,EAAEhB,WAAY;QACrBiB,QAAQ,EAAEtD,OAAO,IAAI,EAACV,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEgD,IAAI,CAAC,CAAC,CAAC;QACvCS,SAAS,EAAC,kKAAkK;QAC5KQ,KAAK,EAAC,yFAA6C;QAAAP,QAAA,gBAEnD9D,OAAA,CAACN,QAAQ;UAACmE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/BpD,OAAO,GAAG,aAAa,GAAG,QAAQ;MAAA;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAENlE,OAAA;MACI6D,SAAS,4DAAA/B,MAAA,CAA4DvB,cAAc,GAC7E,mDAAmD,GACnD,uCAAuC,CACtC;MACP+D,UAAU,EAAEjC,cAAe;MAC3BkC,WAAW,EAAEjC,eAAgB;MAC7BkC,WAAW,EAAE/B,eAAgB;MAC7BgC,MAAM,EAAEzD,UAAW;MAAA8C,QAAA,gBAGnB9D,OAAA;QACI0E,GAAG,EAAEjE,WAAY;QACjBwC,KAAK,EAAE7C,QAAQ,IAAI,EAAG;QACtBuE,QAAQ,EAAE7B,oBAAqB;QAC/B8B,WAAW,EAAC,yEAA6C;QACzDf,SAAS,EAAC,8HAA8H;QACxIgB,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAO;MAAE;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAGD,CAAC9D,QAAQ,IAAI,CAACG,cAAc,iBACzBP,OAAA;QAAK6D,SAAS,EAAC,6FAA6F;QAAAC,QAAA,gBACxG9D,OAAA,CAACP,SAAS;UAACoE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjClE,OAAA;UAAA8D,QAAA,EAAM;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGL9D,QAAQ,IAAIE,OAAO,iBAChBN,OAAA;MAAK6D,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC7D9D,OAAA;QAAK6D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DlE,OAAA,CAACF,uBAAuB;QAACiF,OAAO,EAAE3E;MAAS;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC/D,EAAA,CAjKIF,cAAc;EAAA,QAGCN,WAAW,EACFC,WAAW;AAAA;AAAAoF,EAAA,GAJnC/E,cAAc;AAmKpB,eAAeA,cAAc;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}