import { useParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useState } from "react";
import ClassOfUserTable from "../../../components/table/ClassOfUserTable";
import UserAdminLayout from "../../../layouts/UserAdminLayout";
import ClassSearchInput from "../../../components/ClassSearchInput";
import ConfirmModal from "../../../components/modal/ConfirmModal";
import LoadingSpinner from "../../../components/loading/LoadingSpinner";
import { UserPlus, X } from 'lucide-react';
import { addStudentToClass, kickStudentFromClass } from "../../../features/class/classSlice";
import { fetchClassesByUserId } from "../../../features/class/classSlice";

const UserClassManagement = () => {
    const { userId } = useParams();
    const dispatch = useDispatch();
    const { loading } = useSelector(state => state.states);
    const { student } = useSelector(state => state.users);

    // State for add student to class functionality
    const [showAddToClass, setShowAddToClass] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedClass, setSelectedClass] = useState(null);

    // State for kick confirmation modal
    const [showKickModal, setShowKickModal] = useState(false);
    const [classToKick, setClassToKick] = useState(null);

    // Handle class selection from search
    const handleSelectClass = (classItem) => {
        setSelectedClass(classItem);
        setSearchTerm(`${classItem.name} - Khối ${classItem.grade}`);
    };

    // Handle clear selection
    const handleClearSelection = () => {
        setSelectedClass(null);
        setSearchTerm('');
    };

    // Handle add student to class
    const handleAddStudentToClass = async () => {
        if (!selectedClass) return;

        try {
            await dispatch(addStudentToClass({
                classId: parseInt(selectedClass.id),
                studentId: parseInt(userId)
            })).unwrap();

            // Refresh the class list
            dispatch(fetchClassesByUserId(userId));

            // Reset form
            setSelectedClass(null);
            setSearchTerm('');
            setShowAddToClass(false);
        } catch (error) {
            console.error('Lỗi khi thêm học sinh vào lớp:', error);
        }
    };

    // Handle kick student from class - show modal
    const handleKickFromClass = (classId, className) => {
        setClassToKick({ id: classId, name: className });
        setShowKickModal(true);
    };

    // Confirm kick student from class
    const confirmKickFromClass = async () => {
        if (!classToKick) return;

        try {
            await dispatch(kickStudentFromClass({
                classId: parseInt(classToKick.id),
                studentId: parseInt(userId)
            })).unwrap();

            // Refresh the class list
            dispatch(fetchClassesByUserId(userId));

            // Close modal and reset state
            setShowKickModal(false);
            setClassToKick(null);
        } catch (error) {
            console.error('Lỗi khi kick học sinh khỏi lớp:', error);
        }
    };

    // Cancel kick student from class
    const cancelKickFromClass = () => {
        setShowKickModal(false);
        setClassToKick(null);
    };

    return (
        <UserAdminLayout>
            {/* Content */}
            <div className="flex-1 overflow-hidden p-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                    {/* Add to Class Section */}
                    <div className="flex-shrink-0 p-6 border-b border-gray-200">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold text-gray-900">Quản lý lớp học của học sinh</h3>
                            <button
                                onClick={() => setShowAddToClass(!showAddToClass)}
                                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <UserPlus size={16} />
                                Thêm vào lớp
                            </button>
                        </div>

                        {/* Add to Class Form */}
                        {showAddToClass && (
                            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div className="flex items-center gap-2 mb-3">
                                    <UserPlus size={16} className="text-sky-600" />
                                    <h3 className="font-medium text-gray-900">Thêm học sinh vào lớp học</h3>
                                    <button
                                        onClick={() => setShowAddToClass(false)}
                                        className="ml-auto text-gray-400 hover:text-gray-600"
                                    >
                                        <X size={16} />
                                    </button>
                                </div>

                                <div className="flex gap-3 items-end">
                                    <div className="flex-1">
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Tìm kiếm lớp học
                                        </label>
                                        <ClassSearchInput
                                            value={searchTerm}
                                            selectedClassId={selectedClass?.id}
                                            onChange={setSearchTerm}
                                            onSelect={handleSelectClass}
                                            onClear={handleClearSelection}
                                            placeholder="Nhập tên lớp học..."
                                            className="w-full"
                                        />
                                    </div>
                                    <button
                                        onClick={handleAddStudentToClass}
                                        disabled={!selectedClass}
                                        className={`px-4 py-2 rounded-lg font-medium transition-colors ${selectedClass
                                            ? 'bg-green-600 text-white hover:bg-green-700'
                                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            }`}
                                    >
                                        Thêm vào lớp
                                    </button>
                                </div>

                                {selectedClass && (
                                    <div className="mt-3 p-3 bg-white rounded border border-gray-200">
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">Lớp học được chọn:</span> {selectedClass.name}
                                            <span className="text-gray-400 ml-2">(Khối {selectedClass.grade} - Năm {selectedClass.academicYear})</span>
                                        </p>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Table Container - takes remaining space */}
                    <div className="flex-1 min-h-0 p-6">
                        <ClassOfUserTable
                            userId={userId}
                            onKickFromClass={handleKickFromClass}
                        />
                    </div>
                </div>
            </div>

            {/* Kick Confirmation Modal */}
            <ConfirmModal
                isOpen={showKickModal}
                onClose={cancelKickFromClass}
                onConfirm={confirmKickFromClass}
                title="Xác nhận rời khỏi lớp"
                message={`Bạn có chắc chắn muốn cho học sinh rời khỏi lớp "${classToKick?.name}" không? Hành động này không thể hoàn tác.`}
                confirmText="Rời khỏi lớp"
                cancelText="Hủy"
                type="danger"
            />
        </UserAdminLayout>
    )
}

export default UserClassManagement;
