/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  OCRPageObject,
  OCRPageObject$inboundSchema,
  OCRPageObject$Outbound,
  OCRPageObject$outboundSchema,
} from "./ocrpageobject.js";
import {
  OCRUsageInfo,
  OCRUsageInfo$inboundSchema,
  OCRUsageInfo$Outbound,
  OCRUsageInfo$outboundSchema,
} from "./ocrusageinfo.js";

export type OCRResponse = {
  /**
   * List of OCR info for pages.
   */
  pages: Array<OCRPageObject>;
  /**
   * The model used to generate the OCR.
   */
  model: string;
  /**
   * Formatted response in the request_format if provided in json str
   */
  documentAnnotation?: string | null | undefined;
  usageInfo: OCRUsageInfo;
};

/** @internal */
export const OCRResponse$inboundSchema: z.ZodType<
  OCRResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  pages: z.array(OCRPageObject$inboundSchema),
  model: z.string(),
  document_annotation: z.nullable(z.string()).optional(),
  usage_info: OCRUsageInfo$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "document_annotation": "documentAnnotation",
    "usage_info": "usageInfo",
  });
});

/** @internal */
export type OCRResponse$Outbound = {
  pages: Array<OCRPageObject$Outbound>;
  model: string;
  document_annotation?: string | null | undefined;
  usage_info: OCRUsageInfo$Outbound;
};

/** @internal */
export const OCRResponse$outboundSchema: z.ZodType<
  OCRResponse$Outbound,
  z.ZodTypeDef,
  OCRResponse
> = z.object({
  pages: z.array(OCRPageObject$outboundSchema),
  model: z.string(),
  documentAnnotation: z.nullable(z.string()).optional(),
  usageInfo: OCRUsageInfo$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    documentAnnotation: "document_annotation",
    usageInfo: "usage_info",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OCRResponse$ {
  /** @deprecated use `OCRResponse$inboundSchema` instead. */
  export const inboundSchema = OCRResponse$inboundSchema;
  /** @deprecated use `OCRResponse$outboundSchema` instead. */
  export const outboundSchema = OCRResponse$outboundSchema;
  /** @deprecated use `OCRResponse$Outbound` instead. */
  export type Outbound = OCRResponse$Outbound;
}

export function ocrResponseToJSON(ocrResponse: OCRResponse): string {
  return JSON.stringify(OCRResponse$outboundSchema.parse(ocrResponse));
}

export function ocrResponseFromJSON(
  jsonString: string,
): SafeParseResult<OCRResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OCRResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OCRResponse' from JSON`,
  );
}
