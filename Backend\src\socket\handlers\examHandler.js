import db from '../../models/index.js';
import { EVENTS } from '../constants.js';
import { submitExam } from '../../controllers/ExamController.js';
import { checkAndSubmitExamById } from '../../utils/examAutoSubmit.js';

/**
 * Handle join_exam event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleJoinExam = async (socket, io, { studentId, examId }) => {
  try {
    const exam = await db.Exam.findByPk(examId, {
      include: [{ model: db.Question, as: "questions" }]
    });

    if (!exam) {
      return socket.emit(EVENTS.EXAM_ERROR, { message: "Đề thi không tồn tại." });
    }

    // 🔍 Lấy tất cả các lần làm bài
    const allAttempts = await db.StudentExamAttempt.findAll({
      where: { studentId, examId },
    });

    const unfinishedAttempt = allAttempts.find(attempt => attempt.endTime === null);

    // 🧠 Nếu đã làm >= giới hạn và tất cả đều hoàn thành => chặn
    const hasReachedLimit = exam.attemptLimit !== null &&
      allAttempts.length >= exam.attemptLimit &&
      !unfinishedAttempt;
    
    console.log("Đã làm bài:", allAttempts.length, "Giới hạn:", exam.attemptLimit);
    
    if (hasReachedLimit) {
      return socket.emit(EVENTS.EXAM_ERROR, {
        message: "Bạn đã đạt giới hạn số lần làm bài cho phép.",
      });
    }

    // Nếu có bài chưa hoàn thành thì tiếp tục bài cũ
    let currentAttempt = unfinishedAttempt;

    if (!currentAttempt) {
      // 👉 Nếu không có bài nào đang làm, tạo attempt mới
      currentAttempt = await db.StudentExamAttempt.create({
        studentId,
        examId,
        startTime: new Date(),
        endTime: null,
        score: null
      });

      // Khởi tạo đáp án
      const answers = exam.questions.map(q => ({
        attemptId: currentAttempt.id,
        questionId: q.id,
        answerContent: "",
        result: null,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      await db.Answer.bulkCreate(answers);
    }

    // Trả về attempt đang dùng
    socket.emit(EVENTS.EXAM_STARTED, {
      attemptId: currentAttempt.id,
      startTime: currentAttempt.startTime
    });

  } catch (err) {
    console.log("Lỗi khi tham gia bài thi:", err);
    console.error("join_exam error:", err);
    socket.emit(EVENTS.EXAM_ERROR, { message: "Lỗi khi bắt đầu bài thi." });
  }
};

/**
 * Handle submit_exam event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleSubmitExam = async (socket, io, { attemptId }) => {
  await submitExam(socket, attemptId);
};

/**
 * Handle request_time event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleRequestTime = async (socket, io, { examId, attemptId }) => {
  if (attemptId) {
    // Check if this attempt has timed out
    const autoSubmitted = await checkAndSubmitExamById(attemptId);

    if (autoSubmitted) {
      // If auto-submitted, notify the client
      socket.emit(EVENTS.EXAM_AUTO_SUBMITTED, {
        message: "Bài thi đã tự động nộp do hết thời gian làm bài",
        timestamp: new Date(),
        attemptId,
        score: autoSubmitted.score
      });

      // Return early, don't send remaining time
      return;
    }

    // If not auto-submitted, calculate and send remaining time
    const attempt = await db.StudentExamAttempt.findByPk(attemptId, {
      include: [{ model: db.Exam, as: 'exam' }]
    });

    if (attempt && attempt.exam && attempt.exam.testDuration) {
      const startTime = new Date(attempt.startTime);
      const durationMs = attempt.exam.testDuration * 60 * 1000;
      const endTime = new Date(startTime.getTime() + durationMs);
      const remainingMs = Math.max(0, endTime.getTime() - Date.now());
      const remainingTime = Math.ceil(remainingMs / 1000); // Convert to seconds

      socket.emit(EVENTS.EXAM_TIMER, { remainingTime });
    } else {
      // Default fallback if no duration set
      const remainingTime = 10 * 60; // 10 minutes
      socket.emit(EVENTS.EXAM_TIMER, { remainingTime });
    }
  } else {
    // Default fallback if no attemptId provided
    const remainingTime = 10 * 60; // 10 minutes
    socket.emit(EVENTS.EXAM_TIMER, { remainingTime });
  }
};

/**
 * Handle send_notification event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleSendNotification = (socket, io, { examId, message }) => {
  io.to(`exam-${examId}`).emit(EVENTS.EXAM_NOTIFICATION, { message });
};

/**
 * Handle admin_join_exam_tracking event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleAdminJoinExamTracking = (socket, io, { examId }) => {
  socket.join(`exam-admin-${examId}`);
  console.log(`📊 Admin joined tracking room for exam ${examId}`);
};
