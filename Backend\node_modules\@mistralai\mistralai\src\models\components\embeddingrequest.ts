/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  EmbeddingDtype,
  EmbeddingDtype$inboundSchema,
  EmbeddingDtype$outboundSchema,
} from "./embeddingdtype.js";

/**
 * Text to embed.
 */
export type EmbeddingRequestInputs = string | Array<string>;

export type EmbeddingRequest = {
  /**
   * ID of the model to use.
   */
  model: string;
  /**
   * Text to embed.
   */
  inputs: string | Array<string>;
  /**
   * The dimension of the output embeddings.
   */
  outputDimension?: number | null | undefined;
  outputDtype?: EmbeddingDtype | undefined;
};

/** @internal */
export const EmbeddingRequestInputs$inboundSchema: z.ZodType<
  EmbeddingRequestInputs,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(z.string())]);

/** @internal */
export type EmbeddingRequestInputs$Outbound = string | Array<string>;

/** @internal */
export const EmbeddingRequestInputs$outboundSchema: z.ZodType<
  EmbeddingRequestInputs$Outbound,
  z.ZodTypeDef,
  EmbeddingRequestInputs
> = z.union([z.string(), z.array(z.string())]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace EmbeddingRequestInputs$ {
  /** @deprecated use `EmbeddingRequestInputs$inboundSchema` instead. */
  export const inboundSchema = EmbeddingRequestInputs$inboundSchema;
  /** @deprecated use `EmbeddingRequestInputs$outboundSchema` instead. */
  export const outboundSchema = EmbeddingRequestInputs$outboundSchema;
  /** @deprecated use `EmbeddingRequestInputs$Outbound` instead. */
  export type Outbound = EmbeddingRequestInputs$Outbound;
}

export function embeddingRequestInputsToJSON(
  embeddingRequestInputs: EmbeddingRequestInputs,
): string {
  return JSON.stringify(
    EmbeddingRequestInputs$outboundSchema.parse(embeddingRequestInputs),
  );
}

export function embeddingRequestInputsFromJSON(
  jsonString: string,
): SafeParseResult<EmbeddingRequestInputs, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => EmbeddingRequestInputs$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'EmbeddingRequestInputs' from JSON`,
  );
}

/** @internal */
export const EmbeddingRequest$inboundSchema: z.ZodType<
  EmbeddingRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  model: z.string(),
  input: z.union([z.string(), z.array(z.string())]),
  output_dimension: z.nullable(z.number().int()).optional(),
  output_dtype: EmbeddingDtype$inboundSchema.optional(),
}).transform((v) => {
  return remap$(v, {
    "input": "inputs",
    "output_dimension": "outputDimension",
    "output_dtype": "outputDtype",
  });
});

/** @internal */
export type EmbeddingRequest$Outbound = {
  model: string;
  input: string | Array<string>;
  output_dimension?: number | null | undefined;
  output_dtype?: string | undefined;
};

/** @internal */
export const EmbeddingRequest$outboundSchema: z.ZodType<
  EmbeddingRequest$Outbound,
  z.ZodTypeDef,
  EmbeddingRequest
> = z.object({
  model: z.string(),
  inputs: z.union([z.string(), z.array(z.string())]),
  outputDimension: z.nullable(z.number().int()).optional(),
  outputDtype: EmbeddingDtype$outboundSchema.optional(),
}).transform((v) => {
  return remap$(v, {
    inputs: "input",
    outputDimension: "output_dimension",
    outputDtype: "output_dtype",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace EmbeddingRequest$ {
  /** @deprecated use `EmbeddingRequest$inboundSchema` instead. */
  export const inboundSchema = EmbeddingRequest$inboundSchema;
  /** @deprecated use `EmbeddingRequest$outboundSchema` instead. */
  export const outboundSchema = EmbeddingRequest$outboundSchema;
  /** @deprecated use `EmbeddingRequest$Outbound` instead. */
  export type Outbound = EmbeddingRequest$Outbound;
}

export function embeddingRequestToJSON(
  embeddingRequest: EmbeddingRequest,
): string {
  return JSON.stringify(
    EmbeddingRequest$outboundSchema.parse(embeddingRequest),
  );
}

export function embeddingRequestFromJSON(
  jsonString: string,
): SafeParseResult<EmbeddingRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => EmbeddingRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'EmbeddingRequest' from JSON`,
  );
}
