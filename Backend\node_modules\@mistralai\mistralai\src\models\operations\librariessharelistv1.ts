/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesShareListV1Request = {
  libraryId: string;
};

/** @internal */
export const LibrariesShareListV1Request$inboundSchema: z.ZodType<
  LibrariesShareListV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
  });
});

/** @internal */
export type LibrariesShareListV1Request$Outbound = {
  library_id: string;
};

/** @internal */
export const LibrariesShareListV1Request$outboundSchema: z.ZodType<
  LibrariesShareListV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesShareListV1Request
> = z.object({
  libraryId: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesShareListV1Request$ {
  /** @deprecated use `LibrariesShareListV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesShareListV1Request$inboundSchema;
  /** @deprecated use `LibrariesShareListV1Request$outboundSchema` instead. */
  export const outboundSchema = LibrariesShareListV1Request$outboundSchema;
  /** @deprecated use `LibrariesShareListV1Request$Outbound` instead. */
  export type Outbound = LibrariesShareListV1Request$Outbound;
}

export function librariesShareListV1RequestToJSON(
  librariesShareListV1Request: LibrariesShareListV1Request,
): string {
  return JSON.stringify(
    LibrariesShareListV1Request$outboundSchema.parse(
      librariesShareListV1Request,
    ),
  );
}

export function librariesShareListV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesShareListV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesShareListV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesShareListV1Request' from JSON`,
  );
}
