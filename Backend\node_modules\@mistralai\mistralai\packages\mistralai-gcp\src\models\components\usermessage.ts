/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ContentChunk,
  ContentChunk$inboundSchema,
  ContentChunk$Outbound,
  ContentChunk$outboundSchema,
} from "./contentchunk.js";

export type UserMessageContent = string | Array<ContentChunk>;

export const UserMessageRole = {
  User: "user",
} as const;
export type UserMessageRole = ClosedEnum<typeof UserMessageRole>;

export type UserMessage = {
  content: string | Array<ContentChunk> | null;
  role?: UserMessageRole | undefined;
};

/** @internal */
export const UserMessageContent$inboundSchema: z.ZodType<
  UserMessageContent,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(ContentChunk$inboundSchema)]);

/** @internal */
export type UserMessageContent$Outbound = string | Array<ContentChunk$Outbound>;

/** @internal */
export const UserMessageContent$outboundSchema: z.ZodType<
  UserMessageContent$Outbound,
  z.ZodTypeDef,
  UserMessageContent
> = z.union([z.string(), z.array(ContentChunk$outboundSchema)]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UserMessageContent$ {
  /** @deprecated use `UserMessageContent$inboundSchema` instead. */
  export const inboundSchema = UserMessageContent$inboundSchema;
  /** @deprecated use `UserMessageContent$outboundSchema` instead. */
  export const outboundSchema = UserMessageContent$outboundSchema;
  /** @deprecated use `UserMessageContent$Outbound` instead. */
  export type Outbound = UserMessageContent$Outbound;
}

export function userMessageContentToJSON(
  userMessageContent: UserMessageContent,
): string {
  return JSON.stringify(
    UserMessageContent$outboundSchema.parse(userMessageContent),
  );
}

export function userMessageContentFromJSON(
  jsonString: string,
): SafeParseResult<UserMessageContent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UserMessageContent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UserMessageContent' from JSON`,
  );
}

/** @internal */
export const UserMessageRole$inboundSchema: z.ZodNativeEnum<
  typeof UserMessageRole
> = z.nativeEnum(UserMessageRole);

/** @internal */
export const UserMessageRole$outboundSchema: z.ZodNativeEnum<
  typeof UserMessageRole
> = UserMessageRole$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UserMessageRole$ {
  /** @deprecated use `UserMessageRole$inboundSchema` instead. */
  export const inboundSchema = UserMessageRole$inboundSchema;
  /** @deprecated use `UserMessageRole$outboundSchema` instead. */
  export const outboundSchema = UserMessageRole$outboundSchema;
}

/** @internal */
export const UserMessage$inboundSchema: z.ZodType<
  UserMessage,
  z.ZodTypeDef,
  unknown
> = z.object({
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$inboundSchema)]),
  ),
  role: UserMessageRole$inboundSchema.default("user"),
});

/** @internal */
export type UserMessage$Outbound = {
  content: string | Array<ContentChunk$Outbound> | null;
  role: string;
};

/** @internal */
export const UserMessage$outboundSchema: z.ZodType<
  UserMessage$Outbound,
  z.ZodTypeDef,
  UserMessage
> = z.object({
  content: z.nullable(
    z.union([z.string(), z.array(ContentChunk$outboundSchema)]),
  ),
  role: UserMessageRole$outboundSchema.default("user"),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UserMessage$ {
  /** @deprecated use `UserMessage$inboundSchema` instead. */
  export const inboundSchema = UserMessage$inboundSchema;
  /** @deprecated use `UserMessage$outboundSchema` instead. */
  export const outboundSchema = UserMessage$outboundSchema;
  /** @deprecated use `UserMessage$Outbound` instead. */
  export type Outbound = UserMessage$Outbound;
}

export function userMessageToJSON(userMessage: UserMessage): string {
  return JSON.stringify(UserMessage$outboundSchema.parse(userMessage));
}

export function userMessageFromJSON(
  jsonString: string,
): SafeParseResult<UserMessage, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UserMessage$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UserMessage' from JSON`,
  );
}
