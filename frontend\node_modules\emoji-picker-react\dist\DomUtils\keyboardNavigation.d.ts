import { NullableElement } from './selectors';
export declare function focusFirstVisibleEmoji(parent: NullableElement): void;
export declare function focusAndClickFirstVisibleEmoji(parent: NullableElement): void;
export declare function focusLastVisibleEmoji(parent: NullableElement): void;
export declare function focusNextVisibleEmoji(element: NullableElement): void;
export declare function focusPrevVisibleEmoji(element: NullableElement): void;
export declare function focusVisibleEmojiOneRowUp(element: NullableElement, exitUp: () => void): void;
export declare function focusVisibleEmojiOneRowDown(element: NullableElement): void;
