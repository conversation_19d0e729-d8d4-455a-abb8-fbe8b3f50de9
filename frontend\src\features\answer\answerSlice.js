import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import { getAnswersByAttemptAPI, getQuestionAndAnswersByAttemptAPI } from "../../services/answerApi";
import { setQuestions } from "../question/questionSlice";
import { setExam } from "../exam/examSlice";

export const fetchAnswersByAttempt = createAsyncThunk(
    "answers/fetchAnswersByAttempt",
    async (attemptId, { dispatch }) => {
        return await apiHandler(dispatch, getAnswersByAttemptAPI, { attemptId }, () => { }, false, false);
    }
);



const answerSlice = createSlice({
    name: "answers",
    initialState: {
        answers: [],
        score: null,
        duration: null,
        durationInSeconds: null,
    },
    reducers: {
        setAnswers: (state, action) => {
            const index = state.answers.findIndex(a => a.questionId === action.payload.questionId);
            if (index !== -1) {
                state.answers[index] = action.payload;
            } else {
                state.answers.push(action.payload);
            }
        },
        resetAnswers: (state) => {
            state.answers = [];
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAnswersByAttempt.pending, (state) => {
                state.answers = [];
            })
            .addCase(fetchAnswersByAttempt.fulfilled, (state, action) => {
                if (action.payload) {
                    state.answers = action.payload.data;
                }
            })
    },
});

export const { setAnswers, resetAnswers } = answerSlice.actions;
export default answerSlice.reducer;
