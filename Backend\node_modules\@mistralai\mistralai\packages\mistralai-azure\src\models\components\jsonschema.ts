/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JsonSchema = {
  name: string;
  description?: string | null | undefined;
  schemaDefinition: { [k: string]: any };
  strict?: boolean | undefined;
};

/** @internal */
export const JsonSchema$inboundSchema: z.ZodType<
  JsonSchema,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.string(),
  description: z.nullable(z.string()).optional(),
  schema: z.record(z.any()),
  strict: z.boolean().optional(),
}).transform((v) => {
  return remap$(v, {
    "schema": "schemaDefinition",
  });
});

/** @internal */
export type JsonSchema$Outbound = {
  name: string;
  description?: string | null | undefined;
  schema: { [k: string]: any };
  strict?: boolean | undefined;
};

/** @internal */
export const JsonSchema$outboundSchema: z.ZodType<
  JsonSchema$Outbound,
  z.ZodTypeDef,
  JsonSchema
> = z.object({
  name: z.string(),
  description: z.nullable(z.string()).optional(),
  schemaDefinition: z.record(z.any()),
  strict: z.boolean().optional(),
}).transform((v) => {
  return remap$(v, {
    schemaDefinition: "schema",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JsonSchema$ {
  /** @deprecated use `JsonSchema$inboundSchema` instead. */
  export const inboundSchema = JsonSchema$inboundSchema;
  /** @deprecated use `JsonSchema$outboundSchema` instead. */
  export const outboundSchema = JsonSchema$outboundSchema;
  /** @deprecated use `JsonSchema$Outbound` instead. */
  export type Outbound = JsonSchema$Outbound;
}

export function jsonSchemaToJSON(jsonSchema: JsonSchema): string {
  return JSON.stringify(JsonSchema$outboundSchema.parse(jsonSchema));
}

export function jsonSchemaFromJSON(
  jsonString: string,
): SafeParseResult<JsonSchema, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => JsonSchema$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'JsonSchema' from JSON`,
  );
}
