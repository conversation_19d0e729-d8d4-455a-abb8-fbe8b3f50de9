import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesShareListV1Request = {
    libraryId: string;
};
/** @internal */
export declare const LibrariesShareListV1Request$inboundSchema: z.ZodType<LibrariesShareListV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesShareListV1Request$Outbound = {
    library_id: string;
};
/** @internal */
export declare const LibrariesShareListV1Request$outboundSchema: z.ZodType<LibrariesShareListV1Request$Outbound, z.ZodTypeDef, LibrariesShareListV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesShareListV1Request$ {
    /** @deprecated use `LibrariesShareListV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesShareListV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesShareListV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesShareListV1Request$Outbound, z.ZodTypeDef, LibrariesShareListV1Request>;
    /** @deprecated use `LibrariesShareListV1Request$Outbound` instead. */
    type Outbound = LibrariesShareListV1Request$Outbound;
}
export declare function librariesShareListV1RequestToJSON(librariesShareListV1Request: LibrariesShareListV1Request): string;
export declare function librariesShareListV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesShareListV1Request, SDKValidationError>;
//# sourceMappingURL=librariessharelistv1.d.ts.map