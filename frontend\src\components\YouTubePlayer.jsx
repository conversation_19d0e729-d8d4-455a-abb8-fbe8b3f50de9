const YouTubePlayer = ({ url, sizeClass = "w-full aspect-video" }) => {
    const getVideoId = (youtubeUrl) => {
        try {
            const urlObj = new URL(youtubeUrl);

            // 1. <PERSON><PERSON><PERSON> từ param ?v=
            const vParam = urlObj.searchParams.get("v");
            if (vParam) return vParam;

            // 2. Nếu là youtu.be/VIDEO_ID
            if (urlObj.hostname === "youtu.be") {
                return urlObj.pathname.replace("/", "").split("?")[0];
            }

            // 3. Nếu là /embed/VIDEO_ID hoặc /shorts/VIDEO_ID hoặc /live/VIDEO_ID
            const match = urlObj.pathname.match(/\/(embed|shorts|live)\/([^/?]+)/);
            if (match && match[2]) return match[2];

            // 4. Fallback regex cho mọi trường hợp khác
            const fallback = youtubeUrl.match(
                /(?:youtube\.com\/(?:.*v=|v\/|embed\/|shorts\/|live\/)|youtu\.be\/)([^&?/]+)/i
            );
            if (fallback && fallback[1]) return fallback[1];

            return null;
        } catch (error) {
            return null;
        }
    };

    const videoId = getVideoId(url);
    if (!videoId) {
        return <div className="text-red-500">URL video không hợp lệ</div>;
    }

    const embedUrl = `https://www.youtube.com/embed/${videoId}`;

    return (
        <div className={sizeClass}>
            <iframe
                className="w-full h-full rounded-lg"
                src={embedUrl}
                title="YouTube video player"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                frameBorder="0"
            ></iframe>
        </div>
    );
};

export default YouTubePlayer;
