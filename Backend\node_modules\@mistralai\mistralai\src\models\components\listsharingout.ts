/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  SharingOut,
  SharingOut$inboundSchema,
  SharingOut$Outbound,
  SharingOut$outboundSchema,
} from "./sharingout.js";

export type ListSharingOut = {
  data: Array<SharingOut>;
};

/** @internal */
export const ListSharingOut$inboundSchema: z.ZodType<
  ListSharingOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  data: z.array(SharingOut$inboundSchema),
});

/** @internal */
export type ListSharingOut$Outbound = {
  data: Array<SharingOut$Outbound>;
};

/** @internal */
export const ListSharingOut$outboundSchema: z.ZodType<
  ListSharingOut$Outbound,
  z.ZodTypeDef,
  ListSharingOut
> = z.object({
  data: z.array(SharingOut$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListSharingOut$ {
  /** @deprecated use `ListSharingOut$inboundSchema` instead. */
  export const inboundSchema = ListSharingOut$inboundSchema;
  /** @deprecated use `ListSharingOut$outboundSchema` instead. */
  export const outboundSchema = ListSharingOut$outboundSchema;
  /** @deprecated use `ListSharingOut$Outbound` instead. */
  export type Outbound = ListSharingOut$Outbound;
}

export function listSharingOutToJSON(listSharingOut: ListSharingOut): string {
  return JSON.stringify(ListSharingOut$outboundSchema.parse(listSharingOut));
}

export function listSharingOutFromJSON(
  jsonString: string,
): SafeParseResult<ListSharingOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListSharingOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListSharingOut' from JSON`,
  );
}
