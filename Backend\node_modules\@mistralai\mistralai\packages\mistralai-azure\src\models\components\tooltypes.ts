/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import {
  catchUnrecognizedEnum,
  OpenEnum,
  Unrecognized,
} from "../../types/enums.js";

export const ToolTypes = {
  Function: "function",
} as const;
export type ToolTypes = OpenEnum<typeof ToolTypes>;

/** @internal */
export const ToolTypes$inboundSchema: z.ZodType<
  ToolTypes,
  z.ZodTypeDef,
  unknown
> = z
  .union([
    z.nativeEnum(ToolTypes),
    z.string().transform(catchUnrecognizedEnum),
  ]);

/** @internal */
export const ToolTypes$outboundSchema: z.ZodType<
  ToolTypes,
  z.ZodTypeDef,
  ToolTypes
> = z.union([
  z.nativeEnum(ToolTypes),
  z.string().and(z.custom<Unrecognized<string>>()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolTypes$ {
  /** @deprecated use `ToolTypes$inboundSchema` instead. */
  export const inboundSchema = ToolTypes$inboundSchema;
  /** @deprecated use `ToolTypes$outboundSchema` instead. */
  export const outboundSchema = ToolTypes$outboundSchema;
}
