import express from 'express';
import asyncHandler from '../middlewares/asyncHandler.js';
import validate from '../middlewares/validate.js';
import { requireRoles } from '../middlewares/jwtMiddleware.js';
import Roles from '../constants/Roles.js';
import * as AttendanceController from '../controllers/AttendanceController.js';

const router = express.Router();

// 📌 [GET] Lấy danh sách điểm danh (có tìm kiếm + phân trang)
router.get('/v1/attendance',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.getAllAttendances)
);

// 📌 [POST] Tạo bản ghi điểm danh cho 1 học sinh
router.post('/v1/attendance',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.createAttendance)
);

// 📌 [POST] Tạo toàn bộ điểm danh cho cả lớp
router.post('/v1/attendance/bulk',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.createAllAttendanceInClass)
);

// 📌 [PUT] Cập nhật trạng thái điểm danh
router.put('/v1/attendance/:id',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.updateAttendance)
);

// 📌 [DELETE] Xóa bản ghi điểm danh
router.delete('/v1/attendance/:id',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.deleteAttendance)
);

// 📌 [GET] Lấy tất cả điểm danh của một user (chia theo tháng)
router.get('/v1/admin/:userId/attendance',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.getUserAttendancesByAdmin)
);

router.get('/v1/user/attendance',
    requireRoles(Roles.JustStudent),
    asyncHandler(AttendanceController.getUserAttendances)
);

// 📌 [GET] Thống kê điểm danh cho một buổi học cụ thể
router.get('/v1/attendance/statistics/lesson/:lessonId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.getLessonAttendanceStatistics)
);

// 📌 [GET] Thống kê điểm danh cho một lớp học
router.get('/v1/attendance/statistics/class/:classId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.getClassAttendanceStatistics)
);

// 📌 [GET] Thống kê điểm danh tổng quan
router.get('/v1/attendance/statistics/overall',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.getOverallAttendanceStatistics)
);

// 📌 [PUT] Cập nhật hàng loạt trạng thái điểm danh cho nhiều học sinh
router.put('/v1/attendance/bulk-update/:lessonId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.bulkUpdateAttendanceStatus)
);

// 📌 [PUT] Đánh dấu tất cả học sinh trong buổi học với cùng một trạng thái
router.put('/v1/attendance/mark-all/:lessonId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttendanceController.markAllAttendanceStatus)
);

export default router;
