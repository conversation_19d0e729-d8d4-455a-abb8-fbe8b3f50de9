import { BotMessageSquare } from "lucide-react";
const FloatingAiButton = ({ aiResponse, setIsAIModalOpen }) => {
    return (
        <>
            <button
                onClick={() => setIsAIModalOpen(true)}
                className="fixed bottom-6 right-6 md:hidden w-12 h-12 bg-gradient-to-r from-sky-500 to-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-40 hover:scale-105 active:scale-95"
            >
                <BotMessageSquare size={20} className="drop-shadow-sm" />
                {/* Pulse effect */}
                {aiResponse && <div className="absolute inset-0 rounded-full bg-sky-400 animate-ping opacity-20"></div>}
            </button>
        </>
    );
};

export default FloatingAiButton;
