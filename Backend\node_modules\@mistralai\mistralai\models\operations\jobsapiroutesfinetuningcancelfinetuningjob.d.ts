import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type JobsApiRoutesFineTuningCancelFineTuningJobRequest = {
    /**
     * The ID of the job to cancel.
     */
    jobId: string;
};
/**
 * OK
 */
export type JobsApiRoutesFineTuningCancelFineTuningJobResponse = (components.ClassifierDetailedJobOut & {
    jobType: "classifier";
}) | (components.CompletionDetailedJobOut & {
    jobType: "completion";
});
/** @internal */
export declare const JobsApiRoutesFineTuningCancelFineTuningJobRequest$inboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound = {
    job_id: string;
};
/** @internal */
export declare const JobsApiRoutesFineTuningCancelFineTuningJobRequest$outboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningCancelFineTuningJobRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace JobsApiRoutesFineTuningCancelFineTuningJobRequest$ {
    /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningCancelFineTuningJobRequest>;
    /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound` instead. */
    type Outbound = JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound;
}
export declare function jobsApiRoutesFineTuningCancelFineTuningJobRequestToJSON(jobsApiRoutesFineTuningCancelFineTuningJobRequest: JobsApiRoutesFineTuningCancelFineTuningJobRequest): string;
export declare function jobsApiRoutesFineTuningCancelFineTuningJobRequestFromJSON(jsonString: string): SafeParseResult<JobsApiRoutesFineTuningCancelFineTuningJobRequest, SDKValidationError>;
/** @internal */
export declare const JobsApiRoutesFineTuningCancelFineTuningJobResponse$inboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobResponse, z.ZodTypeDef, unknown>;
/** @internal */
export type JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound = (components.ClassifierDetailedJobOut$Outbound & {
    job_type: "classifier";
}) | (components.CompletionDetailedJobOut$Outbound & {
    job_type: "completion";
});
/** @internal */
export declare const JobsApiRoutesFineTuningCancelFineTuningJobResponse$outboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningCancelFineTuningJobResponse>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace JobsApiRoutesFineTuningCancelFineTuningJobResponse$ {
    /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobResponse$inboundSchema` instead. */
    const inboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobResponse, z.ZodTypeDef, unknown>;
    /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobResponse$outboundSchema` instead. */
    const outboundSchema: z.ZodType<JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound, z.ZodTypeDef, JobsApiRoutesFineTuningCancelFineTuningJobResponse>;
    /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound` instead. */
    type Outbound = JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound;
}
export declare function jobsApiRoutesFineTuningCancelFineTuningJobResponseToJSON(jobsApiRoutesFineTuningCancelFineTuningJobResponse: JobsApiRoutesFineTuningCancelFineTuningJobResponse): string;
export declare function jobsApiRoutesFineTuningCancelFineTuningJobResponseFromJSON(jsonString: string): SafeParseResult<JobsApiRoutesFineTuningCancelFineTuningJobResponse, SDKValidationError>;
//# sourceMappingURL=jobsapiroutesfinetuningcancelfinetuningjob.d.ts.map