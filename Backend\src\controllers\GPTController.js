import { callGPT, askQuestionWithAI, classifyQuestions, fixTextAndLatex } from '../services/gpt.service.js';

const messages = [
    { role: 'system', content: 'Bạn là trợ lý AI hữu ích.' },
    { role: 'user', content: '<PERSON><PERSON>y giải thích GPT hoạt động thế nào?' }
];

export const callGPTController = async (req, res) => {
    const response = await callGPT(messages);
    res.status(200).json({
        message: 'GPT response',
        data: response
    });
}

// Controller cho tính năng hỏi đáp AI với câu hỏi từ database
export const askQuestionController = async (req, res) => {
    try {
        const { questionId } = req.params;
        const { messageId } = req.body;

        // Validate questionId
        if (!questionId || isNaN(questionId)) {
            return res.status(400).json({
                message: 'ID câu hỏi không hợp lệ',
                error: 'questionId phải là một số'
            });
        }

        // Validate messageId
        if (!messageId || isNaN(messageId)) {
            return res.status(400).json({
                message: 'ID tin nhắn không hợp lệ',
                error: 'messageId phải là một số'
            });
        }

        // Gọi service để xử lý
        const result = await askQuestionWithAI(parseInt(questionId), parseInt(messageId));

        return res.status(200).json({
            message: 'Đã xử lý câu hỏi thành công',
            data: result
        });

    } catch (error) {
        console.error('Error in askQuestionController:', error);

        if (error.message === 'Không tìm thấy câu hỏi') {
            return res.status(404).json({
                message: 'Không tìm thấy câu hỏi',
                error: error.message
            });
        }

        return res.status(500).json({
            message: 'Lỗi server khi xử lý câu hỏi',
            error: error.message
        });
    }
}

// Controller cho tính năng phân loại câu hỏi bằng GPT
export const classifyQuestionsController = async (req, res) => {
    try {
        const { questions } = req.body;

        // Validate input
        if (!questions || !Array.isArray(questions)) {
            return res.status(400).json({
                message: 'Dữ liệu không hợp lệ',
                error: 'questions phải là một mảng'
            });
        }

        if (questions.length === 0) {
            return res.status(400).json({
                message: 'Dữ liệu không hợp lệ',
                error: 'Mảng questions không được rỗng'
            });
        }

        // Validate từng câu hỏi
        for (let i = 0; i < questions.length; i++) {
            const question = questions[i];
            if (!question.content || typeof question.content !== 'string') {
                return res.status(400).json({
                    message: 'Dữ liệu không hợp lệ',
                    error: `Câu hỏi thứ ${i + 1} phải có thuộc tính content là string`
                });
            }
        }

        // Giới hạn số lượng câu hỏi để tránh quá tải
        if (questions.length > 50) {
            return res.status(400).json({
                message: 'Quá nhiều câu hỏi',
                error: 'Tối đa 50 câu hỏi mỗi lần phân loại'
            });
        }

        // Gọi service để phân loại
        const result = await classifyQuestions(questions);

        return res.status(200).json({
            message: 'Phân loại câu hỏi thành công',
            data: result
        });

    } catch (error) {
        console.error('Error in classifyQuestionsController:', error);

        if (error.message === 'Không thể parse kết quả phân loại từ GPT') {
            return res.status(502).json({
                message: 'Lỗi xử lý phản hồi từ AI',
                error: error.message
            });
        }

        return res.status(500).json({
            message: 'Lỗi server khi phân loại câu hỏi',
            error: error.message
        });
    }
}

// Controller cho tính năng sửa chính tả và LaTeX bằng GPT
export const fixTextAndLatexController = async (req, res) => {
    try {
        const { text } = req.body;

        // Validate input
        if (!text || typeof text !== 'string') {
            return res.status(400).json({
                message: 'Dữ liệu không hợp lệ',
                error: 'text phải là một chuỗi không rỗng'
            });
        }

        // Giới hạn độ dài text để tránh quá tải
        if (text.length > 10000) {
            return res.status(400).json({
                message: 'Văn bản quá dài',
                error: 'Tối đa 10,000 ký tự'
            });
        }

        // Gọi service để sửa text
        const result = await fixTextAndLatex(text);

        return res.status(200).json({
            message: 'Sửa văn bản thành công',
            data: result
        });

    } catch (error) {
        console.error('Error in fixTextAndLatexController:', error);

        return res.status(500).json({
            message: 'Lỗi server khi sửa văn bản',
            error: error.message
        });
    }
}



