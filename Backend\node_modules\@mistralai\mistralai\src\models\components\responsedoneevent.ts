/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ConversationUsageInfo,
  ConversationUsageInfo$inboundSchema,
  ConversationUsageInfo$Outbound,
  ConversationUsageInfo$outboundSchema,
} from "./conversationusageinfo.js";

export const ResponseDoneEventType = {
  ConversationResponseDone: "conversation.response.done",
} as const;
export type ResponseDoneEventType = ClosedEnum<typeof ResponseDoneEventType>;

export type ResponseDoneEvent = {
  type?: ResponseDoneEventType | undefined;
  createdAt?: Date | undefined;
  usage: ConversationUsageInfo;
};

/** @internal */
export const ResponseDoneEventType$inboundSchema: z.ZodNativeEnum<
  typeof ResponseDoneEventType
> = z.nativeEnum(ResponseDoneEventType);

/** @internal */
export const ResponseDoneEventType$outboundSchema: z.ZodNativeEnum<
  typeof ResponseDoneEventType
> = ResponseDoneEventType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseDoneEventType$ {
  /** @deprecated use `ResponseDoneEventType$inboundSchema` instead. */
  export const inboundSchema = ResponseDoneEventType$inboundSchema;
  /** @deprecated use `ResponseDoneEventType$outboundSchema` instead. */
  export const outboundSchema = ResponseDoneEventType$outboundSchema;
}

/** @internal */
export const ResponseDoneEvent$inboundSchema: z.ZodType<
  ResponseDoneEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ResponseDoneEventType$inboundSchema.default(
    "conversation.response.done",
  ),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  usage: ConversationUsageInfo$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
  });
});

/** @internal */
export type ResponseDoneEvent$Outbound = {
  type: string;
  created_at?: string | undefined;
  usage: ConversationUsageInfo$Outbound;
};

/** @internal */
export const ResponseDoneEvent$outboundSchema: z.ZodType<
  ResponseDoneEvent$Outbound,
  z.ZodTypeDef,
  ResponseDoneEvent
> = z.object({
  type: ResponseDoneEventType$outboundSchema.default(
    "conversation.response.done",
  ),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  usage: ConversationUsageInfo$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseDoneEvent$ {
  /** @deprecated use `ResponseDoneEvent$inboundSchema` instead. */
  export const inboundSchema = ResponseDoneEvent$inboundSchema;
  /** @deprecated use `ResponseDoneEvent$outboundSchema` instead. */
  export const outboundSchema = ResponseDoneEvent$outboundSchema;
  /** @deprecated use `ResponseDoneEvent$Outbound` instead. */
  export type Outbound = ResponseDoneEvent$Outbound;
}

export function responseDoneEventToJSON(
  responseDoneEvent: ResponseDoneEvent,
): string {
  return JSON.stringify(
    ResponseDoneEvent$outboundSchema.parse(responseDoneEvent),
  );
}

export function responseDoneEventFromJSON(
  jsonString: string,
): SafeParseResult<ResponseDoneEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ResponseDoneEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ResponseDoneEvent' from JSON`,
  );
}
