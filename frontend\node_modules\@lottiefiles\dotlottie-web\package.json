{"name": "@lottiefiles/dotlottie-web", "version": "0.49.0", "type": "module", "description": "<PERSON><PERSON> and Dot<PERSON>ottie player for the web", "repository": {"type": "git", "url": "git+https://github.com/LottieFiles/dotlottie-web.git", "directory": "packages/web"}, "homepage": "https://github.com/LottieFiles/dotlottie-web#readme", "bugs": "https://github.com/LottieFiles/dotlottie-web/issues", "author": "<PERSON><PERSON>F<PERSON>", "contributors": ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "keywords": ["<PERSON><PERSON><PERSON>", "lottie", "player", "animation", "web", "canvas", "javascript", "thorvg"], "devDependencies": {"@types/node": "^20.14.11", "@vitest/browser": "2.1.0-beta.5", "@vitest/coverage-istanbul": "2.1.0-beta.5", "cross-env": "7.0.3", "esbuild": "^0.23.0", "esbuild-plugin-replace": "^1.4.0", "playwright": "^1.52.0", "tsup": "8.2.0", "typescript": "5.0.4", "vitest": "2.1.0-beta.5"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint --fix .", "stats:eslint": "cross-env TIMING=1 eslint .", "stats:ts": "tsc -p tsconfig.build.json --extendedDiagnostics", "test": "vitest run --browser.headless", "test:coverage": "vitest run --browser.headless --coverage", "test:node": "vitest run --config ./vitest.config.node.ts", "test:watch": "vitest", "type-check": "tsc --noEmit"}}