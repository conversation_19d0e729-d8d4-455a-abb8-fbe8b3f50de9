'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up (queryInterface, Sequelize) {
    // Xóa các trường university, highSchoolScore, graduationYear, status và email từ bảng user
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('user', 'university', { transaction });
      await queryInterface.removeColumn('user', 'highSchoolScore', { transaction });
      await queryInterface.removeColumn('user', 'graduationYear', { transaction });
      await queryInterface.removeColumn('user', 'status', { transaction });
      await queryInterface.removeColumn('user', 'email', { transaction });
    });
  },

  async down (queryInterface, Sequelize) {
    // Thêm lại các trường đã xóa
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn('user', 'university', {
        type: Sequelize.STRING,
        allowNull: true
      }, { transaction });

      await queryInterface.addColumn('user', 'highSchoolScore', {
        type: Sequelize.FLOAT,
        allowNull: true
      }, { transaction });

      await queryInterface.addColumn('user', 'graduationYear', {
        type: Sequelize.INTEGER,
        allowNull: true
      }, { transaction });

      await queryInterface.addColumn('user', 'status', {
        type: Sequelize.STRING,
        allowNull: true
      }, { transaction });

      await queryInterface.addColumn('user', 'email', {
        type: Sequelize.STRING,
        allowNull: true
      }, { transaction });
    });
  }
};
