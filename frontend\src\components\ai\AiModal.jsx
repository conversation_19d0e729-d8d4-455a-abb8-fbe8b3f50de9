import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { askQuestionWithAI, resetAiResponse } from "src/features/ai/aiSlice";
import { <PERSON><PERSON>, ChevronUp, RefreshCcw } from "lucide-react";
import { BotMessageSquare } from "lucide-react";
import ArticleContent from "../article/ArticleContent";
import { MessageCircle, X as XIcon } from "lucide-react";

// AI Modal Component for Mobile
const AIModal = ({ isOpen, onClose, question }) => {
    const { userQuestion, aiResponse, loading: aiLoading } = useSelector((state) => state.ai);
    const dispatch = useDispatch();
    const [selectedMessageId, setSelectedMessageId] = useState(null);

    const handleAskAI = (messageId) => {
        if (question?.id) {
            // Reset previous response if selecting different question
            if (selectedMessageId !== messageId) {
                dispatch(resetAiResponse());
            }
            setSelectedMessageId(messageId);
            dispatch(askQuestionWithAI({ questionId: question.id, messageId }));
        }
    };

    if (!isOpen) return null;

    return (
        <div
            className="fixed inset-0 bg-black bg-opacity-50 z-[55] flex items-end md:hidden"
            onClick={(e) => {
                if (e.target === e.currentTarget) {
                    onClose();
                }
            }}
        >
            <div className="bg-white w-full max-h-[80vh] rounded-t-2xl overflow-hidden transform transition-transform duration-300 ease-out">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50">
                    <div className="flex items-center gap-2.5">
                        <div className="p-1.5 bg-white rounded-lg shadow-sm">
                            <BotMessageSquare className="text-sky-600" size={18} />
                        </div>
                        <h3 className="text-sm font-semibold text-gray-800">Hỏi AI về câu hỏi này</h3>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-white/50 rounded-lg transition-colors"
                    >
                        <XIcon className="text-gray-500" size={18} />
                    </button>
                </div>

                {/* Content */}
                <div className="p-2 space-y-4 max-h-[calc(80vh-80px)] overflow-y-auto">
                    {/* Question Options */}
                    <div>
                        <h4 className="text-xs font-medium text-gray-600 mb-2.5 uppercase tracking-wide">Chọn câu hỏi:</h4>
                        <div className="space-y-2">
                            {Object.entries(userQuestion).map(([messageId, questionText]) => (
                                <button
                                    key={messageId}
                                    onClick={() => handleAskAI(parseInt(messageId))}
                                    disabled={aiLoading || selectedMessageId === parseInt(messageId)}
                                    className={`group relative w-full p-3 text-left text-xs rounded-lg border transition-all duration-200 ${selectedMessageId === parseInt(messageId) && aiResponse
                                        ? 'bg-gradient-to-r from-sky-50 to-blue-50 border-sky-200 text-sky-800 shadow-sm'
                                        : 'bg-gray-50/50 border-gray-200 hover:bg-white hover:border-gray-300 hover:shadow-sm'
                                        } ${aiLoading && selectedMessageId === parseInt(messageId)
                                            ? 'opacity-60 cursor-not-allowed'
                                            : 'cursor-pointer'
                                        }`}
                                >
                                    <div className="flex items-start gap-2.5">
                                        <div className={`p-1 rounded-full mt-0.5 flex-shrink-0 ${selectedMessageId === parseInt(messageId) && aiResponse
                                            ? 'bg-sky-100'
                                            : 'bg-gray-100 group-hover:bg-gray-200'
                                            }`}>
                                            <MessageCircle size={10} className={`${selectedMessageId === parseInt(messageId) && aiResponse
                                                ? 'text-sky-600'
                                                : 'text-gray-500'
                                                }`} />
                                        </div>
                                        <span className="leading-relaxed">{questionText}</span>
                                        {aiLoading && selectedMessageId === parseInt(messageId) && (
                                            <div className="ml-auto">
                                                <RefreshCcw size={12} className="text-sky-600 animate-spin" />
                                            </div>
                                        )}
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* AI Response */}
                    {aiResponse && selectedMessageId && (
                        <div className="bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 rounded-xl border border-sky-100 shadow-sm">
                            <div className="p-2 flex items-center gap-2 mb-3">
                                <div className="p-1.5 bg-white rounded-lg shadow-sm">
                                    <Bot className="text-sky-600" size={14} />
                                </div>
                                <h5 className="text-xs font-semibold text-sky-800 uppercase tracking-wide">Phản hồi từ AI</h5>
                            </div>
                            <div className="text-xs text-gray-700 leading-relaxed bg-white/60 rounded-lg border border-white/50">
                                <ArticleContent content={aiResponse} />
                            </div>
                        </div>
                    )}

                    {/* Loading State */}
                    {aiLoading && (
                        <div className="flex items-center justify-center py-6">
                            <div className="flex items-center gap-2.5 px-4 py-2 bg-sky-50 rounded-full border border-sky-100">
                                <RefreshCcw size={14} className="text-sky-600 animate-spin" />
                                <span className="text-xs font-medium text-sky-700">AI đang phân tích...</span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default AIModal;
