'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
    class ExamComments extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            ExamComments.belongsTo(models.User, { foreignKey: 'userId', as: 'user' })
            ExamComments.belongsTo(models.Exam, { foreignKey: 'examId', as: 'exam' })
            ExamComments.belongsTo(models.ExamComments, { foreignKey: 'commentId', as: 'parent' })
            ExamComments.hasMany(models.ExamComments, { foreignKey: 'commentId', as: 'replies' })
        }
    }
    ExamComments.init({
        content: DataTypes.TEXT,
        userId: DataTypes.INTEGER,
        examId: DataTypes.INTEGER,
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE,
    }, {
        sequelize,
        modelName: 'ExamComments',
        tableName: 'ExamComments',
    })
    return ExamComments
}