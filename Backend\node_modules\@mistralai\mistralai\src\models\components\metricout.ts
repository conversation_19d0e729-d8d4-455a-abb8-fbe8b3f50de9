/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Metrics at the step number during the fine-tuning job. Use these metrics to assess if the training is going smoothly (loss should decrease, token accuracy should increase).
 */
export type MetricOut = {
  trainLoss?: number | null | undefined;
  validLoss?: number | null | undefined;
  validMeanTokenAccuracy?: number | null | undefined;
};

/** @internal */
export const MetricOut$inboundSchema: z.ZodType<
  MetricOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  train_loss: z.nullable(z.number()).optional(),
  valid_loss: z.nullable(z.number()).optional(),
  valid_mean_token_accuracy: z.nullable(z.number()).optional(),
}).transform((v) => {
  return remap$(v, {
    "train_loss": "trainLoss",
    "valid_loss": "validLoss",
    "valid_mean_token_accuracy": "validMeanTokenAccuracy",
  });
});

/** @internal */
export type MetricOut$Outbound = {
  train_loss?: number | null | undefined;
  valid_loss?: number | null | undefined;
  valid_mean_token_accuracy?: number | null | undefined;
};

/** @internal */
export const MetricOut$outboundSchema: z.ZodType<
  MetricOut$Outbound,
  z.ZodTypeDef,
  MetricOut
> = z.object({
  trainLoss: z.nullable(z.number()).optional(),
  validLoss: z.nullable(z.number()).optional(),
  validMeanTokenAccuracy: z.nullable(z.number()).optional(),
}).transform((v) => {
  return remap$(v, {
    trainLoss: "train_loss",
    validLoss: "valid_loss",
    validMeanTokenAccuracy: "valid_mean_token_accuracy",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricOut$ {
  /** @deprecated use `MetricOut$inboundSchema` instead. */
  export const inboundSchema = MetricOut$inboundSchema;
  /** @deprecated use `MetricOut$outboundSchema` instead. */
  export const outboundSchema = MetricOut$outboundSchema;
  /** @deprecated use `MetricOut$Outbound` instead. */
  export type Outbound = MetricOut$Outbound;
}

export function metricOutToJSON(metricOut: MetricOut): string {
  return JSON.stringify(MetricOut$outboundSchema.parse(metricOut));
}

export function metricOutFromJSON(
  jsonString: string,
): SafeParseResult<MetricOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricOut' from JSON`,
  );
}
