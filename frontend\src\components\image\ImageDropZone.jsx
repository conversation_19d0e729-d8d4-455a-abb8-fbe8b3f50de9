import React, { useState } from "react";
import { Trash2, Plus } from "lucide-react";

const ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove }) => {
    const [isDraggingOver, setIsDraggingOver] = useState(false);

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOver(false);

        const draggedImage = e.dataTransfer.getData("text/plain");
        if (draggedImage && onImageDrop) {
            onImageDrop(draggedImage);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.dataTransfer.types.includes('text/plain')) {
            setIsDraggingOver(true);
        }
    };

    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.dataTransfer.types.includes('text/plain')) {
            setIsDraggingOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!e.currentTarget.contains(e.relatedTarget)) {
            setIsDraggingOver(false);
        }
    };

    return (
        <div
            className={`relative rounded-lg w-full p-4 transition-all duration-200 min-h-[60px] flex items-center justify-center
                    ${isDraggingOver
                    ? "border-2 border-dashed border-blue-400 bg-blue-50"
                    : "border border-gray-200 hover:border-blue-300 hover:bg-blue-25"
                }`}
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
        >
            {imageUrl ? (
                <div className="relative group w-fit bg-gray-50 rounded-lg p-2">
                    <img
                        src={imageUrl}
                        alt="Attached image"
                        className="rounded-md max-h-48 max-w-full object-contain"
                    />
                    <button
                        className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        onClick={onImageRemove}
                    >
                        <div className="bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg">
                            <Trash2 className="w-4 h-4" />
                        </div>
                    </button>
                    <div className="mt-2 text-xs text-gray-500 text-center">
                        Ảnh đã thêm • Click vào icon để xóa
                    </div>
                </div>
            ) : (
                <div className="flex flex-col items-center justify-center gap-2">
                    <Plus className="w-5 h-5 text-gray-400" />
                    <p className="text-xs text-gray-500">Thêm ảnh</p>
                </div>
            )}
        </div>
    )
}

export default ImageDropZone;
