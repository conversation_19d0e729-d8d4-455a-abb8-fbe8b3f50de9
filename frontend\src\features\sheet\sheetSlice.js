import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as sheetApi from "../../services/sheetApi";
import { apiHandler } from "../../utils/apiHandler";

export const readSheetAndUpdateTuitionSheet = createAsyncThunk(
    "sheet/readSheetAndUpdateTuitionSheet",
    async ({ sheetUrl, month, colNames, sheetIndex }, { dispatch }) => {
        return await apiHandler(dispatch, sheetApi.readSheetAndUpdateTuitionSheet, { sheetUrl, month, colNames, sheetIndex }, null, true, false, false, false);

    }
);

const initialState = {
    loadingUpdate: false,
    error: null,
    success: false,
    data: null,
    sheetUrl: null,
    month: null,
    sheetIndex: 0,
    colNames: {
        studentPhone: "SĐT HS",
        parentPhone: "SĐT PH",
        fullName: "HỌ VÀ TÊN",
        targetMonthCol: "ĐÃ ĐÓNG T ..."
    }
};

const sheetSlice = createSlice({
    name: "sheet",
    initialState,
    reducers: {
        setSheetUrl: (state, action) => {
            state.sheetUrl = action.payload;
        },
        setMonth: (state, action) => {
            state.month = action.payload;
        },
        setSheetIndex: (state, action) => {
            state.sheetIndex = action.payload;
        },
        setColNames: (state, action) => {
            state.colNames = action.payload;
        },
        resetSheetState: (state) => {
            state.loadingUpdate = false;
            state.error = null;
            state.success = false;
            state.data = null;
            state.sheetUrl = null;
            state.month = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(readSheetAndUpdateTuitionSheet.pending, (state) => {
                state.loadingUpdate = true;
                state.success = false;
            })
            .addCase(readSheetAndUpdateTuitionSheet.fulfilled, (state, action) => {
                state.loadingUpdate = false;
                state.success = true;
                if (action.payload) {
                    state.data = action.payload.data;
                }
            })
            .addCase(readSheetAndUpdateTuitionSheet.rejected, (state, action) => {
                state.loadingUpdate = false;
            });
    },
});

export const {
    setSheetUrl,
    setMonth,
    setColNames,
    resetSheetState,
    setSheetIndex
} = sheetSlice.actions;
export default sheetSlice.reducer;
