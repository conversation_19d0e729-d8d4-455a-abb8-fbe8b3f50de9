import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import {
  fetchUserTuitionPaymentById,
  clearTuitionPayment,
} from "src/features/tuition/tuitionSlice";
import { formatCurrency } from "src/utils/formatters";
import UserLayout from "src/layouts/UserLayout";
import PaymentModal from "src/components/PaymentModal";
import {
  CreditCard,
  ArrowLeft,
  FileText,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  Receipt,
  Loader,
  ChevronRight,
} from "lucide-react";

const UserTuitionPaymentDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { tuitionPayment, loading, studentClassTuitions } = useSelector((state) => state.tuition);
  // paymentProgress removed - không còn sử dụng với schema mới
  const [classTuitionsLoading, setClassTuitionsLoading] = useState(false);

  // State cho modal thanh toán
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState(null);

  useEffect(() => {
    dispatch(fetchUserTuitionPaymentById(id));

    return () => {
      dispatch(clearTuitionPayment());
    };
  }, [dispatch, id]);

  // useEffect(() => {
  //   if (tuitionPayment) {
  //     setClassTuitionsLoading(true);
  //     dispatch(fetchStudentClassTuitionsByMonth(tuitionPayment.month))
  //       .unwrap()
  //       .then(() => {
  //         setClassTuitionsLoading(false);
  //       })
  //       .catch((error) => {
  //         console.error("Error fetching class tuitions:", error);
  //         setClassTuitionsLoading(false);
  //       });
  //   }
  // }, [dispatch, tuitionPayment]);

  // useEffect for paymentProgress removed - không còn sử dụng với schema mới

  const handleOpenPaymentModal = () => {
    if (!tuitionPayment) return;

    setPaymentInfo({
      id: tuitionPayment.id,
      month: tuitionPayment.monthFormatted,
      amount: "Liên hệ anh Triệu Minh để biết số tiền", // Không còn expectedAmount/paidAmount
      note: tuitionPayment.note,
      description: `${user.firstName} ${user.lastName} ${user.highSchool} HP_${tuitionPayment.monthFormatted.replace(' ', '_')}_${tuitionPayment.id}`
    });
    setIsPaymentModalOpen(true);
  };

  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setPaymentInfo(null);
  };

  const getStatusBadge = (status, isOverdue) => {
    if (status === "PAID") {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
          Đã thanh toán
        </span>
      );
    } else if (isOverdue) {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
          Quá hạn
        </span>
      );
    } else if (status === "PARTIAL") {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
          Thanh toán một phần
        </span>
      );
    } else {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
          Chưa thanh toán
        </span>
      );
    }
  };

  const getPaymentStatusIcon = (status, isOverdue) => {
    if (status === "PAID") {
      return (
        <div className="p-3 bg-green-100 rounded-full">
          <CheckCircle className="w-6 h-6 text-green-600" />
        </div>
      );
    } else if (isOverdue) {
      return (
        <div className="p-3 bg-red-100 rounded-full">
          <AlertCircle className="w-6 h-6 text-red-600" />
        </div>
      );
    } else if (status === "PARTIAL") {
      return (
        <div className="p-3 bg-blue-100 rounded-full">
          <DollarSign className="w-6 h-6 text-blue-600" />
        </div>
      );
    } else {
      return (
        <div className="p-3 bg-yellow-100 rounded-full">
          <CreditCard className="w-6 h-6 text-yellow-600" />
        </div>
      );
    }
  };

  if (loading) {
    return (
      <UserLayout>
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="p-8 text-center text-gray-500">
            <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
            <p>Đang tải thông tin học phí...</p>
          </div>
        </div>
      </UserLayout>
    );
  }

  if (!tuitionPayment) {
    return (
      <UserLayout>
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="p-8 text-center text-gray-500">
            <AlertCircle size={40} className="mx-auto mb-4 text-gray-300" />
            <p>Không tìm thấy thông tin học phí.</p>
            <button
              onClick={() => navigate("/tuition-payments")}
              className="mt-4 px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 transition-colors"
            >
              Quay lại danh sách học phí
            </button>
          </div>
        </div>
      </UserLayout>
    );
  }

  return (
    <UserLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Breadcrumb */}
        <div className="flex items-center mb-6 text-sm">
          <button
            onClick={() => navigate("/tuition-payments")}
            className="text-gray-500 hover:text-sky-600 flex items-center gap-1"
          >
            Danh sách học phí
          </button>
          <ChevronRight size={16} className="mx-2 text-gray-400" />
          <span className="text-gray-700">Chi tiết học phí</span>
        </div>

        {/* Tiêu đề */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <CreditCard className="text-sky-600" />
            Chi tiết học phí {tuitionPayment.monthFormatted}
          </h1>
          {getStatusBadge(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue)}
        </div>

        {/* Thông tin chính */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6 flex gap-6">
            {getPaymentStatusIcon(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue)}
            <div className="flex-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Trạng thái thanh toán</p>
                  <p className="text-xl font-semibold">
                    {tuitionPayment.isPaid ? (
                      <span className="text-green-600">Đã thanh toán</span>
                    ) : tuitionPayment.isOverdue ? (
                      <span className="text-red-600">Quá hạn</span>
                    ) : (
                      <span className="text-yellow-600">Chưa thanh toán</span>
                    )}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Ghi chú</p>
                  <p className="text-xl font-semibold text-gray-600">
                    {tuitionPayment.note || "Không có ghi chú"}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Hạn thanh toán</p>
                  <p className="text-base flex items-center gap-1">
                    <Calendar size={16} className="text-gray-400" />
                    {tuitionPayment.dueDateFormatted || "Chưa có hạn thanh toán"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Ngày thanh toán</p>
                  <p className="text-base flex items-center gap-1">
                    <Calendar size={16} className="text-gray-400" />
                    {tuitionPayment.paymentDateFormatted || "Chưa thanh toán"}
                  </p>
                </div>
              </div>

              {tuitionPayment.note && (
                <div className="mt-6 p-4 bg-gray-50 rounded-md">
                  <p className="text-sm text-gray-500 mb-1">Ghi chú</p>
                  <p className="text-base">{tuitionPayment.note}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Các tùy chọn */}
        <div className="flex justify-end gap-3">
          <button
            onClick={() => navigate("/tuition-payments")}
            className="flex items-center gap-1 text-sm text-sky-600 hover:text-sky-700 px-3 py-1.5 bg-sky-50 hover:bg-sky-100 rounded-md transition-colors"
            title="Quay lại danh sách"
          >
            <ArrowLeft size={16} />
            <span>Quay lại</span>
          </button>

          {!tuitionPayment.isPaid && (
            <button
              onClick={() => handleOpenPaymentModal(tuitionPayment)}
              className="flex items-center gap-1 text-sm text-green-600 hover:text-green-700 px-3 py-1.5 bg-green-50 hover:bg-green-100 rounded-md transition-colors"
              title="Thanh toán"
            >
              <FileText size={16} />
              <span>Thanh toán</span>
            </button>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handleClosePaymentModal}
        paymentInfo={paymentInfo}
      />
    </UserLayout>
  );
};

export default UserTuitionPaymentDetail;
