'use strict';
/** @type {import('sequelize-cli').Migration} */
export default {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addIndex('learningItem', ['lessonId'], {
      name: 'idx_learningItem_lessonId',
    });

    // await queryInterface.addIndex('learningItem', ['typeOfLearningItem'], {
    //   name: 'idx_learningItem_type',
    // });

    // await queryInterface.addIndex('learningItem', ['deadline'], {
    //   name: 'idx_learningItem_deadline',
    // });

    // Nếu có tìm kiếm theo tên
    // await queryInterface.addIndex('learningItem', ['name'], {
    //   name: 'idx_learningItem_name',
    // });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('learningItem', 'idx_learningItem_lessonId');
    // await queryInterface.removeIndex('learningItem', 'idx_learningItem_type');
    // await queryInterface.removeIndex('learningItem', 'idx_learningItem_deadline');
    // await queryInterface.removeIndex('learningItem', 'idx_learningItem_name');
  }
};
