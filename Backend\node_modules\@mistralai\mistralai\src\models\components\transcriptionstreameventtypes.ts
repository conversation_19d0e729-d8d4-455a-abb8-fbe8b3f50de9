/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const TranscriptionStreamEventTypes = {
  TranscriptionLanguage: "transcription.language",
  TranscriptionSegment: "transcription.segment",
  TranscriptionTextDelta: "transcription.text.delta",
  TranscriptionDone: "transcription.done",
} as const;
export type TranscriptionStreamEventTypes = ClosedEnum<
  typeof TranscriptionStreamEventTypes
>;

/** @internal */
export const TranscriptionStreamEventTypes$inboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamEventTypes
> = z.nativeEnum(TranscriptionStreamEventTypes);

/** @internal */
export const TranscriptionStreamEventTypes$outboundSchema: z.ZodNativeEnum<
  typeof TranscriptionStreamEventTypes
> = TranscriptionStreamEventTypes$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamEventTypes$ {
  /** @deprecated use `TranscriptionStreamEventTypes$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamEventTypes$inboundSchema;
  /** @deprecated use `TranscriptionStreamEventTypes$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamEventTypes$outboundSchema;
}
