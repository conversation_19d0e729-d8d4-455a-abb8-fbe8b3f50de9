// scripts/showConstraints.js
import { Sequelize } from 'sequelize';
import config from '../config/config.js';

// L<PERSON>y cấu hình theo NODE_ENV
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize(
    dbConfig.database,
    dbConfig.username,
    dbConfig.password,
    {
        host: dbConfig.host,
        port: dbConfig.port,
        dialect: dbConfig.dialect,
        logging: false
    }
);

const showConstraints = async () => {
    try {
        const [results] = await sequelize.query(`
      SELECT 
        CONSTRAINT_NAME, 
        TABLE_NAME, 
        COLUMN_NAME, 
        REFERENCED_TABLE_NAME, 
        REFERENCED_COLUMN_NAME 
      FROM 
        information_schema.KEY_COLUMN_USAGE 
      WHERE 
        TABLE_NAME = 'tuitionPayment' 
        AND COLUMN_NAME = 'userId' 
        AND CONSTRAINT_SCHEMA = '${dbConfig.database}';
    `);

        console.table(results);
    } catch (error) {
        console.error('❌ Lỗi khi truy vấn constraint:', error.message);
    } finally {
        await sequelize.close();
    }
};

showConstraints();
