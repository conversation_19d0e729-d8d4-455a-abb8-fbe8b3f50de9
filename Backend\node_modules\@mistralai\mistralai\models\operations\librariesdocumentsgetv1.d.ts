import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDocumentsGetV1Request = {
    libraryId: string;
    documentId: string;
};
/** @internal */
export declare const LibrariesDocumentsGetV1Request$inboundSchema: z.ZodType<LibrariesDocumentsGetV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDocumentsGetV1Request$Outbound = {
    library_id: string;
    document_id: string;
};
/** @internal */
export declare const LibrariesDocumentsGetV1Request$outboundSchema: z.ZodType<LibrariesDocumentsGetV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDocumentsGetV1Request$ {
    /** @deprecated use `LibrariesDocumentsGetV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDocumentsGetV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDocumentsGetV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDocumentsGetV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsGetV1Request>;
    /** @deprecated use `LibrariesDocumentsGetV1Request$Outbound` instead. */
    type Outbound = LibrariesDocumentsGetV1Request$Outbound;
}
export declare function librariesDocumentsGetV1RequestToJSON(librariesDocumentsGetV1Request: LibrariesDocumentsGetV1Request): string;
export declare function librariesDocumentsGetV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDocumentsGetV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdocumentsgetv1.d.ts.map