{"version": 3, "sources": ["../src/base-dotlottie-react.tsx", "../src/dotlottie.tsx", "../src/dotlottie-worker.tsx", "../src/index.ts"], "names": ["BaseDotLottieReact", "animationId", "autoplay", "backgroundColor", "className", "createDotLottie", "data", "dotLottieRefCallback", "layout", "loop", "mode", "playOnHover", "renderConfig", "segment", "speed", "src", "style", "themeData", "themeId", "useFrameInterpolation", "workerId", "props", "dotLottieRef", "useRef", "canvasRef", "dotLottieRefCallbackRef", "config", "configRef", "setCanvasRef", "useCallback", "canvas", "useEffect", "handlePlayOnHover", "event", "jsx", "<PERSON><PERSON><PERSON><PERSON>", "DotLottieReact", "createDotLottieWorker", "DotLottieWorker", "DotLottieWorkerReact", "setWasmUrl", "url"], "mappings": "wJAgDO,IAAMA,EAAqB,CAAwC,CACxE,WAAAC,CAAAA,CAAAA,CACA,QAAAC,CAAAA,CAAAA,CACA,eAAAC,CAAAA,CAAAA,CACA,UAAAC,CACA,CAAA,eAAA,CAAAC,CACA,CAAA,IAAA,CAAAC,CACA,CAAA,oBAAA,CAAAC,CACA,CAAA,MAAA,CAAAC,CACA,CAAA,IAAA,CAAAC,CACA,CAAA,IAAA,CAAAC,CACA,CAAA,WAAA,CAAAC,CACA,CAAA,YAAA,CAAAC,EACA,OAAAC,CAAAA,CAAAA,CACA,KAAAC,CAAAA,CAAAA,CACA,GAAAC,CAAAA,CAAAA,CACA,KAAAC,CAAAA,CAAAA,CACA,UAAAC,CACA,CAAA,OAAA,CAAAC,CACA,CAAA,qBAAA,CAAAC,CACA,CAAA,QAAA,CAAAC,CACA,CAAA,GAAGC,CACL,CAEmB,GAAA,CACjB,IAAMC,CAAAA,CAAeC,MAAiB,CAAA,IAAI,CACpCC,CAAAA,CAAAA,CAAYD,MAAiC,CAAA,IAAI,CACjDE,CAAAA,CAAAA,CAA0BF,MAA0ChB,CAAAA,CAAoB,CAExFmB,CAAAA,CAAAA,CAEF,CACF,KAAAZ,CAAAA,CAAAA,CACA,IAAAJ,CAAAA,CAAAA,CACA,IAAAD,CAAAA,CAAAA,CACA,qBAAAU,CAAAA,CAAAA,CACA,QAAAN,CACA,CAAA,eAAA,CAAAV,CACA,CAAA,QAAA,CAAAD,CACA,CAAA,OAAA,CAAAgB,CACA,CAAA,QAAA,CAAAE,EACA,GAAAL,CAAAA,CAAAA,CACA,IAAAT,CAAAA,CAAAA,CACA,MAAAE,CAAAA,CAAAA,CACA,YAAAI,CAAAA,CAAAA,CACA,WAAAX,CAAAA,CACF,CAEM0B,CAAAA,CAAAA,CAAYJ,MAA4FG,CAAAA,CAAM,CAEpHD,CAAAA,CAAAA,CAAwB,QAAUlB,CAClCoB,CAAAA,CAAAA,CAAU,OAAUD,CAAAA,CAAAA,CAEpB,IAAME,CAAAA,CAAeC,WAAaC,CAAAA,CAAAA,EAAqC,CACrEN,CAAU,CAAA,OAAA,CAAUM,CAEhBA,CAAAA,CAAAA,CACFR,CAAa,CAAA,OAAA,CAAUjB,CAAgB,CAAA,CACrC,GAAGsB,CAAU,CAAA,OAAA,CACb,MAAAG,CAAAA,CACF,CAAC,CAAA,EAEDR,CAAa,CAAA,OAAA,EAAS,OAAQ,EAAA,CAC9BA,CAAa,CAAA,OAAA,CAAU,IAGzBG,CAAAA,CAAAA,CAAAA,CAAwB,OAAUH,GAAAA,CAAAA,CAAa,OAAO,EACxD,CAAA,CAAG,EAAE,CAEL,CAAA,OAAAS,SAAU,CAAA,IAAM,CACd,IAAMC,CAAAA,CAAqBC,CAA4B,EAAA,CAChDtB,CAEDsB,GAAAA,CAAAA,CAAM,IAAS,GAAA,YAAA,EACjBX,EAAa,OAAS,EAAA,IAAA,EAGpBW,CAAAA,CAAAA,CAAM,IAAS,GAAA,YAAA,EACjBX,CAAa,CAAA,OAAA,EAAS,KAAM,EAAA,EAEhC,CAEA,CAAA,OAAAE,CAAU,CAAA,OAAA,EAAS,gBAAiB,CAAA,YAAA,CAAcQ,CAAiB,CACnER,CAAAA,CAAAA,CAAU,OAAS,EAAA,gBAAA,CAAiB,YAAcQ,CAAAA,CAAiB,CAE5D,CAAA,IAAM,CACXR,CAAU,CAAA,OAAA,EAAS,mBAAoB,CAAA,YAAA,CAAcQ,CAAiB,CAAA,CACtER,CAAU,CAAA,OAAA,EAAS,oBAAoB,YAAcQ,CAAAA,CAAiB,EACxE,CACF,CAAG,CAAA,CAACrB,CAAW,CAAC,CAEhBoB,CAAAA,SAAAA,CAAU,IAAM,CACdT,CAAa,CAAA,OAAA,EAAS,QAASR,CAAAA,CAAAA,EAAS,CAAC,EAC3C,CAAA,CAAG,CAACA,CAAK,CAAC,CAAA,CAEViB,SAAU,CAAA,IAAM,CACdT,CAAa,CAAA,OAAA,EAAS,OAAQZ,CAAAA,CAAAA,EAAQ,SAAS,EACjD,CAAG,CAAA,CAACA,CAAI,CAAC,CAAA,CAETqB,SAAU,CAAA,IAAM,CACdT,CAAAA,CAAa,OAAS,EAAA,OAAA,CAAQb,GAAQ,KAAK,EAC7C,CAAG,CAAA,CAACA,CAAI,CAAC,CAETsB,CAAAA,SAAAA,CAAU,IAAM,CACdT,CAAAA,CAAa,OAAS,EAAA,wBAAA,CAAyBH,CAAyB,EAAA,IAAI,EAC9E,CAAA,CAAG,CAACA,CAAqB,CAAC,CAE1BY,CAAAA,SAAAA,CAAU,IAAM,CACV,OAAOlB,CAAAA,GAAU,CAAC,CAAM,EAAA,QAAA,EAAY,OAAOA,CAAAA,CAAQ,CAAC,CAAA,EAAM,QAC5DS,EAAAA,CAAAA,CAAa,OAAS,EAAA,UAAA,CAAWT,CAAQ,CAAA,CAAC,CAAGA,CAAAA,CAAAA,CAAQ,CAAC,CAAC,EAK3D,CAAG,CAAA,CAACA,CAAO,CAAC,CAEZkB,CAAAA,SAAAA,CAAU,IAAM,CACdT,EAAa,OAAS,EAAA,kBAAA,CAAmBnB,CAAmB,EAAA,EAAE,EAChE,CAAA,CAAG,CAACA,CAAe,CAAC,CAEpB4B,CAAAA,SAAAA,CAAU,IAAM,CACdT,CAAa,CAAA,OAAA,EAAS,eAAgBV,CAAAA,CAAAA,EAAgB,EAAE,EAC1D,CAAA,CAAG,CAAC,IAAA,CAAK,SAAUA,CAAAA,CAAY,CAAC,CAAC,CAAA,CAEjCmB,SAAU,CAAA,IAAM,CACV,OAAOzB,CAAS,EAAA,QAAA,EAAY,OAAOA,CAAS,EAAA,QAAA,EAEhDgB,CAAa,CAAA,OAAA,EAAS,IAAK,CAAA,CACzB,IAAAhB,CAAAA,CAAAA,CACA,GAAGqB,CAAU,CAAA,OACf,CAAC,EACH,CAAG,CAAA,CAACrB,CAAI,CAAC,CAETyB,CAAAA,SAAAA,CAAU,IAAM,CACV,OAAOhB,CAAAA,EAAQ,QAEnBO,EAAAA,CAAAA,CAAa,SAAS,IAAK,CAAA,CACzB,GAAAP,CAAAA,CAAAA,CACA,GAAGY,CAAAA,CAAU,OACf,CAAC,EACH,CAAG,CAAA,CAACZ,CAAG,CAAC,CAERgB,CAAAA,SAAAA,CAAU,IAAM,CACdT,EAAa,OAAS,EAAA,SAAA,CAAUD,CAAM,CAAA,MAAA,EAAU,EAAE,EACpD,CAAG,CAAA,CAACA,CAAM,CAAA,MAAM,CAAC,CAAA,CAEjBU,SAAU,CAAA,IAAM,CACdT,CAAAA,CAAa,SAAS,aAAcrB,CAAAA,CAAAA,EAAe,EAAE,EACvD,CAAG,CAAA,CAACA,CAAW,CAAC,EAEhB8B,SAAU,CAAA,IAAM,CACV,OAAOb,CAAY,EAAA,QAAA,EACrBI,CAAa,CAAA,OAAA,EAAS,SAASJ,CAAO,EAK1C,CAAG,CAAA,CAACA,CAAO,CAAC,CAEZa,CAAAA,SAAAA,CAAU,IAAM,CACdT,CAAa,CAAA,OAAA,EAAS,YAAaL,CAAAA,CAAAA,EAAa,EAAE,EACpD,EAAG,CAACA,CAAS,CAAC,CAAA,CAEdc,SAAU,CAAA,IAAM,CACdT,CAAAA,CAAa,SAAS,SAAUd,CAAAA,CAAAA,EAAU,EAAE,EAC9C,CAAA,CAAG,CAACA,CAAAA,EAAQ,IAAKA,CAAQ,EAAA,KAAA,EAASA,CAAO,CAAA,KAAA,CAAM,CAAC,CAAA,CAAGA,CAAQ,EAAA,KAAA,EAASA,CAAO,CAAA,KAAA,CAAM,CAAC,CAAC,CAAC,CAAA,CAGlF0B,GAAC,CAAA,KAAA,CAAA,CACC,UAAW9B,CACV,CAAA,GAAI,CAACA,CAAAA,EAAa,CACjB,KAAA,CAAO,CACL,KAAA,CAAO,OACP,MAAQ,CAAA,MAAA,CACR,UAAY,CAAA,CAAA,CACZ,GAAGY,CACL,CACF,CAAA,CAEA,SAAAkB,GAAC,CAAA,QAAA,CAAA,CACC,GAAKN,CAAAA,CAAAA,CACL,KAAO,CAAA,CACL,KAAO,CAAA,MAAA,CACP,MAAQ,CAAA,MACV,CACC,CAAA,GAAGP,CACN,CAAA,CAAA,CACF,CAEJ,CAAA,CClOMhB,IAAAA,CAAAA,CAAmBqB,CAA8B,EAAA,IAAIS,SAAUT,CAAAA,CAAM,EAE9DU,CAAkBf,CAAAA,CAAAA,EACtBa,GAAClC,CAAAA,CAAAA,CAAA,CAAoB,GAAGqB,CAAO,CAAA,eAAA,CAAiBhB,EAAiB,MCJpEgC,CAAyBX,CAAAA,CAAAA,EAA4D,IAAIY,eAAAA,CAAgBZ,CAAM,CAAA,CAExGa,CAAwBlB,CAAAA,CAAAA,EAC5Ba,IAAClC,CAAA,CAAA,CAAoB,GAAGqB,CAAAA,CAAO,eAAiBgB,CAAAA,CAAAA,CAAuB,ECLzE,IAAMG,GAAcC,CAAsB,EAAA,CAC/CH,eAAgB,CAAA,UAAA,CAAWG,CAAG,CAAA,CAC9BN,SAAU,CAAA,UAAA,CAAWM,CAAG,EAC1B", "file": "index.js", "sourcesContent": ["/* eslint-disable no-warning-comments */\n'use client';\n\nimport type { Config, Dot<PERSON><PERSON>ie, Dot<PERSON>ottieWorker } from '@lottiefiles/dotlottie-web';\nimport { useEffect, useCallback, useRef, type ComponentProps, type RefCallback } from 'react';\nimport type { JSX } from 'react';\n\nexport type BaseDotLottieProps<T extends DotLottie | DotLottieWorker> = Omit<Config, 'canvas'> &\n  ComponentProps<'canvas'> & {\n    animationId?: string;\n    /**\n     * A function that creates a `DotLottie` or `DotLottieWorker` instance.\n     */\n    createDotLottie: (config: T extends DotLottieWorker ? Config & { workerId?: string } : Config) => T;\n    /**\n     * A callback function that receives the `DotLottie` or `DotLottieWorker` instance.\n     *\n     * @example\n     * ```tsx\n     * const [dotLottie, setDotLottie] = useState<DotLottie | null>(null);\n     *\n     * <DotLottieReact\n     *   dotLottieRefCallback={setDotLottie}\n     * />\n     * ```\n     */\n    dotLottieRefCallback?: RefCallback<T | null>;\n    /**\n     * @deprecated The `playOnHover` property is deprecated.\n     * Instead, use the `onMouseEnter` and `onMouseLeave` events to control animation playback.\n     * Utilize the `dotLottieRefCallback` to access the `DotLottie` instance and invoke the `play` and `pause` methods.\n     *\n     * Example usage:\n     * ```tsx\n     * const [dotLottie, setDotLottie] = useState<DotLottie | null>(null);\n     *\n     * <DotLottieReact\n     *   dotLottieRefCallback={setDotLottie}\n     *   onMouseEnter={() => dotLottie?.play()}\n     *   onMouseLeave={() => dotLottie?.pause()}\n     * />\n     * ```\n     */\n    playOnHover?: boolean;\n    themeData?: string;\n    workerId?: T extends DotLottieWorker ? string : undefined;\n  };\n\nexport const BaseDotLottieReact = <T extends DotLottie | DotLottieWorker>({\n  animationId,\n  autoplay,\n  backgroundColor,\n  className,\n  createDotLottie,\n  data,\n  dotLottieRefCallback,\n  layout,\n  loop,\n  mode,\n  playOnHover,\n  renderConfig,\n  segment,\n  speed,\n  src,\n  style,\n  themeData,\n  themeId,\n  useFrameInterpolation,\n  workerId,\n  ...props\n}: BaseDotLottieProps<T> & {\n  createDotLottie: (config: T extends DotLottieWorker ? Config & { workerId?: string } : Config) => T;\n}): JSX.Element => {\n  const dotLottieRef = useRef<T | null>(null);\n  const canvasRef = useRef<HTMLCanvasElement | null>(null);\n  const dotLottieRefCallbackRef = useRef<RefCallback<T | null> | undefined>(dotLottieRefCallback);\n\n  const config: Omit<Config, 'canvas'> & {\n    workerId?: T extends DotLottieWorker ? string : undefined;\n  } = {\n    speed,\n    mode,\n    loop,\n    useFrameInterpolation,\n    segment,\n    backgroundColor,\n    autoplay,\n    themeId,\n    workerId,\n    src,\n    data,\n    layout,\n    renderConfig,\n    animationId,\n  };\n\n  const configRef = useRef<Omit<BaseDotLottieProps<T>, 'createDotLottie' | 'dotLottieRefCallback'> | undefined>(config);\n\n  dotLottieRefCallbackRef.current = dotLottieRefCallback;\n  configRef.current = config;\n\n  const setCanvasRef = useCallback((canvas: HTMLCanvasElement | null) => {\n    canvasRef.current = canvas;\n\n    if (canvas) {\n      dotLottieRef.current = createDotLottie({\n        ...configRef.current,\n        canvas,\n      });\n    } else {\n      dotLottieRef.current?.destroy();\n      dotLottieRef.current = null;\n    }\n\n    dotLottieRefCallbackRef.current?.(dotLottieRef.current);\n  }, []);\n\n  useEffect(() => {\n    const handlePlayOnHover = (event: MouseEvent): void => {\n      if (!playOnHover) return;\n\n      if (event.type === 'mouseenter') {\n        dotLottieRef.current?.play();\n      }\n\n      if (event.type === 'mouseleave') {\n        dotLottieRef.current?.pause();\n      }\n    };\n\n    canvasRef.current?.addEventListener('mouseenter', handlePlayOnHover);\n    canvasRef.current?.addEventListener('mouseleave', handlePlayOnHover);\n\n    return () => {\n      canvasRef.current?.removeEventListener('mouseenter', handlePlayOnHover);\n      canvasRef.current?.removeEventListener('mouseleave', handlePlayOnHover);\n    };\n  }, [playOnHover]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setSpeed(speed ?? 1);\n  }, [speed]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setMode(mode ?? 'forward');\n  }, [mode]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setLoop(loop ?? false);\n  }, [loop]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setUseFrameInterpolation(useFrameInterpolation ?? true);\n  }, [useFrameInterpolation]);\n\n  useEffect(() => {\n    if (typeof segment?.[0] === 'number' && typeof segment[1] === 'number') {\n      dotLottieRef.current?.setSegment(segment[0], segment[1]);\n    } else {\n      // TODO: implement it for worker\n      // dotLottieRef.current?.resetSegment();\n    }\n  }, [segment]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setBackgroundColor(backgroundColor ?? '');\n  }, [backgroundColor]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setRenderConfig(renderConfig ?? {});\n  }, [JSON.stringify(renderConfig)]);\n\n  useEffect(() => {\n    if (typeof data !== 'string' && typeof data !== 'object') return;\n\n    dotLottieRef.current?.load({\n      data,\n      ...configRef.current,\n    });\n  }, [data]);\n\n  useEffect(() => {\n    if (typeof src !== 'string') return;\n\n    dotLottieRef.current?.load({\n      src,\n      ...configRef.current,\n    });\n  }, [src]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setMarker(props.marker ?? '');\n  }, [props.marker]);\n\n  useEffect(() => {\n    dotLottieRef.current?.loadAnimation(animationId ?? '');\n  }, [animationId]);\n\n  useEffect(() => {\n    if (typeof themeId === 'string') {\n      dotLottieRef.current?.setTheme(themeId);\n    } else {\n      // TODO: implement it for worker\n      // dotLottieRef.current?.resetTheme();\n    }\n  }, [themeId]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setThemeData(themeData ?? '');\n  }, [themeData]);\n\n  useEffect(() => {\n    dotLottieRef.current?.setLayout(layout ?? {});\n  }, [layout?.fit, layout?.align && layout.align[0], layout?.align && layout.align[1]]);\n\n  return (\n    <div\n      className={className}\n      {...(!className && {\n        style: {\n          width: '100%',\n          height: '100%',\n          lineHeight: 0,\n          ...style,\n        },\n      })}\n    >\n      <canvas\n        ref={setCanvasRef}\n        style={{\n          width: '100%',\n          height: '100%',\n        }}\n        {...props}\n      />\n    </div>\n  );\n};\n", "'use client';\n\nimport type { Config } from '@lottiefiles/dotlottie-web';\nimport { DotLottie } from '@lottiefiles/dotlottie-web';\nimport type { JSX } from 'react';\n\nimport type { BaseDotLottieProps } from './base-dotlottie-react';\nimport { BaseDotLottieReact } from './base-dotlottie-react';\n\nexport type DotLottieReactProps = Omit<BaseDotLottieProps<DotLottie>, 'createDotLottie'>;\n\nconst createDotLottie = (config: Config): DotLottie => new DotLottie(config);\n\nexport const DotLottieReact = (props: DotLottieReactProps): JSX.Element => {\n  return <BaseDotLottieReact {...props} createDotLottie={createDotLottie} />;\n};\n", "'use client';\n\nimport type { Config } from '@lottiefiles/dotlottie-web';\nimport { DotLottieWorker } from '@lottiefiles/dotlottie-web';\n\nimport { BaseDotLottieReact } from './base-dotlottie-react';\nimport type { BaseDotLottieProps } from './base-dotlottie-react';\n\nexport type DotLottieWorkerReactProps = Omit<BaseDotLottieProps<DotLottieWorker>, 'createDotLottie'>;\n\nconst createDotLottieWorker = (config: Config & { workerId?: string }): DotLottieWorker => new DotLottieWorker(config);\n\nexport const DotLottieWorkerReact = (props: DotLottieWorkerReactProps): JSX.Element => {\n  return <BaseDotLottieReact {...props} createDotLottie={createDotLottieWorker} />;\n};\n", "'use client';\n\nimport { <PERSON><PERSON><PERSON><PERSON>, DotLottieWorker } from '@lottiefiles/dotlottie-web';\n\nexport * from './dotlottie';\nexport type * from '@lottiefiles/dotlottie-web';\nexport * from './dotlottie-worker';\n\nexport const setWasmUrl = (url: string): void => {\n  DotLottieWorker.setWasmUrl(url);\n  DotLottie.setWasmUrl(url);\n};\n"]}