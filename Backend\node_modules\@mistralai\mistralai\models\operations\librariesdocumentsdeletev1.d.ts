import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type LibrariesDocumentsDeleteV1Request = {
    libraryId: string;
    documentId: string;
};
/** @internal */
export declare const LibrariesDocumentsDeleteV1Request$inboundSchema: z.ZodType<LibrariesDocumentsDeleteV1Request, z.ZodTypeDef, unknown>;
/** @internal */
export type LibrariesDocumentsDeleteV1Request$Outbound = {
    library_id: string;
    document_id: string;
};
/** @internal */
export declare const LibrariesDocumentsDeleteV1Request$outboundSchema: z.ZodType<LibrariesDocumentsDeleteV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsDeleteV1Request>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace LibrariesDocumentsDeleteV1Request$ {
    /** @deprecated use `LibrariesDocumentsDeleteV1Request$inboundSchema` instead. */
    const inboundSchema: z.ZodType<LibrariesDocumentsDeleteV1Request, z.ZodTypeDef, unknown>;
    /** @deprecated use `LibrariesDocumentsDeleteV1Request$outboundSchema` instead. */
    const outboundSchema: z.ZodType<LibrariesDocumentsDeleteV1Request$Outbound, z.ZodTypeDef, LibrariesDocumentsDeleteV1Request>;
    /** @deprecated use `LibrariesDocumentsDeleteV1Request$Outbound` instead. */
    type Outbound = LibrariesDocumentsDeleteV1Request$Outbound;
}
export declare function librariesDocumentsDeleteV1RequestToJSON(librariesDocumentsDeleteV1Request: LibrariesDocumentsDeleteV1Request): string;
export declare function librariesDocumentsDeleteV1RequestFromJSON(jsonString: string): SafeParseResult<LibrariesDocumentsDeleteV1Request, SDKValidationError>;
//# sourceMappingURL=librariesdocumentsdeletev1.d.ts.map