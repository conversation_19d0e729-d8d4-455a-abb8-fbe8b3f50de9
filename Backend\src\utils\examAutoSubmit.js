// src/utils/examAutoSubmit.js
import db from '../models/index.js';

/**
 * Check for unfinished exam attempts that have timed out and submit them
 * @returns {Promise<Array>} Array of auto-submitted attempts
 */
export const checkAndSubmitTimedOutExams = async () => {
  try {
    // Find all unfinished attempts
    const unfinishedAttempts = await db.StudentExamAttempt.findAll({
      where: { endTime: null },
      include: [{ model: db.Exam, as: 'exam' }]
    });

    console.log(`Checking ${unfinishedAttempts.length} unfinished exam attempts for timeout...`);
    
    const autoSubmittedAttempts = [];
    
    // Check each attempt for timeout
    for (const attempt of unfinishedAttempts) {
      const submitted = await attempt.autoSubmitIfTimedOut();
      if (submitted) {
        autoSubmittedAttempts.push(submitted);
      }
    }
    
    if (autoSubmittedAttempts.length > 0) {
      console.log(`Auto-submitted ${autoSubmittedAttempts.length} timed-out exams`);
    }
    
    return autoSubmittedAttempts;
  } catch (error) {
    console.error('Error checking for timed-out exams:', error);
    return [];
  }
};

/**
 * Check if a specific exam attempt has timed out and submit it if needed
 * @param {number} attemptId - The ID of the attempt to check
 * @returns {Promise<Object|null>} The submitted attempt or null
 */
export const checkAndSubmitExamById = async (attemptId) => {
  try {
    const attempt = await db.StudentExamAttempt.findByPk(attemptId, {
      include: [{ model: db.Exam, as: 'exam' }]
    });
    
    if (!attempt) {
      console.log(`Attempt ${attemptId} not found`);
      return null;
    }
    
    return await attempt.autoSubmitIfTimedOut();
  } catch (error) {
    console.error(`Error checking attempt ${attemptId} for timeout:`, error);
    return null;
  }
};
