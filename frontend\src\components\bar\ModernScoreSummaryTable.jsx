import React from 'react';
import {
    CheckSquare,
    ToggleLeft,
    MessageSquare,
    Award,
    Star,
    TrendingUp,
    BarChart2
} from 'lucide-react';

/**
 * Modern Score Summary Table Component
 *
 * @param {Object} props - Component props
 * @param {Object} props.stats - Statistics object containing scores for different question types
 */
const ModernScoreSummaryTable = ({ stats }) => {
    // Define sections with their icons, labels, and gradient colors
    const sections = [
        {
            key: 'TN',
            label: 'Trắc nghiệm',
            icon: <CheckSquare size={18} className="text-sky-500" />,
            gradient: 'from-sky-50 to-white'
        },
        {
            key: 'DS',
            label: 'Đúng/Sai',
            icon: <ToggleLeft size={18} className="text-indigo-500" />,
            gradient: 'from-indigo-50 to-white'
        },
        {
            key: 'TLN',
            label: 'Tự luận ngắn',
            icon: <MessageSquare size={18} className="text-amber-500" />,
            gradient: 'from-amber-50 to-white'
        },
    ];

    // Calculate total score
    const totalScore = Object.values(stats).reduce((sum, section) => sum + (section.score || 0), 0);

    return (
        <div className="w-full">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <Star className="text-amber-500" size={20} />
                    <span>Điểm từng phần</span>
                </h3>
                <div className="text-sm text-gray-500">Tổng điểm: <span className="font-semibold text-sky-600">{totalScore.toFixed(2)}/10</span></div>
            </div>

            <div className="space-y-4">
                {sections.map(section => {
                    const sectionStats = stats[section.key] || { correct: 0, incorrect: 0, unanswered: 0, score: 0 };
                    const total = sectionStats.correct + sectionStats.incorrect + sectionStats.unanswered;

                    // Calculate percentages for the progress bars
                    const correctPercent = total > 0 ? (sectionStats.correct / total) * 100 : 0;
                    const incorrectPercent = total > 0 ? (sectionStats.incorrect / total) * 100 : 0;
                    const unansweredPercent = total > 0 ? (sectionStats.unanswered / total) * 100 : 0;

                    return (
                        <div
                            key={section.key}
                            className={`bg-gradient-to-r ${section.gradient} rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow`}
                        >
                            <div className="flex justify-between items-center mb-3">
                                <div className="flex items-center gap-2">
                                    {section.icon}
                                    <span className="font-medium text-gray-800">{section.label}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    {section.key === 'TN' && <BarChart2 size={16} className="text-sky-600" />}
                                    {section.key === 'DS' && <TrendingUp size={16} className="text-indigo-600" />}
                                    {section.key === 'TLN' && <Award size={16} className="text-amber-600" />}
                                    <div className="text-lg font-semibold text-gray-800">{sectionStats.score.toFixed(2)}</div>
                                </div>
                            </div>

                            {/* Progress bar for correct/incorrect/unanswered */}
                            <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden mb-2">
                                <div className="flex h-full">
                                    <div
                                        className="bg-green-500 h-full"
                                        style={{ width: `${correctPercent}%` }}
                                    ></div>
                                    <div
                                        className="bg-red-500 h-full"
                                        style={{ width: `${incorrectPercent}%` }}
                                    ></div>
                                    <div
                                        className="bg-yellow-500 h-full"
                                        style={{ width: `${unansweredPercent}%` }}
                                    ></div>
                                </div>
                            </div>

                            {/* Stats details */}
                            <div className="grid grid-cols-3 gap-2 text-sm">
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                    <span className="text-gray-600">Đúng: <span className="font-medium text-green-600">{sectionStats.correct}</span></span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                                    <span className="text-gray-600">Sai: <span className="font-medium text-red-600">{sectionStats.incorrect}</span></span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                                    <span className="text-gray-600">Chưa làm: <span className="font-medium text-yellow-600">{sectionStats.unanswered}</span></span>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default ModernScoreSummaryTable;
