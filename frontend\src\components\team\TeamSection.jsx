import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Users, Star, Award, GraduationCap, Heart, ChevronLeft, ChevronRight } from 'lucide-react';

const TeamSection = ({ teamImages = [] }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(true);

    // Auto-play functionality
    useEffect(() => {
        if (!isAutoPlaying || teamImages.length <= 1) return;

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) =>
                prevIndex === teamImages.length - 1 ? 0 : prevIndex + 1
            );
        }, 4000);

        return () => clearInterval(interval);
    }, [isAutoPlaying, teamImages.length]);

    const goToNext = () => {
        setCurrentIndex((prevIndex) =>
            prevIndex === teamImages.length - 1 ? 0 : prevIndex + 1
        );
    };

    const goToPrevious = () => {
        setCurrentIndex((prevIndex) =>
            prevIndex === 0 ? teamImages.length - 1 : prevIndex - 1
        );
    };

    const goToSlide = (index) => {
        setCurrentIndex(index);
    };

    if (!teamImages || teamImages.length === 0) {
        return (
            <section className="w-full px-4 py-16 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
                <div className="max-w-screen-xl mx-auto text-center">
                    <div className="inline-block px-4 py-2 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium mb-4">
                        Đội ngũ giảng viên
                    </div>
                    <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4">
                        Đội ngũ trợ giảng tận tâm
                    </h2>
                    <p className="text-gray-600 max-w-2xl mx-auto text-lg mb-8">
                        Chưa có thông tin về đội ngũ trợ giảng. Vui lòng quay lại sau.
                    </p>
                </div>
            </section>
        );
    }

    return (
        <section className="w-full px-4 py-16 bg-gradient-to-br from-indigo-50 via-white to-purple-50 overflow-hidden">
            <div className="max-w-screen-xl mx-auto">
                {/* Header */}
                <div className="text-center mb-12">
                    <div className="inline-block px-4 py-2 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium mb-4">
                        Đội ngũ giảng viên
                    </div>
                    <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 font-cubano mb-4 flex items-center justify-center gap-3">
                        <Users className="w-8 h-8 text-indigo-600" />
                        Đội ngũ trợ giảng tận tâm
                    </h2>
                    <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                        Gặp gỡ đội ngũ giáo viên và trợ giảng giàu kinh nghiệm, luôn đồng hành cùng các em trên con đường chinh phục tri thức.
                    </p>
                </div>

                {/* Main Content */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    {/* Left Side - Image Carousel */}
                    <div className="relative">
                        <div
                            className="relative w-full aspect-square rounded-2xl overflow-hidden shadow-2xl"
                            onMouseEnter={() => setIsAutoPlaying(false)}
                            onMouseLeave={() => setIsAutoPlaying(true)}
                        >
                            {/* Main Image */}
                            <motion.div
                                key={currentIndex}
                                initial={{ opacity: 0, scale: 1.1 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.6 }}
                                className="absolute inset-0"
                            >
                                <img
                                    src={teamImages[currentIndex]}
                                    alt={`Đội ngũ trợ giảng ${currentIndex + 1}`}
                                    className="w-full h-full object-cover"
                                />
                                {/* Gradient overlay */}
                                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                            </motion.div>

                            {/* Navigation Arrows */}
                            {teamImages.length > 1 && (
                                <>
                                    <button
                                        onClick={goToPrevious}
                                        className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110"
                                    >
                                        <ChevronLeft className="w-6 h-6 text-gray-700" />
                                    </button>
                                    <button
                                        onClick={goToNext}
                                        className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110"
                                    >
                                        <ChevronRight className="w-6 h-6 text-gray-700" />
                                    </button>
                                </>
                            )}

                            {/* Image Counter */}
                            <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                                {currentIndex + 1} / {teamImages.length}
                            </div>
                        </div>

                        {/* Dots Indicator */}
                        {teamImages.length > 1 && (
                            <div className="flex justify-center mt-6 gap-2">
                                {teamImages.map((_, index) => (
                                    <button
                                        key={index}
                                        onClick={() => goToSlide(index)}
                                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                                            index === currentIndex
                                                ? 'bg-indigo-600 w-8'
                                                : 'bg-gray-300 hover:bg-gray-400'
                                        }`}
                                    />
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Right Side - Content */}
                    <div className="space-y-8">
                        {/* Feature Cards */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.1 }}
                                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100"
                            >
                                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                                    <GraduationCap className="w-6 h-6 text-indigo-600" />
                                </div>
                                <h3 className="text-lg font-bold text-gray-800 mb-2">Kinh nghiệm phong phú</h3>
                                <p className="text-gray-600 text-sm">Đội ngũ giáo viên với nhiều năm kinh nghiệm giảng dạy và đào tạo học sinh xuất sắc.</p>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-purple-100"
                            >
                                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                                    <Heart className="w-6 h-6 text-purple-600" />
                                </div>
                                <h3 className="text-lg font-bold text-gray-800 mb-2">Tận tâm với học sinh</h3>
                                <p className="text-gray-600 text-sm">Luôn đặt học sinh lên hàng đầu, hỗ trợ và đồng hành cùng các em trong mọi khó khăn.</p>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-emerald-100"
                            >
                                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                                    <Star className="w-6 h-6 text-emerald-600" />
                                </div>
                                <h3 className="text-lg font-bold text-gray-800 mb-2">Phương pháp hiện đại</h3>
                                <p className="text-gray-600 text-sm">Áp dụng các phương pháp giảng dạy tiên tiến, hiệu quả và phù hợp với từng học sinh.</p>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-amber-100"
                            >
                                <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center mb-4">
                                    <Award className="w-6 h-6 text-amber-600" />
                                </div>
                                <h3 className="text-lg font-bold text-gray-800 mb-2">Thành tích xuất sắc</h3>
                                <p className="text-gray-600 text-sm">Đã đào tạo nhiều thế hệ học sinh đạt điểm cao và đỗ vào các trường đại học hàng đầu.</p>
                            </motion.div>
                        </div>

                        {/* Call to Action */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.5 }}
                            className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6 rounded-xl text-white"
                        >
                            <h3 className="text-xl font-bold mb-2">Sẵn sàng đồng hành cùng bạn</h3>
                            <p className="text-indigo-100 mb-4">
                                Đội ngũ trợ giảng của chúng tôi luôn sẵn sàng hỗ trợ và giải đáp mọi thắc mắc của các em học sinh.
                            </p>
                            <div className="flex flex-wrap gap-2">
                                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">Hỗ trợ 24/7</span>
                                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">Tư vấn miễn phí</span>
                                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">Theo dõi tiến độ</span>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default TeamSection;
