import { useState } from "react";
import UserAdminLayout from "src/layouts/UserAdminLayout";
import { TableAdmin, TdAdmin, ThAdmin, TheadAdmin } from "src/components/table/TableAdmin";
import AdminMobileAttendancePage from "../attendance/AdminMobileAttendancePage";

export const StudentAttendanceAdmin = () => {
    const [month, setMonth] = useState("");

    return (
        <UserAdminLayout>
            <div className="flex-1 overflow-hidden p-6 text-sm">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                    <AdminMobileAttendancePage />
                </div>
            </div>
        </UserAdminLayout>
    );
};

export default StudentAttendanceAdmin;
