import UserLayout from "../../../layouts/UserLayout";
import { useSelector, useDispatch } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import { fetchPublicQuestionById } from "../../../features/question/questionSlice";
import { fetchCodesByType } from "../../../features/code/codeSlice";
import { useEffect, useState } from "react";
import {
    FileQuestion,
    RefreshCcw,
    BookOpen,
    BarChart2,
    GraduationCap,
    ListOrdered,
    StickyNote,
    ArrowLeft,
    Share2,
    Eye,
    EyeOff,
    Check,
    X,

} from "lucide-react";
import { formatDate } from "src/utils/formatters";
import LoadingText from "src/components/loading/LoadingText";
import LatexRenderer from "src/components/latex/RenderLatex";
import MarkdownPreviewWithMath from "src/components/latex/MarkDownPreview";
import YouTubePlayer from "src/components/YouTubePlayer";
import AIChatWidget from "src/components/ai/AiChatWidget";
import { shareContent } from "src/utils/shareUntil";

const ActionButton = ({ icon: Icon, title, shortTitle, isActive = false, onClick, disabled = false }) => {
    return (
        <button
            onClick={onClick}
            disabled={disabled}
            className={`w-fit px-2 py-[7px] rounded-md text-xs font-medium transition-colors whitespace-nowrap
                ${disabled
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : isActive
                        ? 'bg-sky-100 text-sky-700 border border-sky-300'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}
            `}
        >
            <Icon className="w-4 h-4 inline mr-1 lg:mr-2" />
            <span className="hidden sm:inline">{title}</span>
            <span className="sm:hidden">{shortTitle}</span>
        </button>
    );
};

const ActionButtons = () => {
    const navigate = useNavigate();
    const { questionId } = useParams();

    const handleGoBack = () => {
        navigate(-1);
    };

    const handleShare = () => {
        shareContent({
            title: `Câu hỏi ToanThayBee`,
            text: `Hãy cùng làm câu hỏi này trên ToanThayBee!`,
            url: window.location.href
        });
    };

    return (
        <div className="flex flex-wrap gap-2">
            <ActionButton
                icon={ArrowLeft}
                title="Quay lại"
                shortTitle="Quay lại"
                onClick={handleGoBack}
            />
            <ActionButton
                icon={Share2}
                title="Chia sẻ"
                shortTitle="Chia sẻ"
                onClick={handleShare}
            />
        </div>
    );
};

const InfoRow = ({ icon: Icon, label, value, w = "w-48" }) => {
    const { loading } = useSelector((state) => state.questions);
    return (
        <div className="flex items-center justify-between gap-2 text-sm text-gray-800">
            <div className="flex items-center gap-2">
                <Icon size={16} className="text-sky-600" />
                <span className="font-medium text-gray-800">{label}:</span>
            </div>
            <LoadingText loading={loading} w={w}>
                <span>{value}</span>
            </LoadingText>
        </div>
    );
};

const InfoQuestion = () => {
    const { question, loading } = useSelector((state) => state.questions);
    const { codes } = useSelector((state) => state.codes);

    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            {/** Header **/}
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <LoadingText loading={loading} w="w-48">
                    <p className="font-Inter text-sm font-semibold">Chi tiết câu hỏi</p>
                </LoadingText>
                <div className="flex items-center gap-2 text-gray-500">
                    <RefreshCcw size={16} className="flex-shrink-0" />
                    <LoadingText loading={loading} w="w-40" color="bg-gray-200">
                        <p className="text-xs"><span className="hidden sm:inline">Cập nhật:</span> {formatDate(question?.updatedAt)}</p>
                    </LoadingText>
                </div>
            </div>

            <div className="flex flex-row gap-3 p-4 bg-white">
                <div className="w-full flex flex-col gap-4">
                    {/* Question metadata */}
                    <div className="w-full flex flex-col gap-2">
                        <InfoRow
                            icon={BookOpen}
                            label="Loại câu hỏi"
                            value={question?.typeOfQuestion === "TN" ? "Trắc nghiệm" : question?.typeOfQuestion === "TLN" ? "Tự luận ngắn" : question?.typeOfQuestion === "DS" ? "Đúng sai" : "Không rõ"}
                            w="w-32"
                        />
                        <InfoRow
                            icon={GraduationCap}
                            label="Khối"
                            value={question?.class || "Không rõ"}
                            w="w-24"
                        />
                        <InfoRow
                            icon={ListOrdered}
                            label="Chương"
                            value={codes?.["chapter"]?.find(c => c.code === question?.chapter)?.description || question?.chapter || "Không rõ"}
                            w="w-56"
                        />
                        <InfoRow
                            icon={BarChart2}
                            label="Độ khó"
                            value={question?.difficulty || "Không rõ"}
                            w="w-32"
                        />
                    </div>

                    {/* Description */}
                    {question?.description && (
                        <div className="flex items-start gap-2 text-sm text-gray-600">
                            <StickyNote size={16} className="text-sky-600 mt-0.5" />
                            <span className="font-medium text-gray-800">Mô tả:</span>
                            <span className="leading-relaxed">{question.description}</span>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

const QuestionAnswerAndSolution = () => {
    const { question, loading } = useSelector((state) => state.questions);
    const [showAnswerAndSolution, setShowAnswerAndSolution] = useState(false);

    const getCorrectAnswer = () => {
        if (question?.typeOfQuestion === "TLN") {
            return question?.correctAnswer;
        } else if (question?.typeOfQuestion === "TN" || question?.typeOfQuestion === "DS") {
            const correctStatements = question?.statements?.filter(statement => statement.isCorrect);
            if (correctStatements && correctStatements.length > 0) {
                return correctStatements.map((statement) => {
                    const originalIndex = question.statements.findIndex(s => s.id === statement.id);
                    return `${String.fromCharCode(65 + originalIndex)}: ${statement.content}`;
                }).join("\n");
            }
        }
        return null;
    };

    const correctAnswer = getCorrectAnswer();
    const hasSolution = question?.solution || question?.solutionUrl || question?.solutionImageUrl;

    // Don't show if no answer or solution
    if (!correctAnswer && !hasSolution) return null;

    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <p className="text-sm font-Inter font-semibold">Đáp án & Lời giải</p>
                <button
                    onClick={() => setShowAnswerAndSolution(!showAnswerAndSolution)}
                    className="text-xs flex items-center gap-2 px-2 py-1 rounded-md transition-all text-gray-700 hover:bg-gray-200"
                >
                    {showAnswerAndSolution ? <EyeOff size={16} /> : <Eye size={16} />}
                    <span>{showAnswerAndSolution ? "Ẩn đáp án & lời giải" : "Hiển thị đáp án & lời giải"}</span>
                </button>
            </div>

            {loading && (
                <div className="p-4 w-full h-[200px]">
                    <LoadingText loading={loading} w="w-full" h="h-full" rounded="rounded-md" />
                </div>
            )}

            {!loading && showAnswerAndSolution && (
                <div className="p-4 bg-white space-y-4">
                    {/* Correct Answer Section */}
                    {correctAnswer && (
                        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                            <div className="flex items-start gap-3">
                                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-green-600 flex items-center justify-center">
                                    <span className="text-white text-xs font-medium">✓</span>
                                </div>
                                <div className="flex-1">
                                    <h4 className="text-sm font-semibold text-green-800 mb-2">Đáp án đúng:</h4>
                                    <div className="text-sm text-green-700 leading-relaxed">
                                        <LatexRenderer text={correctAnswer} className="text-green-700" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Solution Section */}
                    {hasSolution && (
                        <div className="bg-sky-50 rounded-lg p-4 border border-sky-200">
                            <h4 className="text-sm font-semibold text-sky-800 mb-3">Lời giải:</h4>

                            {/* Video Solution */}
                            {question?.solutionUrl && (
                                <div className="mb-4">
                                    <YouTubePlayer
                                        url={question.solutionUrl}
                                        sizeClass="w-full aspect-video rounded-lg overflow-hidden border border-gray-200"
                                    />
                                </div>
                            )}

                            {/* Text Solution */}
                            {question?.solution && (
                                <div className="mb-4">
                                    <div className="text-sm text-sky-800 leading-relaxed">
                                        <MarkdownPreviewWithMath content={question.solution} />
                                    </div>
                                </div>
                            )}

                            {/* Solution Image */}
                            {question?.solutionImageUrl && (
                                <div className="flex flex-col items-center justify-center w-full">
                                    <img
                                        src={question.solutionImageUrl}
                                        alt="Solution"
                                        className="object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200"
                                    />
                                </div>
                            )}

                            {/* No solution text fallback */}
                            {!question?.solution && !question?.solutionUrl && !question?.solutionImageUrl && (
                                <p className="text-sm text-sky-700 italic">Không có lời giải chi tiết cho câu hỏi này.</p>
                            )}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

// Interactive Multiple Choice Component for TN questions
const InteractiveMultipleChoice = ({ question }) => {
    const [selectedAnswer, setSelectedAnswer] = useState(null);
    const [showResult, setShowResult] = useState(false);

    const handleAnswerSelect = (statementId) => {
        setSelectedAnswer(statementId);
        setShowResult(true);
    };

    const getStatementStyle = (statement, index) => {
        if (!showResult) {
            return selectedAnswer === statement.id
                ? "bg-sky-100 border-sky-300"
                : "bg-gray-50 border-gray-200 hover:bg-gray-100 cursor-pointer";
        }

        // Show results
        if (statement.isCorrect) {
            return "bg-green-100 border-green-300";
        } else if (selectedAnswer === statement.id && !statement.isCorrect) {
            return "bg-red-100 border-red-300";
        }
        return "bg-gray-50 border-gray-200";
    };

    const getIconStyle = (statement) => {
        if (!showResult) {
            return selectedAnswer === statement.id
                ? "bg-sky-600 text-white"
                : "bg-gray-400 text-white";
        }

        if (statement.isCorrect) {
            return "bg-green-600 text-white";
        } else if (selectedAnswer === statement.id && !statement.isCorrect) {
            return "bg-red-600 text-white";
        }
        return "bg-gray-400 text-white";
    };

    return (
        <div>
            <h3 className="text-sm font-semibold text-gray-800 mb-3">Các lựa chọn:</h3>
            <div className="flex flex-col gap-3">
                {[...question.statements]
                    .sort((a, b) => (a.order || 0) - (b.order || 0))
                    .map((statement, index) => (
                        <div
                            key={statement.id || index}
                            className={`p-3 rounded-lg border transition-all ${getStatementStyle(statement, index)}`}
                            onClick={() => !showResult && handleAnswerSelect(statement.id)}
                        >
                            <div className="flex items-start gap-3">
                                <span className={`flex-shrink-0 md:w-6 md:h-6 w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium ${getIconStyle(statement)}`}>
                                    {showResult && statement.isCorrect ? (
                                        <Check size={12} />
                                    ) : showResult && selectedAnswer === statement.id && !statement.isCorrect ? (
                                        <X size={12} />
                                    ) : (
                                        String.fromCharCode(65 + index)
                                    )}
                                </span>
                                <div className="flex-1">
                                    <div className="text-sm text-gray-800 leading-relaxed">
                                        <LatexRenderer text={statement.content} className="text-gray-800 md:text-base text-sm" />
                                    </div>
                                    {statement.imageUrl && (
                                        <div className="flex flex-col items-center justify-center w-full mt-2">
                                            <img
                                                src={statement.imageUrl}
                                                alt={`Statement ${String.fromCharCode(65 + index)}`}
                                                className="object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200"
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
            </div>
        </div>
    );
};

// Interactive True/False Component for DS questions
const InteractiveTrueFalse = ({ question }) => {
    const [answers, setAnswers] = useState({});
    const [showResult, setShowResult] = useState(false);

    const handleAnswerChange = (statementId, answer) => {
        setAnswers(prev => ({
            ...prev,
            [statementId]: answer
        }));
    };

    const handleCheckAnswers = () => {
        // Only allow checking if all statements have been answered
        const allAnswered = question.statements.every(statement =>
            answers.hasOwnProperty(statement.id)
        );

        if (allAnswered) {
            setShowResult(true);
        }
    };

    const isAllAnswered = question.statements.every(statement =>
        answers.hasOwnProperty(statement.id)
    );

    const getStatementStyle = (statement) => {
        if (!showResult) {
            return "bg-gray-50 border-gray-200";
        }

        const userAnswer = answers[statement.id];
        const isCorrect = userAnswer === statement.isCorrect;

        return isCorrect
            ? "bg-green-100 border-green-300"
            : "bg-red-100 border-red-300";
    };

    return (
        <div>
            <h3 className="text-sm font-semibold text-gray-800 mb-3">Đánh giá từng phát biểu:</h3>
            <div className="flex flex-col gap-3 mb-4">
                {[...question.statements]
                    .sort((a, b) => (a.order || 0) - (b.order || 0))
                    .map((statement, index) => (
                        <div
                            key={statement.id || index}
                            className={`p-3 rounded-lg border transition-all ${getStatementStyle(statement)}`}
                        >
                            <div className="flex flex-col gap-3">
                                <div className="flex items-start gap-3">
                                    <span className="flex-shrink-0 md:w-6 md:h-6 w-5 h-5 rounded-full bg-sky-600 text-white flex items-center justify-center text-xs font-medium">
                                        {String.fromCharCode(97 + index)}
                                    </span>
                                    <div className="flex-1">
                                        <div className="text-sm text-gray-800 leading-relaxed">
                                            <LatexRenderer text={statement.content} className="text-gray-800 md:text-base text-sm" />
                                        </div>
                                        {statement.imageUrl && (
                                            <div className="flex flex-col items-center justify-center w-full mt-2">
                                                <img
                                                    src={statement.imageUrl}
                                                    alt={`Statement ${String.fromCharCode(97 + index)}`}
                                                    className="object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200"
                                                />
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* True/False options */}
                                <div className="flex items-center gap-4 ml-9">
                                    <label className="flex items-center gap-2 cursor-pointer">
                                        <input
                                            type="radio"
                                            name={`statement-${statement.id}`}
                                            checked={answers[statement.id] === true}
                                            onChange={() => handleAnswerChange(statement.id, true)}
                                            disabled={showResult}
                                            className="w-4 h-4 accent-sky-600"
                                        />
                                        <span className="text-sm font-medium text-gray-800">Đúng</span>
                                        {showResult && statement.isCorrect === true && (
                                            <Check size={16} className="text-green-600" />
                                        )}
                                    </label>

                                    <label className="flex items-center gap-2 cursor-pointer">
                                        <input
                                            type="radio"
                                            name={`statement-${statement.id}`}
                                            checked={answers[statement.id] === false}
                                            onChange={() => handleAnswerChange(statement.id, false)}
                                            disabled={showResult}
                                            className="w-4 h-4 accent-sky-600"
                                        />
                                        <span className="text-sm font-medium text-gray-800">Sai</span>
                                        {showResult && statement.isCorrect === false && (
                                            <Check size={16} className="text-green-600" />
                                        )}
                                    </label>
                                </div>
                            </div>
                        </div>
                    ))}
            </div>

            {!showResult && (
                <button
                    onClick={handleCheckAnswers}
                    disabled={!isAllAnswered}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${isAllAnswered
                        ? 'bg-sky-600 text-white hover:bg-sky-700'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                >
                    Kiểm tra đáp án
                </button>
            )}
        </div>
    );
};

// Interactive Short Answer Component for TLN questions
const InteractiveShortAnswer = ({ question }) => {
    const [userAnswer, setUserAnswer] = useState("");
    const [showResult, setShowResult] = useState(false);

    const handleInputChange = (e) => {
        // Replace commas with periods as specified
        const value = e.target.value.replace(/,/g, '.');
        setUserAnswer(value);
    };

    const handleCheckAnswer = () => {
        setShowResult(true);
    };

    const isCorrect = () => {
        if (!question.correctAnswer || !userAnswer.trim()) return false;

        // Normalize both answers for comparison
        const normalizedCorrect = question.correctAnswer.trim().toLowerCase();
        const normalizedUser = userAnswer.trim().toLowerCase();

        return normalizedCorrect === normalizedUser;
    };

    const getInputStyle = () => {
        if (!showResult) {
            return "border-gray-300 focus:border-sky-500 focus:ring-sky-500";
        }

        return isCorrect()
            ? "border-green-500 bg-green-50"
            : "border-red-500 bg-red-50";
    };

    return (
        <div>
            <h3 className="text-sm font-semibold text-gray-800 mb-3">Nhập câu trả lời:</h3>
            <div className="flex flex-col gap-3">
                <div className="flex items-center gap-3">
                    <input
                        type="text"
                        value={userAnswer}
                        onChange={handleInputChange}
                        placeholder="Nhập câu trả lời của bạn..."
                        disabled={showResult}
                        className={`flex-1 px-3 py-2 border rounded-md text-sm transition-colors ${getInputStyle()}`}
                    />

                    {!showResult && (
                        <button
                            onClick={handleCheckAnswer}
                            disabled={!userAnswer.trim()}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${userAnswer.trim()
                                ? 'bg-sky-600 text-white hover:bg-sky-700'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                        >
                            Kiểm tra
                        </button>
                    )}
                </div>

                {showResult && (
                    <div className={`p-3 rounded-lg border ${isCorrect()
                        ? 'bg-green-100 border-green-300'
                        : 'bg-red-100 border-red-300'
                        }`}>
                        <div className="flex items-center gap-2">
                            {isCorrect() ? (
                                <Check size={16} className="text-green-600" />
                            ) : (
                                <X size={16} className="text-red-600" />
                            )}
                            <span className={`text-sm font-medium ${isCorrect() ? 'text-green-800' : 'text-red-800'
                                }`}>
                                {isCorrect() ? 'Chính xác!' : 'Không chính xác'}
                            </span>
                        </div>

                        {!isCorrect() && question.correctAnswer && (
                            <div className="mt-2 text-sm text-gray-700">
                                <span className="font-medium">Đáp án đúng: </span>
                                <LatexRenderer text={question.correctAnswer} className="text-green-700 font-medium" />
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

const QuestionContentAndStatements = () => {
    const { question, loading } = useSelector((state) => state.questions);

    const renderInteractiveComponent = () => {
        if (!question) return null;

        switch (question.typeOfQuestion) {
            case "TN":
                return <InteractiveMultipleChoice question={question} />;
            case "DS":
                return <InteractiveTrueFalse question={question} />;
            case "TLN":
                return <InteractiveShortAnswer question={question} />;
            default:
                return null;
        }
    };

    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <p className="text-sm font-Inter font-semibold">Nội dung câu hỏi</p>
            </div>

            {loading && (
                <div className="p-4 w-full h-[300px]">
                    <LoadingText loading={loading} w="w-full" h="h-full" rounded="rounded-md" />
                </div>
            )}

            {!loading && (
                <div className="p-4 bg-white">
                    {/* Question Content */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 mb-4">
                        <div className="text-sm text-gray-800 leading-relaxed">
                            <LatexRenderer text={question?.content} className="text-gray-800 md:text-base text-sm" />
                        </div>
                        {question?.imageUrl && (
                            <div className="flex flex-col items-center justify-center w-full mt-4">
                                <img
                                    src={question.imageUrl}
                                    alt="Question"
                                    className="object-contain max-w-full h-auto rounded-lg shadow-sm border border-gray-200"
                                />
                            </div>
                        )}
                    </div>

                    {/* Interactive Components */}
                    {renderInteractiveComponent()}
                </div>
            )}
        </div>
    );
};

const QuestionPage = () => {
    const { questionId } = useParams();
    const { question, loading } = useSelector((state) => state.questions);
    const dispatch = useDispatch();


    useEffect(() => {
        dispatch(fetchPublicQuestionById(questionId));
        dispatch(fetchCodesByType(["chapter", "user type"]));
    }, [dispatch, questionId]);

    return (
        <UserLayout>
            <div className="container flex flex-col mb-9">
                <div className="w-full flex flex-col md:flex-row gap-2 justify-between items-center">
                    <div className="items-start justify-start flex flex-wrap gap-2">
                        <FileQuestion className="text-sky-600" />
                        <LoadingText loading={loading} w="w-48">
                            <div className="flex flex-row gap-1">
                                <p className="text-lg font-semibold">Câu hỏi #{questionId}</p>
                                {question?.typeOfQuestion && (
                                    <p className={`h-fit py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 w-fit ${question.typeOfQuestion === 'TN'
                                        ? 'border-blue-300 text-blue-600'
                                        : 'border-green-300 text-green-600'
                                        }`}>
                                        {question.typeOfQuestion === "TN" ? "Trắc nghiệm" : question.typeOfQuestion === "TLN" ? "Tự luận ngắn" : question.typeOfQuestion === "DS" ? "Đúng sai" : "Không rõ"}
                                    </p>
                                )}
                            </div>
                        </LoadingText>
                    </div>
                    <div className="flex flex-row justify-between items-center gap-2">
                        <ActionButtons />
                    </div>
                </div>

                <hr className="my-6 border-gray-300" />

                <div className="flex  flex-row gap-4">
                    <div className="flex-1 flex flex-col md:flex-row gap-4">
                        <div className="flex-1 flex flex-col gap-4">

                            <InfoQuestion />
                            <QuestionContentAndStatements />
                            <QuestionAnswerAndSolution />
                        </div>
                    </div>

                </div>

                {/* AI Chat Widget */}
                <AIChatWidget />
            </div>
        </UserLayout>
    );
};

export default QuestionPage;
