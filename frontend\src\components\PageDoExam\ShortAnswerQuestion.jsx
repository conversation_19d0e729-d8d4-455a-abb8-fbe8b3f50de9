import React from 'react';
import LatexRenderer from '../latex/RenderLatex';
import QuestionImage from './QuestionImage';
import { Bookmark } from 'lucide-react';
import ReportButton from '../button/ReportButton';
import NoTranslate from '../utils/NoTranslate';
import { useSelector } from "react-redux";
import QuestionContent from './QuestionContent';
import { useDispatch } from "react-redux";
import { setErrorMessage } from "src/features/state/stateApiSlice";
import { setAnswers, submitAnswerWithAttempt } from "src/features/doExam/doExamSlice";

const ShortAnswerQuestion = ({ question, index }) => {
    const { darkMode, fontSize, imageSize, isTimeUp, attemptId, answersTLN } = useSelector((state) => state.doExam);
    const dispatch = useDispatch();

    const handleSelectAnswerTLN = (questionId, answerContent, type) => {
        // Không cho phép làm bài nếu đã hết thời gian
        if (isTimeUp) {
            dispatch(setErrorMessage("Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!"));
            return;
        }

        const formattedAnswer = answerContent.trim().replace(",", ".");
        if (!formattedAnswer) return;

        dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));

        dispatch(submitAnswerWithAttempt({
            questionId,
            answerContent: formattedAnswer,
            type,
            attemptId
        }));
    }

    const getDefaultValue = (questionId) => {
        const matched = answersTLN.find((ans) => ans.questionId === questionId);
        const content = matched?.answerContent?.replace(/^"|"$/g, "") || "";

        return content;
    }

    return (
        <div
            key={question.id + "TLN"}
            // ref={(el) => setRef(question.id, el)}
            className={`flex flex-col avoid-page-break gap-2 rounded-md p-3 transition`}
        >
            <QuestionContent question={question} index={index} />

            <input
                placeholder="Nhập câu trả lời..." translate="no"
                defaultValue={getDefaultValue(question.id)}
                style={{ fontSize: `${fontSize}px` }}
                className={`border rounded p-2 resize-none w-[12rem]
              ${darkMode
                        ? "bg-gray-700 text-white border-gray-500 placeholder-gray-300"
                        : "bg-white text-black border-gray-300"}
              ${isTimeUp ? 'cursor-not-allowed opacity-60' : ''}`}
                onBlur={(e) => handleSelectAnswerTLN(question.id, e.target.value, "TLN")}
                disabled={isTimeUp}
            />
        </div>
    )
}

export default ShortAnswerQuestion;