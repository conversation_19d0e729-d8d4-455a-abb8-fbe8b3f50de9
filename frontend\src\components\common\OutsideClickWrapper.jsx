import { useEffect, useRef } from "react";

const OutsideClickWrapper = ({
    onClickOutside,
    children,
    className,
    ignoreOutsideClick = "",
}) => {
    const wrapperRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            const target = e.target;

            const isInsideWrapper = wrapperRef.current?.contains(target);

            let isInIgnoredElement = false;
            if (ignoreOutsideClick) {
                const ignoredElements = document.querySelectorAll(`.${ignoreOutsideClick}`);
                isInIgnoredElement = Array.from(ignoredElements).some((el) => el.contains(target));
            }

            if (!isInsideWrapper && !isInIgnoredElement) {
                onClickOutside?.();
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [onClickOutside, ignoreOutsideClick]);

    return (
        <div className={className} ref={wrapperRef}>
            {children}
        </div>
    );
};

export default OutsideClickWrapper;
