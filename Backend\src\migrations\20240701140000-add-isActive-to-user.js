'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Kiểm tra xem cột isActive đã tồn tại chưa
    const tableInfo = await queryInterface.describeTable('user');
    if (!tableInfo.isActive) {
      await queryInterface.addColumn('user', 'isActive', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Trạng thái hoạt động của người dùng'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Kiểm tra xem cột isActive có tồn tại không
    const tableInfo = await queryInterface.describeTable('user');
    if (tableInfo.isActive) {
      await queryInterface.removeColumn('user', 'isActive');
    }
  }
};
