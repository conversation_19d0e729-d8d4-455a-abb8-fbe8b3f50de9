'use strict'

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Step 1: Add dayOfWeek1 column (copy of dayOfWeek)
    await queryInterface.addColumn('class', 'dayOfWeek1', {
      type: Sequelize.STRING,
      references: {
        model: 'allCode',
        key: 'code'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Step 2: Copy data from dayOfWeek to dayOfWeek1
    await queryInterface.sequelize.query(`
      UPDATE class SET dayOfWeek1 = dayOfWeek WHERE dayOfWeek IS NOT NULL
    `);

    // Step 3: Add dayOfWeek2 column (for second session)
    await queryInterface.addColumn('class', 'dayOfWeek2', {
      type: Sequelize.STRING,
      references: {
        model: 'allCode',
        key: 'code'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Step 4: Remove the old dayOfWeek column
    await queryInterface.removeColumn('class', 'dayOfWeek');
  },

  async down(queryInterface, Sequelize) {
    // Step 1: Add back the original dayOfWeek column
    await queryInterface.addColumn('class', 'dayOfWeek', {
      type: Sequelize.STRING,
      references: {
        model: 'allCode',
        key: 'code'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Step 2: Copy data from dayOfWeek1 back to dayOfWeek
    await queryInterface.sequelize.query(`
      UPDATE class SET dayOfWeek = dayOfWeek1 WHERE dayOfWeek1 IS NOT NULL
    `);

    // Step 3: Remove the new columns
    await queryInterface.removeColumn('class', 'dayOfWeek1');
    await queryInterface.removeColumn('class', 'dayOfWeek2');
  }
}
