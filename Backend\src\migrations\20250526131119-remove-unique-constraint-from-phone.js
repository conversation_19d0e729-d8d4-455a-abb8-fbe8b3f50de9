'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    try {
      // Ki<PERSON><PERSON> tra xem có unique constraint nào cho phone không
      const tableInfo = await queryInterface.describeTable('user');

      // Nếu phone có unique constraint, bỏ nó đi
      if (tableInfo.phone && tableInfo.phone.unique) {
        await queryInterface.removeConstraint('user', 'user_phone_key');
      }

      // Hoặc thử bỏ index unique nếu có
      try {
        await queryInterface.removeIndex('user', 'user_phone_unique');
      } catch (error) {
        // Ignore error nếu index không tồn tại
        console.log('Index user_phone_unique không tồn tại hoặc đã được xóa');
      }

      // Thử bỏ constraint với tên khác có thể có
      try {
        await queryInterface.removeConstraint('user', 'phone_unique');
      } catch (error) {
        // Ignore error nếu constraint không tồn tại
        console.log('Constraint phone_unique không tồn tại hoặc đã được xóa');
      }

    } catch (error) {
      console.log('Lỗi khi bỏ unique constraint cho phone:', error.message);
      // Không throw error để migration có thể tiếp tục
    }
  },

  async down(queryInterface, Sequelize) {
    // Thêm lại unique constraint cho phone
    try {
      await queryInterface.addConstraint('user', {
        fields: ['phone'],
        type: 'unique',
        name: 'user_phone_unique'
      });
    } catch (error) {
      console.log('Lỗi khi thêm lại unique constraint cho phone:', error.message);
    }
  }
};
