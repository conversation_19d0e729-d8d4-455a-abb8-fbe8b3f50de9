'use strict';

export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('ExamComments', 'commentId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'ExamComments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('ExamComments', 'commentId'); // 🛠 sửa lại đúng tên cột
  },
};
