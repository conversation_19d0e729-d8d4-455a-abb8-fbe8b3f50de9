/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { blobLikeSchema } from "../../types/blobs.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsUploadV1DocumentUpload = {
  /**
   * The File object (not file name) to be uploaded.
   *
   * @remarks
   *  To upload a file and specify a custom file name you should format your request as such:
   *  ```bash
   *  file=@path/to/your/file.jsonl;filename=custom_name.jsonl
   *  ```
   *  Otherwise, you can just keep the original file name:
   *  ```bash
   *  file=@path/to/your/file.jsonl
   *  ```
   */
  file: components.FileT | Blob;
};

export type LibrariesDocumentsUploadV1Request = {
  libraryId: string;
  requestBody: LibrariesDocumentsUploadV1DocumentUpload;
};

/** @internal */
export const LibrariesDocumentsUploadV1DocumentUpload$inboundSchema: z.ZodType<
  LibrariesDocumentsUploadV1DocumentUpload,
  z.ZodTypeDef,
  unknown
> = z.object({
  file: components.FileT$inboundSchema,
});

/** @internal */
export type LibrariesDocumentsUploadV1DocumentUpload$Outbound = {
  file: components.FileT$Outbound | Blob;
};

/** @internal */
export const LibrariesDocumentsUploadV1DocumentUpload$outboundSchema: z.ZodType<
  LibrariesDocumentsUploadV1DocumentUpload$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsUploadV1DocumentUpload
> = z.object({
  file: components.FileT$outboundSchema.or(blobLikeSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsUploadV1DocumentUpload$ {
  /** @deprecated use `LibrariesDocumentsUploadV1DocumentUpload$inboundSchema` instead. */
  export const inboundSchema =
    LibrariesDocumentsUploadV1DocumentUpload$inboundSchema;
  /** @deprecated use `LibrariesDocumentsUploadV1DocumentUpload$outboundSchema` instead. */
  export const outboundSchema =
    LibrariesDocumentsUploadV1DocumentUpload$outboundSchema;
  /** @deprecated use `LibrariesDocumentsUploadV1DocumentUpload$Outbound` instead. */
  export type Outbound = LibrariesDocumentsUploadV1DocumentUpload$Outbound;
}

export function librariesDocumentsUploadV1DocumentUploadToJSON(
  librariesDocumentsUploadV1DocumentUpload:
    LibrariesDocumentsUploadV1DocumentUpload,
): string {
  return JSON.stringify(
    LibrariesDocumentsUploadV1DocumentUpload$outboundSchema.parse(
      librariesDocumentsUploadV1DocumentUpload,
    ),
  );
}

export function librariesDocumentsUploadV1DocumentUploadFromJSON(
  jsonString: string,
): SafeParseResult<
  LibrariesDocumentsUploadV1DocumentUpload,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      LibrariesDocumentsUploadV1DocumentUpload$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'LibrariesDocumentsUploadV1DocumentUpload' from JSON`,
  );
}

/** @internal */
export const LibrariesDocumentsUploadV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsUploadV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  RequestBody: z.lazy(() =>
    LibrariesDocumentsUploadV1DocumentUpload$inboundSchema
  ),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "RequestBody": "requestBody",
  });
});

/** @internal */
export type LibrariesDocumentsUploadV1Request$Outbound = {
  library_id: string;
  RequestBody: LibrariesDocumentsUploadV1DocumentUpload$Outbound;
};

/** @internal */
export const LibrariesDocumentsUploadV1Request$outboundSchema: z.ZodType<
  LibrariesDocumentsUploadV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsUploadV1Request
> = z.object({
  libraryId: z.string(),
  requestBody: z.lazy(() =>
    LibrariesDocumentsUploadV1DocumentUpload$outboundSchema
  ),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    requestBody: "RequestBody",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsUploadV1Request$ {
  /** @deprecated use `LibrariesDocumentsUploadV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesDocumentsUploadV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsUploadV1Request$outboundSchema` instead. */
  export const outboundSchema =
    LibrariesDocumentsUploadV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsUploadV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsUploadV1Request$Outbound;
}

export function librariesDocumentsUploadV1RequestToJSON(
  librariesDocumentsUploadV1Request: LibrariesDocumentsUploadV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsUploadV1Request$outboundSchema.parse(
      librariesDocumentsUploadV1Request,
    ),
  );
}

export function librariesDocumentsUploadV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesDocumentsUploadV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesDocumentsUploadV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesDocumentsUploadV1Request' from JSON`,
  );
}
