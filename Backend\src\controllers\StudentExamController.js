import * as studentExamService from '../services/student.exam.service.js';

export const getExamRatingStatistics = async (req, res) => {
    const { examId } = req.params;
    const userId = req.user.id;

    if (!examId) {
        return res.status(400).json({ message: 'examId là bắt buộc.' });
    }

    const { avgStar, starCount, isSaveCount, isDoneCount, studentStatus } = await studentExamService.getExamRatingStatistics({
        examId,
        avgStar: true,
        starCount: true,
        isSave: true,
        isDone: true,
        userId: userId
    });

    return res.status(200).json({
        message: '<PERSON><PERSON><PERSON> số lượng người lưu, làm bài, đ<PERSON>h gi<PERSON> thành công',
        data: {
            avgStar,
            starCount,
            isSaveCount,
            isDoneCount,
            studentStatus,
        }
    });
}

export const saveExamForUser = async (req, res) => {
    const userId = req.user.id;
    const { examId } = req.body;

    const data = await studentExamService.toggleSaveExam(userId, examId);

    return res.status(200).json({
        message: 'Thành công.',
        data
    });
};

export const rateExamForUser = async (req, res) => {
    const userId = req.user.id;
    const { examId, star } = req.body;


    const data = await studentExamService.rateExam(userId, examId, star);
    const { avgStar, starCount } = await studentExamService.getExamRatingStatistics({
        examId,
        avgStar: true,
        starCount: true,
        userId: userId
    });
    return res.status(200).json({
        message: 'Đánh giá thành công.',
        data: { ...data, avgStar, starCount }
    });

};