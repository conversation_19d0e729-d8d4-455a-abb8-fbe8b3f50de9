import api from "./api";

export const getUncompletedLearningItemAPI = ({ page = 1, limit = 10 }) => {
    return api.get(`/v1/user/learning-item/uncompleted`, {
        params: {
            page,
            limit,
        }
    });
}

export const getLearningItemWeekendAPI = ({ startOfWeek, endOfWeek }) => {
    const params = {};

    if (startOfWeek) params.startOfWeek = startOfWeek;
    if (endOfWeek) params.endOfWeek = endOfWeek;

    return api.get(`v1/user/weekend/learning-item`, { params });
};

export const getLearningItemDayAPI = (day) => {
    const params = {};
    if (day) params.day = day;

    return api.get(`v1/user/day/learning-item`, { params });
}

export const getLearningItemMonthAPI = ({ firstDay, lastDay }) => {
    const params = {};
    if (firstDay) params.firstDay = firstDay;
    if (lastDay) params.lastDay = lastDay;

    return api.get(`v1/user/month/learning-item`, { params });
}
